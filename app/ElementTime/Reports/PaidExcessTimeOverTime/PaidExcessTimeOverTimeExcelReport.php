<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\PaidExcessTimeOverTime;

use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRuleWork;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class PaidExcessTimeOverTimeExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\StaffManagerFlag::class,
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'paid-excess-time-over-time';
    const TYPE_SLUG = 'paid-excess-time-over-time.xls';

    /** @var string|PaidExcessTimeOverTimeReportConfig  */
    public $c = PaidExcessTimeOverTimeReportConfig::class;

    use HasPayRunStartAndPayRunEndParameters;

    /** @throws \Throwable */
    protected function buildReportData(): void
    {
        $data = $this->collectReportData();
        $this->eagerLoadReportData($data);

        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetData($data, $sheet);

        $this->sheets = [
            $sheet,
        ];
    }

    /**
     * @return TimeSheetExcessTimeItemRuleWork[]|Collection
     * @throws \Throwable
     */
    private function collectReportData(): array|Collection
    {
        $user = CurrentUser::getUser();

        $constraints = [
            ['IN', 'ExcessTimeRuleSplitItem.ruleType', [ExcessTimeRuleTypes\PaidHoursType::ID, ExcessTimeRuleTypes\AdditionalHoursType::ID]],
            ['BETWEEN', 'TimeSheetDay.date', [$this->getStartDate(), $this->getEndDate()]],
            ['Workflow.status', '=', ApprovedType::ID],
        ];

        if (
            !is_null($user)
            && !$user->access->isPayrollOfficer(true)
            && !$user->access->isBusinessIntelligence(true)
        ) {
            $reportUserIds = $this->getDirectReportUserIds($user, $user->doesIncludeIndirectReportsOnReports);

            $constraints[] = ['IN', 'Workflow.ownerUser_id', $reportUserIds];
        }

        $joins = [
            ['TimeSheetDayTime', 'TimeSheetDayTime.id', '=', 'TimeSheetExcessTimeItemRuleWork.timeSheetDayTime_id'],
            ['TimeSheetDay', 'TimeSheetDay.id', '=', 'TimeSheetDayTime.timeSheetDay_id'],
            ['TimeSheetExcessTimeItemRule', 'TimeSheetExcessTimeItemRule.id', '=', 'TimeSheetExcessTimeItemRuleWork.timeSheetExcessTimeItemRule_id'],
            ['TimeSheetExcessTimeItem', 'TimeSheetExcessTimeItem.id', '=', 'TimeSheetExcessTimeItemRule.timeSheetExcessTimeItem_id'],
            ['TimeSheetExcessTimeItemRuleWorkSplitItem', 'TimeSheetExcessTimeItemRuleWorkSplitItem.timeSheetExcessTimeItemRuleWork_id', '=', 'TimeSheetExcessTimeItemRuleWork.id'],
            ['ExcessTimeRuleSplitItem', 'ExcessTimeRuleSplitItem.id', '=', 'TimeSheetExcessTimeItemRuleWorkSplitItem.excessTimeRuleSplitItem_id'],
            ['Workflow', [
                ['Workflow.rel_type', 'LIKE', '"%' . TimeSheetExcessTimeItem::class . '"'],
                ['Workflow.rel_id', '=', 'TimeSheetExcessTimeItem.id'],
            ], 'inner'],
        ];

        return TimeSheetExcessTimeItemRuleWork::q(
            constraints: $constraints,
            joins: $joins,
            groupBy: 'TimeSheetExcessTimeItemRuleWork.id',
        )->many();
    }

    /**
     * @param TimeSheetExcessTimeItemRuleWork[]|Collection $data
     */
    private function eagerLoadReportData(array|Collection $data): void
    {
        $data->load([
            'work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'work.workOrderType.workOrderTypeActivityTypes.activityType',
            'work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'time.type.project',
            'time.model',
            'time.timeSheetDay',
            'time.userShiftDay.userShift.userRole.role',
            'time.userShiftDay.userShift.userRole.user.userManagers.manager',
            'time.userShiftDay.userShift.userRole.userRoleManagers.manager',
            'time.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'rule.item.timeSheetExcessTime.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleMasters',
            'rule.item.timeSheetExcessTime.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleProjects',
            'rule.item.timeSheetExcessTime.items',
            'rule.excessTimeRule',
            'rule.excessTimeGroupSectionLevelRule.excessTimeRule',
            'rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
        ]);
    }

    /**
     * @param TimeSheetExcessTimeItemRuleWork[]|Collection $excessTimeRuleWorks
     *
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     */
    private function fetchSheetData(array|Collection $excessTimeRuleWorks, \stdClass $sheet): void
    {
        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = [
            $this->createParentColumn('Staff', 5),
            $this->createParentColumn('Cost centre', 2),
            $this->createParentColumn('Paid amounts', 3),
        ];
        $table->columns = [
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Direct manager'),
            $this->createColumn('Department'),
            $this->createColumn('Name'),
            $this->createColumn('Number'),
            $this->createColumn('Recorded hours'),
            $this->createColumn('Adjusted hours'),
            $this->createColumn('Amount paid'),
        ];
        $table->data = [];
        $entries = [];

        foreach ($excessTimeRuleWorks as $excessTimeRuleWork) {
            $timeSheetExcessTimeItem = $excessTimeRuleWork->rule->item;
            if (!$timeSheetExcessTimeItem->isActive) {
                continue;
            }

            $entry = static::getOrSetRecordEntry($entries, $excessTimeRuleWork);

            foreach ($excessTimeRuleWork->splitItems as $splitItem) {
                if (
                    $splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\PaidHoursType::class)
                    || $splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\AdditionalHoursType::class)
                ) {
                    $entry->hoursRecorded += $splitItem->actualHours;
                    $entry->hoursAdjusted += $splitItem->adjustedHours;
                    $entry->amountPaid += $splitItem->adjustedHours * $timeSheetExcessTimeItem->timeSheetExcessTime->hourlyRate;
                }
            }
        }

        usort($entries, function ($prev, $next) {
            return $prev->userExternalId < $next->userExternalId
                ? -1
                : 1;
        });

        $alternate = true;
        $lastUser = null;

        foreach ($entries as $entry) {
            if ($lastUser != $entry->userExternalId) {
                $alternate = !$alternate;
                $lastUser = $entry->userExternalId;
            }

            $table->data[] = [
                'config' => [
                    'color' => $alternate ? 'EEEEF3' : 'FFFFFF',
                ],
                'data' => [
                    $entry->userExternalId,
                    $entry->userName,
                    $entry->roleName,
                    $entry->managerName,
                    $entry->departmentName,
                    $entry->costCentreName,
                    $entry->costCentreNumber,
                    $this->parseFloat($entry->hoursRecorded),
                    $this->parseFloat($entry->hoursAdjusted),
                    $this->parseFloat($entry->amountPaid),
                ],
            ];
        }

        $sheet->data[] = $table;
    }

    /**
     * @return object{
     *     userExternalId: string,
     *     userName: string,
     *     roleName: string,
     *     managerName: string,
     *     departmentName: string,
     *     costCentreName: string,
     *     costCentreNumber: string,
     *     hoursRecorded: float,
     *     hoursAdjusted: float,
     *     amountPaid: float,
     * }
     *
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     */
    private static function getOrSetRecordEntry(&$entries, TimeSheetExcessTimeItemRuleWork $excessTimeItemRuleWork): \stdClass
    {
        $timeSheetDayTime = $excessTimeItemRuleWork->time;
        $userRole = $timeSheetDayTime->userShiftDay->userShift->userRole;

        $entryId = implode('_', [
            $userRole->id,
            $timeSheetDayTime->timeRecordingMethodClass::ID,
            $excessTimeItemRuleWork->formattedCode,
        ]);


        if (!isset($entries[$entryId])) {
            $user = $userRole->user;
            $date = $timeSheetDayTime->timeSheetDay->date;

            $activeUserRoleManager = $userRole->repository->getActiveUserRoleManager($date);
            $manager = !is_null($activeUserRoleManager)
                ? $activeUserRoleManager->manager
                : $user->repository->getManager($date);
            $activeUserRoleDepartments = $userRole->getActiveUserRoleDepartments($date);

            $entry = new \stdClass();
            $entry->userExternalId = $userRole->repository->getExternalId();
            $entry->userName = $user->fullName;
            $entry->roleName = $userRole->role->name;
            $entry->managerName = !is_null($manager)
                ? $manager->fullName
                : '';
            $entry->departmentName = count($activeUserRoleDepartments) > 0
                ? $activeUserRoleDepartments[0]->department->name
                : '';

            $entry->costCentreName = $excessTimeItemRuleWork->name;
            $entry->costCentreNumber = $excessTimeItemRuleWork->formattedCode;

            $entry->hoursRecorded = 0;
            $entry->hoursAdjusted = 0;
            $entry->amountPaid = 0;

            $entries[$entryId] = $entry;
        }

        return $entries[$entryId];
    }
}
