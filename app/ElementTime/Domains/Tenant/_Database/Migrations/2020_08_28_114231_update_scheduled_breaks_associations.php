<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: TimeSheetItemDayTimeBreak
        Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
            $table->unsignedBigInteger('userShiftDayTimeBreak_id')->nullable();
            $table->foreign('userShiftDayTimeBreak_id', 'tsidtb_usdtb_fn')->references('id')->on('UserShiftDayTimeBreak')->onUpdate('cascade')->onDelete('set null');
        });
        //endregion Table: TimeSheetItemDayTimeBreak

        //region Table: LeaveRequestDayBreak
        Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
            $table->unsignedBigInteger('userShiftDayTimeBreak_id')->nullable();
            $table->foreign('userShiftDayTimeBreak_id', 'lrdb_usdtb_fn')->references('id')->on('UserShiftDayTimeBreak')->onUpdate('cascade')->onDelete('set null');
        });
        //endregion Table: LeaveRequestDayBreak
    }

    public function down(): void
    {
        //region Table: LeaveRequestDayBreak

        try {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropForeign('lrdb_usdtb_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropIndex('lrdb_usdtb_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('LeaveRequestDayBreak', 'userShiftDayTimeBreak_id')) {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropColumn('userShiftDayTimeBreak_id');
            });
        }

        //endregion Table: LeaveRequestDayBreak

        //region Table: TimeSheetItemDayTimeBreak

        try {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropForeign('tsidtb_usdtb_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropIndex('tsidtb_usdtb_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetItemDayTimeBreak', 'userShiftDayTimeBreak_id')) {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('userShiftDayTimeBreak_id');
            });
        }

        // Update to make sure null values can be added to d_e_p_r_e_c_a_t_e_d field
        try {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropForeign('tsidtb_usdb_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropForeign('tsidtb_usdb_fn');
                $table->dropIndex('tsidtb_usdb_fn');
            });
        } catch (Throwable) {
        }

        Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
            $table->unsignedBigInteger('userShiftDayBreak_id')->nullable()->change();
        });

        //endregion Table: TimeSheetItemDayTimeBreak
    }
};
