<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\ElementTime\Tenant\Unit\Workflows\Traits;

use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelIssue;
use Element\ElementTime\Domains\Tenant\Workflows\Repositories\ModelIssueRepository;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueLevels\InfoLevel;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueLevels\StopperLevel;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueLevels\WarningLevel;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\UnderRecordedHoursDayType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Domains\Tenant\Workflows\Traits\HasIssuesRepository;
use Illuminate\Database\Eloquent\Collection;
use Mockery\MockInterface;
use Tests\ElementTime\Tenant\TestCase;

class HasIssueRepositoryTest extends TestCase
{
    use HasIssuesRepository;

    private \Element\Core\Support\Domains\Model\Model|\Element\ElementTime\Domains\Tenant\Workflows\Contracts\ModelIssueRelModelContract|null $model = null;

    public function getModel(): \Element\Core\Support\Domains\Model\Model|\Element\ElementTime\Domains\Tenant\Workflows\Contracts\ModelIssueRelModelContract
    {
        if (is_null($this->model)) {
            $this->model = new TimeSheet;
        }

        return $this->model;
    }

    private function getMockModelIssue(array $attributes = [], bool $doesOverrideDescriptionMethod = false): MockInterface|ModelIssue
    {
        $issueMock = mock(new ModelIssue)->makePartial();

        foreach ($attributes as $attribute => $value) {
            $issueMock->{$attribute} = $value;
        }

        $issueMock->repository = mock(ModelIssueRepository::class)->makePartial();
        $issueMock->repository->shouldReceive('getModel')->andReturn($issueMock);

        if ($doesOverrideDescriptionMethod) {
            $issueMock->repository->shouldReceive('getHtmlDescription')->andReturn('');
        }

        return $issueMock;
    }

    private static function buildWhereForColumn(string $column, string $value): string
    {
        return "`" . $column . "` = '" . $value . "'";
    }

    public function testQueryActiveIssuesByStatusByLevel()
    {
        foreach (WorkflowStatusTypes\BaseType::CHILDREN as $workFlowStatusType) {
            /** @var \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Relations\MorphMany|\Illuminate\Database\Query\Builder $query */
            $query = $this->queryActiveIssuesByStatusByLevel($workFlowStatusType);

            $this->assertStringContainsString(static::buildWhereForColumn('workflowStatus', $workFlowStatusType::ID), $query->toRawSql());
        }

        $query = $this->queryActiveIssuesByStatusByLevel(level: InfoLevel::class);
        $this->assertStringContainsString(static::buildWhereForColumn('level', InfoLevel::ID), $query->toRawSql());
    }

    public function testGetActiveIssuesByStatusByLevelWithoutValidation()
    {
        $approvedStopperIssue = $this->getMockModelIssue([
            'workflowStatus' => WorkflowStatusTypes\ApprovedType::ID,
            'level' => StopperLevel::ID,
        ]);

        $approvedWarningIssue = $this->getMockModelIssue([
            'workflowStatus' => WorkflowStatusTypes\ApprovedType::ID,
            'level' => WarningLevel::ID,
        ]);

        $approvedInfoIssue = $this->getMockModelIssue([
            'workflowStatus' => WorkflowStatusTypes\ApprovedType::ID,
            'level' => InfoLevel::ID,
        ]);

        $this->getModel()->setRelation('activeIssues', new Collection([
            $approvedStopperIssue,
            $approvedWarningIssue,
            $approvedInfoIssue,
        ]));

        $approvedStopperIssues = $this->getActiveIssuesByStatusByLevel(WorkflowStatusTypes\ApprovedType::class, StopperLevel::class, false, false);
        $this->assertEquals($approvedStopperIssue->workflowStatusClass, $approvedStopperIssues[0]->workflowStatusClass);
        $this->assertEquals($approvedStopperIssue->levelClass, $approvedStopperIssues[0]->levelClass);

        $approvedWarningIssues = $this->getActiveIssuesByStatusByLevel(WorkflowStatusTypes\ApprovedType::class, WarningLevel::class, false, false);
        $this->assertEquals($approvedWarningIssue->workflowStatusClass, $approvedWarningIssues[0]->workflowStatusClass);
        $this->assertEquals($approvedWarningIssue->levelClass, $approvedWarningIssues[0]->levelClass);

        $approvedInfoIssues = $this->getActiveIssuesByStatusByLevel(WorkflowStatusTypes\ApprovedType::class, InfoLevel::class, false, false);
        $this->assertEquals($approvedInfoIssue->workflowStatusClass, $approvedInfoIssues[0]->workflowStatusClass);
        $this->assertEquals($approvedInfoIssue->levelClass, $approvedInfoIssues[0]->levelClass);
    }

    public function testGetActiveIssuesByStatusByLevelWithValidationOnInvalidIssue()
    {
        $approvedInfoLevelIssue = $this->getMockModelIssue([
            'workflowStatus' => WorkflowStatusTypes\ApprovedType::ID,
            'level' => InfoLevel::ID,
            'model_type' => '',
            'model_id' => 99999,
            'type' => 1,
        ]);

        $this->getModel()->setRelation('activeIssues', new Collection([
            $approvedInfoLevelIssue,
        ]));

        $activeIssues = $this->getActiveIssuesByStatusByLevel(WorkflowStatusTypes\ApprovedType::class, InfoLevel::class, false, true);
        $this->assertCount(0, $activeIssues);
    }

    public function testGetActiveIssuesByStatusByLevelWithValidationOnValidIssue()
    {
        $approvedInfoLevelIssue = $this->getMockModelIssue([
            'workflowStatus' => WorkflowStatusTypes\ApprovedType::ID,
            'level' => InfoLevel::ID,
            'model_type' => '',
            'model_id' => 99999,
            'type' => UnderRecordedHoursDayType::class,
        ], true);

        $this->getModel()->setRelation('activeIssues', new Collection([
            $approvedInfoLevelIssue,
        ]));

        $activeIssues = $this->getActiveIssuesByStatusByLevel(WorkflowStatusTypes\ApprovedType::class, InfoLevel::class, false, true);
        $this->assertCount(1, $activeIssues);
    }
}
