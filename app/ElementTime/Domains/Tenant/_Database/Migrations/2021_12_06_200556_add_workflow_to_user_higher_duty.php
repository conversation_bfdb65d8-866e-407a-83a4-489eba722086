<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->unsignedBigInteger('actor_id')->nullable()->after('reportToManager_id');
            $table->foreign('actor_id', 'uhd_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('originalReplaced_id')->nullable()->after('actor_id');
            $table->foreign('originalReplaced_id', 'uhd_or_fn')->references('id')->on('UserHigherDuty')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('originalSplit_id')->nullable()->after('originalReplaced_id');
            $table->foreign('originalSplit_id', 'uhd_os_fn')->references('id')->on('UserHigherDuty')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('role_id')->nullable()->after('originalSplit_id');
            $table->foreign('role_id', 'uhd_role_fn')->references('id')->on('Role')->onUpdate('cascade')->onDelete('restrict');

            $table->string('hourCode')->nullable()->after('comments');
            $table->boolean('doesApplyDutyRateToAllLeave')->default(false)->after('hourCode');
            $table->boolean('doesApplyDutyRateToPaidExcessTime')->default(true)->after('doesApplyDutyRateToAllLeave');
            $table->boolean('doesApplyDutyRateToAllPublicHolidays')->default(true)->after('doesApplyDutyRateToPaidExcessTime');
        });

        Schema::create('UserHigherDutyUserRole', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userHigherDuty_id');
            $table->foreign('userHigherDuty_id', 'uhdur_uhd_fn')
                ->references('id')->on('UserHigherDuty')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'uhdur_ur_fn')
                ->references('id')->on('UserRole')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unique(['userHigherDuty_id', 'userRole_id'], 'uhdur_uhd_ur_uq');
            $table->timestamps();
        });

        Schema::create('UserHigherDutyAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('mediaFile_id');
            $table->foreign('mediaFile_id', 'uhdaf_mf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('userHigherDuty_id');
            $table->foreign('userHigherDuty_id', 'uhdaf_uhd_fn')->references('id')->on('UserHigherDuty')->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UserHigherDutyAttachmentFile');
        Schema::dropIfExists('UserHigherDutyUserRole');

        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_actor_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserHigherDuty', 'actor_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('actor_id');
            });
        }

        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_or_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserHigherDuty', 'originalReplaced_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('originalReplaced_id');
            });
        }

        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_os_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserHigherDuty', 'originalSplit_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('originalSplit_id');
            });
        }

        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_role_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserHigherDuty', 'role_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('role_id');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'hourCode')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('hourCode');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'doesApplyDutyRateToAllLeave')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('doesApplyDutyRateToAllLeave');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'doesApplyDutyRateToPaidExcessTime')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('doesApplyDutyRateToPaidExcessTime');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'doesApplyDutyRateToAllPublicHolidays')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('doesApplyDutyRateToAllPublicHolidays');
            });
        }
    }
};
