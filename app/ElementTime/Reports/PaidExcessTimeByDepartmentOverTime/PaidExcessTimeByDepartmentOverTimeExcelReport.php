<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\PaidExcessTimeByDepartmentOverTime;

use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItem;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleDepartment;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class PaidExcessTimeByDepartmentOverTimeExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as protected _iSetData;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\DepartmentManagerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'paid-excess-time-by-department-over-time';
    const TYPE_SLUG = 'paid-excess-time-by-department-over-time.xls';

    /** @var string|PaidExcessTimeByDepartmentOverTimeReportConfig */
    public $c = PaidExcessTimeByDepartmentOverTimeReportConfig::class;

    private array $allowed_department_ids = [];

    protected function setData(array $info): void
    {
        $this->_iSetData($info);

        $user = CurrentUser::getUser();

        if (
            !is_null($user)
            && !$user->access->isPayrollOfficer(true)
            && !$user->access->isBusinessIntelligence(true)
        ) {
            $department_ids = [];

            foreach ($user->userRoles as $userRole) {
                if (!$userRole->availability()->isCurrent()) {
                    continue;
                }

                foreach ($userRole->userRoleDepartments as $userRoleDepartment) {
                    if (!$userRoleDepartment->availability()->isCurrent()) {
                        continue;
                    }

                    if (!$userRoleDepartment->isManager) {
                        continue;
                    }

                    $department_ids[] = $userRoleDepartment->department_id;

                    /** @var Collection|UserRoleDepartment[] $indirectUserRoleDepartments */
                    $indirectUserRoleDepartments = $userRoleDepartment->department->qIndirectUserRoleDepartments(direction: 'down')->current()->many();

                    foreach ($indirectUserRoleDepartments as $indirectUserRoleDepartment) {
                        $department_ids[] = $indirectUserRoleDepartment->department_id;
                    }
                }
            }

            $this->allowed_department_ids = array_unique($department_ids);
        }
    }

    protected function buildReportData(): void
    {
        $data = $this->collectReportData();

        $this->eagerLoadReportData($data);
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetData($data, $sheet);

        $this->sheets = [
            $sheet
        ];
    }

    /**
     * @return TimeSheetExcessTime[]|Collection
     */
    private function collectReportData(): array|Collection
    {
        $constraints = [
            ['IN', 'ExcessTimeRuleSplitItem.ruleType', [ExcessTimeRuleTypes\PaidHoursType::ID, ExcessTimeRuleTypes\AdditionalHoursType::ID]],
            ['BETWEEN', 'TimeSheetDay.date', [$this->getStartDate(), $this->getEndDate()]],
            ['Workflow.status', '=', ApprovedType::ID],
        ];

        $joins = [
            ['TimeSheetExcessTimeItem', 'TimeSheetExcessTimeItem.timeSheetExcessTime_id', '=', 'TimeSheetExcessTime.id'],
            ['Workflow', [
                ['Workflow.rel_type', 'LIKE', '"%' . TimeSheetExcessTimeItem::class . '"'],
                ['Workflow.rel_id', '=', 'TimeSheetExcessTimeItem.id'],
            ], 'inner'],
            ['TimeSheetExcessTimeItemRule', 'TimeSheetExcessTimeItemRule.timeSheetExcessTimeItem_id', '=', 'TimeSheetExcessTimeItem.id'],
            ['TimeSheetExcessTimeItemRuleWork', 'TimeSheetExcessTimeItemRuleWork.timeSheetExcessTimeItemRule_id', '=', 'TimeSheetExcessTimeItemRule.id'],
            ['TimeSheetExcessTimeItemRuleWorkSplitItem', 'TimeSheetExcessTimeItemRuleWorkSplitItem.timeSheetExcessTimeItemRuleWork_id', '=', 'TimeSheetExcessTimeItemRuleWork.id'],
            ['ExcessTimeRuleSplitItem', 'ExcessTimeRuleSplitItem.id', '=', 'TimeSheetExcessTimeItemRuleWorkSplitItem.excessTimeRuleSplitItem_id'],
        ];

        if (
            count($this->allowed_department_ids) > 0
        ) {
            $joins[] = ['UserShiftDay', 'UserShiftDay.id', '=', 'TimeSheetDayTime.userShiftDay_id'];
            $joins[] = ['UserShift', 'UserShift.id', '=', 'UserShiftDay.userShift_id'];
            $joins[] = ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserShift.userRole_id'];

            $constraints[] = ['IN', 'UserRoleDepartment.department_id', $this->allowed_department_ids];
        }

        return TimeSheetExcessTime::q(
            constraints: $constraints,
            joins: $joins,
            groupBy: 'TimeSheetExcessTime.id',
        )->many();
    }

    /**
     * @param Collection|TimeSheetExcessTime[] $data
     */
    private function eagerLoadReportData(Collection|array $data): void
    {
        $data->load([
            'userShiftDay.userShift.userRole.userRoleDepartments.department',
            'userShiftDay.userShift.user.userHigherDuties.userHigherDutyUserRoles',
            'userShiftDay.userShift.user.userHigherDuties.payType',
            'userShiftDay.userShift.user.userHigherDuties.workflow',
            'userShiftDay.userShift.user.userHigherDuties.dutyType',

            'timeSheetDayTime.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'timeSheetDayTime.userShiftDay.userShift.user.userHigherDuties.userHigherDutyUserRoles',
            'timeSheetDayTime.userShiftDay.userShift.user.userHigherDuties.payType',
            'timeSheetDayTime.userShiftDay.userShift.user.userHigherDuties.workflow',
            'timeSheetDayTime.userShiftDay.userShift.user.userHigherDuties.dutyType',
            'items.rules.excessTimeRule'
        ]);
    }

    /**
     * @param TimeSheetExcessTime[]|Collection $timeSheetExcessTimes
     */
    private function fetchSheetData(array|Collection $timeSheetExcessTimes, \stdClass $sheet): void
    {
        $table = new \stdClass;

        $table->type = 'data-table';
        $table->parentColumns = [
            $this->createColumn(label: ''),
            $this->createColumn(label: ''),
            $this->createColumn(label: 'Excess time'),
            $this->createColumn(label: 'Values', span: 3),
        ];
        $table->columns = [
            $this->createColumn('#'),
            $this->createColumn('Department'),
            $this->createColumn('Type'),
            $this->createColumn('Recorded hours'),
            $this->createColumn('Adjusted hours'),
            $this->createColumn('Amount paid'),
        ];
        $table->row = [];
        $dataByDepartments = [];

        foreach ($timeSheetExcessTimes as $timeSheetExcessTime) {
            $userShiftDay = static::getUserShiftDayFromExcessTime($timeSheetExcessTime);
            $departments = $userShiftDay->userShift->userRole->getActiveUserRoleDepartments($userShiftDay->date);

            if (count($this->allowed_department_ids) > 0) {
                $departments = $departments->filter(fn (UserRoleDepartment $userRoleDepartment) => in_array($userRoleDepartment->department_id, $this->allowed_department_ids));
            }

            if (count($departments) < 1) {
                continue;
            }

            $departmentEntry = static::getOrSetDataFromDepartment($dataByDepartments, $departments[0]->department);

            static::applyExcessTimeItemDataToEntry($departmentEntry, $timeSheetExcessTime->activeItem, $timeSheetExcessTime->hourlyRate);
        }

        usort($dataByDepartments, fn ($prev, $next) => strcmp($prev->department->name, $next->department->name));

        $index = 0;
        foreach ($dataByDepartments as $entry) {
            if (
                $entry->actualHours <= 0
                && $entry->adjustedHours <= 0
                && $entry->amountPaid <= 0
            ) {
                continue;
            }

            $index++;
            $table->data[] = [
                'config' => [],
                'data' => [
                    $index,
                    $entry->department->name,
                    implode(', ', $entry->typeNames),
                    $this->parseFloat($entry->actualHours),
                    $this->parseFloat($entry->adjustedHours),
                    $this->parseFloat($entry->amountPaid)
                ],
            ];
        }

        $sheet->data[] = $table;
    }

    private static function getUserShiftDayFromExcessTime(TimeSheetExcessTime $excessTime): UserShiftDay
    {
        return $excessTime->type === 'P'
            ? $excessTime->userShiftDay
            : $excessTime->timeSheetDayTime->userShiftDay;
    }

    /**
     * @return object{
     *     department: Department,
     *     typeNames: string[],
     *     actualHours: float,
     *     adjustedHours: float,
     *     amountPaid: float,
     *  }
     */
    private static function getOrSetDataFromDepartment(&$data, Department $department): object
    {
        $department_id = $department->id;
        if (!isset($data[$department_id])) {
            $entry = new \stdClass();
            $entry->department = $department;
            $entry->typeNames = [];
            $entry->actualHours = 0;
            $entry->adjustedHours = 0;
            $entry->amountPaid = 0;

            $data[$department_id] = $entry;
        }

        return $data[$department_id];
    }

    /**
     * @param object{department: Department, typeNames: string[], actualHours: float, adjustedHours: float, amountPaid: float} $entry
     */
    private static function applyExcessTimeItemDataToEntry(object $entry, TimeSheetExcessTimeItem $excessTimeItem, float $overtimeHourlyRate): void
    {
        foreach ($excessTimeItem->rules as $rule) {
            if (in_array($rule->excessTimeRule->name, $entry->typeNames)) {
                continue;
            }

            $entry->typeNames[] = $rule->excessTimeRule->name;
        }

        $adjustedHours = $excessTimeItem->totalHoursPaidAdjusted;

        $entry->actualHours += $excessTimeItem->totalHoursPaid;
        $entry->adjustedHours += $adjustedHours;
        $entry->amountPaid += $overtimeHourlyRate * $adjustedHours;
    }
}
