<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->boolean('doesInheritStaffResponsibilities')->default(true);
            $table->bigInteger('reportToManager_id')->unsigned()->nullable()->default(null);

            $table->foreign('reportToManager_id', 'uhd_rtm_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_rtm_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('UserHigherDuty', 'reportToManager_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('reportToManager_id');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'doesInheritStaffResponsibilities')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('doesInheritStaffResponsibilities');
            });
        }
    }
};
