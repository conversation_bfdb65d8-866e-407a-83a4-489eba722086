FROM 542859091916.dkr.ecr.ap-southeast-2.amazonaws.com/elementtime-dev-www:1.4

RUN docker-php-ext-configure gd --enable-gd --prefix=/usr --with-jpeg --with-freetype \
  && docker-php-ext-install -j$(nproc) gd

COPY ./docker/www/volumes/global-bundle.pem /root

COPY ./docker/www/supplier/start.sh /run
RUN chmod +x /run/start.sh

COPY ./docker/www/volumes/crontab /etc/cron.d/elementtime
RUN chmod 0644 /etc/cron.d/elementtime
RUN crontab /etc/cron.d/elementtime

RUN sed -i -e 's/\r$//' /run/start.sh

ENTRYPOINT ["/run/start.sh"]
