<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V3\Routes\Plant;

use Element\ElementTime\Applications\TenantApi\Http\V3\Controllers\Plant\PlantController;
use Element\ElementTime\Applications\TenantApi\Http\V3\Routes\BaseTenantApiV3Router;
use OpenApi\Attributes as OA;

class MainRoutes extends BaseTenantApiV3Router
{
    protected string|null $defaultPrefix = 'plant';

    protected function routes():void
    {
        $this->getTimeSheetDayIdConfig();
        $this->getTimeSheetDayIdPlantTimes();
        $this->getTimeSheetIdPSummary();
        $this->postPlantTime();
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: "/api/v3/plant/time-sheet-day/{id}/config",
        operationId: 'get_api_v3_plant_time-sheet-day_{id}_config',
        description: 'Return the list of plant items allowed to be used as standalone in a day of a timesheet',
        summary: "Get plants items allowed in a timesheet day",
        security: [
            [
                'bearerAuth' => [],
            ],
        ],
        tags: ['plant'],
        parameters: [
            new OA\Parameter(name: 'id', description: 'This is the ID of a day in the timesheet', in: 'path', schema: new OA\Schema(type: 'integer|string'), example: 'current'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "OK",
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/TimeSheetDayPlantConfigResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getTimeSheetDayIdConfig(): void
    {
        $this->router->get('time-sheet-day/{id}/config', [PlantController::class, 'getTimeSheetDayPlantConfig'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: "/api/v3/plant/time-sheet-day/{id}/plant-times",
        operationId: 'get_api_v3_plant_time-sheet-day_{id}_plant-times',
        description: 'Return a list of recorded plant as standalone in a day of a timesheet',
        summary: "Get recorded plant in a timesheet day",
        security: [
            [
                'bearerAuth' => [],
            ],
        ],
        tags: ['plant'],
        parameters: [
            new OA\Parameter(name: 'id', description: 'This is the ID of a day in the timesheet', in: 'path', schema: new OA\Schema(type: 'integer|string'), example: 'current'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "OK",
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', type: 'array', items: new OA\Items(ref: '#/components/schemas/PlantTimeBasicResource'), minItems: 0),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getTimeSheetDayIdPlantTimes(): void
    {
        $this->router->get('time-sheet-day/{id}/plant-times', [PlantController::class, 'getPlantTimesInTimeSheetDay'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: "/api/v3/plant/time-sheet/{id}/summary",
        operationId: 'get_api_v3_plant_time-sheet_{id}_summary',
        description: 'Return summary of plant recorded as standalone in a timesheet',
        summary: "Get summary of plant recorded as standalone in a timesheet",
        security: [
            [
                'bearerAuth' => [],
            ],
        ],
        tags: ['plant'],
        parameters: [
            new OA\Parameter(name: 'id', description: 'This is the ID of the timesheet', in: 'path', schema: new OA\Schema(type: 'integer'), example: 42),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "OK",
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/TimeSheetPlantSummaryResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getTimeSheetIdPSummary(): void
    {
        $this->router->get('time-sheet/{id}/summary', [PlantController::class, 'getTimesheetPlantSummary'])->middleware($this->getManagerModeMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Post(
        path: '/api/v3/plant/plant-time',
        operationId: 'post_api_v3_plant_plant-time',
        description: 'Add/Update records of standalone plant in a specific timesheet day',
        summary: 'Save standalone plant records',
        security: [
            [
                'bearerAuth' => [],
            ],
        ],
        requestBody: new OA\RequestBody(
            description: 'Additional info',
            content: new OA\JsonContent(
                required: ['plantItem_id', 'timeSheetDay_id', 'plant_times'],
                properties: [
                    new OA\Property(property: 'plantItem_id', type: 'integer', example: 321),
                    new OA\Property(property: 'timeSheetDay_id', type: 'integer', example: 321),
                    new OA\Property(property: 'plant_times', description: 'List of individual plant records', type: 'array', items: new OA\Items(
                        required: [],
                        properties: [
                            new OA\Property(property: 'id', description: 'ID of plant time record - to be sent only if editing record', type: 'integer', example: 4242, nullable: true),
                            new OA\Property(property: 'rel_id', description: 'Relationship ID', type: 'integer', example: 42, nullable: true),
                            new OA\Property(property: 'rel_type', description: 'Relationship ID', type: 'string', example: 'Element\ElementTime\Domains\Tenant\Plant\Models\UserRolePlantItem', nullable: true),
                            new OA\Property(property: 'hours', description: 'Required depending on plant item settings', type: 'float', example: 3.5, nullable: true),
                            new OA\Property(property: 'hoursStart', description: 'Required depending on plant item settings', type: 'float', example: 3.5, nullable: true),
                            new OA\Property(property: 'hoursEnd', description: 'Required depending on plant item settings', type: 'float', example: 7.2, nullable: true),
                            new OA\Property(property: 'secondaryHours', description: 'Required depending on plant item settings', type: 'float', example: 3.5, nullable: true),
                            new OA\Property(property: 'secondaryHoursStart', description: 'Required depending on plant item settings', type: 'float', example: 1345.5, nullable: true),
                            new OA\Property(property: 'secondaryHoursEnd', description: 'Required depending on plant item settings', type: 'float', example: 1347, nullable: true),
                            new OA\Property(property: 'mileage', description: 'Required depending on plant item settings', type: 'float', example: 90, nullable: true),
                            new OA\Property(property: 'mileageStart', description: 'Required depending on plant item settings', type: 'float', example: 124589, nullable: true),
                            new OA\Property(property: 'mileageEnd', description: 'Required depending on plant item settings', type: 'float', example: 124679, nullable: true),
                            new OA\Property(property: 'cartage', description: 'Required depending on plant item settings', type: 'float', example: 2.86, nullable: true),
                            new OA\Property(property: 'costRate', description: 'Required depending on plant item settings', type: 'float', example: 1.85, nullable: true),
                            new OA\Property(property: 'description', type: 'string', example: 'This is a comments field', nullable: true),
                        ], minItems: 1),
                    ),
                ],
            ),
        ),
        tags: ['plant'],
        responses: [
            new OA\Response(
                ref: '#/components/responses/Success',
                response: 200,
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function postPlantTime(): void
    {
        $this->router->post('/plant-time', [PlantController::class, 'postPlantTime'])->middleware($this->getPrivateMiddleware());
    }
}
