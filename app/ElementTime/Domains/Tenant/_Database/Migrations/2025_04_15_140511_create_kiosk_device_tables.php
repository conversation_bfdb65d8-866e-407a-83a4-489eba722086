<?php

use Element\ElementTime\Domains\Tenant\_System\Enums\KioskDeviceRequestStatus\KioskDeviceRequestStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('KioskDevice', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('deviceId');
            $table->string('name');
            $table->string('localization')->nullable();
            $table->boolean('isActive')->default(true);

            $table->uuid('appId');
            $table->string('secret');

            $table->timestamps();
        });

        Schema::create('KioskDeviceRequest', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('deviceId');
            $table->string('ipAddress');
            $table->string('name');
            $table->string('status')->default(KioskDeviceRequestStatus::DEFAULT->id());

            $table->unsignedBigInteger('kioskDevice_id')->nullable();
            $table->foreign('kioskDevice_id', 'kdr_kd_fn')
                ->references('id')->on('KioskDevice')
                ->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('KioskDeviceRequest');
        Schema::dropIfExists('KioskDevice');
    }
};
