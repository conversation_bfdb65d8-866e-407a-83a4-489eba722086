form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$f.submit()' name='$f.form')
  .panel.panel-danger-lighter
    .panel-heading.text-danger
      span.text-danger
        i.fa.fa-warning.mr.inline-block.inline-middle
        h4.h4.m0.inline-block.inline-middle Pending cancellation
    .panel-body
      .row
        .col-xs-12
          p There is a pending cancellation request for this leave, please approve or decline it.
          P(ng-if='!!req.cancellationRequest.notes')
            strong Comments made by {{req.cancellationRequest.actor.fullName}}:
            |
            | {{req.cancellationRequest.notes}}
      .row
        .col-xs-12
          .form-group
            label.label-control(for='LeaveCancel_inputNotes') Reason
            textarea.form-control(id='LeaveCancel_inputNotes' ng-model='$f.data.notes' required='required')

      div(ng-if='!!req.canApproveCancel')
        .row.visible-sm.visible-md.visible-lg
          .col-xs-12
            button.btn.btn-danger.mr(type='button' ng-click='$f.approve()' ng-disabled='!!$f.isSubmitting') Approve cancellation
            button.btn.btn-gray.mr(type='submit' ng-disabled='!!$f.isSubmitting') Decline cancellation
            button.btn.btn-info.pull-right(type='button' ng-click='$ctrl.viewCancellationWorkflow()' ng-disabled='!!$f.isSubmitting') View cancellation workflow
        .row.visible-xs
          .col-xs-4
            button.btn.btn-danger.btn-block(type='button' ng-click='$f.approve()' ng-disabled='!!$f.isSubmitting') Approve cancellation
          .col-xs-4
            button.btn.btn-gray.btn-block(type='submit' ng-disabled='!!$f.isSubmitting') Decline cancellation
          .col-xs-4
            button.btn.btn-info.btn-block(type='button' ng-click='$ctrl.viewCancellationWorkflow()' ng-disabled='!!$f.isSubmitting') View cancellation workflow

      div(ng-if='!req.canApproveCancel')
        .row.visible-sm.visible-md.visible-lg
          .col-xs-12
            button.btn.btn-gray.mr(type='button' ng-disabled='!!$f.isSubmitting' ng-click='$f.cancel()' ng-if='req.canTakeBackCancellationRequest') Take it back
            button.btn.btn-info.pull-right(type='button' ng-click='$ctrl.viewCancellationWorkflow()') View cancellation workflow
        .row.visible-xs
          .col-xs-6
            button.btn.btn-gray.btn-block(type='button' ng-disabled='!!$f.isSubmitting' ng-click='$f.cancel()' ng-if='req.canTakeBackCancellationRequest') Take it back
          .col-xs-6
            button.btn.btn-info.btn-block(type='button' ng-click='$ctrl.viewCancellationWorkflow()') View cancellation workflow
