<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('MsMessageView');
        Schema::dropIfExists('MsMessage');
        Schema::dropIfExists('MsMember');
        Schema::dropIfExists('MsConversation');

        /*
         * # 8.12b ++
         *
         * ### Table fields:
         * - Field leaveDay_id from table TimeSheetItemDayTime
         * - Field excessTimeLeaveDay_id from table TimeSheetItemDayTime
         * - Field rosteredTimeOffDay_id from table TimeSheetItemDayTime
         *
         * - Field project_id from table AllowanceType
         * - Field projectActivity_id from table AllowanceType
         *
         * - Field project_id from table LeaveType
         * - Field projectActivity_id from table LeaveType
         *
         * - Field payrollOfficer_id from table PayRun
         * - Field auditor_id from table PayRun
         */
        try {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_ld_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'leaveDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('leaveDay_id');
            });
        }

        try {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_etld_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'excessTimeLeaveDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveDay_id');
            });
        }

        try {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_rtod_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'rosteredTimeOffDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffDay_id');
            });
        }

        ///

        if (Schema::hasColumn('AllowanceType', 'project_id')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('project_id');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'projectActivity_id')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('projectActivity_id');
            });
        }

        ///

        try {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropForeign('leavetype_project_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('LeaveType', 'project_id')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('project_id');
            });
        }

        try {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropForeign('leavetype_projectactivity_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('LeaveType', 'projectActivity_id')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('projectActivity_id');
            });
        }

        ///

        try {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropForeign('payrun_userPRO_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('PayRun', 'payrollOfficer_id')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('payrollOfficer_id');
            });
        }

        try {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropForeign('payrun_userAUD_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('PayRun', 'auditor_id')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('auditor_id');
            });
        }

        /*
         * # 8.13
         *
         * ### Table fields:
         * - Field receiveSMSRemindersWorkflow from table User
         * - Field receiveSMSRemindersDailyTimeSheet from table User
         * - Field receiveSMSRemindersCloseTimeSheet from table User
         *
         * ### Tables:
         * - UserWorkflow
         *
         * - Leave
         * - LeaveDay
         * - LeaveApproval
         * - LeaveStatusHistory
         * - LeaveAttachmentFile
         * - UserLeaveType
         * - UserLeaveTypeEntry
         *
         * - ExcessTimeLeave
         * - ExcessTimeLeaveDay
         * - ExcessTimeLeaveApproval
         * - ExcessTimeLeaveStatusHistory
         * - ExcessTimeLeaveAttachmentFile
         * - UserExcessTimeBalance
         * - UserExcessTimeBalanceEntry
         * - UserExcessTimeBalanceEntryCommitted
         * - UserExcessTimeBalanceEntryRemoved
         * - UserExcessTimeBalanceEntryDeducted
         *
         * - RosteredTimeOff
         * - RosteredTimeOffDay
         * - RosteredTimeOffApproval
         * - RosteredTimeOffStatusHistory
         * - RosteredTimeOffAttachmentFile
         * - UserRosteredTimeOffType
         * - UserRosteredTimeOffTypeEntry
         */
        if (Schema::hasColumn('User', 'receiveSMSRemindersWorkflow')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('receiveSMSRemindersWorkflow');
            });
        }

        if (Schema::hasColumn('User', 'receiveSMSRemindersDailyTimeSheet')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('receiveSMSRemindersDailyTimeSheet');
            });
        }

        if (Schema::hasColumn('User', 'receiveSMSRemindersCloseTimeSheet')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('receiveSMSRemindersCloseTimeSheet');
            });
        }

        Schema::dropIfExists('UserWorkflow');

        ///

        try {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropForeign('ulte_ul_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropForeign('ulte_prevulte_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropForeign('ulte_ulte_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropForeign('usd_leave_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('UserShiftDay', 'leave_id')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('leave_id');
            });
        }

        Schema::dropIfExists('LeaveAttachmentFile');
        Schema::dropIfExists('LeaveStatusHistory');
        Schema::dropIfExists('LeaveApproval');
        Schema::dropIfExists('LeaveDay');
        Schema::dropIfExists('Leave');
        Schema::dropIfExists('UserLeaveTypeEntry');
        Schema::dropIfExists('UserLeaveType');

        ///

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_etl_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_ce_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_ee_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_ne_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_re_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserExcessTimeBalanceEntry', function (Blueprint $table) {
                $table->dropForeign('uetbe_ue_fn');
            });
        } catch (Throwable) {
        }

        Schema::dropIfExists('ExcessTimeLeaveAttachmentFile');
        Schema::dropIfExists('ExcessTimeLeaveStatusHistory');
        Schema::dropIfExists('ExcessTimeLeaveApproval');
        Schema::dropIfExists('ExcessTimeLeaveDay');
        Schema::dropIfExists('ExcessTimeLeave');
        Schema::dropIfExists('UserExcessTimeBalanceEntryCommitted');
        Schema::dropIfExists('UserExcessTimeBalanceEntryRemoved');
        Schema::dropIfExists('UserExcessTimeBalanceEntryDeducted');
        Schema::dropIfExists('UserExcessTimeBalanceEntry');
        Schema::dropIfExists('UserExcessTimeBalance');

        ///

        try {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropForeign('urtote_rto_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropForeign('urtote_rto_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropForeign('urtote_rtot_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropForeign('urtote_urtote_fn');
            });
        } catch (Throwable) {
        }

        Schema::dropIfExists('RosteredTimeOffAttachmentFile');
        Schema::dropIfExists('RosteredTimeOffStatusHistory');
        Schema::dropIfExists('RosteredTimeOffApproval');
        Schema::dropIfExists('RosteredTimeOffDay');
        Schema::dropIfExists('RosteredTimeOff');
        Schema::dropIfExists('UserRosteredTimeOffTypeEntry');
        Schema::dropIfExists('UserRosteredTimeOffType');
    }

    public function down()
    {
        //
    }
};
