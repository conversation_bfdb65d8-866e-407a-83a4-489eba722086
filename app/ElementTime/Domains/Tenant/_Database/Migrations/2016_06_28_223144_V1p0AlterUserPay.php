<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('User', function (Blueprint $table) {
            // Pay Details
            $table->string('payrollEmail')->nullable();
            $table->string('mobilePhone')->nullable();
            $table->boolean('sendSMSReminders')->nullable();
            $table->float('overHeadedCost')->nullable();
            $table->bigInteger('payGroupType_id')->unsigned()->nullable();

            // Pay Split Options
            $table->char('paySplitType', 1)->nullable(); // Values: (P)ercentage | (F)ixed
            $table->longText('paySplitMap')->nullable(); // JSON format:
            //  [
            //      {
            //          "bankAccount":  "Bank Account 1 Details",
            //          "paySplit": 30
            //      },
            //      {
            //          "bankAccount":  "Bank Account 2 Details",
            //          "paySplit": 70
            //      }
            //  ]
            // PS. The sum of all 'paySplit' values must be 100 if 'paySplitType' is set to 'P'

            $table->foreign('payGroupType_id', 'user_paygroup_fn')->references('id')->on('PayGroupType')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('User', function (Blueprint $table) {
            // Foreign Keys
            $table->dropForeign([
                'user_paygroup_fn',
            ]);

            // Pay Details
            $table->dropColumn([
                'payrollEmail',
                'mobilePhone',
                'sendSMSReminders',
                'overHeadedCost',
                'payGroup_id',
            ]);

            // Pay Split Options
            $table->dropColumn([
                'paySplitType',
                'paySplitMap',
            ]);
        });
    }
};
