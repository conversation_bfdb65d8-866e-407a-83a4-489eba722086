<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PenaltyType

        Schema::create('PenaltyType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // General information
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('incrementRate_id')->nullable();
            $table->foreign('incrementRate_id', 'pnlty_ir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('set null');
            $table->unsignedBigInteger('project_id')->nullable();
            $table->foreign('project_id', 'pnlty_prjct_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
            $table->unsignedBigInteger('projectActivity_id')->nullable();
            $table->foreign('projectActivity_id', 'pnlty_prjcta_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('set null');

            // Add rules
            $table->boolean('isAppliedPriorAdjustments')->default(true);
            $table->char('rateType', 2)->default('FH');
            // Possible values:g
            // - FH => Hourly fixed rate
            // - FD => Daily fixed rate
            // - FW => Weekly fixed rate
            // - FP => Payrun fixed rate
            // - PH => Hourly percentage rate
            // - PD => Daily percentage rate
            // - PW => Weekly percentage rate
            // - PP => Payrun percentage rate
            $table->decimal('rateAmount', 16, 10)->default(0);

            // Status
            $table->char('status', 1)->default('A');

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: PenaltyType

        //region Table: PenaltyTypeRule

        Schema::create('PenaltyTypeRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('penaltyType_id');
            $table->foreign('penaltyType_id', 'ptr_pt_fn')->references('id')->on('PenaltyType')->onUpdate('cascade')->onDelete('cascade');

            // Flow information
            $table->unsignedBigInteger('parentPenaltyTypeRule_id')->nullable(); // ID of parent (AND) rule
            $table->foreign('parentPenaltyTypeRule_id', 'ptr_pptr_fn')->references('id')->on('PenaltyTypeRule')->onUpdate('cascade')->onDelete('cascade');
            $table->boolean('isNot')->default(false); // Is a NOT rule

            // Conditions
            $table->string('ruleType'); // ID of rule (class)
            $table->longText('details'); // JSON with rule details

            $table->integer('order')->default(1); // Order on the OR list

            $table->timestamps();
        });

        //endregion Table: PenaltyTypeRule

        //region Table: UserPenaltyType

        Schema::create('UserPenaltyType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'upnlty_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('penaltyType_id');
            $table->foreign('penaltyType_id', 'upnlty_pnlty_fn')->references('id')->on('PenaltyType')->onUpdate('cascade')->onDelete('restrict');

            $table->boolean('isAppliedToAllShifts')->default(false);
            $table->unsignedBigInteger('userShift_id')->nullable();
            $table->foreign('userShift_id', 'upnlty_ush_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('cascade');

            $table->date('startDate');
            $table->date('endDate')->nullable();
            $table->text('comment')->nullable();

            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'upnlty_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->char('status', 1)->default('A');

            $table->timestamps();
        });

        //endregion Table: UserPenaltyType

        //region Table: TimeSheetPenalty

        Schema::create('TimeSheetPenalty', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheet_id');
            $table->foreign('timeSheet_id', 'tsp_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('userPenaltyType_id');
            $table->foreign('userPenaltyType_id', 'tsp_upnlty_fn')->references('id')->on('UserPenaltyType')->onUpdate('cascade')->onDelete('restrict');

            // In case of type has a rate types: FD => Daily fixed rate | PD => Daily percentage rate
            $table->date('dayDate')->nullable();

            // In case of type has a rate types: FW => Weekly fixed rate | PW => Weekly percentage rate
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->decimal('amount', 16, 10); // Amount after all calculations
            $table->text('comment')->nullable();

            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'tsp_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenalty

        //region Table: TimeSheetPenaltyTrigger

        Schema::create('TimeSheetPenaltyTrigger', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenalty_id');
            $table->foreign('timeSheetPenalty_id', 'tspt_tsp_fn')->references('id')->on('TimeSheetPenalty')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('timeSheetItemDayTime_id')->nullable();
            $table->foreign('timeSheetItemDayTime_id', 'tspt_tsidt_fn')->references('id')->on('TimeSheetItemDayTime')->onUpdate('cascade')->onDelete('cascade');
            $table->decimal('hours', 16, 10)->nullable();
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->unsignedBigInteger('penaltyTypeRule_id')->nullable(); // ID of last rule in the inheritance chain (*** only rules with no children!!! ***)
            $table->foreign('penaltyTypeRule_id', 'tspt_ptr_fn')->references('id')->on('PenaltyTypeRule')->onUpdate('cascade')->onDelete('restrict');
            $table->longText('details')->nullable();
            // JSON array object with details of how this rule was triggered per rule (parent and all children)
            // For each item, add the following:
            // - PenaltyTypeRule ID
            // - Related data (project_id, projectActivity_id, leave_id, userShiftDay_id, timeSheetExcessTime_id, timeSheetDay_id, plant_id, time-span, etc)

            $table->timestamps();
        });

        Schema::create('TimeSheetPenaltyAmendment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenalty_id');
            $table->foreign('timeSheetPenalty_id', 'tspa_tsp_fn')->references('id')->on('TimeSheetPenalty')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('payrollOfficer_id')->nullable();
            $table->foreign('payrollOfficer_id', 'tspa_prouser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            $table->dateTime('dateTime');

            $table->char('changeType', 1)->default('D'); // D => Deduction | A => Addition
            $table->decimal('changeAmount', 16, 10);
            $table->text('comments')->nullable();

            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'tspa_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenaltyTrigger

        //region Table: TimeSheetDay

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('valuePenalty', 16, 10)->default(0);
            $table->decimal('valueOrdinaryHours', 16, 10)->default(0);
            $table->decimal('valueWorkedOrdinaryHours', 16, 10)->default(0);
            $table->decimal('valueLeaveTaken', 16, 10)->default(0);

            $table->decimal('glidePeriodValuePenalty', 16, 10)->default(0);
            $table->decimal('glidePeriodValueOrdinaryHours', 16, 10)->default(0);
            $table->decimal('glidePeriodValueWorkedOrdinaryHours', 16, 10)->default(0);
            $table->decimal('glidePeriodValueLeaveTaken', 16, 10)->default(0);
        });

        //endregion Table: TimeSheetDay

        //region Table: TimeSheet

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('valuePenalty', 16, 10)->default(0);
            $table->decimal('valueOrdinaryHours', 16, 10)->default(0);
            $table->decimal('valueWorkedOrdinaryHours', 16, 10)->default(0);
            $table->decimal('valueLeaveTaken', 16, 10)->default(0);
            $table->decimal('valueTax', 16, 10)->default(0);
            $table->decimal('valueDeduction', 16, 10)->default(0);
            $table->decimal('valueContribution', 16, 10)->default(0);
            $table->decimal('valueSpecialPay', 16, 10)->default(0);

            $table->boolean('arePenaltiesCalculated')->default(false);
            $table->boolean('arePenaltiesCalculating')->default(false);
        });

        //endregion Table: TimeSheet

        //region Table: TimeSheetItemDayTime

        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->boolean('arePenaltiesCalculated')->default(false);
            $table->boolean('arePenaltiesCalculating')->default(false);
        });

        //endregion Table: TimeSheetItemDayTime
    }

    public function down(): void
    {
        //region Table: TimeSheetItemDayTime

        if (Schema::hasColumn('TimeSheetItemDayTime', 'arePenaltiesCalculated')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('arePenaltiesCalculated');
            });
        }
        if (Schema::hasColumn('TimeSheetItemDayTime', 'arePenaltiesCalculating')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('arePenaltiesCalculating');
            });
        }

        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheet

        if (Schema::hasColumn('TimeSheet', 'valuePenalty')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valuePenalty');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueOrdinaryHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueWorkedOrdinaryHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueWorkedOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueLeaveTaken')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueLeaveTaken');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueTax')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueTax');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueDeduction')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueDeduction');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueContribution')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueContribution');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueSpecialPay')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueSpecialPay');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'arePenaltiesCalculated')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('arePenaltiesCalculated');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'arePenaltiesCalculating')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('arePenaltiesCalculating');
            });
        }

        //endregion Table: TimeSheet

        //region Table: TimeSheetDay

        if (Schema::hasColumn('TimeSheetDay', 'valuePenalty')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valuePenalty');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'valueOrdinaryHours')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valueOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'valueWorkedOrdinaryHours')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valueWorkedOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'valueLeaveTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valueLeaveTaken');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValuePenalty')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValuePenalty');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValueOrdinaryHours')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValueOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValueWorkedOrdinaryHours')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValueWorkedOrdinaryHours');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValueLeaveTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValueLeaveTaken');
            });
        }

        //endregion Table: TimeSheetDay

        //region Table: TimeSheetPenaltyAmendment

        Schema::dropIfExists('TimeSheetPenaltyAmendment');

        //endregion Table: TimeSheetPenaltyAmendment

        //region Table: TimeSheetPenaltyTrigger

        Schema::dropIfExists('TimeSheetPenaltyTrigger');

        //endregion Table: TimeSheetPenaltyTrigger

        //region Table: TimeSheetPenalty

        Schema::dropIfExists('TimeSheetPenalty');

        //endregion Table: TimeSheetPenalty

        //region Table: UserPenaltyType

        Schema::dropIfExists('UserPenaltyType');

        //endregion Table: UserPenaltyType

        //region Table: PenaltyTypeRule

        Schema::dropIfExists('PenaltyTypeRule');

        //endregion Table: PenaltyTypeRule

        //region Table: PenaltyType

        Schema::dropIfExists('PenaltyType');

        //endregion Table: PenaltyType
    }
};
