<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AccruedHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\PaidHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\UnpaidHoursType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected function updateTimeSheetExcessTimeItemRuleTypeFromType($from, $to): void
    {
        $table = DB::table('TimeSheetExcessTimeItemRule');

        $table
            ->where('type', '=', $from)
            ->update(['ruleType' => $to]);
    }

    public function up(): void
    {
        $this->down();

        Schema::create('ExcessTimeRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // General data
            $table->string('name');
            $table->string('description')->nullable();
            $table->char('status', 1)->default(\Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus::ID);
            $table->string('ruleType');

            // Custom project/activity code
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();

            // Rules
            $table->decimal('minimumHours', 16, 10)->default(0);
            $table->decimal('ratio', 16, 10)->nullable();
            $table->boolean('doesAllowAdjustingActualHours')->default(true);
            $table->boolean('doesRestrictToPayrollOfficers')->default(true);

            $table->timestamps();
        });

        Schema::table('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
            $table->unsignedBigInteger('excessTimeRule_id')->nullable()->after('excessTimeGroupSectionLevelRule_id');
            $table->foreign('excessTimeRule_id', 'tsetir_etr')->references('id')->on('ExcessTimeRule')->onUpdate('cascade')->onDelete('set null');

            $table->string('ruleType')->after('type');
        });

        Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
            $table->unsignedInteger('minutesAdditional')->nullable()->default(0)->after('minutesUnPaid');
        });

        $this->updateTimeSheetExcessTimeItemRuleTypeFromType('A', AccruedHoursType::ID);
        $this->updateTimeSheetExcessTimeItemRuleTypeFromType('P', PaidHoursType::ID);
        $this->updateTimeSheetExcessTimeItemRuleTypeFromType('U', UnpaidHoursType::ID);
    }

    public function down(): void
    {
        Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
            if (Schema::hasColumn('TimeSheetExcessTimeItem', 'minutesAdditional')) {
                $table->dropColumn('minutesAdditional');
            }
        });

        Schema::table('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
            if (Schema::hasColumn('TimeSheetExcessTimeItemRule', 'excessTimeRule_id')) {
                $table->dropForeign('tsetir_etr');
                $table->dropColumn('excessTimeRule_id');
            }
            if (Schema::hasColumn('TimeSheetExcessTimeItemRule', 'ruleType')) {
                $table->dropColumn('ruleType');
            }
        });

        Schema::dropIfExists('ExcessTimeRule');
    }
};
