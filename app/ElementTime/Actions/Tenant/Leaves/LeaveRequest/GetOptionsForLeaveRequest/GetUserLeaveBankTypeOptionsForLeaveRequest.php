<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest;

use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Database\Eloquent\Collection;

class GetUserLeaveBankTypeOptionsForLeaveRequest extends BaseGetOptionsForLeaveRequest
{
    /**
     * @todo - Constraints and action class might be changed - instead of User, UserRole as parameter
     * @return Collection<UserLeaveBankType>|UserLeaveBankType[]
     * @throws \Throwable
     */
    public function handle(User $user, User $actor): Collection|array
    {
        $this->validateAccess($user, $actor);

        return UserLeaveBankType::q()
            ->constraints([
                ['User.id', '=', $user->id],
                ['UserLeaveBankType.canTakeLeave', '=', true],
                ['UserLeaveBankType.status', '=', ActiveStatus::ID],
                [
                    [
                        ['IS NULL', 'LeaveType.id', 'OR'],
                        ['LeaveType.status', '=', ActiveStatus::ID, 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'RosteredTimeOffType.id', 'OR'],
                        ['RosteredTimeOffType.status', '=', ActiveStatus::ID, 'OR'],
                    ],
                ],
            ])
            ->many();
    }
}
