<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ToilType

        Schema::table('ToilType', function (Blueprint $table) {
            $table->boolean('doesAccrueLeaveWhenEarning')->default(false);
        });

        //endregion Table: ToilType

        //region Table: OvertimeType

        Schema::table('OvertimeType', function (Blueprint $table) {
            $table->boolean('doesAccrueLeaveWhenEarning')->default(false);
        });

        //endregion Table: OvertimeType
    }

    public function down(): void
    {
        //region Table: OvertimeType

        if (Schema::hasColumn('OvertimeType', 'accrueLeaveWhenEarning')) {
            Schema::table('OvertimeType', function (Blueprint $table) {
                $table->dropColumn('accrueLeaveWhenEarning');
            });
        }

        //endregion Table: OvertimeType

        //region Table: ToilType

        if (Schema::hasColumn('ToilType', 'accrueLeaveWhenEarning')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('accrueLeaveWhenEarning');
            });
        }

        //endregion Table: ToilType
    }
};
