// Status
.panel-body.pt0
  .row
    hr.hr.mt0

  // Leave data
  div(ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/leave-data.html'")
  div(ng-if="$ctrl.leave.view != 'approving'" ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/workflow.html'")
  div(ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/attachments.html'")

  div(ng-if='$ctrl.leave.view === "approving"' ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/actions/approving.html'" ng-init='$f = $ctrl.leave.approving')

  // TODO Who else is on leave
  div(ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/actions.html'")
