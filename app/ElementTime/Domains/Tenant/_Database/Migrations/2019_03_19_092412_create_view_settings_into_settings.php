<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('displayTimeSheetPaymentToManager')->default(false);
            $table->boolean('displayTimeSheetPaymentToOwner')->default(false);
            $table->boolean('displayAccruedLeaveBalances')->default(false);
            $table->boolean('displayHistoricalLeaveBalances')->default(false);
            $table->boolean('displaySubDepartmentsToManager')->default(false);
            $table->boolean('displayIndirectReportsToManager')->default(false);
            $table->boolean('displayDepartmentScheduleToManager')->default(false);
            $table->boolean('displayScheduleToAnyone')->default(false);
        });

        //endregion Table: Settings
    }

    public function down(): void
    {
        //region Table: Settings

        if (Schema::hasColumn('Settings', 'displayScheduleToAnyone')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayScheduleToAnyone');
            });
        }
        if (Schema::hasColumn('Settings', 'displayDepartmentScheduleToManager')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayDepartmentScheduleToManager');
            });
        }
        if (Schema::hasColumn('Settings', 'displayIndirectReportsToManager')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayIndirectReportsToManager');
            });
        }
        if (Schema::hasColumn('Settings', 'displaySubDepartmentsToManager')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displaySubDepartmentsToManager');
            });
        }
        if (Schema::hasColumn('Settings', 'displayHistoricalLeaveBalances')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayHistoricalLeaveBalances');
            });
        }
        if (Schema::hasColumn('Settings', 'displayAccruedLeaveBalances')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayAccruedLeaveBalances');
            });
        }
        if (Schema::hasColumn('Settings', 'displayTimeSheetPaymentToOwner')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayTimeSheetPaymentToOwner');
            });
        }
        if (Schema::hasColumn('Settings', 'displayTimeSheetPaymentToManagers')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('displayTimeSheetPaymentToManagers');
            });
        }

        //endregion Table: Settings
    }
};
