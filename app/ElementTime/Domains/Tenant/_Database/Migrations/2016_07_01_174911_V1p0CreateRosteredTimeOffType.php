<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('RosteredTimeOffType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Details
            $table->string('name')->nullable();
            $table->boolean('canBeAccruedAsLiability')->nullable();
            $table->char('isPaidAt', 1)->nullable();

            // Rules
            $table->float('cyclePeriodAmount')->nullable();
            $table->char('cyclePeriodType', 1)->nullable();
            $table->char('cyclePeriodStartsOn', 1)->nullable();
            $table->float('receivePerPeriodAmount')->nullable();
            $table->char('receivePerPeriodType', 1)->nullable();
            $table->char('isFlexibleOrFixed', 1)->nullable();
            $table->boolean('mustBeApproved')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('EmployeeType', function (Blueprint $table) {
            $table->bigInteger('rosteredTimeOffType_id')->unsigned()->nullable();
            $table->foreign('rosteredTimeOffType_id', 'et_rtot_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('RosteredTimeOffType');
        Schema::table('EmployeeType', function (Blueprint $table) {
            // Drop Foreign
            $table->dropForeign([
                'et_rtot_fn',
            ]);

            // Drop Column
            $table->dropColumn([
                'rosteredTimeOffType_id',
            ]);
        });
    }
};
