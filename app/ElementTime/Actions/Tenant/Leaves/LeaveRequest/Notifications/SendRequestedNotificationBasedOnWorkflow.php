<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\Notifications;

use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Notifications\Support\NotificationTypeTypes\LeaveRequests\LeaveRequestApprovedType;
use Element\ElementTime\Domains\Tenant\Notifications\Support\NotificationTypeTypes\LeaveRequests\LeaveRequestedOnBehalfType;
use Element\ElementTime\Domains\Tenant\Notifications\Support\NotificationTypeTypes\LeaveRequests\LeaveRequestedType;

class SendRequestedNotificationBasedOnWorkflow extends BaseLeavesAction
{
    /**
     * @throws \Throwable
     */
    public function handle(LeaveRequest $leaveRequest): LeaveRequest
    {
        if ($leaveRequest->repository->isSubmitted() || $leaveRequest->repository->isPartiallyApproved()) {
            if ($leaveRequest->repository->isPartiallyApproved()) {
                $leaveRequest->load('workflow.steps.items');
            }

            $userIds = [];
            $step = $leaveRequest->repository->getFirstActiveStep();

            if (is_null($step)) {
                return $leaveRequest;
            }

            foreach ($step->items as $item) {
                if ($item->isDone || $item->areNotificationsDisabled) {
                    continue;
                }

                foreach ($item->repository->getMatchingUsers() as $matchingUser) {
                    if ($matchingUser->access->isSuperUser()) {
                        continue;
                    }

                    if (in_array($matchingUser->id, $userIds)) {
                        continue;
                    }

                    if ($leaveRequest->actor_id == $leaveRequest->ownerUser->id) { // Request by own user
                        LeaveRequestedType::notifyAsync($matchingUser, $leaveRequest);
                    } elseif ($matchingUser->id != $leaveRequest->actor_id) {
                        LeaveRequestedOnBehalfType::notifyAsync($matchingUser, $leaveRequest);
                    }

                    $userIds[] = $matchingUser->id;
                }
            }
        } elseif ($leaveRequest->repository->isApproved() || $leaveRequest->repository->isSplit()) {
            LeaveRequestApprovedType::notifyAsync($leaveRequest->ownerUser, $leaveRequest);
        }

        return $leaveRequest;
    }
}
