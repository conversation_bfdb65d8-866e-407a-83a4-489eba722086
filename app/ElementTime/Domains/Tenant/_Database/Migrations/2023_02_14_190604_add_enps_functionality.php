<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('isEnpsEnabled')->default(false)->after('doesAllowProfileManagers');
            $table->boolean('isEnpsMonitoringOptionsEnabled')->default(false)->after('isEnpsEnabled');
            $table->text('enpsRankingCustomHeader')->nullable()->after('isEnpsEnabled');
            $table->boolean('allowsEnpsAccessUserLevel')->default(false)->after('isEnpsMonitoringOptionsEnabled');
            $table->boolean('allowsEnpsResultsAccessAtManagerLevel')->default(false)->after('allowsEnpsAccessUserLevel');
            $table->integer('enpsPromptFrequencyByPayRun')->default(1)->after('allowsEnpsResultsAccessAtManagerLevel');
            $table->text('hrEmail')->nullable()->after('payrollEmail');
        });

        Schema::create('EnpsResponse', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheet_id');
            $table->foreign('timeSheet_id', 'er_ts_fn')
                ->references('id')->on('TimeSheet')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->integer('score')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('EnpsResponse');

        if (Schema::hasColumn('Settings', 'isEnpsEnabled')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('isEnpsEnabled');
            });
        }

        if (Schema::hasColumn('Settings', 'isEnpsMonitoringOptionsEnabled')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('isEnpsMonitoringOptionsEnabled');
            });
        }

        if (Schema::hasColumn('Settings', 'enpsRankingCustomHeader')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('enpsRankingCustomHeader');
            });
        }

        if (Schema::hasColumn('Settings', 'allowsEnpsAccessUserLevel')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsEnpsAccessUserLevel');
            });
        }

        if (Schema::hasColumn('Settings', 'allowsEnpsResultsAccessAtManagerLevel')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsEnpsResultsAccessAtManagerLevel');
            });
        }

        if (Schema::hasColumn('Settings', 'enpsPromptFrequencyByPayRun')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('enpsPromptFrequencyByPayRun');
            });
        }

        if (Schema::hasColumn('Settings', 'hrEmail')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('hrEmail');
            });
        }
    }
};
