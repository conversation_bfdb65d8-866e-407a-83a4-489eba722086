<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromUserRole;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRun\CreateScheduleForUserOnPayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Enums\PayRunStatus\PayRunStatus;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\TimeType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeGroup;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\FullTimeType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRoleRepository;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

describe('Duration only - set relationships from UserRole', function () {
    it('does set timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $userRole = $leaveSettings->userRoleSchedule->userRole;
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUserRole::make()->handle($userRole);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Work pattern - set relationships from UserRole', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $userRole = $leaveSettings->userRoleSchedule->userRole;
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUserRole::make()->handle($userRole);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Zero based - set relationships from UserRole', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $userRole = $leaveSettings->userRoleSchedule->userRole;
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUserRole::make()->handle($userRole);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('General - set relationships from UserRole', function () {
    it('does set to multiple leave requests across two different payrun periods with different schedule types', function () {
        $openPayruns = PayRun::q()
            ->constraints([
                ['IN', 'PayRun.status', [PayRunStatus::NotStarted, PayRunStatus::Current]]
            ])->many();

        if ($openPayruns->count() < 2) {
            $this->markTestIncomplete('This test does require at least 2 payrun to be opened');
        }

        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[0]);
        $userRole = $workPatternLeaveSettings->userRoleSchedule->userRole;
        [$workPatternLeaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);

        $zeroBasedUserRoleSchedule = UserRoleSchedule::addToUserRoleByData(
            $userRole,
            $openPayruns[1]->startDate->copy(),
            [
                'type' => UserRoleScheduleType::ZeroBased->id(),
                'zeroBased' => [
                    'dailyMaxDuration' => null,
                    'doesHaveDailyMaxDuration' => false,
                    'doesHaveFixedLocation' => false,
                    'doesHaveMaxDuration' => false,
                    'maxDuration' => null,
                    'scheduleLocation_id' => null,
                ],
            ],
            true,
        );

        $zeroBasedLeaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[1], userRoleSchedule: $zeroBasedUserRoleSchedule);
        [$zeroBasedLeaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettings);

        $expectedScheduledPayRunWorkPattern = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $openPayruns[0]->startDate],
                ['ScheduledPayRun.endDate', '=', $openPayruns[0]->endDate],
            ])
            ->first();
        $expectedTimeSheetForWorkPatternPeriod = TimeSheet::getByDate($userRole->user, $workPatternLeaveRequest->startDate);
        $expectedScheduledPayRunZeroBased = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $zeroBasedLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $openPayruns[1]->startDate],
                ['ScheduledPayRun.endDate', '=', $openPayruns[1]->endDate],
            ])
            ->first();
        $expectedTimeSheetForZeroBasedPeriod = TimeSheet::getByDate($userRole->user, $zeroBasedLeaveRequest->startDate);

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromUserRole::make()->handle($userRole);

        $workPatternLeaveRequest->refresh();
        $zeroBasedLeaveRequest->refresh();

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunWorkPattern->id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeInt()
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForWorkPatternPeriod->id)
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunZeroBased->id)
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeInt()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForZeroBasedPeriod->id)
            ->and($workPatternLeaveRequest->timeSheet_id)->not()->toEqual($zeroBasedLeaveRequest->timeSheet_id)
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->not()->toEqual($zeroBasedLeaveRequest->scheduledPayRun_id)
        ;
    });

    it('does not affect leave requests placed on a different user role', function () {
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $userRoleSchedule = $workPatternLeaveSettings->userRoleSchedule;
        $payRun = $workPatternLeaveSettings->payRun;
        $userRole = $userRoleSchedule->userRole;
        $expectedScheduledPayRunWorkPattern = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedTimeSheetForWorkPatternPeriod = TimeSheet::getByDate($userRole->user, $workPatternLeaveSettings->startDate);

        $role = Role::q([
            ['Role.status', '=', ActiveStatus::ID],
            ['Role.id', '<>', $userRole->role_id]
        ])->many()->random();

        $newUserRole = UserRoleRepository::assignRoleToUser(
            $role,
            $workPatternLeaveSettings->user,
            $userRole->startDate,
            false,
            (object) [
                'doesOverrideExternalId' => false,
                'externalId' => 'test_lorem_ipsum',
                'contractedHours' => 80,
                'employeeTypeStatusType' => FullTimeType::ID,
                'costing' => [
                    'doesAllowNonStandardPlant' => false,
                    'doesAllowUserToUpdateWorkDetails' => false,
                ],
                'departmentData' => [[
                    'department_id' => Department::q()->onlyActive()->many()->random()->id,
                    'isManager' => false,
                ]],
                'masterProjectData' => [[
                    'project_id' => Project::q()->onlyActive()->many()->random()->id,
                    'isMaster' => false,
                ]],
                'payTypesData' => [[
                    'payType_id' => PayType::q()->onlyActive()->many()->random()->id,
                    'overtimeCustomAmount' => null,
                    'overtimeCustomRateType' => 'Y',
                ]],
                'roleManagerData' => [[
                    'manager_id' => User::q()->onlyActive()->many()->random()->id,
                ]],
                'timeTypesData' => [
                    [
                        'timeType_id' => TimeType::q()->onlyActive()->many()->random()->id,
                        'excessTimeGroup_id' => ExcessTimeGroup::q()->onlyActive()->many()->random()->id,
                        'isActive' => true,
                        'isMaster' => true,
                    ]
                ],
            ],
        );

        $zeroBasedUserRoleSchedule = UserRoleSchedule::addToUserRoleByData(
            $newUserRole,
            $payRun->startDate,
            [
                'type' => UserRoleScheduleType::ZeroBased->id(),
                'zeroBased' => [
                    'dailyMaxDuration' => null,
                    'doesHaveDailyMaxDuration' => false,
                    'doesHaveFixedLocation' => false,
                    'doesHaveMaxDuration' => false,
                    'maxDuration' => null,
                    'scheduleLocation_id' => null,
                ],
            ],
            true,
        );

        $zeroBasedLeaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun, userRoleSchedule: $zeroBasedUserRoleSchedule);

        [$workPatternLeaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);
        [$zeroBasedLeaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettings);

        CreateScheduleForUserOnPayRun::make()->handle($zeroBasedUserRoleSchedule, $payRun);

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromUserRole::make()->handle($userRole);

        $workPatternLeaveRequest->refresh();

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunWorkPattern->id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeInt()
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForWorkPatternPeriod->id)
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeNull()
        ;
    });
});
