<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeRecordingMethods\ProjectsOnlyMethod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table UserRole

        Schema::table('UserRole', function (Blueprint $table) {
            $table->string('timeRecordingMethod')->default(ProjectsOnlyMethod::ID)->after('endDate');
        });

        //endregion Table UserRole
    }

    public function down(): void
    {
        //region Table UserRole

        if (Schema::hasColumn('UserRole', 'timeRecordingMethod')) {
            Schema::table('UserRole', function (Blueprint $table) {
                $table->dropColumn('timeRecordingMethod');
            });
        }

        //endregion Table UserRole
    }
};
