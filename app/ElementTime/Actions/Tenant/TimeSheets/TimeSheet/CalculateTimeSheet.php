<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet;

use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetDay\CalculateTimeSheetDayRelatedRecords;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetHoliday\SyncHolidaysToTimeSheet;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod\CalculateTimeSheetPeriodRelatedRecords;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheet\TimeSheetRepository;

/**
 * @todo finish implementing this class based on old one (`checkAndFix`) into `TimeSheetRepository`
 * @see TimeSheetRepository::checkAndFix()
 */
class CalculateTimeSheet extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheet $timeSheet,

        bool $allCalculations = false,

        bool $checkWorkflow = false,
        bool $setRelatedWorkflows = false,
        bool $setModelTaskDependant = false,
        bool $syncHolidays = false,

        bool $doesFailOnExceptions = false,
    ): void
    {
        if ($allCalculations || $checkWorkflow) {
            $this->safeRun(
                fn () => $timeSheet->repository->checkWorkflow(),
                $doesFailOnExceptions,
            );
        }

        if ($allCalculations || $setRelatedWorkflows) {
            $this->safeRun(
                fn () => $timeSheet->setRelatedWorkflows(),
                $doesFailOnExceptions,
            );
        }

        if ($allCalculations || $setModelTaskDependant) {
            $this->safeRun(
                fn () => $timeSheet->repository->setupAsModelTaskDependant(),
                $doesFailOnExceptions,
            );
        }

        if ($allCalculations || $syncHolidays) {
            $this->safeRun(
                fn () => SyncHolidaysToTimeSheet::make()->handle($timeSheet),
                $doesFailOnExceptions,
            );
        }

        foreach ($timeSheet->timeSheetDays as $timeSheetDay) {
            $this->safeRun(
                fn () => CalculateTimeSheetDayRelatedRecords::make()->handle(
                    timeSheetDay: $timeSheetDay,
                    doesCalculateExcessTime: true,
                    doesCalculatePenalty: true,
                    doesPreventBroadcasting: true,
                ),
                $doesFailOnExceptions,
            );
        }

        foreach ($timeSheet->timeSheetPeriods as $timeSheetPeriod) {
            $this->safeRun(
                fn () => CalculateTimeSheetPeriodRelatedRecords::make()->handle(
                    timeSheetPeriod: $timeSheetPeriod,
                    doesCalculateExcessTime: true,
                    doesPreventBroadcasting: true,
                ),
                $doesFailOnExceptions,
            );
        }

        // TODO: Auto-allowances
        // try {
        //     $this->fixLockedRunningPenalties();
        // } catch (\Throwable $e) {
        //     report($e);
        // }
        //
        // try {
        //     if ($this->canCalculateAutoAllowances(strict: true, throwsExceptions: false)) {
        //         $this->runAutoAllowanceCalculations(true, false, CurrentUser::getUser(), false);
        //     }
        // } catch (\Throwable $e) {
        //     report($e);
        // }

        // TODO: TimeSheet totals

        // TODO: Send broadcasts (what broadcasts?)
        // $model->sendBroadcast();
    }

    /** @throws \Throwable */
    private function safeRun(\Closure $callable, bool $doesFailOnException): void
    {
        try {
            $callable();
        } catch (\Throwable $e) {
            if ($doesFailOnException) {
                throw $e;
            }

            report($e);
        }
    }
}
