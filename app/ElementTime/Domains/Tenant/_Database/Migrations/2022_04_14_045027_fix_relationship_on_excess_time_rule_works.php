<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        try {
            Schema::table('TimeSheetExcessTimeItemRuleWork', function (Blueprint $table) {
                $table->dropForeign('tsetirw_tsdt_fn');
            });
        } catch (Throwable) {
        }

        Schema::table('TimeSheetExcessTimeItemRuleWork', function (Blueprint $table) {
            $table->unsignedBigInteger('timeSheetDayTime_id')->nullable(false)->change();

            $table->foreign('timeSheetDayTime_id', 'tsetirw_tsdt_fn')
                ->references('id')->on('TimeSheetDayTime')
                ->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        //
    }
};
