.row(ng-if='!!$ctrl.form.calculation.hasRun && !!$ctrl.form.calculation.canBeRequested')
  .col-xs-12.col-md-6
    elt-attachment-file(
      ng-model='$ctrl.form.data.attachmentFile'
      ng-change='$ctrl.form.$events.onChangeAttachmentFile()'
      ng-required='$ctrl.form.calculation.data.doesRequireAttachmentsOnSubmit'
      id='Leave_attachmentFile'
      elt-label='{{"Attachment file" + ($ctrl.form.calculation.data.doesRequireAttachmentsOnSubmit ? " (Required)" : "")}}'
      name='attachmentFile'
      elt-file-options='{{settings.form.LeaveRequest.attachmentFileOptions}}'
    )
.row(ng-if='!!$ctrl.form.calculation.data.doesRequireAttachmentsOnSubmit')
  .col-xs-12
    .list-group.mb0
      .list-group-item.list-group-item-warning
        i.fa.fa-warning
        span.ml-sm
          | You must upload an attachment to this leave request prior to submission.
          |
          | Refer to your leave policy to ensure your attachment meets your organisations requirements.
