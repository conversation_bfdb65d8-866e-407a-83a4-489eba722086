<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('UserToilTypeBalanceUse_UserToilTypeBalanceIn');
        Schema::dropIfExists('UserToilTypeBalanceUse_TimeSheetItemDay');
        Schema::dropIfExists('UserToilTypeBalanceUnUse');
        Schema::dropIfExists('UserToilTypeBalanceLost');
        Schema::dropIfExists('UserToilTypeBalanceUse');
        Schema::dropIfExists('UserToilTypeBalanceIn');
    }

    public function down(): void
    {
        //
    }
};
