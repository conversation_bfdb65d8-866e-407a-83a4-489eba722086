<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('WorkflowTypeTree', function (Blueprint $table) {
            $table->boolean('isSubmitRequired')->nullable()->default(true)->after('name');
            $table->boolean('canSubmitAtAnyTime')->default(true)->after('isSubmitRequired');
        });

        Schema::table('Workflow', function (Blueprint $table) {
            $table->boolean('allowsManualReassignment')->default(true)->after('status');
            $table->boolean('isSubmitRequired')->nullable()->default(true)->after('allowsManualReassignment');
            $table->boolean('canSubmitAtAnyTime')->default(true)->after('isSubmitRequired');
        });
    }

    public function down(): void
    {
        //region Remove relationship to d_e_p_r_e_c_a_t_e_d approval structure
        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetApproval WHERE Key_name=\'timesheetapproval_timesheet_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetApproval', function (Blueprint $table) {
                $table->dropForeign('timesheetapproval_timesheet_fn');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetApproval WHERE Key_name=\'timesheetapproval_manager_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetApproval', function (Blueprint $table) {
                $table->dropForeign('timesheetapproval_manager_fn');
            });
        }
        //endregion Remove relationship to d_e_p_r_e_c_a_t_e_d approval structure

        if (Schema::hasColumn('WorkflowTypeTree', 'canSubmitAtAnyTime')) {
            Schema::table('WorkflowTypeTree', function (Blueprint $table) {
                $table->dropColumn('canSubmitAtAnyTime');
            });
        }

        if (Schema::hasColumn('WorkflowTypeTree', 'isSubmitRequired')) {
            Schema::table('WorkflowTypeTree', function (Blueprint $table) {
                $table->dropColumn('isSubmitRequired');
            });
        }

        if (Schema::hasColumn('Workflow', 'canSubmitAtAnyTime')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('canSubmitAtAnyTime');
            });
        }

        if (Schema::hasColumn('Workflow', 'isSubmitRequired')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('isSubmitRequired');
            });
        }

        if (Schema::hasColumn('Workflow', 'allowsManualReassignment')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('allowsManualReassignment');
            });
        }
    }
};
