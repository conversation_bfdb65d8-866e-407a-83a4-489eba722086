<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesPromptHigherDutyOnLeave')->default(false)->after('doesShowNegativeExcessTimeBalance');
            $table->float('minimumHoursToPromptHigherDutiesOnLeave')->default(0)->after('doesPromptHigherDutyOnLeave');
        });

        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->unsignedBigInteger('fromLeaveRequest_id')->nullable()->after('doesApplyDutyRateToAllPublicHolidays');
            $table->foreign('fromLeaveRequest_id', 'uhd_lr_fn')
                ->references('id')->on('LeaveRequest')
                ->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('Settings', 'doesPromptHigherDutyOnLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesPromptHigherDutyOnLeave');
            });
        }

        if (Schema::hasColumn('Settings', 'minimumHoursToPromptHigherDutiesOnLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('minimumHoursToPromptHigherDutiesOnLeave');
            });
        }

        if (Schema::hasColumn('UserHigherDuty', 'isFromLeaveRequestPrompt')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('isFromLeaveRequestPrompt');
            });
        }

        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_lr_fn');
            });
        } catch (\Throwable) {
        }

        if (Schema::hasColumn('UserHigherDuty', 'fromLeaveRequest_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('fromLeaveRequest_id');
            });
        }
    }
};
