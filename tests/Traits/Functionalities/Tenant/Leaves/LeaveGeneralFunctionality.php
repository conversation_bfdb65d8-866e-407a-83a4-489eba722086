<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Traits\Functionalities\Tenant\Leaves;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleZeroBased;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityZeroBased;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShiftDay;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDefaultData;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Testing\Exceptions\InvalidArgumentException;
use JetBrains\PhpStorm\ArrayShape;
use JetBrains\PhpStorm\ObjectShape;

trait LeaveGeneralFunctionality
{
    #[ArrayShape([
        User::class,
        UserRoleSchedule::class,
        UserLeaveBankType::class,
        [StructScheduledPayRunPeriodDayDefaultData::class],
    ])]
    public function l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType $scheduleType, array $constraints = [], array $joins = [], array $relations = [], CarbonInterface|null $date = null, bool $isScheduled = true): array
    {
        if (is_null($date)) {
            $date = Carbon::today();
        }

        $joinsSet = [
            ['UserRoleSchedule', [
                ['UserRoleSchedule.userRole_id', '=', 'ActiveUserRole.id'],
                ['UserRoleSchedule.startDate', '<=', DB::raw('"' . $date->toDateString())->getValue(DB::getQueryGrammar()) . '"'],
                ['UserRoleSchedule.type', '=', '"' . $scheduleType->id() . '"'],
                [
                    [
                        ['UserRoleSchedule.endDate', '<=>', DB::raw('NULL')->getValue(DB::getQueryGrammar()), 'OR'],
                        ['UserRoleSchedule.endDate', '=', DB::raw('"0000-00-00"')->getValue(DB::getQueryGrammar()), 'OR'],
                        ['UserRoleSchedule.endDate', '>=', DB::raw('"' . $date->toDateString())->getValue(DB::getQueryGrammar()) . '"', 'OR'],
                    ], 'and',
                ],
            ], 'inner'],
            ['UserLeaveBank', 'UserLeaveBank.user_id', '=', 'User.id', 'inner'],
            ['UserLeaveBankType', [
                ['UserLeaveBankType.type_type', '<=>', DB::raw('NULL')->getValue(DB::getQueryGrammar())],
                ['UserLeaveBankType.userLeaveBank_id', '=', 'UserLeaveBank.id'],
            ], 'inner'],
        ];

        if ($scheduleType->is(UserRoleScheduleType::WorkPattern)) {
            $joinsSet = [
                ...$joinsSet,
                ['UserRoleSchedulePattern', 'UserRoleSchedulePattern.userRoleSchedule_id', '=', 'UserRoleSchedule.id'],
                ['UserRoleSchedulePatternPeriod', 'UserRoleSchedulePatternPeriod.userRoleSchedulePattern_id', '=', 'UserRoleSchedulePattern.id'],
                ['UserRoleSchedulePatternPeriodShift', 'UserRoleSchedulePatternPeriodShift.userRoleSchedulePatternPeriod_id', '=', 'UserRoleSchedulePatternPeriod.id'],
                ['UserRoleSchedulePatternPeriodShiftDay', 'UserRoleSchedulePatternPeriodShiftDay.userRoleSchedulePatternPeriodShift_id', '=', 'UserRoleSchedulePatternPeriodShift.id'],
            ];

            $constraints[] = ['UserRoleSchedulePatternPeriodShiftDay.isScheduled', '=', $isScheduled];
        } elseif ($scheduleType->is(UserRoleScheduleType::DurationOnly)) {
            $joinsSet = [
                ...$joinsSet,
                ['UserRoleScheduleDurationOnly', 'UserRoleScheduleDurationOnly.userRoleSchedule_id', '=', 'UserRoleSchedule.id'],
            ];

            $constraints[] = ['JSON_CONTAINS', 'UserRoleScheduleDurationOnly.days', [['isScheduled' => $isScheduled]]];
        }

        $constraints[] = ['IS NOT NULL', 'UserLeaveBankType.id'];

        if (count($joins) > 0) {
            $joinsSet = array_merge($joinsSet, $joins);
        }

        $user = User::q()
            ->current($date)
            ->joins($joinsSet)
            ->constraints($constraints)
            ->relations([
                'userRoles.schedules',
                'userLeaveBankTypes.type',
            ])
            ->first();

        $userRoleSchedule = $user?->getMasterUserRole($date)?->getActiveSchedule($date);
        $userLeaveBankType = $user?->userLeaveBankTypes?->whereNotNull('type_type')?->where('canTakeLeave', '=', true)->random();

        $scheduledDays = [];

        if (!is_null($userRoleSchedule)) {
            $scheduledPayRun = ScheduledPayRun::q()
                ->constraints([
                    ['ScheduledPayRun.userRoleSchedule_id', '=', $userRoleSchedule->id],
                    ['ScheduledPayRun.startDate', '<=', $date->toDateString()],
                    ['ScheduledPayRun.endDate', '>=', $date->toDateString()],
                ])
                ->relations([
                    'periods.days.specific',
                ])
                ->first();

            if (is_null($scheduledPayRun)) {
                $scheduledPayRunData = $userRoleSchedule->specificAssignment->getScheduledPayRunDefaultData($date);
                $scheduledPeriodsData = $userRoleSchedule->specificAssignment->getScheduledPayRunPeriodDefaultData($scheduledPayRunData);

                foreach ($scheduledPeriodsData as $scheduledPeriodsDatum) {
                    $scheduledDaysData = $userRoleSchedule->specificAssignment->getScheduledPayRunPeriodDayDefaultData($scheduledPeriodsDatum);
                    $scheduledDays = [
                        ...$scheduledDays,
                        ...$scheduledDaysData,
                    ];
                }
            } else {
                foreach ($scheduledPayRun->periods as $scheduledPayRunPeriod) {
                    foreach ($scheduledPayRunPeriod->days as $scheduledPayRunPeriodDay) {
                        $scheduledDays[] = StructScheduledPayRunPeriodDayDefaultData::make([
                            'type' => $scheduledPayRunPeriodDay->specific,
                            'label' => $scheduledPayRunPeriodDay->label,
                            'date' => $scheduledPayRunPeriodDay->date,
                        ]);
                    }
                }
            }
        }

        return [
            $user,
            $userRoleSchedule,
            $userLeaveBankType,
            $scheduledDays,
        ];
    }

    #[ArrayShape([
        User::class,
        UserRoleSchedule::class,
        UserLeaveBankType::class,
        [StructScheduledPayRunPeriodDayDefaultData::class],
        [StructScheduledPayRunPeriodDefaultData::class],
    ])]
    public function l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType $scheduleType, array $constraints = [], array $joins = [], array $relations = [], CarbonInterface|null $date = null, bool $isScheduled = true): array
    {
        if (is_null($date)) {
            $date = Carbon::today();
        }

        $joinsSet = [
            ['UserLeaveBank', 'UserLeaveBank.user_id', '=', 'User.id', 'inner'],
            ['UserLeaveBankType', [
                ['UserLeaveBankType.type_type', '<=>', DB::raw('NULL')->getValue(DB::getQueryGrammar())],
                ['UserLeaveBankType.userLeaveBank_id', '=', 'UserLeaveBank.id'],
            ], 'inner'],

            ['ScheduledPayRunPeriod', 'ScheduledPayRunPeriod.scheduledPayRun_id', '=', 'ScheduledPayRun.id'],
            ['ScheduledPayRunPeriodDay', 'ScheduledPayRunPeriodDay.scheduledPayRunPeriod_id', '=', 'ScheduledPayRunPeriod.id'],
        ];

        $constraints = [
            ['IS NOT NULL', 'UserLeaveBankType.id'],
            ['UserRoleSchedule.type', '=', $scheduleType->id()],
            ['ScheduledPayRun.startDate', '<=', $date->toDateString()],
            ['ScheduledPayRun.endDate', '>=', $date->toDateString()],
            ...$constraints,
        ];

        if ($scheduleType->is(UserRoleScheduleType::WorkPattern)) {
            $joinsSet = [
                ...$joinsSet,
                ['ScheduledPayRunPeriodDayWorkPattern', 'ScheduledPayRunPeriodDayWorkPattern.scheduledPayRunPeriodDay_id', '=', 'ScheduledPayRunPeriodDay.id'],
            ];

            $constraints[] = ['ScheduledPayRunPeriodDayWorkPattern.isScheduled', '=', $isScheduled];
        } elseif ($scheduleType->is(UserRoleScheduleType::DurationOnly)) {
            $joinsSet = [
                ...$joinsSet,
                ['ScheduledPayRunPeriodDayDurationOnly', 'ScheduledPayRunPeriodDayDurationOnly.scheduledPayRunPeriodDay_id', '=', 'ScheduledPayRunPeriodDay.id'],
            ];

            $constraints[] = ['ScheduledPayRunPeriodDayDurationOnly.isScheduled', '=', $isScheduled];
        }

        $joins = [
            ...$joinsSet,
            ...$joins,
        ];

        $scheduledPayRun = ScheduledPayRun::q()
            ->joins($joins)
            ->constraints($constraints)
            ->relations([
                'userRoleSchedule.userRole.user',
            ])
            ->first();

        $userRoleSchedule = $scheduledPayRun?->userRoleSchedule;
        $user = $userRoleSchedule?->userRole?->user;
        $userLeaveBankType = $user?->userLeaveBankTypes?->whereNotNull('type_type')?->where('canTakeLeave', '=', true)?->random();

        $scheduledPeriods = [];
        $scheduledDays = [];

        foreach ($scheduledPayRun->periods as $scheduledPayRunPeriod) {
            $scheduledPeriods[] = StructScheduledPayRunPeriodDefaultData::make([
                'type' => $scheduledPayRunPeriod->specific,
                'name' => $scheduledPayRunPeriod->name,
                'startDate' => $scheduledPayRunPeriod->startDate->copy(),
                'endDate' => $scheduledPayRunPeriod->endDate->copy(),
            ]);

            foreach ($scheduledPayRunPeriod->days as $scheduledPayRunPeriodDay) {
                $scheduledDays[] = StructScheduledPayRunPeriodDayDefaultData::make([
                    'type' => $scheduledPayRunPeriodDay->specific,
                    'label' => $scheduledPayRunPeriodDay->label,
                    'date' => $scheduledPayRunPeriodDay->date->copy(),
                ]);
            }
        }

        return [
            $user,
            $userRoleSchedule,
            $userLeaveBankType,
            $scheduledDays,
            $scheduledPeriods,
        ];
   }

    /**
     * @param StructScheduledPayRunPeriodDayDefaultData[]|Collection<StructScheduledPayRunPeriodDayDefaultData> $structScheduledPayRunPeriodDaysDefaultData
     * @return StructScheduledPayRunPeriodDayDefaultData|null
     */
    public function l_lg_getFirstNonScheduledDayFromScheduledPayRunPeriodDaysDefaultData(array|Collection $structScheduledPayRunPeriodDaysDefaultData): StructScheduledPayRunPeriodDayDefaultData|null
    {
        foreach ($structScheduledPayRunPeriodDaysDefaultData as $structScheduledPayRunPeriodDayDefaultData) {
            $type = $structScheduledPayRunPeriodDayDefaultData->type;

            if (($type instanceof UserRoleSchedulePatternPeriodShiftDay || $type instanceof ScheduledPayRunPeriodDayWorkPattern) && !$type->isScheduled) {
                return $structScheduledPayRunPeriodDayDefaultData;
            }
        }

        return null;
    }

    /**
     * @param StructScheduledPayRunPeriodDayDefaultData[]|Collection<StructScheduledPayRunPeriodDayDefaultData> $structScheduledPayRunPeriodDaysDefaultData
     * @return StructScheduledPayRunPeriodDayDefaultData|null
     */
    public function l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData(array|Collection $structScheduledPayRunPeriodDaysDefaultData): StructScheduledPayRunPeriodDayDefaultData|null
    {
        foreach ($structScheduledPayRunPeriodDaysDefaultData as $structScheduledPayRunPeriodDayDefaultData) {
            $type = $structScheduledPayRunPeriodDayDefaultData->type;

            if (($type instanceof UserRoleSchedulePatternPeriodShiftDay || $type instanceof ScheduledPayRunPeriodDayWorkPattern) && $type->isScheduled) {
                return $structScheduledPayRunPeriodDayDefaultData;
            }

            if ($type instanceof ScheduledPayRunPeriodDayDurationOnly && $type->isScheduled) {
                return $structScheduledPayRunPeriodDayDefaultData;
            }

            if ($type instanceof \stdClass) {
                return $structScheduledPayRunPeriodDayDefaultData;
            }
        }

        return null;
    }

    /**
     * @param StructScheduledPayRunPeriodDayDefaultData[]|Collection<StructScheduledPayRunPeriodDayDefaultData> $structScheduledPayRunPeriodDaysDefaultData
     * @return StructScheduledPayRunPeriodDayDefaultData[]|Collection<StructScheduledPayRunPeriodDayDefaultData
     */
    public function l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData(array|Collection $structScheduledPayRunPeriodDaysDefaultData): Collection|array
    {
        $ret = Collection::make();

        foreach ($structScheduledPayRunPeriodDaysDefaultData as $structScheduledPayRunPeriodDayDefaultData) {
            $type = $structScheduledPayRunPeriodDayDefaultData->type;

            if (($type instanceof UserRoleSchedulePatternPeriodShiftDay || $type instanceof ScheduledPayRunPeriodDayWorkPattern) && $type->isScheduled) {
                $ret->push($structScheduledPayRunPeriodDayDefaultData);

                continue;
            }

            if ($type instanceof ScheduledPayRunPeriodDayDurationOnly && $type->isScheduled) {
                $ret->push($structScheduledPayRunPeriodDayDefaultData);
            }
        }

        return $ret;
    }

    public function l_lg_loadScheduledDaysData(
        UserRoleScheduleType $scheduleType,
        bool $onFuturePayrun = true,
        PayRun &$payRun = null,
        User|null &$user = null,
        UserRoleSchedule|null &$userRoleSchedule = null,
        UserLeaveBankType|null &$userLeaveBankType = null,
        array|Collection|null &$scheduledDays = null,
    ): void
    {
        $constraints = [];

        if (is_null($payRun)) {
            $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        }

        if ($onFuturePayrun) {
            while (!is_null($payRun->id)) {
                $payRun = $payRun->next;
            }
        }

        if (!is_null($userRoleSchedule)) {
            $constraints[] = ['UserRoleSchedule.id', '=', $userRoleSchedule->id];
        }

        if ($onFuturePayrun) {
            [$user, $userRoleSchedule, $userLeaveBankType, $scheduledDays] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment($scheduleType, constraints: $constraints, date: $payRun->startDate);
        } else {
            [$user, $userRoleSchedule, $userLeaveBankType, $scheduledDays] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment($scheduleType, constraints: $constraints, date: $payRun->startDate);
        }

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new InvalidArgumentException('There is no user on a ' . $scheduleType->id() . ' schedule with actual schedules at the moment');
        }
    }

    #[ObjectShape([
        'onFuturePayrun' => 'bool',
        'doesApprove' => 'bool',
        'duration' => TimeDuration::class,
        'payRun' => PayRun::class,
        'user' => User::class,
        'userRoleSchedule' => UserRoleSchedule::class,
        'userLeaveBankType' => UserLeaveBankType::class,
        'scheduledDays' => 'array',
    ])]
    public function l_lg_getZeroBasedLeaveSettings(
        bool $onFuturePayrun = true,
        bool $doesApprove = false,
        TimeDuration $duration = null,
        PayRun &$payRun = null,
        UserRoleSchedule &$userRoleSchedule = null,
    ): \stdClass
    {

        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::ZeroBased,
            $onFuturePayrun,
            $payRun,
            $user,
            $userRoleSchedule,
            $userLeaveBankType,
            $scheduledDays,
        );

        return (object)[
            'onFuturePayrun' => !!$onFuturePayrun,
            'doesApprove' => !!$doesApprove,
            'duration' => $duration ?? TimeDuration::parseFromHours(10),
            'payRun' => $payRun,
            'user' => $user,
            'userRoleSchedule' => $userRoleSchedule,
            'userLeaveBankType' => $userLeaveBankType,
            'scheduledDays' => $scheduledDays,
        ];
    }

    #[ObjectShape([
        'doesHaveDailyMaxDuration' => 'bool',
        'doesHaveMaxDuration' => 'bool',
        'dailyMaxDuration' => TimeDuration::class,
        'maxDuration' => TimeDuration::class,
        'actorUser' => User::class,
    ])]
    public function l_lg_getZeroBasedScheduleSettings(
        TimeDuration|null $doesHaveDailyMaxDuration,
        TimeDuration|null $doesHaveMaxDuration,
        TimeDuration|null $dailyMaxDuration,
        TimeDuration|null $maxDuration,
        TimeDuration|null $actorUser,
    ): \stdClass
    {
        return (object) [
            'doesHaveDailyMaxDuration' => !is_null($doesHaveDailyMaxDuration) && boolval($doesHaveDailyMaxDuration),
            'doesHaveMaxDuration' => !is_null($doesHaveMaxDuration) && boolval($doesHaveMaxDuration),
            'dailyMaxDuration' => $dailyMaxDuration ?? TimeDuration::parseFromHours(20),
            'maxDuration' => $maxDuration ?? TimeDuration::parseFromHours(80),
            'actorUser' => $actorUser ?? SuperUserFlag::getOne(),
        ];
    }

    #[ArrayShape([
        LeaveRequest::class,
        StructLeaveRequestPreAvailability::class,
    ])]
    public function l_lg_placeZeroBasedLeaveRequest(\stdClass|null $leaveSettings = null, \stdClass|null $scheduleSettings = null)
    {
        if (is_null($leaveSettings)) {
            $leaveSettings = new \stdClass;
        }

        if (is_null($scheduleSettings)) {
            $scheduleSettings = new \stdClass;
        }

        $onFuturePayrun = $leaveSettings->onFuturePayrun ?? true;
        $duration = $leaveSettings->duration ?? TimeDuration::parseFromHours(10);

        $doesHaveDailyMaxDuration = $scheduleSettings->doesHaveDailyMaxDuration ?? false;
        $doesHaveMaxDuration = $scheduleSettings->doesHaveMaxDuration ?? false;
        $dailyMaxDuration = $scheduleSettings->dailyMaxDuration ?? TimeDuration::parseFromHours(18);
        $maxDuration = $scheduleSettings->maxDuration ?? TimeDuration::parseFromHours(80);
        $actorUser = $scheduleSettings->actorUser ?? null;

        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::ZeroBased,
            $onFuturePayrun,
            $leaveSettings->payRun,
            $leaveSettings->user,
            $leaveSettings->userRoleSchedule,
            $leaveSettings->userLeaveBankType,
            $leaveSettings->scheduledDays,
        );

        $leaveSettings->userRoleSchedule->zeroBased->doesHaveMaxDuration = $doesHaveMaxDuration;
        $leaveSettings->userRoleSchedule->zeroBased->doesHaveDailyMaxDuration = $doesHaveDailyMaxDuration;

        if ($leaveSettings->userRoleSchedule->zeroBased->doesHaveMaxDuration) {
            $leaveSettings->userRoleSchedule->zeroBased->maxDuration = $maxDuration;
        }

        if ($leaveSettings->userRoleSchedule->zeroBased->doesHaveDailyMaxDuration) {
            $leaveSettings->userRoleSchedule->zeroBased->dailyMaxDuration = $dailyMaxDuration;
        }

        $leaveSettings->userRoleSchedule->zeroBased->clearSave();

        $actorUser = $actorUser ?? SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $leaveSettings->userLeaveBankType,
            userRoleSchedule: $leaveSettings->userRoleSchedule,
            startDate: $leaveSettings->payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $leave = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $leaveSettings->userRoleSchedule,
            userLeaveBankType: $leaveSettings->userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
            doesApprove: $leaveSettings->doesApprove,
        );

        return [
            $leave,
            $availability,
        ];
    }

    #[ObjectShape([
        'onFuturePayrun' => 'bool',
        'doesApprove' => 'bool',
        'dailyDuration' => TimeDuration::class,
        'payRun' => PayRun::class,
        'user' => User::class,
        'userRoleSchedule' => UserRoleSchedule::class,
        'userLeaveBankType' => UserLeaveBankType::class,
        'scheduledDays' => 'array',
        'startDate' => CarbonInterface::class,
        'endDate' => CarbonInterface::class,
    ])]
    public function l_lg_getDurationOnlyLeaveSettings(
        bool $onFuturePayrun = true,
        bool $doesApprove = false,
        TimeDuration $dailyDuration = null,
        PayRun &$payRun = null,
        UserRoleSchedule &$userRoleSchedule = null,
    ): \stdClass
    {
        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::DurationOnly,
            $onFuturePayrun,
            $payRun,
            $user,
            $userRoleSchedule,
            $userLeaveBankType,
            $scheduledDays,
        );

        return (object)[
            'onFuturePayrun' => !!$onFuturePayrun,
            'doesApprove' => !!$doesApprove,
            'dailyDuration' => $dailyDuration ?? TimeDuration::parseFromHours(10),
            'payRun' => $payRun,
            'user' => $user,
            'userRoleSchedule' => $userRoleSchedule,
            'userLeaveBankType' => $userLeaveBankType,
            'scheduledDays' => $scheduledDays,
            'startDate' => $scheduledDays[0]->date->copy(),
            'endDate' => $scheduledDays[0]->date->copy(),
        ];
    }

    #[ObjectShape([
        'doesHaveFixedHours' => 'bool',
        'duration' => TimeDuration::class,
        'minDuration' => TimeDuration::class,
        'maxDuration' => TimeDuration::class,

        'doesHaveDailyFixedHours' => 'bool',
        'dailyDuration' => TimeDuration::class,
        'dailyMinDuration' => TimeDuration::class,
        'dailyMaxDuration' => TimeDuration::class,

        'actorUser' => User::class,
    ])]
    public function l_lg_getDurationOnlyScheduleSettings(
        bool|null $doesHaveFixedHours = null,
        TimeDuration|null $duration = null,
        TimeDuration|null $minDuration = null,
        TimeDuration|null $maxDuration = null,

        bool|null $doesHaveDailyFixedHours = null,
        TimeDuration|null $dailyDuration = null,
        TimeDuration|null $dailyMinDuration = null,
        TimeDuration|null $dailyMaxDuration = null,
    ): \stdClass
    {
        return (object) [
            'doesHaveFixedHours' => !is_null($doesHaveFixedHours) && boolval($doesHaveFixedHours),
            'duration' => $duration ?? TimeDuration::parseFromHours(80),
            'minDuration' => $minDuration ?? TimeDuration::zero(),
            'maxDuration' => $maxDuration ?? TimeDuration::parseFromHours(80),

            'doesHaveDailyFixedHours' => !is_null($doesHaveDailyFixedHours) && boolval($doesHaveDailyFixedHours),
            'dailyDuration' => $dailyDuration ?? TimeDuration::parseFromHours(10),
            'dailyMinDuration' => $dailyMinDuration ?? TimeDuration::zero(),
            'dailyMaxDuration' => $dailyMaxDuration ?? TimeDuration::parseFromHours(20),

            'actorUser' => $actorUser ?? SuperUserFlag::getOne(),
        ];
    }

    #[ArrayShape([
        LeaveRequest::class,
        StructLeaveRequestPreAvailability::class,
    ])]
    public function l_lg_placeDurationOnlyLeaveRequest(\stdClass|null $leaveSettings = null, \stdClass|null $scheduleSettings = null): array
    {
        if (is_null($leaveSettings)) {
            $leaveSettings = new \stdClass;
        }

        if (is_null($scheduleSettings)) {
            $scheduleSettings = new \stdClass;
        }

        $onFuturePayrun = $leaveSettings->onFuturePayrun ?? true;
        $leaveDailyDuration = $leaveSettings->dailyDuration ?? TimeDuration::parseFromHours(10);

        $doesHaveFixedHours = $scheduleSettings->doesHaveFixedHours ?? false;
        $duration = $scheduleSettings->duration ?? TimeDuration::parseFromHours(80);
        $minDuration = $minDuration ?? TimeDuration::zero();
        $maxDuration = $maxDuration ?? TimeDuration::parseFromHours(80);

        $doesHaveDailyFixedHours = $scheduleSettings->doesHaveDailyFixedHours ?? false;
        $dailyDuration = $scheduleSettings->dailyDuration ?? TimeDuration::parseFromHours(8);
        $dailyMinDuration = $scheduleSettings->dailyMinDuration ?? TimeDuration::zero();
        $dailyMaxDuration = $scheduleSettings->dailyMaxDuration ?? TimeDuration::parseFromHours(24);

        $actorUser = $scheduleSettings->actorUser ?? null;

        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::DurationOnly,
            $onFuturePayrun,
            $leaveSettings->payRun,
            $leaveSettings->user,
            $leaveSettings->userRoleSchedule,
            $leaveSettings->userLeaveBankType,
            $leaveSettings->scheduledDays,
        );

        /** @var UserRoleSchedule $userRoleSchedule */
        $userRoleSchedule = $leaveSettings->userRoleSchedule;
        $userRoleSchedule->durationOnly->doesHaveFixedHours = $doesHaveFixedHours;
        $userRoleSchedule->durationOnly->doesHaveDailyFixedHours = $doesHaveDailyFixedHours;

        if ($userRoleSchedule->durationOnly->doesHaveDailyFixedHours) {
            $userRoleSchedule->durationOnly->dailyDuration = $dailyDuration;
        } else {
            $userRoleSchedule->durationOnly->dailyMinDuration = $dailyMinDuration;
            $userRoleSchedule->durationOnly->dailyMaxDuration = $dailyMaxDuration;
        }

        if ($userRoleSchedule->durationOnly->doesHaveFixedHours) {
            $userRoleSchedule->durationOnly->duration = $duration;
        } else {
            $userRoleSchedule->durationOnly->minDuration = $minDuration;
            $userRoleSchedule->durationOnly->maxDuration = $maxDuration;
        }

        $userRoleSchedule->durationOnly->clearSave();

        $actorUser = $actorUser ?? SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            userLeaveBankType: $leaveSettings->userLeaveBankType,
            userRoleSchedule: $leaveSettings->userRoleSchedule,
            startDate: $leaveSettings->startDate,
            endDate: $leaveSettings->endDate,
            dailyDuration: $leaveDailyDuration,
            actorUser: $actorUser,
        );

        $leave = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            userRoleSchedule: $leaveSettings->userRoleSchedule,
            userLeaveBankType: $leaveSettings->userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
            doesApprove: $leaveSettings->doesApprove,
        );

        return [
            $leave,
            $availability,
        ];
    }

    #[ObjectShape([
        'onFuturePayrun' => 'bool',
        'doesApprove' => 'bool',
        'isFullDay' => 'bool',
        'dailyStartTime' => 'string',
        'dailyEndTime' => 'string',
        'dailyDuration' => TimeDuration::class,
        'payRun' => PayRun::class,
        'user' => User::class,
        'userRoleSchedule' => UserRoleSchedule::class,
        'userLeaveBankType' => UserLeaveBankType::class,
        'scheduledDays' => 'array',
        'startDate' => CarbonInterface::class,
        'endDate' => CarbonInterface::class,
    ])]
    public function l_lg_getWorkPatternLeaveSettings(
        bool $onFuturePayrun = true,
        bool $doesApprove = false,
        bool $isFullDay = true,
        string $dailyStartTime = null,
        string $dailyEndTime = null,
        TimeDuration $dailyDuration = null,
        PayRun &$payRun = null,
        UserRoleSchedule &$userRoleSchedule = null,
    ): \stdClass
    {
        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::WorkPattern,
            $onFuturePayrun,
            $payRun,
            $user,
            $userRoleSchedule,
            $userLeaveBankType,
            $scheduledDays,
        );

        return (object)[
            'onFuturePayrun' => !!$onFuturePayrun,
            'doesApprove' => !!$doesApprove,
            'isFullDay' => $isFullDay,
            'dailyStartTime' => $dailyStartTime ?? '08:00',
            'dailyEndTime' => $dailyEndTime ?? '18:00',
            'dailyDuration' => $dailyDuration ?? TimeDuration::parseFromHours(8),
            'payRun' => $payRun,
            'user' => $user,
            'userRoleSchedule' => $userRoleSchedule,
            'userLeaveBankType' => $userLeaveBankType,
            'scheduledDays' => $scheduledDays,
            'startDate' => $scheduledDays[0]->date->copy(),
            'endDate' => $scheduledDays[0]->date->copy(),
        ];
    }

    #[ObjectShape([
        'doesHaveFixedHours' => 'bool',
        'duration' => TimeDuration::class,
        'minDuration' => TimeDuration::class,
        'maxDuration' => TimeDuration::class,

        'doesHaveDailyFixedHours' => 'bool',
        'dailyExpectedDuration' => TimeDuration::class,
        'dailyMinDuration' => TimeDuration::class,
        'dailyMaxDuration' => TimeDuration::class,

        'doesHaveExpectedTimeSpan' => 'bool',
        'ordinarySpan' => TimeSpan::class,
        'expectedSpan' => TimeSpan::class,
        'breaks' => TimeSpanList::class,

        'actorUser' => User::class,
    ])]
    public function l_lg_getWorkPatternScheduleSettings(
        bool|null $doesHaveFixedHours = null,
        TimeDuration|null $duration = null,
        TimeDuration|null $minDuration = null,
        TimeDuration|null $maxDuration = null,

        bool|null $doesHaveDailyFixedHours = null,
        TimeDuration|null $dailyExpectedDuration = null,
        TimeDuration|null $dailyMinDuration = null,
        TimeDuration|null $dailyMaxDuration = null,

        TimeSpan|null $ordinarySpan = null,
        bool|null $doesHaveExpectedTimeSpan = null,
        TimeSpan|null $expectedSpan = null,
        TimeSpanList|null $breaks = null,

        User $actorUser = null,
    ): \stdClass
    {
        return (object) [
            'doesHaveFixedHours' => !is_null($doesHaveFixedHours) && boolval($doesHaveFixedHours),
            'duration' => $duration ?? TimeDuration::parseFromHours(80),
            'minDuration' => $minDuration ?? TimeDuration::zero(),
            'maxDuration' => $maxDuration ?? TimeDuration::parseFromHours(80),

            'doesHaveDailyFixedHours' => !is_null($doesHaveDailyFixedHours) && boolval($doesHaveDailyFixedHours),
            'dailyExpectedDuration' => $dailyExpectedDuration ?? TimeDuration::parseFromHours(10),
            'dailyMinDuration' => $dailyMinDuration ?? TimeDuration::zero(),
            'dailyMaxDuration' => $dailyMaxDuration ?? TimeDuration::parseFromHours(20),

            'ordinary' => $ordinarySpan ?? TimeSpan::make('06:00', '20:00'),
            'doesHaveExpectedTimeSpan' => !is_null($doesHaveExpectedTimeSpan) && boolval($doesHaveExpectedTimeSpan),
            'expectedSpan' => $expectedSpan ?? TimeSpan::make('09:00', '17:00'),
            'breaks' => $breaks ?? TimeSpanList::make([TimeSpan::make('12:00', '13:00')]),

            'actorUser' => $actorUser ?? SuperUserFlag::getOne(),
        ];
    }

    #[ArrayShape([
        LeaveRequest::class,
        StructLeaveRequestPreAvailability::class,
    ])]
    public function l_lg_placeWorkPatternLeaveRequest(\stdClass|null $leaveSettings = null, \stdClass|null $scheduleSettings = null): array
    {
        if (is_null($leaveSettings)) {
            $leaveSettings = new \stdClass;
        }

        if (is_null($scheduleSettings)) {
            $scheduleSettings = new \stdClass;
        }

        $onFuturePayrun = $leaveSettings->onFuturePayrun ?? true;
        $dailyStartTime = $leaveSettings->dailyStartTime ?? '08:00';
        $dailyEndTime = $leaveSettings->dailyEndTime ?? '18:00';
        $dailyDuration = $leaveSettings->dailyDuration ?? TimeDuration::parseFromHours(8);

        $doesHaveFixedHours = $scheduleSettings->doesHaveFixedHours ?? false;
        $duration = $scheduleSettings->duration ?? TimeDuration::parseFromHours(80);
        $minDuration = $minDuration ?? TimeDuration::zero();
        $maxDuration = $maxDuration ?? TimeDuration::parseFromHours(80);

        $doesHaveDailyFixedHours = $scheduleSettings->doesHaveDailyFixedHours ?? false;
        $dailyExpectedDuration = $scheduleSettings->dailyExpectedDuration ?? TimeDuration::parseFromHours(8);
        $dailyMinDuration = $scheduleSettings->dailyMinDuration ?? TimeDuration::zero();
        $dailyMaxDuration = $scheduleSettings->dailyMaxDuration ?? TimeDuration::parseFromHours(24);

        $doesHaveExpectedTimeSpan = $scheduleSettings->doesHaveExpectedTimeSpan ?? false;
        $ordinarySpan = $scheduleSettings->ordinarySpan ?? TimeSpan::make('06:00', '20:00');
        $expectedSpan = $scheduleSettings->expectedSpan ?? TimeSpan::make('06:00', '20:00');
        $breaks = $scheduleSettings->breaks ?? TimeSpanList::make([TimeSpan::make('12:00', '13:00')]);

        $actorUser = $scheduleSettings->actorUser ?? null;

        $this->l_lg_loadScheduledDaysData(
            UserRoleScheduleType::WorkPattern,
            $onFuturePayrun,
            $leaveSettings->payRun,
            $leaveSettings->user,
            $leaveSettings->userRoleSchedule,
            $leaveSettings->userLeaveBankType,
            $leaveSettings->scheduledDays,
        );

        $scheduleShift = null;
        /** @var UserRoleSchedule $userRoleSchedule */
        $userRoleSchedule = $leaveSettings->userRoleSchedule;
        $userRoleSchedule->schedulePattern->periods[0]->periodShifts[0]->scheduleShift->dailyDoesHaveFixedHours = $doesHaveDailyFixedHours;
        $userRoleSchedule->schedulePattern->periods[0]->periodShifts[0]->scheduleShift->doesHaveFixedHours = $doesHaveFixedHours;

        if ($onFuturePayrun) {
            foreach ($leaveSettings->scheduledDays as $scheduledDay) {
                /** @var UserRoleSchedulePatternPeriodShiftDay $userRoleSchedulePatternPeriodShiftDay */
                $userRoleSchedulePatternPeriodShiftDay = $scheduledDay->type;
                $scheduleShift = $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift;

                if ($userRoleSchedulePatternPeriodShiftDay->isScheduled) {
                    $userRoleSchedulePatternPeriodShiftDay->periodShift->duration = $duration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->periodShift->minDuration = $minDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->periodShift->maxDuration = $maxDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->periodShift->clearSaveOrFail();

                    $userRoleSchedulePatternPeriodShiftDay->duration = $dailyDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->expectedDuration = $dailyExpectedDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->minDuration = $dailyMinDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->maxDuration = $dailyMaxDuration->copy();
                    $userRoleSchedulePatternPeriodShiftDay->ordinarySpan = $ordinarySpan->copy()->setDate($scheduledDay->date->copy());
                    $userRoleSchedulePatternPeriodShiftDay->doesHaveExpectedTimeSpan = $doesHaveExpectedTimeSpan;
                    $userRoleSchedulePatternPeriodShiftDay->expectedSpan = $expectedSpan->copy()->setDate($scheduledDay->date->copy());
                    $userRoleSchedulePatternPeriodShiftDay->breaks = $breaks->duplicate()->setDate($scheduledDay->date->copy());
                    $userRoleSchedulePatternPeriodShiftDay->clearSaveOrFail();
                }
            }
        } else {
            foreach ($leaveSettings->scheduledDays as $scheduledDay) {
                /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
                $scheduledPayRunPeriodDayWorkPattern = $scheduledDay->type;
                $scheduleShift = $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift;

                /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
                $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;
                $scheduledPayRunPeriodWorkPattern->duration = $duration->copy();
                $scheduledPayRunPeriodWorkPattern->minDuration = $minDuration->copy();
                $scheduledPayRunPeriodWorkPattern->maxDuration = $maxDuration->copy();
                $scheduledPayRunPeriodWorkPattern->clearSaveOrFail();

                if ($scheduledPayRunPeriodDayWorkPattern->isScheduled) {
                    $scheduledPayRunPeriodDayWorkPattern->duration = $dailyDuration->copy();
                    $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $dailyExpectedDuration->copy();
                    $scheduledPayRunPeriodDayWorkPattern->minDuration = $dailyMinDuration->copy();
                    $scheduledPayRunPeriodDayWorkPattern->maxDuration = $dailyMaxDuration->copy();
                    $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinarySpan->copy()->setDate($scheduledDay->date->copy());
                    $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = $doesHaveExpectedTimeSpan;
                    $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedSpan->copy()->setDate($scheduledDay->date->copy());
                    $scheduledPayRunPeriodDayWorkPattern->breaks = $breaks->duplicate()->setDate($scheduledDay->date->copy());
                    $scheduledPayRunPeriodDayWorkPattern->clearSaveOrFail();
                }
            }
        }

        $scheduleShift->doesHaveFixedHours = $doesHaveFixedHours;

        $actorUser = $actorUser ?? SuperUserFlag::getOne();

        $availability = $leaveSettings->isFullDay
            ? CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
                userLeaveBankType: $leaveSettings->userLeaveBankType,
                userRoleSchedule: $leaveSettings->userRoleSchedule,
                isFullDay: true,
                startDate: $leaveSettings->startDate,
                endDate: $leaveSettings->endDate,
                actorUser: $actorUser,
            )
            : CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
                userLeaveBankType: $leaveSettings->userLeaveBankType,
                userRoleSchedule: $leaveSettings->userRoleSchedule,
                isFullDay: false,
                startDate: $leaveSettings->startDate,
                endDate: $leaveSettings->endDate,
                dailyStartTime: $dailyStartTime,
                dailyEndTime: $dailyEndTime,
                dailyDuration: $dailyDuration,
                actorUser: $actorUser,
            );

        $leave = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            userRoleSchedule: $leaveSettings->userRoleSchedule,
            userLeaveBankType: $leaveSettings->userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
            doesApprove: $leaveSettings->doesApprove,
        );

        return [
            $leave,
            $availability,
        ];
    }
}
