<?php

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk;

use Element\ElementTime\Domains\Tenant\_System\Models\KioskDevice;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'KioskDeviceBasicResource',
    title: 'Kiosk Device data',
    required: ['id', 'name', 'localization'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'name', type: 'string', example: 'kiosk tablet front door'),
        new OA\Property(property: 'localization', type: 'string', example: 'front door'),
    ],
)]
class KioskDeviceBasicResource extends Resource
{
    public static $relations = [];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var KioskDevice $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'name' => $r->name,
            'localization' => $r->localization,
        ];
    }
}
