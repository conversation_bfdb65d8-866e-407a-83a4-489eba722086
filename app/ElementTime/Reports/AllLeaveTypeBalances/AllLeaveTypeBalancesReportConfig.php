<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\AllLeaveTypeBalances;

use Element\ElementTime\Reports\ReportCategoryType;
use Element\ElementTime\Support\Reports\TenantReportConfig;

class AllLeaveTypeBalancesReportConfig extends TenantReportConfig
{
    const SLUG = 'all-leave-type-balances';
    const CATEGORY = ReportCategoryType::General;
    const NAME = 'All leave balances';
    const DESCRIPTION = 'Select a department to view all Pro-rata, Available, Committed and Historic leave balances for users within that department.';
    const ROUTE = 'all-leave-type-balances.{{type}}';
    const FILE_NAME_TEMPLATE = '{{tenant.slug}}__{{slug}}-{{department.id}}-{{dateGenerated}}-{{genUser.id}}.{{fileType}}';
    const DOWNLOAD_FILE_NAME_TEMPLATE = '{{name}} for {{department.name}} - {{tenant.name}} - {{dateGenerated}}.{{fileType}}';

    const TYPES = [
        [
            'class' => AllLeaveTypeBalancesExcelReport::class,
            'options' => [
                'order' => 1,
            ],
        ]
    ];

    const FILTERS = [
        self::REQUIRED_FILTER_DEPARTMENT,
    ];

    const PARAMETERS = [
        'idDepartment' => 'Department.id',
    ];

    /** @var string */
    const COUNTRY = null;

    /** @var string */
    const REGION = null;
}
