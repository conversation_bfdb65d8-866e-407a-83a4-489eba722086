<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\_System\Models;

use Element\ElementTime\Domains\Tenant\_System\Config\KioskDeviceConfig;
use Element\ElementTime\Domains\Tenant\_System\Repositories\KioskDeviceRepository;
use Element\ElementTime\Support\Domains\Models\TenantBaseModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 *
 * @property string $deviceId
 * @property string $name
 * @property string $localization
 * @property bool $isActive
 *
 * @property string $appId
 * @property string $secret
 *
 * @property-read Collection|KioskDeviceRequest[] $requests
 */
class KioskDevice extends TenantBaseModel
{
    /** @var KioskDeviceConfig */
    public static $config = KioskDeviceConfig::class;

    /** @var KioskDeviceRepository */
    protected static $repositoryClass = KioskDeviceRepository::class;

    /** @var KioskDeviceRepository */
    public $repository;

    protected $primaryKey = 'id';
    public $incrementing = true;
    public $timestamps = true;
    protected $table = 'KioskDevice';

    public function requests(): HasMany
    {
        return $this->hasMany(KioskDeviceRequest::class, 'kioskDevice_id');
    }
}
