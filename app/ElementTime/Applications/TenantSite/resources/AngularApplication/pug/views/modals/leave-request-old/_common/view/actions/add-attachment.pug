form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$f.submit()' name='$f.form')
  .panel.panel-default
    .panel-heading
      h4.h4.m0 Attach new file
    .panel-body
      .row
        .col-xs-12.col-md-6
          elt-attachment-file(id='LeaveAttach_attachmentFile' elt-label='Attachment file' name='attach_attachmentFile' ng-model='$f.data.attachmentFile' elt-file-options='{{settings.form.LeaveRequest.attachmentFileOptions}}')
        .col-xs-12.col-md-6.form-horizontal(ng-if='!!$ctrl.leave.data.hasSplitParts')
          label &nbsp;
          elt-toggle(ng-model='$f.data.doesLinkAttachment' elt-name='' elt-label='Link attachment to requests also linked to this request' elt-help-text='')
      .row
        .col-xs-12
          hr.mt0.mb-lg
      .row.visible-sm.visible-md.visible-lg
        .col-xs-12.text-right
          button.btn.btn-default.ml(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Cancel
          button.btn.btn-success.ml(type='submit' ng-disabled='!!$f.isSubmitting')
            | Save
      .row.visible-xs
        .col-xs-6
          button.btn.btn-default.btn-block(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Cancel
        .col-xs-6
          button.btn.btn-success.btn-block(type='submit' ng-disabled='!!$f.isSubmitting')
            | Save
