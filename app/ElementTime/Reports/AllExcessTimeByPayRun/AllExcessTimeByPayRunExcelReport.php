<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\AllExcessTimeByPayRun;

use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayRunType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class AllExcessTimeByPayRunExcelReport extends TenantExcelReport
{
    use HasPayRunParameters {
        setData as _iSetData;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const REPORT_SLUG = 'all-excess-time-by-payrun';
    const TYPE_SLUG = 'all-excess-time-by-payrun.xls';

    /** @var string|AllExcessTimeByPayRunReportConfig */
    public $c = AllExcessTimeByPayRunReportConfig::class;

    protected PayRun|null $payRun;
    protected PayRunType|null $payRunType;
    protected float $fteHoursPerDay = 7.6;

    /** @throws InvalidArgumentException */
    protected function setData(array $info): void
    {
        $this->_iSetData($info);

        $this->fteHoursPerDay = TenantSystemSettings::getValue('settings.fteHoursPerWeek') / 5;
    }

    /** @throws InvalidArgumentException */
    protected function buildReportData(): void
    {
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->buildSheetHeader($sheet);
        $this->buildSheetDetails($sheet);

        $this->addSheet($sheet);
    }

    protected function buildSheetHeader(\stdClass|null &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Payrun', 'text', 'center', 4),
            $this->createColumn('Start date', 'text', 'center', 3),
            $this->createColumn('End date', 'float', 'center', 3),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                $this->payRunType->name . ' #' . $this->payRun->number . ' (' . $this->payRun->fiscalYear . ')', // Payrun
                $this->parseDate($this->getStartDate()), // Start date
                $this->parseDate($this->getEndDate()), // End date
            ],
        ]];

        $sheet->data[] = $data;
    }

    /** @throws InvalidArgumentException */
    protected function buildSheetDetails(\stdClass|null &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = $this->getSheetDetailsParentHeader();
        $table->columns = $this->getSheetDetailsHeader();
        $table->data = $this->getSheetDetailsData();
        $sheet->data[] = $table;
    }

    protected function getSheetDetailsParentHeader(): array
    {
        $ret = [
            $this->createParentColumn(''),
            $this->createParentColumn('Staff', 5),
            $this->createParentColumn('Cost centre', 2),
            $this->createParentColumn('Details', 5),
            $this->createParentColumn('Value', 3),
        ];

        return $ret;
    }

    protected function getSheetDetailsHeader(): array
    {
        $ret = [
            $this->createColumn('#', 'integer', 'center'),

            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Direct manager'),
            $this->createColumn('Department'),

            $this->createColumn('Name'),
            $this->createColumn('Number'),
            $this->createColumn('Comments'),

            $this->createColumn('Hour code'),
            $this->createColumn('Rule'),
            $this->createColumn('Type'),
            $this->createColumn('Date'),

            $this->createFloatColumn('Recorded hours'),
            $this->createFloatColumn('Adjusted hours'),
            $this->createColumn('Amount paid', align: 'right'),
        ];

        return $ret;
    }

    /** @throws InvalidArgumentException */
    protected function getSheetDetailsData(): array
    {
        $tableData = [];

        $data = $this->fetchData();

        $last = null;
        $userCount = 0;
        $i = 0;
        foreach ($data as $datum) {
            if (is_null($last) || $last != $datum->userId) {
                $userCount++;
                $last = $datum->userId;
            }

            $rowConfig = [
                'color' => $userCount % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => [
                    $i, // #
                    $datum->userExternalId, // Ext ref ID
                    $datum->userFullName, // Name
                    $datum->roleName, // Role
                    $datum->managerFullName, // Direct manager
                    $datum->departmentName, // Department

                    $datum->costName, // Name
                    $datum->costCode, // Number
                    $datum->comments, // Comments

                    $datum->hourCode, // Hour code
                    $datum->ruleName, // Rule
                    $datum->typeName, // Type
                    $datum->date, // Date

                    $datum->actualHours, // Recorded hours
                    $datum->adjustedHours > 0 ? $datum->adjustedHours : null, // Adjusted hours
                    $datum->amountPaid > 0 ? '$' . $datum->amountPaid : null, // Amount paid
                ],
            ];

            $i++;
        }

        return $tableData;
    }

    /** @throws InvalidArgumentException */
    protected function fetchData(): array
    {
        $ret = [];

        $timeSheets = $this->getTimeSheets();

        foreach ($timeSheets as $timeSheet) {
            $user = $timeSheet->payRunItem->user;
            try {
                $manager = $user->repository->getManager($this->getEndDate());
            } catch (\Throwable) {
                $manager = null;
            }

            foreach ($timeSheet->repository->getAllExcessTimeActiveItemRuleRecords() as $rule) {
                foreach ($rule->works as $work) {
                    $userRole = $work->time->userShiftDay->userShift->userRole;
                    $userRoleManager = $userRole->repository->getActiveUserRoleManager($this->getStartDate(), $this->getEndDate());
                    if (!is_null($userRoleManager)) {
                        $manager = $userRoleManager->manager;
                    }

                    $userRoleDepartments = $userRole->getActiveUserRoleDepartments($this->getEndDate());

                    $department = '';
                    foreach ($userRoleDepartments as $userRoleDepartment) {
                        if (strlen($department) > 0) {
                            $department .= ', ';
                        }

                        $department .= $userRoleDepartment->department->name;
                    }

                    foreach ($work->splitItems as $splitItem) {
                        if (is_null($splitItem->excessTimeRuleSplitItem_id) || is_null($splitItem->excessTimeRuleSplitItem)) {
                            $actualHours = $this->parseFloat($work->originalHours);
                            $adjustedHours = $this->parseFloat($work->adjustedHours);
                        } else {
                            $actualHours = $this->parseFloat($splitItem->excessTimeRuleSplitItem->repository->applyPercentage($work->originalHours));
                            $adjustedHours = $this->parseFloat($splitItem->adjustedHours);
                        }

                        if ($actualHours <= 0) {
                            continue;
                        }

                        $newRow = new \stdClass;

                        $newRow->userId = $timeSheet->payRunItem->user_id;
                        $newRow->userExternalId = $userRole->repository->getExternalId();
                        $newRow->userFullName = $timeSheet->payRunItem->user->fullName;
                        $newRow->roleName = $userRole->role->name;
                        $newRow->managerFullName = $manager?->fullName ?? '-';
                        $newRow->departmentName = strlen($department) > 0 ? $department : '-';

                        $newRow->costName = $work->name;
                        $newRow->costCode = $work->formattedCode;
                        $newRow->comments = $rule->item->timeSheetExcessTime->comments;

                        $newRow->hourCode = $splitItem->externalId;
                        $newRow->ruleName = !is_null($splitItem->excessTimeRuleSplitItem) ? $splitItem->excessTimeRuleSplitItem->excessTimeRule->name : $rule->name;
                        $newRow->typeName = $splitItem->ruleTypeClass::SHORT_NAME;
                        $newRow->date = $rule->item->timeSheetExcessTime->type == 'P'
                            ? ($this->parseDate($rule->item->timeSheetExcessTime->startDate) . '- ' . $this->parseDate($rule->item->timeSheetExcessTime->endDate))
                            : $this->parseDate($rule->item->timeSheetExcessTime->date);

                        $newRow->actualHours = $actualHours;
                        $newRow->adjustedHours = $this->parseFloat($adjustedHours);
                        if ($splitItem->ruleTypeClass::IS_PAID) {
                            $newRow->amountPaid = $this->parseFloat($adjustedHours * $work->time->userShiftDay->relatedRate->overtimeHourlyRate);
                        } else {
                            $newRow->amountPaid = 0;
                        }

                        $ret[] = $newRow;
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * @return Collection|TimeSheet[]
     * @throws InvalidArgumentException
     */
    protected function getTimeSheets(): Collection|array
    {
        $u = $this->genUser;

        $constraints = [
            ['PayRunItem.payRun_id', '=', $this->payRun->id],
        ];
        $relations = [
            'payRunItem.user.userManagers.manager',

            /// Daily
            'timeSheetDayTimes.excessTimes.items.rules.item.timeSheetExcessTime',
            'timeSheetDayTimes.excessTimes.items.rules.works.splitItems.excessTimeRuleSplitItem.excessTimeRule',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.userShiftDay.userShift.userRole.role',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.userShiftDay.userShift.userRole.userRoleManagers.manager',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.userShiftDay.userShift.userRole.userRolePayTypes.payType.payBand',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.userShiftDay.userShift.user.userHigherDuties',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.model',
            'timeSheetDayTimes.excessTimes.items.rules.works.time.type',

            'timeSheetDayTimes.excessTimes.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'timeSheetDayTimes.excessTimes.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.activityType',
            'timeSheetDayTimes.excessTimes.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',

            'timeSheetDayTimes.excessTimes.items.rules.works.rule.excessTimeRule',
            'timeSheetDayTimes.excessTimes.items.rules.works.rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
            'timeSheetDayTimes.excessTimes.items.rules.works.rule.excessTimeGroupSectionLevelRule.excessTimeRule',

            /// Period
            'timeSheetDays.excessTime.items.rules.item.timeSheetExcessTime',
            'timeSheetDays.excessTime.items.rules.works.splitItems.excessTimeRuleSplitItem.excessTimeRule',
            'timeSheetDays.excessTime.items.rules.works.time.userShiftDay.userShift.userRole.role',
            'timeSheetDays.excessTime.items.rules.works.time.userShiftDay.userShift.userRole.userRoleManagers.manager',
            'timeSheetDays.excessTime.items.rules.works.time.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'timeSheetDays.excessTime.items.rules.works.time.userShiftDay.userShift.userRole.userRolePayTypes.payType.payBand',
            'timeSheetDays.excessTime.items.rules.works.time.userShiftDay.userShift.user.userHigherDuties',
            'timeSheetDays.excessTime.items.rules.works.time.model',
            'timeSheetDays.excessTime.items.rules.works.time.type',

            'timeSheetDays.excessTime.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'timeSheetDays.excessTime.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.activityType',
            'timeSheetDays.excessTime.items.rules.works.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',

            'timeSheetDays.excessTime.items.rules.works.rule.excessTimeRule',
            'timeSheetDays.excessTime.items.rules.works.rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
            'timeSheetDays.excessTime.items.rules.works.rule.excessTimeGroupSectionLevelRule.excessTimeRule',
        ];

        if (
            !$u->access->isPayrollOfficer(true)
            && !$u->access->isBusinessIntelligence(true)
            && !$u->access->isPayrollOfficer(true, $this->getEndDate())
            && !$u->access->isBusinessIntelligence(true, $this->getEndDate())
        ) {
            $constraints[] = ['IN', 'PayRunItem.user_id', $this->getAllReportsAndRoleReportsIdsForPayRun(doesIncludeIndirectReports: $u->doesIncludeIndirectReportsOnReports)];
        }

        return TimeSheet::q()
            ->constraints($constraints)
            ->relations($relations)
            ->order('CAST(User.externalId AS UNSIGNED)')
            ->many();
    }
}
