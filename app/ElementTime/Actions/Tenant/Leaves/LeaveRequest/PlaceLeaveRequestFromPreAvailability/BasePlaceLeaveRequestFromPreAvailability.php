<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Carbon\Carbon;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\LeaveSyncAttachments;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\Notifications\SendRequestedNotificationBasedOnWorkflow;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet\CalculateTimeSheet;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet\GetTimeSheetForUser;
use Element\ElementTime\Applications\_System\Jobs\Integrations\Edrms\PushLeaveAttachmentsToEdrmsJob;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeValidationFlag\ModelActionTypeValidationFlagLeave;
use Element\ElementTime\Support\Database\EDB;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Database\Eloquent\Collection;

abstract class BasePlaceLeaveRequestFromPreAvailability extends BaseLeavesAction
{
    protected LeaveRequest $leave;
    protected UserRoleSchedule $userRoleSchedule;
    protected UserLeaveBankType $userLeaveBankType;
    protected StructLeaveRequestPreAvailability $preAvailability;
    protected string|null $reason;
    protected Collection|array $attachments;
    protected LeaveRequest|null $replaced = null;
    protected User|null $actorUser = null;
    protected RecordedVia|string $recordedVia;

    /**
     * @throws \Throwable
     */
    public function handle(
        UserRoleSchedule $userRoleSchedule,
        UserLeaveBankType $userLeaveBankType,
        StructLeaveRequestPreAvailability $preAvailability,
        string|null $reason = null,
        Collection|array|null $attachments = null,
        LeaveRequest|null $replaced = null,
        User|null $actorUser = null,

        bool $doesApprove = false,
        RecordedVia $recordedVia = RecordedVia::DEFAULT,
        bool $doesSendBroadcastEvents = true,
        bool $doesCalculateRelatedRecords = true,
    ): LeaveRequest
    {
        $this->userRoleSchedule = $userRoleSchedule;
        $this->userLeaveBankType = $userLeaveBankType;
        $this->preAvailability = $preAvailability;
        $this->reason = $reason;
        $this->attachments = $attachments ?? Collection::make([]);
        $this->replaced = $replaced;
        $this->actorUser = $actorUser;
        $this->recordedVia = $recordedVia;

        return EDB::tenantConn()->transaction(function () use ($doesApprove, $doesSendBroadcastEvents, $doesCalculateRelatedRecords) {
            if (is_null($this->actorUser)) {
                $this->actorUser = $this->userLeaveBankType->bank->user;
            }

            $this->leave = new LeaveRequest;

            if (!is_null($this->replaced)) {
                if ($this->replaced->repository->isReplaced()) {
                    throw new UnauthorizedActionException([
                        'action' => 'replace',
                        'model' => 'leave request',
                        'reason' => 'There is another request replacing this one, please check it and if you need to change it, create a new one from there',
                    ]);
                }

                if (!$this->replaced->repository->canReplace($this->actorUser)) {
                    throw new UnauthorizedActionException([
                        'action' => 'replace',
                        'model' => 'leave request',
                        'reason' => 'This leave request is unable to be replaced',
                    ]);
                }

                $this->leave->isFromAutoComplete = $this->replaced->isFromAutoComplete;
            }

            $this->setCommonLeaveData();
            $this->setLeaveData();

            /**
             * TODO
             *  - [x] Set LeaveRequest's general data
             *      - [x] Common data - bank type, user role schedule
             *      - [x] Specific data - startDate x payRunStartDate, isFullDay and other cases that is not work pattern
             *  - [ ] Populate column/data if payrun/schedule already exists - ideally achieved by new action class so it could be used on other contexts
             *  - [x]  Create LeaveRequestDays
             *      - [x] Add specific implementation for each schedule type
             *  - [x] Replace work hours
             *  - [x] Replace leave request
             *  - [x] Validate reason requirement based on availability on submit
             *  - [x] Validate reason requirement based on availability on approve
             *  - [x] Validate attachment requirement based on availability on submit
             *  - [x] Validate attachment requirement based on availability on approve
             *  - [x] build workflow
             *      - [x] covers individual workflow conditions
             *      - [x] covers individual workflow approval types
             *      - [x] covers individual Support\WorkflowTypeTreeRuleTypes
             *      - [?] WorkflowStatusChangedEvent
             *          - [-] TimeSheets\Listeners\onLeaveRequestStatusChanged - Reimplement on Timesheet rework phase ?
             *          - [-] Users\Listeners\onLeaveRequestStatusChanged - Reimplement on User timeline rework phase ?
             *  - [x] approve if applicable
             *      - [x] calculate balances if approved
             *      - [x] push to calendar - LeaveRequestRepository@makeApproveActions - ExternalCalendarEntry::createAllRelevantFromLocalEvent ?
             *  - [ ] solve all the notifications regarding the LeaveRequest's workflow
             *  - [x] syncAttachments
             *      - [x] PushLeaveAttachmentsToEdrmsJob
             *  - [x] broadcast for pages where leave instance is shown
             *  - [x] build ModelTasks
             *  - [ ] Recalculate timesheet
             *      - [ ] Recalculate excess time
             *      - [ ] Recalculate penalty
             *      - [ ] Recalculate allowance
             *      - [ ] Recalculate total
             */

            $this->leave->clearSave();

            LeaveSyncAttachments::make()->handle($this->leave, $this->attachments);

            $this->leave->validateModelActionFlags(ModelActionTypeValidationFlagLeave::Submit, true);

            $this->generateDays();

            if ($this->preAvailability->clashedWorks && count($this->preAvailability->clashedWorks) > 0) {
                $this->replaceWorkedHours();
            }

            if (!is_null($this->replaced)) {
                $this->replaced->workflow->otherRel_id = $this->leave->id;
                $this->replaced->repository->replace($this->actorUser, $this->reason ?? '');
                $this->replaced->updateOutstandingModelTasks();

                $this->leave->load('userLeaveBankType.bank');
            }

            $this->leave->repository->buildWorkflow($this->actorUser);

            /// TODO - model tasks need to be revisited for work-pattern and duration-only cases as well
            $this->leave->buildModelTasks();

            // Approve if submitted by approver
            $shouldApprove = $doesApprove && $this->leave->ownerUser->id != $this->actorUser->id;

            // Generate submission workflow entry for leave request
            $this->leave->repository->submit(
                $this->actorUser,
                $this->reason ?? '',
                Carbon::now()->addSeconds(1),
                runsStatusEvent: false, // Use of this flag is discouraged as it will be removed in the future
                recordedVia: $this->recordedVia,
            );

            if (
                $shouldApprove
                && !$this->leave->repository->isApproved()
                && $this->leave->repository->canApprove($this->actorUser, false)
            ) {
                $this->leave->repository->approve(
                    actor: $this->actorUser,
                    dateTime: Carbon::now()->addSeconds(2),
                    runsStatusEvent: false, // Use of this flag is discouraged as it will be removed in the future
                );
            } else {
                SendRequestedNotificationBasedOnWorkflow::make()->handle($this->leave);
            }

            /**
             * @todo:
             *      - Check if this logic could be moved to leave request repository (and HasWorkflowRepository) - approve/makeApproveActions
             *      - Check if it is missing some logic here
             */
            if ($doesCalculateRelatedRecords) {
                $timeSheet = GetTimeSheetForUser::make()->handle($this->userLeaveBankType->bank->user, $this->leave->startDate);

                if (!is_null($timeSheet)) {
                    CalculateTimeSheet::make()->handle($timeSheet);
                }
            }

            if ($doesSendBroadcastEvents) {
                $this->sendBroadcastEvents();
            }

            PushLeaveAttachmentsToEdrmsJob::dispatch($this->leave)->afterResponse();

            return $this->leave;
        });
    }

    // ---------

    protected function setCommonLeaveData(): void
    {
        $this->leave->scheduleType = $this->preAvailability->scheduleType;
        $this->leave->userRoleSchedule_id = $this->userRoleSchedule->id;
        $this->leave->userLeaveBankType_id = $this->userLeaveBankType->id;
        $this->leave->duration = $this->preAvailability->duration->copy();
        $this->leave->durationAdjusted = $this->preAvailability->durationAdjusted->copy();
        $this->leave->reason = $this->reason;
        $this->leave->actor_id = $this->actorUser->id;
        $this->leave->doesHaveFTECalculation = !!$this->preAvailability->doesHaveFteAdjustments;
        $this->leave->recordedVia = $this->recordedVia;

        if (!is_null($this->replaced)) {
            $this->leave->originalReplaced_id = $this->replaced->id;
        }
    }

    protected function replaceWorkedHours(): void
    {
        return;
    }

    abstract protected function setLeaveData(): void;

    abstract protected function generateDays(): void;

    abstract protected function sendBroadcastEvents(): void;
}
