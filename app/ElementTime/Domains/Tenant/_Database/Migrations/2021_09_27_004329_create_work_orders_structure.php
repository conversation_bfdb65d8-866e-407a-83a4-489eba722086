<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        // Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->string('fullFormattedCodeMask')->default('[[pc]].[[cc]].[[ac]]')->after('projectCodeMask');
        });

        // Classification

        //region Table: WorkOrderClassificationItem

        Schema::create('WorkOrderClassificationItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->unsignedTinyInteger('level');
            $table->text('description')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
            $table->softDeletes();

            $table->index(['level'], 'woci_level_ix');
        });

        //endregion Table: WorkOrderClassificationItem

        //region Table: WorkOrderClassificationGroup

        Schema::create('WorkOrderClassificationGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->text('description')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: WorkOrderClassificationGroup

        //region Table: WorkOrderClassificationGroupItem

        Schema::create('WorkOrderClassificationGroupItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('workOrderClassificationGroup_id');
            $table->foreign('workOrderClassificationGroup_id', 'wocgi_wocg_fn')
                ->references('id')->on('WorkOrderClassificationGroup')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('workOrderClassificationItem_id');
            $table->foreign('workOrderClassificationItem_id', 'wocgi_woci_fn')
                ->references('id')->on('WorkOrderClassificationItem')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedTinyInteger('level');

            $table->timestamps();

            $table->unique(['workOrderClassificationGroup_id', 'level'], 'wocgi_gr_lvl_uq');
            $table->unique(['workOrderClassificationGroup_id', 'workOrderClassificationItem_id'], 'wocgi_gr_it_uq');
        });

        //endregion Table: WorkOrderClassificationGroupItem

        //region Table: WorkOrderClassificationGroupPayGroupType

        Schema::create('WorkOrderClassificationGroupPayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('workOrderClassificationGroup_id');
            $table->foreign('workOrderClassificationGroup_id', 'wocgpgt_wocg_fn')
                ->references('id')->on('WorkOrderClassificationGroup')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('payGroupType_id');
            $table->foreign('payGroupType_id', 'wocgpgt_pgt_fn')
                ->references('id')->on('PayGroupType')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->timestamps();

            $table->unique(['workOrderClassificationGroup_id', 'payGroupType_id'], 'wocgpgt_gr_pgt_uq');
        });

        //endregion Table: WorkOrderClassificationGroupPayGroupType

        //region Table: WorkOrderClassificationGroupDepartment

        Schema::create('WorkOrderClassificationGroupDepartment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('workOrderClassificationGroup_id');
            $table->foreign('workOrderClassificationGroup_id', 'wocgd_wocg_fn')
                ->references('id')->on('WorkOrderClassificationGroup')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('department_id');
            $table->foreign('department_id', 'wocgd_dept_fn')
                ->references('id')->on('Department')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->timestamps();

            $table->unique(['workOrderClassificationGroup_id', 'department_id'], 'wocgd_gr_dept_uq');
        });

        //endregion Table: WorkOrderClassificationGroupDepartment

        // Activities

        //region Table: ActivityGroup

        Schema::create('ActivityGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->text('description')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
            $table->softDeletes();

            $table->index(['name'], 'ag_name_ix');
        });

        //endregion Table: ActivityGroup

        //region Table: ActivityType

        Schema::create('ActivityType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->string('costCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->text('description')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
            $table->softDeletes();

            $table->index(['name', 'activityCode'], 'at_namecode_ix');
        });

        //endregion Table: ActivityType

        //region Table: ActivityGroupType

        Schema::create('ActivityGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('activityGroup_id');
            $table->foreign('activityGroup_id', 'agt_ag_fn')
                ->references('id')->on('ActivityGroup')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('activityType_id');
            $table->foreign('activityType_id', 'agt_at_fn')
                ->references('id')->on('ActivityType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('costCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->string('budgetRateType')->default(\Element\ElementTime\Domains\Tenant\Projects\Support\ProjectBudgetRateTypes\NoneType::ID);
            $table->decimal('budgetHourlyRate', 16, 10)->nullable(); // Only for when rateType is fixed hourly rate
            $table->decimal('budgetAmount', 16, 10)->nullable();
            $table->text('description')->nullable();

            $table->timestamps();

            $table->unique(['activityGroup_id', 'activityType_id'], 'agt_g_t_uq');
        });

        //endregion Table: ActivityGroupType

        // Work orders

        //region Table WorkOrderType

        Schema::create('WorkOrderType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->unsignedBigInteger('ownerUser_id')->nullable();
            $table->foreign('ownerUser_id', 'wot_ou_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('name');
            $table->string('projectCode');
            $table->string('costCode');
            $table->string('activityCode');
            $table->text('description')->nullable();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->string('viewType')->default(\Element\ElementTime\Domains\Tenant\Projects\Support\ProjectViewTypes\NameOnly::ID);
            $table->boolean('isOpen')->default(false);

            $table->string('budgetRateType')->default(\Element\ElementTime\Domains\Tenant\Projects\Support\ProjectBudgetRateTypes\NoneType::ID);
            $table->decimal('budgetHourlyRate', 16, 10)->nullable(); // Only for when rateType is fixed hourly rate
            $table->decimal('budgetAmount', 16, 10)->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
            $table->softDeletes();

            $table->index(['name'], 'wot_name_ix');
            $table->index(['projectCode'], 'wot_pcode_ix');
        });

        //endregion Table WorkOrderType

        //region Table WorkOrderTypeActivityGroup

        Schema::create('WorkOrderTypeActivityGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotag_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityGroup_id');
            $table->foreign('activityGroup_id', 'wotag_ag_fn')
                ->references('id')->on('ActivityGroup')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();

            $table->unique(['workOrderType_id', 'activityGroup_id'], 'wotag_wot_ag_uq');
        });

        //endregion Table WorkOrderTypeActivityGroup

        //region Table WorkOrderTypeActivityType

        Schema::create('WorkOrderTypeActivityType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotat_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityType_id');
            $table->foreign('activityType_id', 'wotat_at_fn')
                ->references('id')->on('ActivityType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityGroupType_id')->nullable();
            $table->foreign('activityGroupType_id', 'wotat_agt_fn')
                ->references('id')->on('ActivityGroupType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('projectCode')->nullable();
            $table->string('costCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->boolean('doesUseDefaultBudget')->default(true);
            $table->string('budgetRateType')->nullable();
            $table->decimal('budgetHourlyRate', 16, 10)->nullable();
            $table->decimal('budgetAmount', 16, 10)->nullable();

            $table->timestamps();

            $table->unique(['workOrderType_id', 'activityType_id'], 'wotat_wot_at_uq');
        });

        //endregion Table WorkOrderTypeActivityType

        //region Table WorkOrderTypeDepartment

        Schema::create('WorkOrderTypeDepartment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotd_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('department_id');
            $table->foreign('department_id', 'wotd_d_fn')
                ->references('id')->on('Department')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();

            $table->unique(['workOrderType_id', 'department_id'], 'wotd_wot_d_uq');
        });

        //endregion Table WorkOrderTypeDepartment

        //region Table WorkOrderTypePayGroupType

        Schema::create('WorkOrderTypePayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotpgt_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('payGroupType_id');
            $table->foreign('payGroupType_id', 'wotpgt_pgt_fn')
                ->references('id')->on('PayGroupType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();

            $table->unique(['workOrderType_id', 'payGroupType_id'], 'wotpgt_wot_pgt_uq');
        });

        //endregion Table WorkOrderTypePayGroupType

        // Projects

        //region Table ProjectActivityGroup

        Schema::create('ProjectActivityGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('project_id');
            $table->foreign('project_id', 'pag_p_fn')
                ->references('id')->on('Project')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityGroup_id');
            $table->foreign('activityGroup_id', 'pag_ag_fn')
                ->references('id')->on('ActivityGroup')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();

            $table->unique(['project_id', 'activityGroup_id'], 'pag_p_ag_uq');
        });

        //endregion Table ProjectActivityGroup

        //region Table ProjectActivityType

        Schema::create('ProjectActivityType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('project_id');
            $table->foreign('project_id', 'pat_p_fn')
                ->references('id')->on('Project')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityType_id');
            $table->foreign('activityType_id', 'pat_at_fn')
                ->references('id')->on('ActivityType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('activityGroupType_id')->nullable();
            $table->foreign('activityGroupType_id', 'pat_agt_fn')
                ->references('id')->on('ActivityGroupType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('costCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->boolean('doesUseDefaultBudget')->default(true);
            $table->string('budgetRateType')->nullable();
            $table->decimal('budgetHourlyRate', 16, 10)->nullable();
            $table->decimal('budgetAmount', 16, 10)->nullable();

            $table->timestamps();

            $table->unique(['project_id', 'activityType_id'], 'pat_wot_at_uq');
        });

        //endregion Table ProjectActivityType
    }

    public function down(): void
    {
        Schema::dropIfExists('ProjectActivityType');
        Schema::dropIfExists('ProjectActivityGroup');

        Schema::dropIfExists('WorkOrderTypePayGroupType');
        Schema::dropIfExists('WorkOrderTypeDepartment');
        Schema::dropIfExists('WorkOrderTypeActivityType');
        Schema::dropIfExists('WorkOrderTypeActivityGroup');
        Schema::dropIfExists('WorkOrderType');

        Schema::dropIfExists('ActivityGroupType');
        Schema::dropIfExists('ActivityType');
        Schema::dropIfExists('ActivityGroup');

        Schema::dropIfExists('WorkOrderClassificationGroupDepartment');
        Schema::dropIfExists('WorkOrderClassificationGroupPayGroupType');
        Schema::dropIfExists('WorkOrderClassificationGroupItem');
        Schema::dropIfExists('WorkOrderClassificationGroup');
        Schema::dropIfExists('WorkOrderClassificationItem');

        if (Schema::hasColumn('Settings', 'fullFormattedCodeMask')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('fullFormattedCodeMask');
            });
        }
    }
};
