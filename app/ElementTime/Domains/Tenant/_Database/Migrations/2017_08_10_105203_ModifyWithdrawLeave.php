<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Requests
        Schema::table('Leave', function (Blueprint $table) {
            $table->bigInteger('originalLeave_id')->unsigned()->nullable();
            $table->foreign('originalLeave_id', 'leave_ol_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->bigInteger('originalToil_id')->unsigned()->nullable();
            $table->foreign('originalToil_id', 'toil_ot_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->bigInteger('originalRosteredTimeOff_id')->unsigned()->nullable();
            $table->foreign('originalRosteredTimeOff_id', 'rto_orto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region <Leave|Toil|RosteredTimeOff>StatusHistory
        Schema::create('LeaveStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('leave_id')->unsigned();
            $table->bigInteger('otherLeave_id')->unsigned()->nullable();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();
            $table->bigInteger('leaveApproval_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();
            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('leave_id', 'lsh_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('otherLeave_id', 'lsh_ol_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('actorUser_id', 'lsh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leaveApproval_id', 'lsh_la_fn')->references('id')->on('LeaveApproval')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('ToilStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('toil_id')->unsigned();
            $table->bigInteger('otherToil_id')->unsigned()->nullable();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();
            $table->bigInteger('toilApproval_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();
            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('toil_id', 'tsh_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('otherToil_id', 'tsh_ol_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('actorUser_id', 'tsh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('toilApproval_id', 'tsh_la_fn')->references('id')->on('ToilApproval')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('RosteredTimeOffStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('rosteredTimeOff_id')->unsigned();
            $table->bigInteger('otherRosteredTimeOff_id')->unsigned()->nullable();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();
            $table->bigInteger('rosteredTimeOffApproval_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();
            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('rosteredTimeOff_id', 'rtosh_rto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('otherRosteredTimeOff_id', 'rtosh_orto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('actorUser_id', 'rtosh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOffApproval_id', 'rtosh_rtoa_fn')->references('id')->on('RosteredTimeOffApproval')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region TimeSheets
        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->bigInteger('leaveDay_id')->unsigned()->nullable();
            $table->foreign('leaveDay_id', 'tsidt_ld_fn')->references('id')->on('LeaveDay')->onUpdate('cascade')->onDelete('cascade');

            $table->bigInteger('toilDay_id')->unsigned()->nullable();
            $table->foreign('toilDay_id', 'tsidt_td_fn')->references('id')->on('ToilDay')->onUpdate('cascade')->onDelete('cascade');

            $table->bigInteger('rosteredTimeOffDay_id')->unsigned()->nullable();
            $table->foreign('rosteredTimeOffDay_id', 'tsidt_rtod_fn')->references('id')->on('RosteredTimeOffDay')->onUpdate('cascade')->onDelete('cascade');
        });
        //endregion
    }

    public function down(): void
    {
        //region TimeSheets
        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_rtod_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_rtod_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'rosteredTimeOffDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffDay_id');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_td_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_td_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'toilDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('toilDay_id');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_ld_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_ld_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'leaveDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('leaveDay_id');
            });
        }
        //endregion

        //region <Leave|Toil|RosteredTimeOff>StatusHistory
        Schema::dropIfExists('RosteredTimeOffStatusHistory');
        Schema::dropIfExists('ToilStatusHistory');
        Schema::dropIfExists('LeaveStatusHistory');
        //endregion

        //region Requests
        if (DB::select(DB::raw('SHOW KEYS FROM RosteredTimeOff WHERE Key_name=\'rto_orto_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropForeign('rto_orto_fn');
            });
        }

        if (Schema::hasColumn('RosteredTimeOff', 'originalRosteredTimeOff_id')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('originalRosteredTimeOff_id');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM Toil WHERE Key_name=\'toil_ot_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropForeign('toil_ot_fn');
            });
        }

        if (Schema::hasColumn('Toil', 'originalToil_id')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('originalToil_id');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM `Leave` WHERE Key_name=\'leave_ol_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropForeign('leave_ol_fn');
            });
        }

        if (Schema::hasColumn('Leave', 'originalLeave_id')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('originalLeave_id');
            });
        }
        //endregion
    }
};
