<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance;

use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDayEntry;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'AdHocAllowanceEntryResource',
    title: 'User role - Ad hoc allowance inside of a sub-item',
    required: ['id', 'allowanceType_id', 'duration', 'startDateTime', 'endDateTime', 'miles', 'quantity', 'value', 'notes', 'allowance_type'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'allowanceType_id', type: 'integer', example: 1),
        new OA\Property(property: 'duration', type: 'float', example: 9.19, nullable: true),
        new OA\Property(property: 'startDateTime', type: 'string', example: '2024-04-28 06:00:00', nullable: true),
        new OA\Property(property: 'endDateTime', type: 'string', example: '2024-04-28 06:00:00', nullable: true),
        new OA\Property(property: 'miles', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'quantity', type: 'float', example: 1.0, nullable: true),
        new OA\Property(property: 'value', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'notes', type: 'string', example: 'this is a note :)', nullable: true),
        new OA\Property(property: 'allowance_type', ref: '#/components/schemas/AdHocAllowanceTypeConfigResource'),
    ],
)]
class AdHocAllowanceEntryResource extends Resource
{
    public static $relations = [
        'timeSheetAllowanceDay.timeSheetAllowance.allowanceType',
    ];

    public function toArray(Request $request): array
    {
        /** @var TimeSheetAllowanceDayEntry $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'allowanceType_id' => $r->timeSheetAllowanceDay->timeSheetAllowance->allowanceType_id,

            'duration' => $r->duration?->getTotalHours(),
            'startDateTime' => $this->fetchDateTime($r->startDateTime),
            'endDateTime' => $this->fetchDateTime($r->endDateTime),
            'miles' => $r->miles,
            'quantity' => $r->quantity,
            'value' => $r->value,

            'notes' => $r->notes,
            'allowance_type' => AdHocAllowanceTypeConfigResource::make($r->timeSheetAllowanceDay->timeSheetAllowance->allowanceType),
        ];
    }
}
