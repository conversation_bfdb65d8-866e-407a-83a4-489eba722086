<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region AllowanceType changes
        Schema::table('AllowanceType', function (Blueprint $table) {
            $table->boolean('requiresTime')->default(false);
            $table->boolean('addsTimeToWorked')->default(false);
        });
        //endregion

        //region TimeSheet changes
        Schema::create('TimeSheetAllowance', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('allowanceType_id')->unsigned();

            $table->longText('quarterNotes')->nullable();
            $table->longText('monthNotes')->nullable();
            $table->longText('fourWeeksNotes')->nullable();
            $table->longText('fortnightNotes')->nullable();
            $table->longText('weekNotes')->nullable();

            $table->decimal('totalHours', 16, 10)->nullable();
            $table->decimal('totalMiles', 16, 10)->nullable();
            $table->decimal('totalValue', 16, 10)->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'tsa_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('allowanceType_id', 'tsa_at_fn')->references('id')->on('AllowanceType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetAllowanceDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetAllowance_id')->unsigned();
            $table->bigInteger('timeSheetDay_id')->unsigned();
            $table->bigInteger('publicHoliday_id')->unsigned()->nullable();

            $table->date('date');
            $table->decimal('hours', 16, 10)->nullable();
            $table->decimal('miles', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->default(0);
            $table->string('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetAllowance_id', 'tsad_tsa_fn')->references('id')->on('TimeSheetAllowance')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('timeSheetDay_id', 'tsad_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('publicHoliday_id', 'tsad_ph_fn')->references('id')->on('PublicHoliday')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetAllowanceDay_id')->unsigned();

            $table->decimal('hours', 16, 10)->nullable();
            $table->decimal('miles', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->default(0);
            $table->string('notes')->nullable();

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetAllowanceDay_id', 'tsade_tsad_fn')->references('id')->on('TimeSheetAllowanceDay')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->bigInteger('timeSheetAllowanceDayEntry_id')->unsigned()->nullable();
            $table->foreign('timeSheetAllowanceDayEntry_id', 'tsidt_tsade_fn')->references('id')->on('TimeSheetAllowanceDayEntry')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('milesAllowance', 16, 10)->nullable();
            $table->decimal('valueAllowance', 16, 10)->nullable();
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('cMilesAllowance', 16, 10)->nullable();
            $table->decimal('cValueAllowance', 16, 10)->nullable();
            $table->decimal('fMilesAllowance', 16, 10)->nullable();
            $table->decimal('fValueAllowance', 16, 10)->nullable();
        });

        //endregion
    }

    public function down(): void
    {
        //region TimeSheet changes
        if (Schema::hasColumn('TimeSheet', 'fValueAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fValueAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fMilesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fMilesAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cValueAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cValueAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cMilesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cMilesAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'valueAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valueAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'milesAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('milesAllowance');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_tsade_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_tsade_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'timeSheetAllowanceDayEntry_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('timeSheetAllowanceDayEntry_id');
            });
        }

        Schema::dropIfExists('TimeSheetAllowanceDayEntry');
        Schema::dropIfExists('TimeSheetAllowanceDay');
        Schema::dropIfExists('TimeSheetAllowance');
        //endregion

        //region AllowanceType changes
        if (Schema::hasColumn('AllowanceType', 'addsTimeToWorked')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('addsTimeToWorked');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'requiresTime')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('requiresTime');
            });
        }
        //endregion
    }
};
