div(ng-if='!$ctrl.view.editing.isOpen')
  .row.visible-md.visible-lg
    .col-xs-12.text-right
      button.btn.btn-default.pull-left(type='button' ng-click='$ctrl.view.attachments.open()' ng-if='!!$ctrl.view.data.canAddAttachment && !$ctrl.view.attachments.isOpen')
        i.fa.fa-paperclip.mr.hidden-md
        | Attach new file

      button.btn.btn-info.ml(type='button' ng-click='$ctrl.view.openWorkflowModal()')
        i.fa.fa-window-maximize.mr.hidden-md
        | View workflow

      button.btn.btn-warning.inline-block.ml(type='button' ng-click='$ctrl.view.editing.open()' ng-if='!!$ctrl.view.data.canReplace && !$ctrl.view.editing.isOpen')
        i.fa.fa-pencil.mr.hidden-md
        | Edit request

      button.btn.btn-danger.inline-block.ml(type='button' ng-click='$ctrl.view.cancel.open()' ng-if='!!$ctrl.view.data.canCancel && !$ctrl.view.cancel.isOpen')
        i.fa.fa-times.mr.hidden-md
        | Cancel request

      button.btn.btn-leave.inline-block.ml(type='button' ng-click='$ctrl.view.report.generate()' ng-if='!!$ctrl.view.data.pdfReportGenerateUri' ng-disabled='!!$ctrl.view.report.isGenerating || !$ctrl.view.report.canGenerate')
        i.fa.fa-download.mr.hidden-md
        | Download request

      button.btn.btn-default.inline-block.ml(type='button' ng-click='$ctrl.close()')
        i.fa.fa-times.mr.hidden-md
        span Close

  .row.visible-xs.visible-sm
    .col-xs-12.col-sm-6(ng-if='!!$ctrl.view.data.canAddAttachment && !$ctrl.view.attachments.isOpen')
      button.btn.btn-default.btn-block.mt(type='button' ng-click='$ctrl.view.attachments.open()')
        i.fa.fa-paperclip.mr
          | Attach new file

    .col-xs-12.col-sm-6
      button.btn.btn-info.btn-block.mt(type='button' ng-click='$ctrl.view.openWorkflowModal()')
        i.fa.fa-window-maximize.mr
        | View workflow

    .col-xs-12.col-sm-6(ng-if='!!$ctrl.view.data.canReplace && !$ctrl.view.editing.isOpen')
      button.btn.btn-warning.btn-block.mt(type='button' ng-click='$ctrl.view.editing.open()')
        i.fa.fa-pencil.mr-sm
        | Edit request

    .col-xs-12.col-sm-6(ng-if='!!$ctrl.view.data.canCancel && !$ctrl.view.cancel.isOpen')
      button.btn.btn-danger.btn-block.mt(type='button' ng-click='$ctrl.view.cancel.open()')
        i.fa.fa-times.mr-sm
        | Cancel request

    .col-xs-12.col-sm-6(ng-if='!!$ctrl.view.data.pdfReportGenerateUri')
      button.btn.btn-leave.btn-block.mt(type='button' ng-click='$ctrl.view.report.generate()' ng-disabled='!!$ctrl.view.report.isGenerating || !$ctrl.view.report.canGenerate')
        i.fa.fa-download.mr-sm
        | Download request

    .col-xs-12.col-sm-6
      button.btn.btn-default.btn-block.mt(type='button' ng-click='$ctrl.close()')
        i.fa.fa-times.mr
        | Close

div(ng-if='!!$ctrl.view.editing.isOpen')
  .row
    .col-xs-12.text-center
      button.btn.btn-default(type='button' ng-click='$ctrl.close()')
        i.fa.fa-times
        span.ml Cancel

      button.btn.btn-info.ml(ng-if='!!$ctrl.form.canSubmit' type='submit' ng-disabled='!!$ctrl.form.isSubmitting' ng-click='$ctrl.form.setSubmitAsSubmitAndSave()')
        i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$ctrl.form.isSubmitting}')
        span(ng-if='!!$ctrl.isSubmitting') '&nbsp; Wait'
        span(ng-if='!$ctrl.isSubmitting && !!$ctrl.fromPayrollOfficer') Submit for approval
        span(ng-if='!$ctrl.isSubmitting && !$ctrl.fromPayrollOfficer') Submit request

      button.btn.btn-success.ml(ng-if='$ctrl.user.data.id !== $ctrl.loggedUser.id && !!$ctrl.form.canSubmit' type='submit' ng-click='$ctrl.form.setSubmitAsApproveAndSave()' ng-disabled='!!$ctrl.form.isSubmitting || !!$ctrl.form.missingApprovalRequirement')
        i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$ctrl.form.isSubmitting}')
        span(ng-if='!!$ctrl.isSubmitting') '&nbsp; Wait'
        span(ng-if='!$ctrl.isSubmitting') Approve
        span(ng-if='!!$ctrl.form.missingApprovalRequirement')
          | ({{$ctrl.form.missingApprovalRequirement}})
