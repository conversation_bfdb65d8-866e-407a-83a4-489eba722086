<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromUser;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRun\CreateScheduleForUserOnPayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Enums\PayRunStatus\PayRunStatus;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\TimeType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeGroup;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\FullTimeType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRoleRepository;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

describe('Duration only - set relationships from User', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUser::make()->handle($leaveSettings->user);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Work pattern - set relationships from User', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUser::make()->handle($leaveSettings->user);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Zero based - set relationships from User', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromUser::make()->handle($leaveSettings->user);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('General - set relationships from User', function () {
    it('does set to multiple leave requests from different roles with different schedule types across two payrun periods', function () {
        $openPayruns = PayRun::q()
            ->constraints([
                ['IN', 'PayRun.status', [PayRunStatus::NotStarted, PayRunStatus::Current]]
            ])->many();

        if ($openPayruns->count() < 2) {
            $this->markTestIncomplete('This test does require at least 2 payrun to be opened');
        }

        $workPatternLeaveSettingsOnFirstPayrun = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[0]);
        $userRole = $workPatternLeaveSettingsOnFirstPayrun->userRoleSchedule->userRole;
        $user = $workPatternLeaveSettingsOnFirstPayrun->user;
        $role = Role::q([
            ['Role.status', '=', ActiveStatus::ID],
            ['Role.id', '<>', $userRole->role_id]
        ])->many()->random();

        $newUserRole = UserRoleRepository::assignRoleToUser(
            $role,
            $user,
            $openPayruns[0]->startDate,
            false,
            (object) [
                'doesOverrideExternalId' => false,
                'externalId' => 'test_lorem_ipsum',
                'contractedHours' => 80,
                'employeeTypeStatusType' => FullTimeType::ID,
                'costing' => [
                    'doesAllowNonStandardPlant' => false,
                    'doesAllowUserToUpdateWorkDetails' => false,
                ],
                'departmentData' => [[
                    'department_id' => Department::q()->onlyActive()->many()->random()->id,
                    'isManager' => false,
                ]],
                'masterProjectData' => [[
                    'project_id' => Project::q()->onlyActive()->many()->random()->id,
                    'isMaster' => false,
                ]],
                'payTypesData' => [[
                    'payType_id' => PayType::q()->onlyActive()->many()->random()->id,
                    'overtimeCustomAmount' => null,
                    'overtimeCustomRateType' => 'Y',
                ]],
                'roleManagerData' => [[
                    'manager_id' => User::q()->onlyActive()->many()->random()->id,
                ]],
                'timeTypesData' => [
                    [
                        'timeType_id' => TimeType::q()->onlyActive()->many()->random()->id,
                        'excessTimeGroup_id' => ExcessTimeGroup::q()->onlyActive()->many()->random()->id,
                        'isActive' => true,
                        'isMaster' => true,
                    ]
                ],
            ],
        );

        $workPatternUserRoleSchedule = $workPatternLeaveSettingsOnFirstPayrun->userRoleSchedule;
        $zeroBasedUserRoleSchedule = UserRoleSchedule::addToUserRoleByData(
            $newUserRole,
            $openPayruns[0]->startDate->copy(),
            [
                'type' => UserRoleScheduleType::ZeroBased->id(),
                'zeroBased' => [
                    'dailyMaxDuration' => null,
                    'doesHaveDailyMaxDuration' => false,
                    'doesHaveFixedLocation' => false,
                    'doesHaveMaxDuration' => false,
                    'maxDuration' => null,
                    'scheduleLocation_id' => null,
                ],
            ],
            true,
        );

        $firstPayrunScheduledPayrunConstraint = ['ScheduledPayRun.startDate', '=', $openPayruns[0]->startDate];
        $secondPayrunScheduledPayrunConstraint = ['ScheduledPayRun.startDate', '=', $openPayruns[1]->startDate];
        $workPatternUserScheduledPayRunsConstraints = ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternUserRoleSchedule->id];
        $zeroBasedUserScheduledPayRunsConstraints = ['ScheduledPayRun.userRoleSchedule_id', '=', $zeroBasedUserRoleSchedule->id];

        $expectedScheduledPayRunForFirstWorkPatternLeave = ScheduledPayRun::q()->constraints([$workPatternUserScheduledPayRunsConstraints, $firstPayrunScheduledPayrunConstraint])->first();
        if (is_null($expectedScheduledPayRunForFirstWorkPatternLeave)) {
            $expectedScheduledPayRunForFirstWorkPatternLeave = CreateScheduleForUserOnPayRun::make()->handle($workPatternUserRoleSchedule, $openPayruns[0]);
        }

        $expectedScheduledPayRunForSecondWorkPatternLeave = ScheduledPayRun::q()->constraints([$workPatternUserScheduledPayRunsConstraints, $secondPayrunScheduledPayrunConstraint])->first();
        if (is_null($expectedScheduledPayRunForSecondWorkPatternLeave)) {
            $expectedScheduledPayRunForSecondWorkPatternLeave = CreateScheduleForUserOnPayRun::make()->handle($workPatternUserRoleSchedule, $openPayruns[1]);
        }

        $expectedScheduledPayRunForFirstZeroBasedLeave = ScheduledPayRun::q()->constraints([$zeroBasedUserScheduledPayRunsConstraints, $firstPayrunScheduledPayrunConstraint])->first();
        if (is_null($expectedScheduledPayRunForFirstZeroBasedLeave)) {
            $expectedScheduledPayRunForFirstZeroBasedLeave = CreateScheduleForUserOnPayRun::make()->handle($zeroBasedUserRoleSchedule, $openPayruns[0]);
        }

        $expectedScheduledPayRunForSecondZeroBasedLeave = ScheduledPayRun::q()->constraints([$zeroBasedUserScheduledPayRunsConstraints, $secondPayrunScheduledPayrunConstraint])->first();
        if (is_null($expectedScheduledPayRunForSecondZeroBasedLeave)) {
            $expectedScheduledPayRunForSecondZeroBasedLeave = CreateScheduleForUserOnPayRun::make()->handle($zeroBasedUserRoleSchedule, $openPayruns[1]);
        }

        $zeroBasedLeaveSettingOnFirstPayrun = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[0], userRoleSchedule: $zeroBasedUserRoleSchedule);
        $workPatternLeaveSettingsOnSecondPayrun = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[1], userRoleSchedule: $workPatternUserRoleSchedule);
        $zeroBasedLeaveSettingOnSecondPayrun = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $openPayruns[1], userRoleSchedule: $zeroBasedUserRoleSchedule);

        [$workPatternLeaveOnFirstPayrun] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettingsOnFirstPayrun);
        [$workPatternLeaveOnSecondPayrun] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettingsOnSecondPayrun);
        [$zeroBasedLeaveOnFirstPayrun] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettingOnFirstPayrun);
        [$zeroBasedLeaveOnSecondPayrun] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettingOnSecondPayrun);

        $timeSheetOnFirstPayrunPeriod = TimeSheet::getByDate($user, $openPayruns[0]->startDate);
        $timeSheetOnSecondPayrunPeriod = TimeSheet::getByDate($user, $openPayruns[1]->startDate);

        expect($workPatternLeaveOnFirstPayrun->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveOnFirstPayrun->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveOnFirstPayrun->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveOnFirstPayrun->timeSheet_id)->toBeNull()
            ->and($workPatternLeaveOnSecondPayrun->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveOnSecondPayrun->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveOnSecondPayrun->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveOnSecondPayrun->timeSheet_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromUser::make()->handle($user);

        $workPatternLeaveOnFirstPayrun->refresh();
        $zeroBasedLeaveOnFirstPayrun->refresh();
        $workPatternLeaveOnSecondPayrun->refresh();
        $zeroBasedLeaveOnSecondPayrun->refresh();

        expect($workPatternLeaveOnFirstPayrun->scheduledPayRun_id)->toBeInt()
            ->and($workPatternLeaveOnFirstPayrun->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForFirstWorkPatternLeave->id)
            ->and($workPatternLeaveOnFirstPayrun->timeSheet_id)->toEqual($timeSheetOnFirstPayrunPeriod->id)
            ->and($zeroBasedLeaveOnFirstPayrun->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForFirstZeroBasedLeave->id)
            ->and($zeroBasedLeaveOnFirstPayrun->timeSheet_id)->toEqual($timeSheetOnFirstPayrunPeriod->id)
            ->and($workPatternLeaveOnSecondPayrun->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForSecondWorkPatternLeave->id)
            ->and($workPatternLeaveOnSecondPayrun->timeSheet_id)->toEqual($timeSheetOnSecondPayrunPeriod->id)
            ->and($zeroBasedLeaveOnSecondPayrun->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForSecondZeroBasedLeave->id)
            ->and($zeroBasedLeaveOnSecondPayrun->timeSheet_id)->toEqual($timeSheetOnSecondPayrunPeriod->id)
        ;
    });

    it('does not affect leave requests placed by different user', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        $staff1 = $workPatternLeaveSettings->user;
        $staff2 = $workPatternLeaveSettings->user;
        $userRoleSchedulesOnPayRun = UserRoleSchedule::q()->current($payRun->startDate, $payRun->endDate)->many();
        $remainingAttempts = 10;
        $workPatternLeaveSettingsForOtherUser = null;

        while ($remainingAttempts > 0 && $staff1->id === $staff2->id) {
            $remainingAttempts--;
            $randomUserRoleSchedule = $userRoleSchedulesOnPayRun->random();
            try {
                $workPatternLeaveSettingsForOtherUser = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, userRoleSchedule: $randomUserRoleSchedule);
                $staff2 = $workPatternLeaveSettingsForOtherUser->user;
            } catch (\Throwable $e) {}
        }

        if ($staff1->id === $staff2->id) {
            $this->markTestSkipped('Not found compatible schedules from different users');
        }

        $expectedScheduledPayRunWorkPattern = ScheduledPayRun::q()->constraints([
            ['ScheduledPayRun.startDate', '=', $payRun->startDate],
            ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
        ])->first();
        $expectedTimeSheet = TimeSheet::getByDate($staff1, $workPatternLeaveSettings->startDate);

        [$workPatternLeaveRequestFromStaff1] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);
        [$workPatternLeaveRequestFromStaff2] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettingsForOtherUser);

        expect($workPatternLeaveRequestFromStaff1->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequestFromStaff1->timeSheet_id)->toBeNull()
            ->and($workPatternLeaveRequestFromStaff2->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequestFromStaff2->timeSheet_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromUser::make()->handle($staff1);

        $workPatternLeaveRequestFromStaff1->refresh();
        $workPatternLeaveRequestFromStaff2->refresh();

        expect($workPatternLeaveRequestFromStaff1->scheduledPayRun_id)->toBeInt()
            ->and($workPatternLeaveRequestFromStaff1->scheduledPayRun_id)->toEqual($expectedScheduledPayRunWorkPattern->id)
            ->and($workPatternLeaveRequestFromStaff1->timeSheet_id)->toBeInt()
            ->and($workPatternLeaveRequestFromStaff1->timeSheet_id)->toEqual($expectedTimeSheet->id)
            ->and($workPatternLeaveRequestFromStaff2->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequestFromStaff2->timeSheet_id)->toBeNull()
        ;
    });
});
