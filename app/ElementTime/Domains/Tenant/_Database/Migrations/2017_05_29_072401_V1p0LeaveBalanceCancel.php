<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
            $table->boolean('isDeleted')->default(false);
        });

        Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
            $table->boolean('isDeleted')->default(false);
        });

        Schema::table('UserToilTypeBalanceIn', function (Blueprint $table) {
            $table->boolean('isDeleted')->default(false);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserToilTypeBalanceIn', 'isDeleted')) {
            Schema::table('UserToilTypeBalanceIn', function (Blueprint $table) {
                $table->dropColumn('isDeleted');
            });
        }

        if (Schema::hasColumn('UserRosteredTimeOffTypeEntry', 'isDeleted')) {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropColumn('isDeleted');
            });
        }

        if (Schema::hasColumn('UserLeaveTypeEntry', 'isDeleted')) {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropColumn('isDeleted');
            });
        }
    }
};
