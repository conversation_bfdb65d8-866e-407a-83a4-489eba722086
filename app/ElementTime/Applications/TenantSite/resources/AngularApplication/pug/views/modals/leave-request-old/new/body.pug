.panel-body
  form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$ctrl.form.submit(false)')
    .row
      .col-xs-12
        .form-group(elt-help-text="modals.leave-request|fields.leaveType")
          label.label-control Select leave type
          ui-select(ng-model='$ctrl.form.data.type_id', theme='bootstrap', reset-search-input='true' ng-change='$ctrl.form.changeType()' ng-required='true')
            ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.name}}
            ui-select-choices(repeat='object.id as object in $ctrl.form.typesList | filter : $select.search | orderBy: "leaveName"')
              div(ng-bind-html='object.leaveName | highlight: $select.search')

    div(ng-if='!!$ctrl.form.isTypeSelected')
      .row
        .col-xs-12.mb-lg
          elt-custom-content(ng-model='$ctrl.form.customContent')

      .row
        .col-xs-12
          hr.mt.mb
      .row
        .col-xs-12
          .form-group(elt-help-text="modals.leave-request|fields.shift")
            div(ng-if='$ctrl.form.shiftsList.length === 1')
              label.label-control.mb0 Role/shift
              .lead.mb0.bb.pb-xs {{$ctrl.form.shiftsList[0].role.name + " (" + $ctrl.form.shiftsList[0].name + ")"}}
            div(ng-if='$ctrl.form.shiftsList.length !== 1')
              label.label-control Select role/shift
              ui-select(ng-model='$ctrl.form.data.shift_id', theme='bootstrap', reset-search-input='true' ng-change='$ctrl.form.changeShift()' ng-required='true' ng-disabled='$ctrl.form.isSelectingType')
                ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.role.name + ' (' + $select.selected.name + ')'}}
                ui-select-choices(group-by="'group'" repeat='object.id as object in $ctrl.form.shiftsList | filter : $select.search')
                  div(ng-bind-html='object.role.name + " (" + object.name + ")" | highlight: $select.search')

    div(ng-if='!!$ctrl.form.selectedShift')
      .row
        .col-xs-12
          hr.mt.mb
      .row.form-horizontal
        .col-xs-12.col-md-6
          elt-toggle(ng-model='$ctrl.form.data.isFullDay' elt-name='isFullDay' elt-label='Apply for full day/s leave' elt-help-text='modals.leave-request|fields.isFullDay' elt-line='false' elt-change-tt='' ng-change='$ctrl.form.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!$ctrl.form.data.isFullDay && $ctrl.form.selectedShift.type.id != "G"')
          elt-toggle(ng-model='$ctrl.form.data.isApplyingByDuration' elt-name='isApplyingByDuration' elt-label='Apply by duration' elt-help-text='modals.leave-request|fields.isApplyingByDuration' elt-line='false' elt-change-tt='' ng-change='$ctrl.form.changeRequestData()')

      .row
        .col-xs-12.col-md-6
          .form-group(elt-help-text='modals.leave-request|fields.startDate')
            label.label-control(for='Leave_startDate') Start date
            p.input-group.mb-sm
              input.form-control(id="Leave_startDate" name='inputStartDate' type='text' date-picker ng-model='$ctrl.form.data.startDate' uib-datepicker-popup='{{settings.system.settings.dateFormat}}' is-open='date_pickers["Leave_openStartDate"].opened' close-text='Close' ng-change='$ctrl.form.changeStartDate(); $ctrl.form.getRecordedFromStartDate();' datepicker-options="{minDate: $ctrl.minDate, maxDate: $ctrl.maxDate}" ng-required='true')
              span.input-group-btn
                button.btn.btn-default(type='button' ng-click='openDatePicker($event, "Leave_openStartDate")')
                  em.fa.fa-calendar
        .col-xs-12.col-md-6(ng-if='$ctrl.form.selectedShift.type.id != "G" && !$ctrl.form.data.isFullDay')
          .form-group(elt-help-text='modals.leave-request|fields.startTime')
            label.label-control(for='Leave_startTime') Start time
            div.input-group(id='Leave_startTime' timepicker-popup ng-model="$ctrl.form.data.startTime" is-meridian='settings.system.settings.meridian' is-date="true" ng-required='true' ng-change='$ctrl.form.changeRequestData()')

        .clearfix(ng-if='$ctrl.form.selectedShift.type.id != "G" && !$ctrl.form.data.isFullDay')

        .col-xs-12.col-md-6(ng-if='!!$ctrl.form.data.isFullDay || !$ctrl.form.data.isApplyingByDuration')
          .form-group(elt-help-text='modals.leave-request|fields.endDate')
            label.label-control(for='Leave_endDate') End date
            p.input-group.mb-sm
              input.form-control(id="Leave_endDate" name='inputEndDate' type='text' date-picker ng-model='$ctrl.form.data.endDate' uib-datepicker-popup='{{settings.system.settings.dateFormat}}' is-open='date_pickers["Leave_openEndDate"].opened' close-text='Close' datepicker-options="{minDate: !!$ctrl.form.data.startDate ? $ctrl.form.data.startDate : $ctrl.minDate, maxDate: $ctrl.maxDate}" ng-required='true' ng-change='$ctrl.form.changeRequestData()')
              span.input-group-btn
                button.btn.btn-default(type='button' ng-click='openDatePicker($event, "Leave_openEndDate")')
                  em.fa.fa-calendar
        .col-xs-12.col-md-6(ng-if='$ctrl.form.selectedShift.type.id != "G" && !$ctrl.form.data.isFullDay && !$ctrl.form.data.isApplyingByDuration')
          .form-group(elt-help-text='modals.leave-request|fields.endTime')
            label.label-control(for='Leave_endTime') End time
            div.input-group(id='Leave_endTime' timepicker-popup ng-model="$ctrl.form.data.endTime" is-meridian='settings.system.settings.meridian' is-date="true" ng-required='true' ng-change='$ctrl.form.changeRequestData()')

        .col-xs-12.col-md-6(ng-if='$ctrl.form.selectedShift.type.id != "G" && !$ctrl.form.data.isFullDay && !!$ctrl.form.data.isApplyingByDuration')
          .form-group(elt-help-text='modals.leave-request|fields.duration')
            div
              label.label-md(for='Leave_duration') Duration (hours)
              .pull-right
                label.switch
                  small.mr-sm.text-normal Show in hour format
                  input(id='toggleHourFormat' type='checkbox' ng-model="$ctrl.toggleHourFormat")
                  span
            div(ng-if='!$ctrl.toggleHourFormat')
              input.form-control(id='Leave_duration' type='text' name='hours' ng-model='$ctrl.form.data.duration' input-mask input-options='{"name": "number", "allowMinus": "false"}' ng-required='true' ng-change='$ctrl.updateFormattedHours($ctrl.form)')
            div(ng-if='!!$ctrl.toggleHourFormat')
              div.input-group(id='Leave_durationFormatted' timepicker-popup ng-model='$ctrl.form.data.durationFormatted' is-meridian='false' is-date="false" date="{{$ctrl.form.data.startDate}}" ng-change='$ctrl.updateUnFormattedHours($ctrl.form)')

      .row(ng-if='$ctrl.form.selectedShift.type.id === "G" && !$ctrl.form.data.isFullDay')
        .col-xs-12.col-md-6(ng-if='!!$ctrl.form.selectedShift.glideRequireTimeBlock')
          .form-group(elt-help-text='modals.leave-request|fields.glideDailyStartTime')
            label.label-control(for='Leave_glideDailyStartTime') Daily start time
            div.input-group(id='Leave_glideDailyStartTime' timepicker-popup ng-model="$ctrl.form.data.glideDailyStartTime" is-meridian='settings.system.settings.meridian' ng-required='true' ng-change='$ctrl.form.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!!$ctrl.form.selectedShift.glideRequireTimeBlock == true')
          .form-group(elt-help-text='modals.leave-request|fields.glideDailyEndTime')
            label.label-control(for='Leave_glideDailyEndTime') Daily end time
            div.input-group(id='Leave_glideDailyEndTime' timepicker-popup ng-model="$ctrl.form.data.glideDailyEndTime" is-meridian='settings.system.settings.meridian' ng-required='true' ng-change='$ctrl.form.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!$ctrl.form.selectedShift.glideRequireTimeBlock')
          .form-group(elt-help-text='modals.leave-request|fields.glideDailyHours')
            label.label-control(for="Leave_glideDailyHours") Enter daily hours
            input.form-control(id="Leave_glideDailyHours" type='text' name='glideDailyHours' ng-model='$ctrl.form.data.glideDailyHours' input-mask input-options='{"name": "number", "allowMinus": "false", "max": "24"}' ng-required='true' ng-change='$ctrl.form.changeRequestData()')

      div(ng-if='!$ctrl.form.calculated')
        .row
          .col-xs-12.text-center
            button.btn.btn-info(type='button' ng-click='$ctrl.form.calculateRequest()' ng-disabled='$ctrl.form.isCalculating || !$ctrl.form.canCalculate()' elt-help-text='modals.leave-request|actions.calculate')
              i.fa(ng-class='{"fa-circle-o-notch fa-spin": $ctrl.form.isCalculating}')
              | {{$ctrl.$controller.calculate_loading ? '&nbsp; Calculating' : 'Check / Calculate' }}

      div(ng-if='!!$ctrl.form.calculatedError')
        .row
          .col-xs-12.col-md-6.col-md-offset-3.mt.mb
            .p.rounded.bg-danger
              .row
                .col-xs-12.text-center
                  h4.h4 {{$ctrl.form.calculatedError}}
              div(ng-if='!!$ctrl.form.calculatedClashes')
                .row
                  .col-xs-12
                    hr.hr.mt.mb
                .row
                  .col-xs-12.text-center
                    a.btn.btn-default.btn-xs.m0.pt0.pb0.pl.pr.text-nowrap(type='button' href='javascript:' ng-click='$ctrl.showModalTimeFrame($ctrl.form)') See more

      div(ng-if='!!$ctrl.form.calculatedWarning')
        .row
          .col-xs-12.mt.mb
            .list-group-item.list-group-item-danger
              .row
                .col-xs-12.text-center
                  h4.h4.m0 {{$ctrl.form.calculatedWarning.title}}
                  div {{$ctrl.form.calculatedWarning.message}}
                  button.mt.button.btn.btn-danger.center-block(ng-click='$ctrl.form.calculateRequest(true)' ng-disabled='$ctrl.form.isCalculating || !$ctrl.form.canCalculate()' type='button')
                    i.fa(ng-class='{"fa-circle-o-notch fa-spin": $ctrl.form.isCalculating}')
                    | {{$ctrl.$controller.isCalculating ? '&nbsp; Calculating' : 'Continue' }}

      div(ng-if='!!$ctrl.form.calculated')
        div(ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/form/availability-box.html'" ng-init='$availabilityBoxForm = $ctrl.form;')

        .row
          .col-xs-12
            .form-group
              label.label-control(for='Leave_inputReason') Reason / comments{{!!$ctrl.form.calculated.isCommentRequired ? ' (Required)' : ''}}
              textarea.form-control(id='Leave_inputReason' name='inputReason' ng-model='$ctrl.form.data.reason' ng-required='$ctrl.form.calculated.isCommentRequired' ng-class='{"ng-dirty": !!$ctrl.form.calculated.isCommentRequired}')

        .row
          .col-xs-12.col-md-6
            elt-attachment-file(id='Leave_attachmentFile' elt-label='{{"Attachment file" + ($ctrl.form.calculated.isAttachmentRequired ? " (Required)" : "")}}' name='attachmentFile' ng-model='$ctrl.form.data.attachmentFile' elt-file-options='{{settings.form.LeaveRequest.attachmentFileOptions}}' ng-required='$ctrl.form.calculated.isAttachmentRequired')
        .row(ng-if='!!$ctrl.form.calculated.isAttachmentRequired')
          .col-xs-12
            .list-group.mb0
              .list-group-item.list-group-item-warning
                i.fa.fa-warning
                span.ml-sm You must upload an attachment to this leave request prior to submission. Refer to your leave policy to ensure your attachment meets your organisations requirements

    .row
      .col-xs-12
        hr.mt.mb0
    .row
      .col-xs-12.mt-lg.text-center
        button.btn.btn-default.mr(type='button' ng-click='$ctrl.close()') {{'form.CANCEL' | translate}}
        button.btn.btn-elementtime(ng-if='!!$ctrl.form.canSubmit()' type='submit' ng-disabled='!!$ctrl.form.isSubmitting || !$ctrl.form.canBeRequested')
          i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$ctrl.isSubmitting}')
          | {{!!$ctrl.isSubmitting ? '&nbsp; Wait' : (!!$ctrl.fromPayrollOfficer ? 'Submit for approval' : 'Submit request') }}
        button.btn.btn-success.ml(ng-if="!!$ctrl.fromPayrollOfficer && !!$ctrl.form.canSubmit()" type="button" ng-click="$ctrl.form.submit(true)" ng-disabled='!!$ctrl.form.isSubmitting || !$ctrl.form.canBeRequested || (!!$ctrl.form.doesRequireCommentsOnApprove && !$ctrl.form.data.reason) || (!!$ctrl.form.doesRequireAttachmentsOnApproval && !$ctrl.form.data.attachmentFile)')
          i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$ctrl.isSubmitting}')
          | {{!!$ctrl.isSubmitting ? '&nbsp; Wait' : 'Approve' }}
          span(ng-if='(!!$ctrl.form.doesRequireCommentsOnApprove && !$ctrl.form.data.reason) && (!!$ctrl.form.doesRequireAttachmentsOnApproval && !$ctrl.form.data.attachmentFile)')
            |
            | (Provide comment and attachment)
          span(ng-if='(!!$ctrl.form.doesRequireCommentsOnApprove && !$ctrl.form.data.reason) && (!$ctrl.form.doesRequireAttachmentsOnApproval || !!$ctrl.form.data.attachmentFile)')
            |
            | (Provide comment)
          span(ng-if='(!$ctrl.form.doesRequireCommentsOnApprove || !!$ctrl.form.data.reason) && (!!$ctrl.form.doesRequireAttachmentsOnApproval && !$ctrl.form.data.attachmentFile)')
            |
            | (Provide attachment)
