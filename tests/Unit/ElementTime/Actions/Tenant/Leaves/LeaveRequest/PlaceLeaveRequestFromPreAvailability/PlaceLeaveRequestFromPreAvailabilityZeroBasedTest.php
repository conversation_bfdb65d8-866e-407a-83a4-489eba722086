<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Element\Core\Exceptions\AttachmentRequiredException;
use Element\Core\Exceptions\CommentsRequiredException;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleZeroBased;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\Notifications\SendRequestedNotificationBasedOnWorkflow;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityZeroBased;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelTaskType\ModelTaskType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\MediaFileFunctionality;
use Tests\Traits\Functionalities\Tenant\Workflows\ModelActionGeneralFunctionality;

uses(LeaveGeneralFunctionality::class, MediaFileFunctionality::class, ModelActionGeneralFunctionality::class);

describe('Zero based - Place leave request', function () {
    it('can place leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        SendRequestedNotificationBasedOnWorkflow::spy()->allows('handle')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->userRoleSchedule_id)->toEqual($availability->userRoleSchedule_id)
            ->and($leaveRequest->userLeaveBankType_id)->toEqual($availability->userLeaveBankType_id)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->actor_id)->toEqual($actorUser->id)
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->payRun)->not()->toBeNull()
            ->and($leaveRequest->workflow->timeSheet)->not()->toBeNull()
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
            ->and($leaveRequest->attachmentFiles)->toHaveCount(0)
            ->and($leaveRequest->days)->toHaveCount(0);
    });

    it('can place leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        SendRequestedNotificationBasedOnWorkflow::spy()->allows('handle')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->userRoleSchedule_id)->toEqual($availability->userRoleSchedule_id)
            ->and($leaveRequest->userLeaveBankType_id)->toEqual($availability->userLeaveBankType_id)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->actor_id)->toEqual($actorUser->id)
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->payRun)->toBeNull()
            ->and($leaveRequest->workflow->timeSheet)->toBeNull()
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
            ->and($leaveRequest->attachmentFiles)->toHaveCount(0)
            ->and($leaveRequest->days)->toHaveCount(0);
    });

    it('can be submitted and approved', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $beforeLeaveCommitedBalance = $userLeaveBankType->bank->refresh()->totalCommittedBalance;

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
            doesApprove: true,
        );

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->userRoleSchedule_id)->toEqual($availability->userRoleSchedule_id)
            ->and($leaveRequest->userLeaveBankType_id)->toEqual($availability->userLeaveBankType_id)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->actor_id)->toEqual($actorUser->id)
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->payRun)->not()->toBeNull()
            ->and($leaveRequest->workflow->timeSheet)->not()->toBeNull()
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\ApprovedType::c())
            ->and($leaveRequest->attachmentFiles)->toHaveCount(0)
            ->and($leaveRequest->days)->toHaveCount(0)
            ->and($userLeaveBankType->bank->refresh()->totalCommittedBalance)->toEqual($beforeLeaveCommitedBalance + $leaveRequest->durationAdjusted->getTotalHours())
        ;
    });

    it('can be submitted with attachment', function (){
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
        );

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->userRoleSchedule_id)->toEqual($availability->userRoleSchedule_id)
            ->and($leaveRequest->userLeaveBankType_id)->toEqual($availability->userLeaveBankType_id)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->actor_id)->toEqual($actorUser->id)
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->payRun)->not()->toBeNull()
            ->and($leaveRequest->workflow->timeSheet)->not()->toBeNull()
            ->and($leaveRequest->attachmentFiles)->toHaveCount(1);
    });

    it('can be submitted with reason', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'lorem';

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            actorUser: $actorUser,
        );

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->userRoleSchedule_id)->toEqual($availability->userRoleSchedule_id)
            ->and($leaveRequest->userLeaveBankType_id)->toEqual($availability->userLeaveBankType_id)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->reason)->toEqual($reason)
            ->and($leaveRequest->actor_id)->toEqual($actorUser->id)
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->attachmentFiles)->toHaveCount(0);
    });

    it('can replace leave', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'This is reason replacing previous leave';

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $originalReplacedLeave = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            actorUser: $actorUser,
        );

        expect($originalReplacedLeave)->toBeInstanceOf(LeaveRequest::class)
            ->and($originalReplacedLeave->id)->toBeInt()
            ->and($originalReplacedLeave->attachmentFiles)->toHaveCount(0);

        $newDuration = TimeDuration::parseFromHours(30);
        $newAvailability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $newDuration,
            actorUser: $actorUser,
        );
        $replacingLeaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $newAvailability,
            reason: $reason,
            replaced: $originalReplacedLeave,
            actorUser: $actorUser,
        );

        expect($originalReplacedLeave->workflow->otherRel_id)->not()->toBeNull()
            ->and($replacingLeaveRequest->originalReplaced_id)->toEqual($originalReplacedLeave->id)
            ->and($originalReplacedLeave->duration)->toEqual($duration)
            ->and($replacingLeaveRequest->duration)->toEqual($newAvailability->duration)
            ->and($replacingLeaveRequest->duration)->not()->toEqual($originalReplacedLeave->duration);
    });

    it('cannot place request without attachment if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should fail';

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            actorUser: $actorUser,
        );
    })->throws(AttachmentRequiredException::class);

    it('cannot place request without reason if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
        );
    })->throws(CommentsRequiredException::class);

    it('can place request with attachment and comments when required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should not fail';

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $leave = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
        );

        expect($leave->id)->toBeInt()
            ->and($leave->attachmentFiles)->toHaveCount(1)
            ->and($leave->reason)->toEqual($reason);
    });

    it('cannot approve request if attachment is required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should fail';

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireAttachmentPriorToApprove->title());

    it('cannot approve request without reason if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireCommentPriorToApprove->title());

    it('can approve request with attachment and comments when required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);
        $reason = 'it should fail';

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        $duration = TimeDuration::parseFromHours(10);
        $actorUser = SuperUserFlag::getOne();

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: $actorUser,
        );

        $attachments = $this->g_mf_createRandomMediaFiles($actorUser);

        $leave = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            attachments: $attachments,
            actorUser: $actorUser,
            doesApprove: true,
        );

        expect($leave->id)->toBeInt()
            ->and($leave->attachmentFiles)->toHaveCount($attachments->count())
            ->and($leave->reason)->toEqual($reason);
    });
});
