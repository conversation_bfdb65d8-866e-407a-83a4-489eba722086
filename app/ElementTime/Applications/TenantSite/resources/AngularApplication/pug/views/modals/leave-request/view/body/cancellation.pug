.row(ng-if='!!$ctrl.view.cancellation.isOpen')
  .col-xs-12.mb-lg
    form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$ctrl.view.cancellation.submit()' name='$ctrl.view.cancellation.form')
      .panel.panel-danger-lighter.m0
        .panel-heading.text-danger
          span.text-danger
            i.fa.fa-warning.mr.inline-block.inline-middle
            h4.h4.m0.inline-block.inline-middle Pending cancellation
        .panel-body
          .row
            .col-xs-12
              p There is a pending cancellation request for this leave, please approve or decline it.
              P(ng-if='!!$ctrl.view.data.cancellationRequest.notes')
                strong Comments made by {{$ctrl.view.data.cancellationRequest.actor.fullName}}:
                |
                | {{$ctrl.view.data.cancellationRequest.notes}}

          div(ng-if='!!$ctrl.view.data.cancellationRequest.canApprove || !!$ctrl.view.data.cancellationRequest.canDecline || !!$ctrl.view.data.cancellationRequest.canCancel')
            .row
              .col-xs-12
                .form-group
                  label.label-control(for='LeaveCancel_inputNotes') Your comments / notes
                  textarea.form-control(id='LeaveCancel_inputNotes' ng-model='$ctrl.view.cancellation.data.notes' required='required')

            .row
              .col-xs-12
                button.btn.btn-danger.mr(ng-if='!!$ctrl.view.data.cancellationRequest.canApprove' type='submit' ng-click='$ctrl.view.cancellation.approve()' ng-disabled='!!$ctrl.view.cancellation.isSubmitting') Approve cancellation
                button.btn.btn-gray.mr(ng-if='!!$ctrl.view.data.cancellationRequest.canDecline' type='submit' ng-click='$ctrl.view.cancellation.decline()' ng-disabled='!!$ctrl.view.cancellation.isSubmitting') Decline cancellation
                button.btn.btn-gray.mr(ng-if='!!$ctrl.view.data.cancellationRequest.canCancel' type='submit' ng-click='$ctrl.view.cancellation.cancel()' ng-disabled='!!$ctrl.view.cancellation.isSubmitting') Take it back
                button.btn.btn-info.pull-right(type='button' ng-click='$ctrl.view.cancellation.openWorkflowModal()' ng-disabled='!!$ctrl.view.cancellation.isSubmitting') View cancellation workflow
