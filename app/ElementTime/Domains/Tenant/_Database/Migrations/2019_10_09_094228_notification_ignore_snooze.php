<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('NotificationType', function (Blueprint $table) {
            $table->boolean('ignoresSnooze')->default(false)->after('order');
        });

        Schema::table('Notification', function (Blueprint $table) {
            $table->unsignedBigInteger('notificationTypeTrigger_id')->nullable()->after('notificationType_id');
            $table->foreign('notificationTypeTrigger_id', 'notif_ntt_fn')->references('id')->on('NotificationTypeTrigger')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM Notification WHERE Key_name=\'notif_ntt_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropForeign('notif_ntt_fn');
            });
        }

        if (Schema::hasColumn('Notification', 'notificationTypeTrigger_id')) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropColumn('notificationTypeTrigger_id');
            });
        }

        if (Schema::hasColumn('NotificationType', 'ignoresSnooze')) {
            Schema::table('NotificationType', function (Blueprint $table) {
                $table->dropColumn('ignoresSnooze');
            });
        }
    }
};
