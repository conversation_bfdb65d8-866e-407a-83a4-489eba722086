<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\_System\KioskDeviceRequest;

use Element\Core\Exceptions\InvalidStatusException;
use Element\ElementTime\Actions\Tenant\_System\BaseSystemAction;
use Element\ElementTime\Actions\Tenant\_System\KioskDevice\CreateKioskDevice;
use Element\ElementTime\Domains\Tenant\_System\BroadcastEvents\KioskDeviceRequestChangedStatusBroadcastEvent;
use Element\ElementTime\Domains\Tenant\_System\Enums\KioskDeviceRequestStatus\KioskDeviceRequestStatus;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;

class ResolveKioskDeviceRequest extends BaseSystemAction
{
    /** @throws \Throwable */
    public function handle(KioskDeviceRequest $kioskDeviceRequest, KioskDeviceRequestStatus $status): KioskDeviceRequest
    {
        if (!$kioskDeviceRequest->status->canChangeTo($status)) {
            throw new InvalidStatusException([
                'action' => 'change kiosk device request status',
                'reason' => 'Please verify the workflow',
            ]);
        }

        $kioskDeviceRequest->status = $status;
        $kioskDeviceRequest->saveOrFail();

        if ($status == KioskDeviceRequestStatus::Approved) {
            CreateKioskDevice::make()->handle($kioskDeviceRequest);
        }

        KioskDeviceRequestChangedStatusBroadcastEvent::fire($kioskDeviceRequest);

        return $kioskDeviceRequest;
    }
}
