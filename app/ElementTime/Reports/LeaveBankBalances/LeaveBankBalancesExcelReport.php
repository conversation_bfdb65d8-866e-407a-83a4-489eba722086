<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\LeaveBankBalances;

use Carbon\Carbon;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBank;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\UserLeaveBankEntryRepository;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\UserLeaveBankRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Support\Collection;

class LeaveBankBalancesExcelReport extends TenantExcelReport
{
    const REPORT_SLUG = 'leave-bank-balances';
    const TYPE_SLUG = 'leave-bank-balances.xls';
    const DOES_CACHE_REPORT = false;

    public $c = LeaveBankBalancesReportConfig::class;

    protected UserLeaveBank|null $userLeaveBank;
    protected User|null $user;

    public function getReplacedString(string $string): string
    {
        $string = parent::getReplacedString($string);

        if (isset($this->user) && $this->user instanceof User) {
            $string = Str::replace('{{user.fullName}}', $this->user->fullName, $string);
        }

        if (isset($this->userLeaveBank) && $this->userLeaveBank instanceof UserLeaveBank) {
            $string = Str::replace('{{userLeaveBank.name}}', $this->userLeaveBank->name, $string);
        }

        return $string;
    }

    /**
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     */
    protected function setData(array $info): void
    {
        $this->userLeaveBank = UserLeaveBankRepository::getOneOrFail([
            'constraints' => [
                ['UserLeaveBank.id', '=', $info['id']]
            ],
            'returnType' => 'model',
        ]);

        $this->user = $this->userLeaveBank->user;
    }

    /**
     * @throws \Throwable
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     * @throws \Element\Core\Exceptions\UnauthorizedActionException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     */
    protected function buildReportData(): void
    {
        $this->sheets = [];
        $entries = UserLeaveBankEntryRepository::getMany([
            'constraints' => [
                ['UserLeaveBankEntry.userLeaveBank_id', '=', $this->userLeaveBank->id],
            ],
            'order' => 'UserLeaveBankEntry.entryDateTime desc',
            'relations' => [
                'manager'
            ],
            'returnType' => 'model',
        ]);

        $sheet = $this->createSheet($this->userLeaveBank->name);
        $this->fetchSheetTitle($sheet, $this->userLeaveBank->name);
        $this->fetchGeneralData($sheet);
        $this->fetchBalancesSummary($sheet);
        $this->fetchEntriesSheetData($sheet, $entries);

        $this->sheets[] = $sheet;
    }

    protected function createSheet(string $label, bool $autoSize = true):\stdClass
    {
        $sheet = new \stdClass();
        $sheet->label = $label;
        $sheet->autoSize = $autoSize;
        $sheet->data = [];

        return $sheet;
    }

    protected function fetchSheetTitle(\stdClass $sheet, string $title = ''): void
    {
        $sheetTitle = new \stdClass();
        $sheetTitle->type = 'sheet-title';
        $sheetTitle->title = $title;
        $sheet->data[] = $sheetTitle;
    }

    // region --\\   General data   //--

    protected function fetchGeneralData(\stdClass $sheet): void
    {
        $bank = $this->userLeaveBank;
        $user = $bank->user;

        $department = '';

        if (!is_null($user->masterUserRole)) {
            foreach ($user->masterUserRole->getActiveUserRoleDepartments() as $userRoleDepartment) {
                if (strlen($department) > 0) {
                    $department .= ', ';
                }

                $department .= $userRoleDepartment->department->name;
            }
        }

        $generalTable = new \stdClass();
        $generalTable->config = new \stdClass();

        $generalTable->type = 'data-table';
        $generalTable->title = 'Staff info';
        $generalTable->columns = [
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
        ];
        $generalTable->data = [[
            'config' => [],
            'data' => [
                $user->externalId,
                $user->fullName,
                $user->masterRole,
                $department,
            ],
        ]];

        $sheet->data[] = $generalTable;
    }

    // endregion --\\   General data   //--

    // region --\\   Leave bank balances summary   //--

    /** @throws \Throwable */
    protected function fetchBalancesSummary(\stdClass $sheet): void
    {
        $bank = $this->userLeaveBank;
        $summaryTable = new \stdClass();
        $summaryTable->config = new \stdClass();

        $summaryTable->type = 'data-table';
        $summaryTable->title = 'Summary';
        $summaryTable->data = [];
        $summaryTable->config->noLabel = true;
        $summaryTable->columns = [
            $this->createColumn('', 'text', 'left', 4),
            $this->createColumn('', 'float', 'right', 1),
        ];

        $config = [];

        $summaryTable->data[] = [
            'config' => $config,
            'data' => [
                'Date/Time: ',
                ' ' . Carbon::now()->format($this->dateTimeFormat),
            ],
        ];

        if ($bank->isForAccruedHours) {
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Available balance: ',
                    $this->parseFloat($bank->repository->getAvailableBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Committed balance: ',
                    $this->parseFloat($bank->repository->getCommittedBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Historic leave taken: ',
                    $this->parseFloat($bank->repository->getTakenBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Historic leave paid: ',
                    $this->parseFloat($bank->repository->getPaidBalance()),
                ],
            ];
        } else {
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Pro-rata (accrued) balance: ',
                    $this->parseFloat($bank->repository->getAccruedBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Available balance: ',
                    $this->parseFloat($bank->repository->getAvailableBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Committed balance: ',
                    $this->parseFloat($bank->repository->getCommittedBalance()),
                ],
            ];
            $summaryTable->data[] = [
                'config' => $config,
                'data' => [
                    'Historic leave taken: ',
                    $this->parseFloat($bank->repository->getTakenBalance()),
                ],
            ];
        }

        $sheet->data[] = $summaryTable;
    }

    // endregion --\\   Leave bank balances summary   //--

    // region --\\   Leave bank balances entries table   //--

    protected function fetchEntriesSheetData(\stdClass $sheet, Collection $userLeaveBankEntries): void
    {
        $table = new \stdClass();
        $table->type = 'data-table';

        $this->fetchEntriesColumns($table);
        $this->fetchEntriesRows($table, $userLeaveBankEntries);

        $sheet->data[] = $table;
    }

    protected function fetchEntriesColumns(\stdClass $table): void
    {
        $entriesColumns = [
            $this->createColumn('Type'),
            $this->createColumn('Date'),
            $this->createFloatColumn('Hours'),
            $this->createColumn('By'),
            $this->createColumn('Description', 'text', 'left'),
        ];

        if ($this->userLeaveBank->isForAccruedHours) {
            $table->columns = $entriesColumns;
        } else {
            $balancesColumns = [
                $this->createFloatColumn('Unavailable'),
                $this->createFloatColumn('Available'),
                $this->createFloatColumn('Committed'),
                $this->createFloatColumn('Historic'),
            ];
            $table->parentColumns = [
                $this->createParentColumn('', count($entriesColumns)),
                $this->createParentColumn('Balances', count($balancesColumns)),
            ];
            $table->columns = array_merge_recursive($entriesColumns, $balancesColumns);
        }
    }

    protected function fetchEntriesRows(\stdClass $table, Collection $userLeaveBankEntries): void
    {
        $table->data = [];

        foreach ($userLeaveBankEntries as $userLeaveBankEntry) {
            $type = $userLeaveBankEntry->entryTypeClass::getMyConfig();

            $data = [
                $type['name'],
                $this->parseDate($userLeaveBankEntry->entryDateTime, 'd/m/Y'),
                $this->parseFloat($userLeaveBankEntry->entryBalance, 2, '-'),
                $userLeaveBankEntry->manager
                    ? $userLeaveBankEntry->manager->fullName
                    : '',
                $userLeaveBankEntry->description,
            ];

            if (!$this->userLeaveBank->isForAccruedHours) {
                $data = array_merge_recursive($data, [
                    $this->parseFloat($userLeaveBankEntry->currentAccruedBalance, 2, '-'),
                    $this->parseFloat($userLeaveBankEntry->currentAvailableBalance, 2, '-'),
                    $this->parseFloat($userLeaveBankEntry->currentCommittedBalance, 2, '-'),
                    $this->parseFloat($userLeaveBankEntry->currentHistoricBalance, 2, '-'),
                ]);
            }

            $table->data[] = [
                'config' => [],
                'data' => $data,
            ];
        }
    }

    // endregion --\\   Leave bank balances entries table   //--
}
