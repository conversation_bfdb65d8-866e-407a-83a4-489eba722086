<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('UserToilTypeBalanceUnUse', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilType_id')->unsigned();
            $table->bigInteger('toil_id')->unsigned(); // Declined/Deleted Toil register related

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime');

            $table->boolean('isManual')->default(false);

            $table->decimal('entryBalance', 16, 10);
            $table->decimal('entryValue', 16, 10);

            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            $table->bigInteger('payrollOfficer_id')->unsigned()->nullable(); // Payroll officer that manually managed balance
            $table->bigInteger('userToilTypeBalanceUse_id')->unsigned(); // UserToilTypeBalanceUse register that is being cancelled

            $table->foreign('userToilType_id', 'uttbuu_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('payrollOfficer_id', 'uttbuu_po_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('toil_id', 'uttbuu_t_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilTypeBalanceUse_id', 'uttbuu_uttbu_fn')->references('id')->on('UserToilTypeBalanceUse')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserToilTypeBalanceUse_UserToilTypeBalanceIn', function (Blueprint $table) {
            $table->decimal('balance', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserToilTypeBalanceUse_UserToilTypeBalanceIn', 'balance')) {
            Schema::table('UserToilTypeBalanceUse_UserToilTypeBalanceIn', function (Blueprint $table) {
                $table->dropColumn('balance');
            });
        }

        if (Schema::hasColumn('UserToilTypeBalanceUse_UserToilTypeBalanceIn', 'value')) {
            Schema::table('UserToilTypeBalanceUse_UserToilTypeBalanceIn', function (Blueprint $table) {
                $table->dropColumn('value');
            });
        }

        Schema::drop('UserToilTypeBalanceUnUse');
    }
};
