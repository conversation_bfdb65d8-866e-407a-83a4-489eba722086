<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        try {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropForeign('eimr_or_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropForeign('eimr_eim_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropIndex('eimr_or_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropIndex('eimr_eim_fn');
            });
        } catch (Throwable) {
        }

        Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
            $table->foreign('externalIntegrationModule_id', 'eimr_eim_fn')
                ->references('id')
                ->on('ExternalIntegrationModule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('originalRun_id', 'eimr_or_fn')
                ->references('id')
                ->on('ExternalIntegrationModuleRun')
                ->onUpdate('cascade')
                ->onDelete('set null');
        });
    }

    public function down(): void
    {
        //
    }
};
