<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('DutyType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name')->unique('DutyType_Name_Index');
            $table->string('type');
            $table->string('colour')->default('higher-duty');
            $table->string('description');

            $table->string('hourCode')->nullable();
            $table->boolean('doesApplyToLeave')->default(false);
            $table->boolean('doesApplyToPaidExcessTime')->default(true);
            $table->boolean('doesApplyToPublicHolidays')->default(true);
            $table->string('defaultComment')->nullable();
            $table->string('status')->default(\Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus::ID);
            $table->boolean('is_deletable')->default(true);

            $table->timestamps();
        });

        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->unsignedBigInteger('dutyType_id')->nullable()->after('onBehalfUserRole_id');
            $table->foreign('dutyType_id', 'uhd_dt_fn')
                ->references('id')->on('DutyType')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->boolean('doesHaveDifferentPayType')
                ->default(false)
                ->after('doesApplyDutyRateToAllPublicHolidays');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_dt_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserHigherDuty', 'dutyType_id')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('dutyType_id');
            });
        }
        if (Schema::hasColumn('UserHigherDuty', 'doesHaveDifferentPayType')) {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropColumn('doesHaveDifferentPayType');
            });
        }

        Schema::dropIfExists('DutyType');
    }
};
