<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Plant;

use Element\ElementTime\Domains\Tenant\Plant\Models\PlantSheetDayTime;
use Element\ElementTime\Domains\Tenant\Plant\Structures\PlantItemAssignment;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'PlantSheetDayTimeRecordResource',
    title: 'Plant item recorded data',
    required: ['id', 'rel_id', 'rel_type', 'plantItem_id', 'duration', 'durationStart', 'durationEnd', 'secondaryDuration', 'secondaryDurationStart', 'secondaryDurationEnd', 'mileage', 'mileageStart', 'mileageEnd', 'cartage', 'costRate', 'description', 'assignment'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'rel_id', type: 'integer', example: 3),
        new OA\Property(property: 'rel_type', type: 'string', example: 2),
        new OA\Property(property: 'plantItem_id', type: 'integer', example: 1),
        new OA\Property(property: 'duration', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'durationStart', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'durationEnd', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'secondaryDuration', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'secondaryDurationStart', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'secondaryDurationEnd', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'mileage', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'mileageStart', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'mileageEnd', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'cartage', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'costRate', type: 'float', example: 1.3, nullable: true),
        new OA\Property(property: 'description', type: 'string', example: 'this is a description', nullable: true),
        new OA\Property(property: 'assignment', ref: '#/components/schemas/PlantAssignmentConfigResource'),
    ],
)]
class PlantSheetDayTimeRecordResource extends Resource
{
    public static $relations = [
        'plantSheet.plantItem',
        'rel',
    ];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var PlantSheetDayTime $r */
        $r = $this->getModel();
        $plantItem = $r->plantItem;

        $assignment = new PlantItemAssignment([
            'plantItem' => $r->plantItem,
            'assignment' => $r->rel,
        ]);

        return [
            'id' => $r->id,
            'rel_id' => $r->rel_id,
            'rel_type' => $r->rel_type,
            'plantItem_id' => $plantItem->id,

            'duration' => $r->duration?->toString(true),
            'durationStart' => $r->durationStart?->toString(true),
            'durationEnd' => $r->durationEnd?->toString(true),
            'secondaryDuration' => $r->secondaryDuration?->toString(true),
            'secondaryDurationStart' => $r->secondaryDurationStart?->toString(true),
            'secondaryDurationEnd' => $r->secondaryDurationEnd?->toString(true),
            'mileage' => $r->mileage,
            'mileageStart' => $r->mileageStart,
            'mileageEnd' => $r->mileageEnd,
            'cartage' => $r->cartage,
            'costRate' => $r->costRate,

            'description' => $r->description,
            'assignment' => PlantAssignmentConfigResource::make($assignment),
        ];
    }
}
