<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('NotificationType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('slug')->unique('nt_slug_index');

            // Notification Type Data
            $table->char('category', 1)->nullable();
            $table->string('name')->unique('nt_name_index');
            $table->text('description');
            $table->text('fields'); // string array

            $table->boolean('hasEmail')->default(true);
            $table->string('emailSubject')->nullable();
            $table->text('emailMessage')->nullable();

            $table->boolean('hasPush')->default(false);
            $table->string('pushSubject')->nullable();
            $table->text('pushMessage')->nullable();

            $table->boolean('hasSms')->default(true);
            $table->string('smsSubject')->nullable();
            $table->text('smsMessage')->nullable();

            $table->boolean('isActive')->nullable();

            $table->integer('order')->default(1);

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('ReminderType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('slug')->unique('rt_slug_index');

            // Notification Type Data
            $table->char('category', 1)->nullable();
            $table->string('name')->unique('rt_name_index');
            $table->text('description');
            $table->text('fields'); // string array

            $table->boolean('hasEmail')->default(true);
            $table->string('emailSubject')->nullable();
            $table->text('emailMessage')->nullable();

            $table->boolean('hasPush')->default(false);
            $table->string('pushSubject')->nullable();
            $table->text('pushMessage')->nullable();

            $table->boolean('hasSms')->default(true);
            $table->string('smsSubject')->nullable();
            $table->text('smsMessage')->nullable();

            $table->integer('daysBeforeDue')->nullable();
            $table->integer('daysAfterDue')->nullable();
            $table->boolean('isActive')->nullable();

            $table->integer('order')->default(1);

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('ActionLog', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('actorUser_id')->unsigned()->nullable();
            $table->string('actorUserName')->nullable();
            $table->string('actorUserEmail')->nullable();
            $table->string('actorUserLogin')->nullable();
            $table->string('actorUserSystemAccess')->nullable();

            $table->bigInteger('impersonatedByUser_id')->unsigned()->nullable();
            $table->string('impersonatedByUserName')->nullable();
            $table->string('impersonatedByUserEmail')->nullable();
            $table->string('impersonatedByUserLogin')->nullable();
            $table->string('impersonatedByUserSystemAccess')->nullable();

            $table->dateTime('dateTime');
            $table->text('description');

            // Polymorphic relation
            $table->string('actionable_type')->nullable();
            $table->bigInteger('actionable_id')->unsigned()->nullable();

            $table->integer('revisionNumber')->default(1);
            $table->string('revisionKey')->nullable();
            $table->longText('data')->nullable(); // JSON
            $table->longText('oldObject')->nullable(); // Serialized object
            $table->longText('newObject')->nullable(); // Serialized object

            $table->index(['actionable_type', 'actionable_id']);
            $table->foreign('actorUser_id', 'al_auser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('impersonatedByUser_id', 'al_ibuser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('Notification', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->char('type', 1)->default('N')->nullable(); // N => Notification | R => Reminder | null => none of them (custom)
            $table->string('slug')->nullable(); // <type>-<notificationType_slug/reminderType_slug> | XX-<custom_slug>
            $table->bigInteger('notificationType_id')->unsigned()->nullable();
            $table->bigInteger('reminderType_id')->unsigned()->nullable();

            $table->bigInteger('user_id')->unsigned()->nullable(); // User related - if null, custom
            $table->string('email')->nullable(); // Receiver email
            $table->string('mobile')->nullable(); // Receiver mobile
            $table->text('pushData')->nullable(); // Receiver push data

            $table->string('emailSender')->nullable();
            $table->string('emailSubject')->nullable();
            $table->text('emailMessage')->nullable();

            $table->string('pushSender')->nullable();
            $table->string('pushSubject')->nullable();
            $table->text('pushMessage')->nullable();

            $table->string('smsSender')->nullable();
            $table->string('smsSubject')->nullable();
            $table->text('smsMessage')->nullable();

            $table->string('sendTypes')->default('["E","S"]'); // Array of chars - E => Email | S => SMS | P => Push
            $table->dateTime('scheduledDateTime')->nullable();
            $table->dateTime('queuedDateTime')->nullable();
            $table->char('status', 1)->default('Q'); // H => Scheduled | Q => Queued (sent to elementSUP) | S => Sent | D => Delivered | C => Cancelled | B => Bounce | T => Complaint | F => Failed

            $table->bigInteger('sup_id')->unsigned()->nullable(); // From elementSUP
            $table->dateTime('supDateTime')->nullable();

            $table->text('supResponses')->nullable(); // JSON { status, sendType }[]

            $table->string('supEmailId')->nullable();
            $table->char('supEmailStatus', 1)->nullable();
            $table->dateTime('supEmailSentDateTime')->nullable();
            $table->text('supEmailReadDateTimes')->nullable(); // datetime[]
            $table->dateTime('supEmailCancelDateTime')->nullable();
            $table->dateTime('supEmailDeliveredDateTime')->nullable();

            $table->string('supSmsSender')->nullable();
            $table->string('supSmsId')->nullable();
            $table->char('supSmsStatus', 1)->nullable();
            $table->dateTime('supSmsSentDateTime')->nullable();
            $table->dateTime('supSmsCancelDateTime')->nullable();
            $table->dateTime('supSmsDeliveredDateTime')->nullable();

            $table->string('supPushId')->nullable();
            $table->char('supPushStatus', 1)->nullable();
            $table->dateTime('supPushSentDateTime')->nullable();
            $table->text('supPushReadData')->nullable(); // JSON { datetime, deviceData }[]
            $table->dateTime('supPushCancelDateTime')->nullable();
            $table->dateTime('supPushDeliveredDateTime')->nullable();

            // Keys
            $table->foreign('notificationType_id', 'not_ntype_fn')->references('id')->on('NotificationType')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('reminderType_id', 'not_rtype_fn')->references('id')->on('ReminderType')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('user_id', 'not_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('User', function (Blueprint $table) {
            $table->date('birthday')->nullable();
        });

        Schema::create('UserPasswordReminder', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id')->unsigned();
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('requestNotification_id')->unsigned()->nullable();
            $table->bigInteger('successNotification_id')->unsigned()->nullable();
            $table->text('hash')->nullable();
            $table->dateTime('createdDateTime');
            $table->dateTime('executedDateTime')->nullable();
            $table->dateTime('expireDateTime');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id', 'upr_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('requestNotification_id', 'upr_rn_fn')->references('id')->on('Notification')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('successNotification_id', 'upr_sn_fn')->references('id')->on('Notification')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::table('PayRunType', function (Blueprint $table) {
            $table->boolean('canSubmitAnyTime')->default(true);
        });

        Schema::table('PayRun', function (Blueprint $table) {
            $table->boolean('canSubmitAnyTime')->default(true);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('PayRun', 'canSubmitAnyTime')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('canSubmitAnyTime');
            });
        }
        if (Schema::hasColumn('PayRunType', 'canSubmitAnyTime')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('canSubmitAnyTime');
            });
        }
        Schema::dropIfExists('UserPasswordReminder');
        if (Schema::hasColumn('User', 'birthday')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('birthday');
            });
        }
        Schema::dropIfExists('Reminder');
        Schema::dropIfExists('Notification');
        Schema::dropIfExists('ActionLog');
        Schema::dropIfExists('ReminderType');
        Schema::dropIfExists('NotificationType');
    }
};
