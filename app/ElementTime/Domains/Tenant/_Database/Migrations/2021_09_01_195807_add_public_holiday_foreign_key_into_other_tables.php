<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private array $fallbackTables = [];

    private function updatedInvalidEntries(): void
    {
        $relatedTables = [
            'TimeSheetDay',
            'UserShiftDay',
        ];

        $validPublicHolidayIds = DB::table('PublicHoliday')
            ->distinct()
            ->get('id')
            ->pluck('id');

        foreach ($relatedTables as $relatedTable) {
            $invalidTargets = DB::table($relatedTable)->whereNotIn('publicHoliday_id', $validPublicHolidayIds);
            $invalidRows = $invalidTargets->get(['id', 'publicHoliday_id']);

            $this->fallbackTables[$relatedTable] = $invalidRows->toArray();
            $invalidTargets->update(['publicHoliday_id' => null]);
        }
    }

    private function rollbackInvalidEntries(): void
    {
        foreach ($this->fallbackTables as $tableName => $fallbackEntries) {
            foreach ($fallbackEntries as $fallbackEntry) {
                DB::table($tableName)
                    ->where('id', '=', $fallbackEntry->id)
                    ->update(['publicHoliday_id' => $fallbackEntry->publicHoliday_id]);
            }
        }
    }

    public function up(): void
    {
        $this->down();
        $this->updatedInvalidEntries();

        try {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->foreign('publicHoliday_id', 'tsd_ph_fn')->references('id')->on('PublicHoliday')->onUpdate('cascade')->onDelete('set null');
            });

            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->foreign('publicHoliday_id', 'usd_ph_fn')->references('id')->on('PublicHoliday')->onUpdate('cascade')->onDelete('set null');
            });

        } catch (Throwable $e) {
            $this->down();
            $this->rollbackInvalidEntries();

            throw $e;
        }
    }

    public function down(): void
    {
        try {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropForeign('tsd_ph_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropForeign('usd_ph_fn');
            });
        } catch (Throwable) {
        }
    }
};
