<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Element\Core\Exceptions\AttachmentRequiredException;
use Element\Core\Exceptions\CommentsRequiredException;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodDurationOnly;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelTaskType\ModelTaskType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\MediaFileFunctionality;
use Tests\Traits\Functionalities\Tenant\Workflows\ModelActionGeneralFunctionality;

uses(LeaveGeneralFunctionality::class, ModelActionGeneralFunctionality::class, MediaFileFunctionality::class);

describe('Duration only - Place leave request', function () {
    it('can place single day leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(1);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($leaveRequest->duration)->toEqual($dailyDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
            //->and($leaveRequest->timeSheet_id)->not()->toBeNull()
            //->and($leaveRequest->scheduledPayRun_id)->not()->toBeNull()
        ;
    });

    it('can place single day leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $dailyDuration = TimeDuration::parseFromHours(1);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($leaveRequest->duration)->toEqual($dailyDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
            ->and($leaveRequest->timeSheet_id)->toBeNull()
            ->and($leaveRequest->scheduledPayRun_id)->toBeNull()
        ;
    });

    it('can place multiple day leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $publicHolidays = PublicHoliday::q()->many();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $expectedDays = [];
        $expectedLeaveDaysCount = 3;
        $expectedDuration = TimeDuration::zero();
        $dailyDuration = TimeDuration::parseFromHours(4);

        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $expectedDays[] = $day;
            $expectedDuration->add($dailyDuration->copy());

            if (count($expectedDays) >= $expectedLeaveDaysCount) {
                break;
            }
        }

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $expectedStartDate = reset($expectedDays)->date;
        $expectedEndDate = last($expectedDays)->date;

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $expectedStartDate->copy(),
            $expectedEndDate->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($expectedStartDate)
            ->and($leaveRequest->endDate)->toEqual($expectedEndDate)
            ->and($leaveRequest->duration)->toEqual($expectedDuration)
            ->and($leaveRequest->days)->toHaveCount(count($expectedDays))
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can place multiple day leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $publicHolidays = PublicHoliday::q()->many();

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $expectedDays = [];
        $expectedLeaveDaysCount = 3;
        $expectedDuration = TimeDuration::zero();
        $dailyDuration = TimeDuration::parseFromHours(4);

        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $day->type->duration = $dailyDuration->copy();
            $day->type->doesHaveFixedHours = true;
            $expectedDuration->add($dailyDuration->copy());
            $expectedDays[] = $day;

            if (count($expectedDays) >= $expectedLeaveDaysCount) {
                break;
            }
        }

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }
        $expectedStartDate = reset($expectedDays)->date;
        $expectedEndDate = last($expectedDays)->date;

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $expectedStartDate->copy(),
            $expectedEndDate->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->duration)->toEqual($expectedDuration)
            ->and($leaveRequest->startDate)->toEqual($expectedStartDate)
            ->and($leaveRequest->endDate)->toEqual($expectedEndDate)
            ->and($leaveRequest->days)->toHaveCount(count($expectedDays))
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can be submitted with attachment', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $publicHolidays = PublicHoliday::q()->many();

        $actorUser = SuperUserFlag::getOne();
        $expectedDays = [];
        $expectedLeaveDaysCount = 2;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = $dailyDuration->copy()->multiply($expectedLeaveDaysCount);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $expectedDays[] = $day;

            if (count($expectedDays) >= $expectedLeaveDaysCount) {
                break;
            }
        }

        $attachments = $this->g_mf_createRandomMediaFiles($actorUser);

        $startDate = reset($expectedDays)->date->copy();
        $endDate = last($expectedDays)->date->copy();
        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            attachments: $attachments,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($startDate)
            ->and($leaveRequest->endDate)->toEqual($endDate)
            ->and($leaveRequest->durationAdjusted)->toEqual($expectedDuration)
            ->and($leaveRequest->attachmentFiles)->toHaveCount($attachments->count())
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can be submitted with reason', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $publicHolidays = PublicHoliday::q()->many();

        $actorUser = SuperUserFlag::getOne();
        $expectedDays = [];
        $expectedLeaveDaysCount = 2;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = $dailyDuration->copy()->multiply($expectedLeaveDaysCount);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $reason = 'Duration only leave request - just because yes';

        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $expectedDays[] = $day;

            if (count($expectedDays) >= $expectedLeaveDaysCount) {
                break;
            }
        }

        $startDate = reset($expectedDays)->date->copy();
        $endDate = last($expectedDays)->date->copy();
        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            reason: $reason,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($startDate)
            ->and($leaveRequest->endDate)->toEqual($endDate)
            ->and($leaveRequest->durationAdjusted)->toEqual($expectedDuration)
            ->and($leaveRequest->reason)->toEqual($reason)
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can be submitted and approved', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $publicHolidays = PublicHoliday::q()->many();
        $actorUser = SuperUserFlag::getOne();
        $expectedDays = [];
        $expectedLeaveDaysCount = 5;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = $dailyDuration->copy()->multiply($expectedLeaveDaysCount);

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $expectedDays[] = $day;

            if (count($expectedDays) >= $expectedLeaveDaysCount) {
                break;
            }
        }

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $expectedStartDate = reset($expectedDays)->date;
        $expectedEndDate = last($expectedDays)->date;
        $beforeLeaveCommitedBalance = $userLeaveBankType->bank->refresh()->totalCommittedBalance;
        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $expectedStartDate->copy(),
            $expectedEndDate->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
            doesApprove: true,
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->duration)->toEqual($expectedDuration)
            ->and($availability->startDate)->toEqual($expectedStartDate)
            ->and($availability->endDate)->toEqual($expectedEndDate)
            ->and($leaveRequest->days)->toHaveCount(count($expectedDays))
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\ApprovedType::c())
            ->and($userLeaveBankType->bank->refresh()->totalCommittedBalance)->toEqual($beforeLeaveCommitedBalance + $leaveRequest->durationAdjusted->getTotalHours())
        ;
    });


    it('can place leave across multiple payruns', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $actorUser = SuperUserFlag::getOne();
        $expectedDuration = TimeDuration::zero();
        $dailyDuration = TimeDuration::parseFromHours(4);
        $scheduledDays = [];
        $remainingAttempts = 12;
        $payrunsData = [];
        $userRoleSchedule = null;
        $userLeaveBankType = null;

        while (count($payrunsData) < 2 && $remainingAttempts > 0) {
            $remainingAttempts--;
            $joins = [];
            $constraints = [];

            if (!is_null($userRoleSchedule)) {
                $constraints[] = ['UserRoleSchedule.id', '=', $userRoleSchedule->id];
            }

            if (!is_null($userLeaveBankType)) {
                $constraints[] = ['UserLeaveBank.id', '=', $userLeaveBankType->userLeaveBank_id];
            }

            $scheduleData = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(
                UserRoleScheduleType::DurationOnly,
                constraints: $constraints,
                joins: $joins,
                date: $payRun->startDate
            );

            [$user, $userRoleSchedule, $userLeaveBankType, $days] = $scheduleData;

            if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
                continue;
            }

            $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

            if (is_null($scheduledDayData)) {
                continue;
            }

            foreach ($days as $day) {
                if ($day->type->isScheduled) {
                    $expectedDuration->add($dailyDuration->copy());
                    $scheduledDays[] = $day;
                }
            }

            $payrunsData[] = $scheduleData;
            $payRun = $payRun->next;
        }

        $firstDaySchedule = reset($scheduledDays);
        $lastDaySchedule = last($scheduledDays);

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $firstDaySchedule->date,
            $lastDaySchedule->date,
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->duration)->toEqual($expectedDuration)
            ->and($availability->startDate)->toEqual($firstDaySchedule->date)
            ->and($availability->endDate)->toEqual($lastDaySchedule->date)
            ->and($leaveRequest->days)->toHaveCount(count($scheduledDays))
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can be places across multiple payruns and approved directly', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $actorUser = SuperUserFlag::getOne();
        $expectedDuration = TimeDuration::zero();
        $dailyDuration = TimeDuration::parseFromHours(4);
        $scheduledDays = [];
        $remainingAttempts = 12;
        $payRuns = [];
        $userRoleSchedule = null;
        $userLeaveBankType = null;

        while (count($payRuns) < 2 && $remainingAttempts > 0) {
            $remainingAttempts--;
            $joins = [];
            $constraints = [];

            if (!is_null($userRoleSchedule)) {
                $constraints[] = ['UserRoleSchedule.id', '=', $userRoleSchedule->id];
            }

            if (!is_null($userLeaveBankType)) {
                $constraints[] = ['UserLeaveBank.id', '=', $userLeaveBankType->userLeaveBank_id];
            }

            $scheduleData = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(
                UserRoleScheduleType::DurationOnly,
                constraints: $constraints,
                joins: $joins,
                date: $payRun->startDate
            );

            [$user, $userRoleSchedule, $userLeaveBankType, $days] = $scheduleData;

            if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
                continue;
            }

            $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

            if (is_null($scheduledDayData)) {
                continue;
            }

            foreach ($days as $day) {
                if ($day->type->isScheduled) {
                    $expectedDuration->add($dailyDuration->copy());
                    $scheduledDays[] = $day;
                }
            }

            $payRuns[] = $scheduleData;
            $payRun = $payRun->next;
        }

        $firstDaySchedule = reset($scheduledDays);
        $lastDaySchedule = last($scheduledDays);
        $beforeLeaveCommitedBalance = $userLeaveBankType->bank->refresh()->totalCommittedBalance;
        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $firstDaySchedule->date,
            $lastDaySchedule->date,
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
            doesApprove: true,
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->duration)->toEqual($expectedDuration)
            ->and($availability->startDate)->toEqual($firstDaySchedule->date)
            ->and($availability->endDate)->toEqual($lastDaySchedule->date)
            ->and($leaveRequest->days)->toHaveCount(count($scheduledDays))
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SplitType::c())
            ->and($userLeaveBankType->bank->refresh()->totalCommittedBalance)->toEqual($beforeLeaveCommitedBalance + $leaveRequest->durationAdjusted->getTotalHours())
            ->and($leaveRequest->splitParts)->toHaveCount(count($payRuns))
        ;
    });

    it('can replace leave', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $newDailyDuration = TimeDuration::parseFromHours(6);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $previousAvailability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $previousLeaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $previousAvailability,
            actorUser: $actorUser,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $newDailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            replaced: $previousLeaveRequest,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($previousLeaveRequest->startDate)->toEqual($leaveRequest->startDate->copy())
            ->and($previousLeaveRequest->endDate)->toEqual($leaveRequest->endDate->copy())
            ->and($previousLeaveRequest->duration)->not()->toEqual($leaveRequest->duration)
            ->and($previousLeaveRequest->duration)->toEqual($dailyDuration)
            ->and($previousLeaveRequest->days)->toHaveSameSize($leaveRequest->days)
            ->and($previousLeaveRequest->duration)->not()->toEqual($leaveRequest->duration)
            ->and($previousLeaveRequest->duration)->toEqual($dailyDuration)
            ->and($leaveRequest->duration)->toEqual($newDailyDuration)
            ->and($previousLeaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\ReplacedType::c())
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('can be submitted and applies FTE calculation', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $userRoleSchedule->userRole->user->employeeType->statusType = 'F';
        $userRoleSchedule->userRole->user->employeeType->clearSave();

        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        /** @var ScheduledPayRunPeriodDurationOnly $scheduledPayRunPeriodDurationOnly */
        $scheduledPayRunPeriodDurationOnly = $scheduledStartDayData->type->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;
        $scheduledPayRunPeriodDurationOnly->maxDuration?->divide(10);
        $scheduledPayRunPeriodDurationOnly->duration?->divide(10);
        $scheduledPayRunPeriodDurationOnly->clearSave();

        $userLeaveBankType->type->doesApplyFteAdjustmentsOnFullDayLeave = true;
        $userLeaveBankType->type->clearSave();

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate->copy())
            ->and($leaveRequest->endDate)->toEqual($availability->endDate->copy())
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->durationAdjusted)->not()->toEqual($leaveRequest->duration)
            ->and($leaveRequest->durationAdjusted)->toEqual($availability->durationAdjusted)
            ->and($leaveRequest->doesHaveFTECalculation)->toEqual($availability->doesHaveFteAdjustments)
            ->and($leaveRequest->doesHaveFTECalculation)->toBeTrue()
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('does broadcast relevant events', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::partialMock()
            ->expects('sendBroadcastEvents')
            ->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate->copy())
            ->and($leaveRequest->endDate)->toEqual($availability->endDate->copy())
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->statusClass)->toEqual(WorkflowStatusTypes\SubmittedType::c())
        ;
    });

    it('cannot place request without attachment if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should throw exception';

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            reason: $reason,
            actorUser: $actorUser,
        );
    })->throws(AttachmentRequiredException::class);

    it('cannot place request without reason if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
        );
    })->throws(CommentsRequiredException::class);

    it('cannot approve request if attachment is required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should throw exception on approve';

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            reason: $reason,
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireAttachmentPriorToApprove->title());

    it('cannot approve request if reason is required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(3);
        $actorUser = SuperUserFlag::getOne();

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledStartDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledStartDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledStartDayData->date->copy(),
            $scheduledStartDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireCommentPriorToApprove->title());
});
