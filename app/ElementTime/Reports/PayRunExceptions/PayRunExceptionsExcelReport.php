<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\PayRunExceptions;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\InvalidStatusException;
use Element\Core\Types\Minute;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDayHoliday;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Domains\Tenant\Workflows\Contracts\WorkflowRelModelContract;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PayRunExceptionsExcelReport extends TenantExcelReport
{
    use HasPayRunParameters;

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'payrun-exceptions';
    const TYPE_SLUG = 'payrun-exceptions.xls';

    public $c = PayRunExceptionsReportConfig::class;

    /**
     * @throws InvalidStatusException
     * @throws InvalidArgumentException
     */
    protected function buildReportData(): void
    {
        $data = $this->collectReportData();
        $this->setSheetTitle($sheet, PayRunExceptionsReportConfig::NAME);
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$doesCache || !$this->payRun->isFinished()) {
            $this->reportValidity = Carbon::now()->subSeconds();
        }

        return $this;
    }

    /**
     * @throws InvalidArgumentException
     */
    protected function collectReportData(): array|Collection
    {
        $data = [];

        $this->payRun->loadMissing([
            'timeSheets.workflow',

            'payRunItems.timeSheet' => function (Builder $query) {
                $query->select([
                    'id',
                    'payRunItem_id',
                ]);
                $query->whereHas('workflow', function (Builder $query) {
                    $query->whereIn('status', [
                        WorkflowStatusTypes\ApprovedType::ID,
                        WorkflowStatusTypes\PartiallyApprovedType::ID,
                        WorkflowStatusTypes\SubmittedType::ID,
                        WorkflowStatusTypes\NewType::ID,
                    ]);
                });

                return $query;
            },
            'payRunItems.user.userRoles' => function (Builder $query) {
                $query->where(function (Builder $subQuery) {
                    $subQuery->orWhereNull('UserRole.endDate');
                    $subQuery->orWhere('UserRole.endDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserRole.endDate', '>', $this->getStartDate());
                });
                $query->where(function (Builder $subQuery) {
                    $subQuery->orWhereNull('UserRole.startDate');
                    $subQuery->orWhere('UserRole.startDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserRole.startDate', '<', $this->getEndDate());
                });
            },
            'payRunItems.user.userRoles.userRoleMasters',
            'payRunItems.user.userRoles.userRoleProjects.project',
            'payRunItems.user.userRoles.userRoleDepartments.department',
            'payRunItems.user.userRoles.userRoleDepartments.userRole.user',
            'payRunItems.user.userRoles.userRoleManagers.manager',
            'payRunItems.timeSheet.workflow',
            'payRunItems.timeSheet.timeSheetDayTimes',

            'payRunItems.timeSheet.timeSheetAllowances.allowanceType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheet.payRunItem.payRun',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheet.payRunItem.user.userAllowanceTypes',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheet.payRunItem.user.userAllowanceTypes.user.userRoles.userRoleMasters',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheet.payRunItem.user.userAllowanceTypes.user.userRoles.userRoleProjects.project',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.userShiftDay.userShift.userRole.role',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.userShiftDay.userShift.userRole.userRoleDepartments.userRole.user',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.userShiftDay.userShift.userRole.userRoleManagers.manager',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork.workOrderType.workOrderTypeActivityTypes.activityType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork.activityType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.allowanceType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user.userAllowanceTypes.allowanceType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user.userAllowanceTypes.user.userRoles.userRoleMasters',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user.userAllowanceTypes.user.userRoles.userRoleProjects.project',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.allowanceType',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.payRun',
            'payRunItems.timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user',

            'payRunItems.timeSheet.penaltySets.userPenaltyType.penaltyType',
            'payRunItems.timeSheet.penaltySets.items.set.timeSheet.payRunItem.payRun',
            'payRunItems.timeSheet.penaltySets.items.set.userPenaltyType.penaltyType',
            'payRunItems.timeSheet.penaltySets.items.set.userPenaltyType.user.userRoles.role',
            'payRunItems.timeSheet.penaltySets.items.set.userPenaltyType.user.userRoles.userRoleMasters',
            'payRunItems.timeSheet.penaltySets.items.set.userPenaltyType.user.userRoles.userRoleProjects.project',

            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.role',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.userRoleDepartments.userRole.user',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.userRoleManagers.manager',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.userRolePenaltyRule.penaltyRule',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.calculation.penalty.userRolePenaltyRule.userRole.user.userHigherDuties.workflow',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.calculation.penalty.userRolePenaltyRule.penaltyRule',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.calculation.penalty.timeSheetDayTime.timeSheetDay',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'payRunItems.timeSheet.timeSheetDayTimes.penalties.calculations.items.work.workOrderType.workOrderTypeActivityTypes.activityType',

            'payRunItems.user.userRoles.userShifts.userShiftDays' => function (Builder $query) {
                $query->whereBetween('UserShiftDay.date', [$this->getStartDate()->toDateString(), $this->getEndDate()->toDateString()]);
            },
            'payRunItems.user.userRoles.userShifts.userShiftDays.leaveRequestDays.leaveRequest.userLeaveBankType.type',
            'payRunItems.user.userRoles.userShifts.userShiftDays.leaveRequestDays.leaveRequest.userLeaveBankType.bank.user.userRoles.userRoleMasters',
            'payRunItems.user.userRoles.userShifts.userShiftDays.leaveRequestDays.leaveRequest.userLeaveBankType.bank.user.userRoles.userRoleProjects.project',
            'payRunItems.user.userRoles.userShifts.userShiftDays.leaveRequestDays.leaveRequest.workflow',
            'payRunItems.user.userRoles.userShifts.userShiftDays.leaveRequestDays.userShiftDay',
            'payRunItems.user.userRoles.userShifts.userShiftDays.userShift.user.userHigherDuties.originalSplit.userHigherDutyUserRoles.userRole',
            'payRunItems.user.userRoles.userShifts.userShiftDays.userShift.user.userHigherDuties.userHigherDutyUserRoles.userRole',
            'payRunItems.user.userRoles.userShifts.userShiftDays.userShift.user.userHigherDuties.userHigherDutyUserRoles',
            'payRunItems.user.userRoles.userShifts.userShiftDays.userShift.user.userHigherDuties.workflow',
            'payRunItems.user.userRoles.userShifts.userShiftDays.userShift.userRole',

            'payRunItems.user.userRoles.userShifts.userRole.role',
            'payRunItems.user.userRoles.userShifts.userRole.userRoleDepartments.department',
            'payRunItems.user.userRoles.userShifts.userRole.userRoleDepartments.userRole.user',
            'payRunItems.user.userRoles.userShifts.userRole.userRoleManagers.manager',

            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.item.workflow',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.item.timeSheetExcessTime.timeSheetDayTime',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.excessTimeRule',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.excessTimeGroupSectionLevelRule.excessTimeRule',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.activityType',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.splitItems.excessTimeRuleSplitItem',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.time.model',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.time.type.project',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.user.userHigherDuties.workflow',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.user.userHigherDuties.userHigherDutyUserRoles',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.user.userHigherDuties.originalSplit.userHigherDutyUserRoles',

            'payRunItems.timeSheet.timeSheetDayTimes.model',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole',
            'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.user.userHigherDuties',
            'payRunItems.timeSheet.timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.employeeType',
            'payRunItems.timeSheet.timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleMasters',
            'payRunItems.timeSheet.timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleProjects.project',
            'payRunItems.timeSheet.timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleProjects.projectActivity.project',
        ]);

        foreach ($this->payRun->payRunItems as $payRunItem) {
            if (is_null($payRunItem->timeSheet) || !$payRunItem->user->isActiveOnTime($this->getStartDate())) {
                continue;
            }

            $masterUserRole = $payRunItem->user->getMasterUserRole($this->payRun->startDate, false);

            if (is_null($masterUserRole)) {
                continue;
            }

            $this->loadAdhocAllowancesFromTimeSheet($payRunItem, $data, $masterUserRole);
            $this->loadAutoAllowancesFromTimeSheet($payRunItem, $data, $masterUserRole);
            $this->loadPenaltiesFromTimeSheet($payRunItem, $data);
            $this->loadLeaveDataFromTimeSheet($payRunItem, $data);
            $this->loadExcessTimesFromTimeSheet($payRunItem, $data);
            $this->loadPublicHolidayFromTimeSheet($payRunItem, $data);
        }

        return $data;
    }

    /**
     * @return object{
     *     externalId: string,
     *     lastName: string,
     *     fullName: string,
     *     roleName: string,
     *     roleManager: string,
     *     departmentNames: string,
     *     exception: string,
     *     code: string,
     *     quantity: float,
     *     quantityAdjusted: float,
     *     value: float,
     *     comments: string,
     *     status: string,
     * }
     * @throws InvalidArgumentException
     */
    protected function getOrSetRecordEntry(array &$rows, UserRole $userRole, string $type, string $type_id, string $costCode, string $status): object
    {
        $user = $userRole->user;
        $uniqueKey = implode('_', [$userRole->id, $type, $type_id, $costCode]);

        if (!isset($rows[$uniqueKey])) {
            $entry = new \stdClass;
            $entry->externalId = $userRole->repository->getExternalId();
            $entry->lastName = $user->nameLast;
            $entry->fullName = $user->fullName;
            $entry->roleName = $userRole->role->name;
            $entry->roleManager = $this->getManagerNameFromUserRole($userRole);
            $entry->departmentNames = $this->getDepartmentNamesFromUserRole($userRole);
            $entry->exception = '';
            $entry->code = $costCode;
            $entry->quantity = 0;
            $entry->quantityAdjusted = 0;
            $entry->value = 0;
            $entry->comments = '';
            $entry->status = $status;

            $rows[$uniqueKey] = $entry;
        }

        return $rows[$uniqueKey];
    }

    /* @throws InvalidArgumentException */
    protected function getManagerNameFromUserRole(UserRole $userRole): string
    {
        $roleManager = $userRole->repository->getActiveUserRoleManager($this->getStartDate());

        if (!is_null($roleManager)) {
            return $roleManager->manager->fullName;
        }

        $directManager = $userRole->user->repository->getManager($this->getStartDate());

        if (!is_null($directManager)) {
            return $directManager->fullName;
        }

        return '';
    }

    protected function getDepartmentNamesFromUserRole(UserRole $userRole): string
    {
        $departmentNames = [];

        foreach ($userRole->userRoleDepartments as $userRoleDepartment) {
            if (!$userRoleDepartment->availability($this->getStartDate(), $this->getEndDate())->isCurrent(true)) {
                continue;
            }

            $departmentNames[] = $userRoleDepartment->department->name;
        }

        return implode(', ', $departmentNames);
    }

    protected function hasRelevantWorkflowStatus(WorkflowRelModelContract $relModelContract): bool
    {
        if (
            $relModelContract->repository->isExcluded()
            || $relModelContract->repository->isCancelled()
            || $relModelContract->repository->isDeclined()
            || $relModelContract->repository->isSplit()
            || $relModelContract->repository->isReplaced()
        ) {
            return false;
        }

        return true;
    }

    /* @throws InvalidArgumentException */
    protected function loadAdhocAllowancesFromTimeSheet(PayRunItem $payRunItem, array &$rows, UserRole $masterUserRole): void
    {
        $type = 'adhoc-allowance';

        foreach ($payRunItem->timeSheet->timeSheetAllowances as $timeSheetAllowance) {
            $allowanceType = $timeSheetAllowance->allowanceType;
            $exception = $allowanceType->name . ' - ' . $allowanceType->externalId;

            foreach ($timeSheetAllowance->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
                $type_id = $timeSheetAllowanceDay->timeSheetAllowance->allowanceType_id;

                foreach ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries as $allowanceDayEntry) {
                    if (!is_null($allowanceDayEntry->timeSheetDayTime)) {
                        $recordUserRole = $allowanceDayEntry->timeSheetDayTime->userShiftDay->userShift->userRole;
                    } else {
                        $recordUserRole = $masterUserRole;
                    }

                    $record = $this->getOrSetRecordEntry($rows, $recordUserRole, $type, $type_id, $allowanceDayEntry->formattedCode, 'N/A');
                    $record->exception = $exception;
                    $record->value += $allowanceDayEntry->value;
                    $this->setNotesToRecord($allowanceDayEntry->notes, $record);

                    if (in_array($allowanceType->rateType, ['F', 'D'])) {
                        $record->quantity += 1;
                    } elseif ($allowanceType->rateType === 'H') {
                        $record->quantity += $allowanceDayEntry->hours;
                    } elseif ($allowanceType->rateType === 'M') {
                        $record->quantity += $allowanceDayEntry->miles;
                    }

                    $record->quantityAdjusted = $record->quantity;
                }
            }
        }
    }
    protected function setNotesToRecord(string|null $notes, \stdClass $record): void
    {
        if (is_null($notes) || strlen($notes) < 1) {
            return;
        }

        $record->comments .= strlen($record->comments) > 0
            ? $this->br . $notes
            : $notes;
    }

    /* @throws InvalidArgumentException */
    protected function loadAutoAllowancesFromTimeSheet(PayRunItem $payRunItem, array &$rows, UserRole $masterUserRole): void
    {
        $type = 'auto-allowance';
        foreach ($payRunItem->timeSheet->penaltySets as $penaltySet) {
            $penaltyType = $penaltySet->userPenaltyType->penaltyType;
            $userRole = !is_null($penaltySet->userPenaltyType->userShift)
                ? $penaltySet->userPenaltyType->userShift->userRole
                : $masterUserRole;
            $type_id = $penaltyType->id;

            foreach ($penaltySet->items as $penaltySetItem) {
                $record = $this->getOrSetRecordEntry($rows, $userRole, $type, $type_id, $penaltySetItem->formattedCode, 'N/A');
                $record->exception = $penaltyType->name . ' - ' . $penaltyType->externalId;
                $record->value += $penaltySetItem->value;
                $this->setNotesToRecord($penaltySetItem->comments, $record);

                if ($penaltyType->rateTypeClass::IS_HOURLY) {
                    $totalMinutes = Minute::parseFromMinutes(0);
                    foreach ($penaltySetItem->triggers as $trigger) {
                        $totalMinutes->addMinutes($trigger->minutes);
                    }
                    $record->quantity += $totalMinutes->toHours();
                } else {
                    $record->quantity += $penaltySetItem->triggers->count();
                }

                $record->quantityAdjusted = $record->quantity;
            }
        }
    }

    /* @throws InvalidArgumentException */
    protected function loadPenaltiesFromTimeSheet(PayRunItem $payRunItem, array &$rows): void
    {
        $type = 'penalty';
        foreach ($payRunItem->timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            $userRole = $timeSheetDayTime->userShift->userRole;

            foreach ($timeSheetDayTime->penalties as $penalty) {
                $penaltyRule = $penalty->userRolePenaltyRule->penaltyRule;
                $type_id = $penaltyRule->id;

                foreach ($penalty->activeCalculation->items as $item) {
                    $record = $this->getOrSetRecordEntry($rows, $userRole, $type, $type_id, $item->formattedCode, 'N/A');
                    $record->exception = $penaltyRule->name . ' - ' . $penaltyRule->externalId;
                    $record->quantity += $item->minutes->toHours();
                    $record->quantityAdjusted = $record->quantity;
                    $record->value += $item->value;

                    $this->setNotesToRecord($item->comments, $record);
                }
            }
        }
    }

    /* @throws InvalidArgumentException */
    protected function loadLeaveDataFromTimeSheet(PayRunItem $payRunItem, array &$rows): void
    {
        $userRoles = $payRunItem->user->getActiveUserRoles($this->getStartDate());
        $type = 'leave-request';

        foreach ($userRoles as $userRole) {
            foreach ($userRole->userShifts as $userShift) {
                foreach ($userShift->userShiftDays as $userShiftDay) {

                    foreach ($userShiftDay->leaveRequestDays as $leaveRequestDay) {
                        if (!$leaveRequestDay->date->isBetween($this->getStartDate(), $this->getEndDate())) {
                            continue;
                        }

                        $leaveRequest = $leaveRequestDay->leaveRequest;

                        if (!$this->hasRelevantWorkflowStatus($leaveRequest)) {
                            continue;
                        }

                        $type_id = $leaveRequest->userLeaveBankType_id;
                        $record = $this->getOrSetRecordEntry($rows, $userRole, $type, $type_id, $leaveRequest->formattedCode, $leaveRequest->workflow->statusClass::NAME);
                        $record->exception = $leaveRequest->userLeaveBankType->typeName . ' - ' . $leaveRequest->typeExternalId;
                        $record->quantity += $leaveRequestDay->duration->getTotalHours();
                        $record->quantityAdjusted += $leaveRequestDay->durationAdjusted->getTotalHours();
                        $record->value += $leaveRequestDay->durationAdjusted->getTotalHours() * $userShiftDay->hourlyRate;

                        $this->setNotesToRecord($leaveRequest->reason, $record);
                    }
                }
            }
        }
    }

    /* @throws InvalidArgumentException */
    protected function loadExcessTimesFromTimeSheet(PayRunItem $payRunItem, array &$rows): void
    {
        $type = 'excess-time';

        foreach ($payRunItem->timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            $dayTimeComments = !is_null($timeSheetDayTime->notes) && strlen($timeSheetDayTime->notes) > 0
                ? $timeSheetDayTime->notes
                : '';

            foreach ($timeSheetDayTime->excessTimeWorks as $excessTimeWork) {
                if (!$this->hasRelevantWorkflowStatus($excessTimeWork->rule->item)) {
                    continue;
                }

                $userRole = $timeSheetDayTime->userShiftDay->userShift->userRole;
                $type_id = !is_null($excessTimeWork->rule->excessTimeRule_id)
                    ? $excessTimeWork->rule->excessTimeRule_id
                    : 'excess_time_group_'. $excessTimeWork->rule->excessTimeGroupSectionLevelRule_id;
                $status = $excessTimeWork->rule->item->workflow->statusClass::NAME;
                $record = $this->getOrSetRecordEntry($rows, $userRole, $type, $type_id, $excessTimeWork->formattedCode, $status);
                $record->exception = $excessTimeWork->rule->name;

                $entryComment = '';
                if (!is_null($excessTimeWork->rule->comments) && strlen($excessTimeWork->rule->comments) > 0) {
                    $entryComment = strlen($dayTimeComments) > 0
                        ? $dayTimeComments . $this->br . $excessTimeWork->rule->comments
                        : $excessTimeWork->rule->comments;
                }

                $this->setNotesToRecord($entryComment, $record);

                foreach ($excessTimeWork->splitItems as $splitItem) {
                    if (is_null($splitItem->excessTimeRuleSplitItem_id) || is_null($splitItem->excessTimeRuleSplitItem)) {
                        $actualHours = $excessTimeWork->originalHours;
                        $adjustedHours = $excessTimeWork->adjustedHours;
                    } else {
                        $actualHours = $splitItem->excessTimeRuleSplitItem->repository->applyPercentage($excessTimeWork->originalHours);
                        $adjustedHours = $splitItem->adjustedHours;
                    }

                    $record->quantity += $actualHours;
                    $record->quantityAdjusted += $adjustedHours;

                    if ($splitItem->ruleTypeClass::IS_PAID) {
                        $record->value += $timeSheetDayTime->userShiftDay->hourlyRateOvertime * $adjustedHours;
                    }
                }
            }
        }
    }

    /* @throws InvalidArgumentException */
    protected function loadPublicHolidayFromTimeSheet(PayRunItem $payRunItem, array &$rows): void
    {
        $type = 'public-holiday';
        foreach ($payRunItem->timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            if (
                $timeSheetDayTime->model instanceof UserShiftDayHoliday
                || $timeSheetDayTime->model instanceof PublicHoliday
                || $timeSheetDayTime->model instanceof Holiday
            ) {
                $userRole = $timeSheetDayTime->userShift->userRole;
                $type_id = $timeSheetDayTime->model_id;
                $record = $this->getOrSetRecordEntry($rows, $userRole, $type, $type_id, $timeSheetDayTime->formattedCode, $payRunItem->timeSheet->workflow->statusClass::NAME);
                $record->exception = $timeSheetDayTime->model->name;
                $record->quantity += $timeSheetDayTime->hours;
                $record->quantityAdjusted += $timeSheetDayTime->hours;
                $record->value += ($timeSheetDayTime->hours * $timeSheetDayTime->userShiftDay->hourlyRate);
            }
        }
    }

    /* @throws InvalidStatusException */
    protected function fetchSheetTotals(\stdClass $sheet): void
    {
        $data = new \stdClass;
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Pay-run period'),
            $this->createColumn('Timesheets on this pay-run'),
            $this->createColumn('Payrun status'),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                $this->parseDate($this->payRun->startDate) . ' - ' . $this->parseDate($this->payRun->endDate),
                $this->payRun->timeSheets->filter(fn (TimeSheet $timeSheet) => !$timeSheet->repository->isExcluded())->count(),
                $this->payRun->getStatusObject()['name'],
            ],
        ]];

        $sheet->data[] = $data;
    }

    protected function fetchSheetData(\stdClass $sheet, array $data): void
    {
        $sheetData = new \stdClass;
        $sheetData->type = 'data-table';
        $sheetData->parentColumns = $this->getSheetDataParentColumns();
        $sheetData->columns = $this->getSheetDataColumns();
        $sheetData->data = $this->getSheetDataData($data);

        $sheet->data[] = $sheetData;
    }

    protected function getSheetDataParentColumns(): array
    {
        return [
            $this->createParentColumn('Staff', 6),
            $this->createParentColumn('Exception', 7),
        ];
    }

    protected function getSheetDataColumns(): array
    {
        return [
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Last name'),
            $this->createColumn('Full name'),
            $this->createColumn('Role name'),
            $this->createColumn('Role manager'),
            $this->createColumn('Department'),
            $this->createColumn('Exception'),
            $this->createColumn('Hour / Allowance code'),
            $this->createFloatColumn('Quantity actual'),
            $this->createFloatColumn('Quantity adjusted'),
            $this->createFloatColumn('Value'),
            $this->createColumn('Comments'),
            $this->createColumn('Status'),
        ];
    }

    protected function getSheetDataData(array $data): array
    {
        $rows = [];

        foreach ($data as $datum) {
            if ($datum->quantity <= 1 && $datum->value <= 1) {
                continue;
            }

            $rows[] = [
                'config' => [],
                'data' => [
                    $datum->externalId,
                    $datum->lastName,
                    $datum->fullName,
                    $datum->roleName,
                    $datum->roleManager,
                    $datum->departmentNames,
                    $datum->exception,
                    $datum->code,
                    $datum->quantity,
                    $datum->quantityAdjusted,
                    $datum->value,
                    $datum->comments,
                    $datum->status,
                ],
            ];
        }

        return $rows;
    }
}
