<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('doApplyFteCalculation')->default(false);
            $table->boolean('doNotAccrueRDO')->default(false);
            $table->boolean('doNotAccrueLeave')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
        });

        Schema::table('ToilType', function (Blueprint $table) {
            $table->boolean('doApplyFteCalculation')->default(false);
            $table->boolean('doNotAccrueRDO')->default(false);
            $table->boolean('doNotAccrueLeave')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
        });

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->boolean('doApplyFteCalculation')->default(false);
            $table->boolean('doNotAccrueRDO')->default(false);
            $table->boolean('doNotAccrueLeave')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
        });

        Schema::table('Leave', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->bigInteger('userShiftDay_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->text('breaks')->nullable();
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });

        Schema::table('ToilDay', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->bigInteger('userShiftDay_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->text('breaks')->nullable();
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });

        Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
            $table->bigInteger('userShiftDay_id')->unsigned()->nullable()->change();
            $table->boolean('haveFTECalculation')->default(false);
            $table->boolean('doDeductBreaks')->default(true);
            $table->text('breaks')->nullable();
            $table->decimal('hoursBalance', 16, 10)->default(null);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('LeaveType', 'doApplyFteCalculation')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doApplyFteCalculation');
            });
        }
        if (Schema::hasColumn('LeaveType', 'doNotAccrueRDO')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueRDO');
            });
        }
        if (Schema::hasColumn('LeaveType', 'doNotAccrueLeave')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueLeave');
            });
        }
        if (Schema::hasColumn('LeaveType', 'doDeductBreaks')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }

        if (Schema::hasColumn('ToilType', 'doApplyFteCalculation')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('doApplyFteCalculation');
            });
        }
        if (Schema::hasColumn('ToilType', 'doNotAccrueRDO')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueRDO');
            });
        }
        if (Schema::hasColumn('ToilType', 'doNotAccrueLeave')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueLeave');
            });
        }
        if (Schema::hasColumn('ToilType', 'doDeductBreaks')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'doApplyFteCalculation')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doApplyFteCalculation');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'doNotAccrueRDO')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueRDO');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'doNotAccrueLeave')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doNotAccrueLeave');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'doDeductBreaks')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }

        if (Schema::hasColumn('Leave', 'haveFTECalculation')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('Leave', 'doDeductBreaks')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('Leave', 'hoursBalance')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }

        if (Schema::hasColumn('LeaveDay', 'haveFTECalculation')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('LeaveDay', 'doDeductBreaks')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('LeaveDay', 'breaks')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('breaks');
            });
        }
        if (Schema::hasColumn('LeaveDay', 'hoursBalance')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }

        if (Schema::hasColumn('Toil', 'haveFTECalculation')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('Toil', 'doDeductBreaks')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('Toil', 'hoursBalance')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }

        if (Schema::hasColumn('ToilDay', 'haveFTECalculation')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('ToilDay', 'doDeductBreaks')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('ToilDay', 'breaks')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->dropColumn('breaks');
            });
        }
        if (Schema::hasColumn('ToilDay', 'hoursBalance')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }

        if (Schema::hasColumn('RosteredTimeOff', 'haveFTECalculation')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'doDeductBreaks')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'hoursBalance')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffDay', 'haveFTECalculation')) {
            Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
                $table->dropColumn('haveFTECalculation');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffDay', 'doDeductBreaks')) {
            Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
                $table->dropColumn('doDeductBreaks');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffDay', 'breaks')) {
            Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
                $table->dropColumn('breaks');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffDay', 'hoursBalance')) {
            Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
                $table->dropColumn('hoursBalance');
            });
        }
    }
};
