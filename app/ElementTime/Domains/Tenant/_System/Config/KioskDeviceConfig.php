<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2023 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\_System\Config;

use Element\ElementTime\Support\Domains\Config\TenantBaseModelConfig;

class KioskDeviceConfig extends TenantBaseModelConfig
{
    protected static $attributes = [
         'deviceId' => null,
         'name' => null,
         'localization' => null,
         'isActive' => true,

         'appId' => null,
         'secret' => null,
    ];

    protected static $casts = [
        // Integer values
        'id' => 'integer',

        // Boolean values
        'isActive' => 'boolean',
    ];
}
