<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromLeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetPeriod;
use Pest\Expectation;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

describe('Duration only - set relationships from LeaveRequest', function () {
    it('does not set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on future date that payrun does not exists', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeNull()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeFalse()
            ->and($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeFalse()
        ;
    });

    it('does set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on current date that payrun exists', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $expectedScheduledPayRun = ScheduledPayRun::q()->constraints([
            ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
            ['ScheduledPayRun.startDate', '<=', $leaveRequest->startDate],
            ['ScheduledPayRun.endDate', '>=', $leaveRequest->endDate],
        ])->first();
        $expectedTimeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($expectedTimeSheet->id)
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });

    it('does fix scheduledPayRun_id and timeSheet_id on LeaveRequest if it has wrong data', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $wrongTimeSheet = TimeSheet::q()->first();
        $wrongScheduledPayRun = ScheduledPayRun::q()->first();

        $expectedScheduledPayRun = ScheduledPayRun::q()->constraints([
            ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
            ['ScheduledPayRun.startDate', '<=', $leaveRequest->startDate],
            ['ScheduledPayRun.endDate', '>=', $leaveRequest->endDate],
        ])->first();
        $expectedTimeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);

        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest->save(['isSafeSave' => true]);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->not()->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->timeSheet_id)->toEqual($expectedTimeSheet->id)
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->not()->toEqual($wrongScheduledPayRun->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });

    it('does set leave request days from scheduled pay run and time sheet', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)->and($leaveRequest->id)->toBeInt();

        foreach ($leaveRequest->days as $day) {
            expect($day->wasChanged('scheduledPayRunPeriodDay_id'))->toBeTrue()
                ->and($day->wasChanged('timeSheetPeriod_id'))->toBeTrue()
                ->and($day->wasChanged('timeSheetDay_id'))->toBeTrue()
                ->and($day->wasChanged('userRoleScheduleDay_id'))->toBeFalse()
                ->and($day->wasChanged('userRoleScheduleDay_type'))->toBeFalse()
            ;
        }
    });

    it('does fix leave request days from scheduled pay run and timesheet if leave days has invalid relationship data set', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);

        $wrongTimeSheetDay = TimeSheetDay::q()->first();
        $wrongScheduledPayRunPeriodDay = ScheduledPayRunPeriodDay::q()->first();
        $wrongTimeSheetPeriod = TimeSheetPeriod::q()->first();

        foreach ($leaveRequest->days as $day) {
            $day->timeSheetDay_id = $wrongTimeSheetDay->id;
            $day->scheduledPayRunPeriodDay_id = $wrongScheduledPayRunPeriodDay->id;
            $day->timeSheetPeriod_id = $wrongTimeSheetPeriod->id;
            $day->clearSaveOrFail();
            $day->syncChanges();
        }

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)->and($leaveRequest->id)->toBeInt();

        foreach ($leaveRequest->days as $day) {
            expect($day->wasChanged('scheduledPayRunPeriodDay_id'))->toBeTrue()
                ->and($day->wasChanged('timeSheetPeriod_id'))->toBeTrue()
                ->and($day->wasChanged('timeSheetDay_id'))->toBeTrue()
                ->and($day->scheduledPayRunPeriodDay_id)->not()->toEqual($wrongScheduledPayRunPeriodDay->id)
                ->and($day->timeSheetPeriod_id)->not()->toEqual($wrongTimeSheetPeriod->id)
                ->and($day->timeSheetDay_id)->not()->toEqual($wrongTimeSheetDay->id)
                ->and($day->wasChanged('userRoleScheduleDay_id'))->toBeFalse()
                ->and($day->wasChanged('userRoleScheduleDay_type'))->toBeFalse()
            ;
        }
    });
});

describe('Work pattern - set relationships from LeaveRequest', function () {
    it('does not set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on future date that payrun does not exists', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeNull()
            ->and($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeFalse()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeFalse()
        ;
    });

    it('does set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on current date that payrun exists', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });

    it('does fix scheduledPayRun_id and timeSheet_id on LeaveRequest if it has incompatible data', function () {
        $wrongTimeSheet = TimeSheet::q()->first();
        $wrongScheduledPayRun = ScheduledPayRun::q()->first();

        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });

    it('does set leave request days from scheduled pay run and timesheet', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
            ->and($leaveRequest->days)
            ->each(fn (Expectation|LeaveRequestDay $day) =>
            $day
                ->and($day->wasChanged('scheduledPayRunPeriodDay_id')->toBeTrue())
                ->and($day->wasChanged('timeSheetPeriod_id')->toBeTrue())
                ->and($day->wasChanged('timeSheetDay_id')->toBeTrue())
                ->and($day->wasChanged('userRoleScheduleDay_id')->toBeTrue())
                ->and($day->wasChanged('userRoleScheduleDay_type')->toBeTrue())
            )
        ;
    });

    it('does fix leave request days from scheduled pay run and time sheet if day has incompatible data', function () {
        $wrongTimeSheetDay = TimeSheetDay::q()->first();
        $wrongScheduledPayRunPeriodDay = ScheduledPayRunPeriodDay::q()->first();
        $wrongTimeSheetPeriod = TimeSheetPeriod::q()->first();

        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);

        foreach ($leaveRequest->days as $day) {
            $day->timeSheetPeriod_id = $wrongTimeSheetPeriod->id;
            $day->timeSheetDay_id = $wrongTimeSheetDay->id;
            $day->scheduledPayRunPeriodDay_id = $wrongScheduledPayRunPeriodDay->id;
            $day->userRoleScheduleDay_id = 0;
            $day->userRoleScheduleDay_type = 'InvalidModelClass';
            $day->clearSave();
            $day->syncChanges();
        }

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
            ->and($leaveRequest->days)
            ->each(fn (Expectation|LeaveRequestDay $day) =>
            $day->wasChanged()->toBeTrue()
                ->and($day->wasChanged('scheduledPayRunPeriodDay_id')->toBeTrue())
                ->and($day)->scheduledPayRunPeriodDay_id
                ->toBeInt()
                ->not()->toEqual($wrongScheduledPayRunPeriodDay->id)

                ->and($day->wasChanged('timeSheetPeriod_id')->toBeTrue())
                ->and($day)->timeSheetPeriod_id
                ->toBeInt()
                ->not()->toEqual($wrongTimeSheetPeriod->id)

                ->and($day->wasChanged('timeSheetDay_id')->toBeTrue())
                ->and($day)->timeSheetDay_id
                ->toBeInt()
                ->not()->toEqual($wrongTimeSheetDay->id)

                ->and($day->wasChanged('userRoleScheduleDay_id')->toBeTrue())
                ->and($day)->userRoleScheduleDay_id
                ->toBeInt()
                ->not()->toEqual(0)

                ->and($day->wasChanged('userRoleScheduleDay_type')->toBeTrue())
            )
        ;
    });
});

describe('Zero based - set relationships from LeaveRequest', function () {
    it('does not set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on future date that payrun does not exists', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: true);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeNull()
            ->and($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeFalse()
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeFalse()
        ;
    });

    it('does set scheduledPayRun_id and timeSheet_id on LeaveRequest placed on current date that payrun exists', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $expectedScheduledPayRun = ScheduledPayRun::q()->constraints([
            ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
            ['ScheduledPayRun.startDate', '<=', $leaveRequest->startDate],
            ['ScheduledPayRun.endDate', '>=', $leaveRequest->endDate],
        ])->first();
        $expectedTimeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($expectedTimeSheet->id)
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });

    it('does fix scheduledPayRun_id and timeSheet_id on LeaveRequest if it has wrong data', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $wrongTimeSheet = TimeSheet::q()->first();
        $wrongScheduledPayRun = ScheduledPayRun::q()->first();

        $expectedScheduledPayRun = ScheduledPayRun::q()->constraints([
            ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
            ['ScheduledPayRun.startDate', '<=', $leaveRequest->startDate],
            ['ScheduledPayRun.endDate', '>=', $leaveRequest->endDate],
        ])->first();
        $expectedTimeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);

        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest->save(['isSafeSave' => true]);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        SetLeaveRequestRelationshipsFromLeaveRequest::make()->handle($leaveRequest);

        expect($leaveRequest)->toBeInstanceOf(LeaveRequest::class)
            ->and($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->not()->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->timeSheet_id)->toEqual($expectedTimeSheet->id)
            ->and($leaveRequest->wasChanged('timeSheet_id'))->toBeTrue()
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->not()->toEqual($wrongScheduledPayRun->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
            ->and($leaveRequest->wasChanged('scheduledPayRun_id'))->toBeTrue()
        ;
    });
});
