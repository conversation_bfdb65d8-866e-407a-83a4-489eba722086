<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\CostingByEmployeeStatusByDepartment;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Exceptions\InvalidPropertyException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\EmployeeType;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\BaseType;
use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyRuleCalculationMethods\OffsetMethod;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRuleWorkSplitItem;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CostingByEmployeeStatusByDepartmentExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'costing-by-employee-status-by-department';
    const TYPE_SLUG = 'costing-by-employee-status-by-department.xls';
    const DOES_CACHE_REPORT = true;

    /** @var CostingByEmployeeStatusByDepartmentReportConfig */
    public $c = CostingByEmployeeStatusByDepartmentReportConfig::class;

    protected Department|null $department = null;
    protected bool $doesIncludeSubDepartments = false;
    protected string|null $employeeTypeStatusType = null;

    protected function setData(array $info): void
    {
        $this->_iSetData($info);

        if (isset($info['idDepartment']) && strlen($info['idDepartment']) > 0) {
            $this->department = Department::q()->find($info['idDepartment']);

            if (isset($info['doesIncludeSubDepartments']) && strlen($info['doesIncludeSubDepartments']) > 0) {
                $this->doesIncludeSubDepartments = !!$info['doesIncludeSubDepartments'];
            }
        }

        if (isset($info['employeeTypeStatusType']) && strlen($info['employeeTypeStatusType']) > 0) {
            $this->employeeTypeStatusType = $info['employeeTypeStatusType'];
        }
    }

    /** @throws InvalidPropertyException */
    public function getReplacedString(string $string): string
    {
        $string = $this->_iGetReplacedString($string);

        if (!is_null($this->department)) {
            $string = Str::replace('{{department.id}}', $this->department->id . ($this->doesIncludeSubDepartments ? '-s' : ''), $string);
            $string = Str::replace('{{department.name}}', $this->department->name . ($this->doesIncludeSubDepartments ? ' and sub departments' : ''), $string);
        } else {
            $string = Str::replace('{{department.id}}', '0', $string);
            $string = Str::replace('{{department.name}}', 'All departments', $string);
        }

        if (!is_null($this->employeeTypeStatusType)) {
            $string = Str::replace('{{employeeTypeStatusType.name}}', BaseType::getClassById($this->employeeTypeStatusType)::NAME, $string);
            $string = Str::replace('{{employeeTypeStatusType.id}}', $this->employeeTypeStatusType, $string);
        } else {
            $string = Str::replace('{{employeeTypeStatusType.name}}', 'All employee types', $string);
            $string = Str::replace('{{employeeTypeStatusType.id}}', '0', $string);
        }

        return $string;
    }

    protected function buildReportData(): void
    {
        $payRunItems = $this->collectReportData($this->getStartDate(), $this->getEndDate(), $this->department, $this->doesIncludeSubDepartments, $this->employeeTypeStatusType);

        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetHeading($sheet);
        $this->fetchSheetData($sheet, $payRunItems);

        $this->sheets = [
            $sheet
        ];
    }

    /**
     * @return Collection|PayRunItem[]
     */
    private function collectReportData(Carbon|CarbonInterface $startDate, Carbon|CarbonInterface $endDate, Department|null $department, bool $doesIncludeSubDepartments = false, string $employeeTypeStatusType = null): Collection|array
    {
        if (!is_null($department)) {
            $users = $department->repository->getAllStaff($doesIncludeSubDepartments, $doesIncludeSubDepartments, $endDate, $startDate);
        } else {
            $users = User::q()
                ->constraints([
                    ['User.id', '>', 1],
                    [
                        [
                            ['IS NULL', 'User.startDate', 'OR'],
                            ['User.startDate', '=', '0000-00-00', 'OR'],
                            ['User.startDate', '<=', $endDate->toDateString(), 'OR'],
                        ],
                    ],
                    [
                        [
                            ['IS NULL', 'User.endDate', 'OR'],
                            ['User.endDate', '=', '0000-00-00', 'OR'],
                            ['User.endDate', '>=', $startDate->toDateString(), 'OR'],
                        ],
                    ],
                ])
                ->many();
        }

        $staffIds = [];
        $employeeTypeIds = [];

        if (!is_null($employeeTypeStatusType)) {
            $employeeTypes = EmployeeType::q()
                ->constraints([
                    ['EmployeeType.statusType', '=', $employeeTypeStatusType]
                ])
                ->many();

            foreach ($employeeTypes as $employeeType) {
                $employeeTypeIds[] = $employeeType->id;
            }
        }

        foreach ($users as $user) {
            if (!is_null($employeeTypeStatusType) && !in_array($user->employeeType_id, $employeeTypeIds)) {
                continue;
            }

            $staffIds[] = $user->id;
        }

        $payRunItems = PayRunItem::q()
            ->constraints([
                ['PayRun.startDate', '>=', $startDate],
                ['PayRun.endDate', '<=', $endDate],
                ['IN', 'PayRunItem.user_id', $staffIds],
            ])
            ->many();

        $payRunItems->loadMissing([
            'user.userShifts.userRole.userRoleProjects',
            'user.userShifts.userRole.userRoleProjects.project',
            'user.userShifts.userRole.userRoleProjects.projectActivity',
            'user.userShifts.userShiftDays' => function (Builder $query) use ($startDate, $endDate) {
                $query->whereBetween('UserShiftDay.date', [$startDate, $endDate]);
            },
            'user.userShifts.userShiftDays.leaveRequestDays' => function (Builder $query) use ($startDate, $endDate) {
                $query->whereBetween('LeaveRequestDay.date', [$startDate, $endDate]);
            },
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.penaltyCalculationItems.calculation.penalty.userRolePenaltyRule.penaltyRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.splitItems',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.splitItems.excessTimeRuleSplitItem',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.item.timeSheetExcessTime.timeSheetDayTime.type',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.excessTimeRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.excessTimeGroupSectionLevelRule.excessTimeRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.work.workOrderType.workOrderTypeActivityTypes.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.splitItems.excessTimeRuleSplitItem',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.item.timeSheetExcessTime.items',

            'user.userShifts.userShiftDays.timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.penalties.userRolePenaltyRule.penaltyRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.item.timeSheetExcessTime.timeSheetDayTime.type',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.excessTimeRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.excessTimeGroupSectionLevelRule.excessTimeGroupSectionLevel.excessTimeGroupSection.excessTimeGroup',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.excessTimeGroupSectionLevelRule.excessTimeRule',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.work.workOrderType.workOrderTypeActivityTypes.activityType',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.splitItems.excessTimeRuleSplitItem',
            'user.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.item.timeSheetExcessTime.items',
            'user.userShifts.userShiftDays.timeSheetDayTimes.type.projectActivity',
            'user.userShifts.userShiftDays.timeSheetDayTimes.type.project',
            'user.userShifts.userShiftDays.timeSheetDayTimes.model',
            'user.userShifts.userShiftDays.userShift.user.userHigherDuties.payType',
            'user.userShifts.userShiftDays.leaveRequestDays.leaveRequest.workflow',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.allowanceType',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user.userAllowanceTypes',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetAllowanceDay.timeSheetAllowance.timeSheet.payRunItem.user',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.type',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime.model',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork',
            'timeSheet.penaltySets.userPenaltyType',
        ]);

        return $payRunItems;
    }

    private function fetchSheetHeading(\stdClass $sheet): void
    {
        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn(label: 'Period', span: 4),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                '#' . $this->payRunStart->number . ' ' . $this->parseDate($this->payRunStart->startDate) . ' - ' . $this->parseDate($this->payRunEnd->endDate) . ' to #' . $this->payRunEnd->number . ' ' . $this->parseDate($this->payRunEnd->startDate) . ' - ' . $this->parseDate($this->payRunEnd->endDate)
            ],
        ]];

        $sheet->data[] = $data;
    }

    /**
     * @param Collection|PayRunItem[] $payRunItems
     */
    private function fetchSheetData(\stdClass $sheet, Collection|array $payRunItems): void
    {
        $entries = [];
        foreach ($payRunItems as $payRunItem) {
            if (is_null($payRunItem->timeSheet)) {
                continue;
            }

            if ($payRunItem->timeSheet->repository->isExcluded()) {
                continue;
            }

            foreach ($payRunItem->user->userShifts as $userShift) {
                foreach ($userShift->userShiftDays as $userShiftDay) {
                    $this->fetchDataFromUserShiftDay($entries, $userShiftDay);
                }
            }

            foreach ($payRunItem->timeSheet->penaltySets as $penaltySet) {
                $record = static::getOrSetRecordFrom($entries, $penaltySet->userPenaltyType->penaltyType->externalId);
                $record->totalCost += $penaltySet->totalValue;
            }

            foreach ($payRunItem->timeSheet->timeSheetAllowances as $timeSheetAllowance) {
                foreach ($timeSheetAllowance->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
                    foreach ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries as $entry) {
                        $record = static::getOrSetRecordFrom($entries, $timeSheetAllowance->allowanceType->externalId);
                        $record->hoursActual += $entry->hours;
                        $record->hoursAdjusted += $entry->hours;
                        $record->totalCost += $entry->value;
                    }
                }
            }
        }

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Hour code'),
            $this->createFloatColumn('Total hours actual'),
            $this->createFloatColumn('Total hours adjusted'),
            $this->createFloatColumn('Total cost'),
        ];
        $data->data = [];

        foreach ($entries as $entry) {
            $data->data[] = [
                'config' => [],
                'data' => [
                    $entry->code,
                    $entry->hoursActual,
                    $entry->hoursAdjusted,
                    $entry->totalCost,
                ],
            ];
        }

        $sheet->data[] = $data;
    }

    private function fetchDataFromUserShiftDay(array &$entries, UserShiftDay $userShiftDay): void
    {
        foreach ($userShiftDay->timeSheetDayTimes as $time) {
            $actual = $time->hours;

            foreach ($time->penalties as $penalty) {
                if ($penalty->userRolePenaltyRule->penaltyRule->calculationMethodClass::isType(OffsetMethod::class)) {
                    $actual -= $penalty->minutes->toHours();
                }

                foreach($penalty->activeCalculation->items as $item) {
                    $record = static::getOrSetRecordFrom($entries, $penalty->userRolePenaltyRule->penaltyRule->externalId);
                    $record->hoursActual += $item->minutes->toHours();
                    $record->totalCost += $item->value;
                }
            }

            foreach ($time->excessTimeWorks as $excessTimeWork) {
                if (!$excessTimeWork->rule->item->isActive) {
                    continue;
                }

                foreach ($excessTimeWork->splitItems as $splitItem) {
                    $this->fetchDataFromExcessTimeItemRuleWork($entries, $splitItem, $actual, $userShiftDay->hourlyRateOvertime);
                }
            }

            foreach ($time->works as $work) {
                $actual -= $work->hours;
                $workActual = $work->hours;

                foreach ($work->excessTimeItemRuleWorks as $excessTimeItemRuleWork) {
                    if (!$excessTimeItemRuleWork->rule->item->isActive) {
                        continue;
                    }

                    foreach ($excessTimeItemRuleWork->splitItems as $splitItem) {
                        $this->fetchDataFromExcessTimeItemRuleWork($entries, $splitItem, $workActual, $userShiftDay->hourlyRateOvertime);
                    }
                }

                $record = $this->getOrSetRecordFrom($entries, $time->timeTypeHourCode ?? $time->timeTypeExternalId);
                $record->hoursActual += $workActual;
                $record->hoursAdjusted += $workActual;
                $record->totalCost += ($workActual * $userShiftDay->hourlyRate);
            }

            $record = $this->getOrSetRecordFrom($entries, $time->recordTypeClass::IS_HOLIDAY ? $time->itemExternalId : ($time->timeTypeHourCode ?? $time->timeTypeExternalId));
            $record->hoursActual += $actual;
            $record->hoursAdjusted += $actual;
            $record->totalCost += ($actual * $userShiftDay->hourlyRate);
        }

        foreach ($userShiftDay->leaveRequestDays as $leaveRequestDay) {
            if (
                $leaveRequestDay->leaveRequest->repository->isCancelled()
                || $leaveRequestDay->leaveRequest->repository->isDeclined()
                || $leaveRequestDay->leaveRequest->repository->isReplaced()
            ) {
                continue;
            }

            $record = $this->getOrSetRecordFrom($entries, $leaveRequestDay->leaveRequest->typeExternalId);
            $record->hoursActual += $leaveRequestDay->actualHours;
            $record->hoursAdjusted += $leaveRequestDay->actualHours;
            $record->totalCost += $leaveRequestDay->actualHours * $userShiftDay->hourlyRate;
        }
    }

    private function fetchDataFromExcessTimeItemRuleWork(array &$entries, TimeSheetExcessTimeItemRuleWorkSplitItem $splitItem, float &$parentHours, float $rate): void
    {
        $record = $this->getOrSetRecordFrom($entries, $splitItem->externalId);

        $parentHours -= $splitItem->actualHours;
        $record->hoursActual += $splitItem->actualHours;

        if ($splitItem->ruleTypeClass::IS_PAID) {
            $record->totalCost += $splitItem->adjustedHours * $rate;
            $record->hoursAdjusted += $splitItem->adjustedHours;
        }
    }

    /**
     * @return object{code: string, hoursActual: float, hoursAdjusted: float, totalCost: float}
     */
    private function getOrSetRecordFrom(array &$entries, string|null $code): \stdClass
    {
        $code = $code ?? '-';

        if (!isset($entries[$code])) {
            $entry = new \stdClass();
            $entry->code = $code;
            $entry->hoursActual = 0;
            $entry->hoursAdjusted = 0;
            $entry->totalCost = 0;
            $entries[$code] = $entry;
        }

        return $entries[$code];
    }
}
