.row.mb
  .col-xs-12.text-md
    .label.col-xs-12.p(class='label-{{req.status.color}}') {{req.status.name}}

div(ng-if='!!req.cancellationRequest' ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/approve-cancel.html'" ng-init='$f = $ctrl.leave.approveCancel')

.row
  .col-xs-12
    .panel.panel-default.mt
      .panel-body
        .row(style='line-height: 0.8')
          .col-xs-12.mb-lg
            label Role/Shift:
            .text-md {{ req.userShift.role.name + ' (' + req.userShift.name + ')' }}

        .row(style='line-height: 0.8')
          .col-xs-12.col-sm-6.mb-lg(ng-if='isSameDate(req.start, req.end)')
            label Date:
            .text-md {{ req.start | _Elt$DateFormat }}
          .col-xs-12.col-sm-6.mb-lg(ng-if='!isSameDate(req.start, req.end)')
            label Period:
            .text-md {{ (req.start | _Elt$DateFormat) + ' to ' + (req.end | _Elt$DateFormat) }}
          .col-xs-12.col-sm-6.mb-lg(ng-if='!!req.startTime && !!req.endTime')
            label Time:
            .text-md {{ (req.startTime | _Elt$TimeFormat: false) + ' to ' + (req.endTime | _Elt$TimeFormat: false) }}

        .row(style='line-height: 0.8')
          .col-xs-12.col-sm-6.mb-lg
            label Hours taken:
            .text-md {{ req.hours | number: 2 }} hours
              span(ng-if='req.hours !== req.hoursCalculated') ({{ req.hoursCalculated | number: 2 }} hours FTE)
          .col-xs-12.col-sm-6.mb-lg(ng-if='req.balanceTotal === 0 || !!req.balanceTotal')
            label Estimated remaining balance after leave is taken:
            .text-md {{ req.balanceTotal | number: 2 }}h
            .h4.text-thin.mt-sm.mb0(ng-if='!!req.balanceAccrued && req.balanceAccrued > 0') ({{req.balanceAvailable}}h available + {{req.balanceAccrued}}h pro-rata)

        .row(style='line-height: 0.8' ng-if='!!req.reason && req.reason.length > 0')
          .col-xs-12
            label Reason:
            .text-md(ng-bind-html='req.reason | _Elt$NoteContent')

.row
  .col-xs-12.mb-lg
    elt-custom-content.mb(ng-model='req.customContent')

.row(ng-if='$ctrl.leave.getPendingModelTasks().length > 0')
  .col-xs-12
    h4.h4 Outstanding tasks
  .col-xs-12
    .list-group(ng-repeat='outstandingTask in $ctrl.leave.getPendingModelTasks()')
      div(ng-bind-html='outstandingTask.htmlDescription')
