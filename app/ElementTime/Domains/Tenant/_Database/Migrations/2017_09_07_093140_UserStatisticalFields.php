<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->char('gender', 1)->nullable();
            $table->char('ethnicity', 1)->nullable();
            $table->char('citizenshipType', 1)->nullable();
            $table->char('qualification', 1)->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'gender')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('gender');
            });
        }

        if (Schema::hasColumn('User', 'ethnicity')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('ethnicity');
            });
        }

        if (Schema::hasColumn('User', 'citizenshipType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('citizenshipType');
            });
        }

        if (Schema::hasColumn('User', 'qualification')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('qualification');
            });
        }
    }
};
