<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Actions\Tenant\PayRuns\PayRun\GetOpenAndFuturePayRunsForSchedule;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;

describe('Get payrun options from user role schedule', function () {
    it('does not return ended payruns', function () {
        $userRoleSchedule = UserRoleSchedule::q()
            ->constraints([
                ['UserRoleSchedule.type', '=', UserRoleScheduleType::ZeroBased]
            ])
            ->order('UserRoleSchedule.startDate DESC')
            ->first();

        $payRunOptions = GetOpenAndFuturePayRunsForSchedule::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            actor: SuperUserFlag::getOne()
        );

        expect($payRunOptions->first(fn (PayRun $payRun) => $payRun->isFinished()))->toBeNull();
    });

    it('does not return payrun where actor user is owner staff and is already submitted')->todo('Submit of zero based schedule not available yet');
});
