//.row
//  .col-xs-12
//    .list-group.b
//      .list-group-item.b0(ng-repeat='entry in req.timeline' class='list-group-item-{{entry.status.color}}' style='border-top-right-radius: 0 !important; border-top-left-radius: 0 !important; border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important; {{$index > 0 ? "border-top: 1px solid rgba(0, 0, 0, 0.12) !important;" : ""}}')
//        small {{ entry.dateTime | _Elt$DateTimeFormat : true : false }}
//        h5.h4.m0 {{ entry.description }}
//        .m0(ng-if='!!entry.reason && entry.reason.length > 0') {{ entry.reason }}

.row.mb(ng-if='!!req.workflow_expecting && !!req.workflow_expecting.steps && req.workflow_expecting.steps.length > 0')
  .col-xs-12(ng-repeat='step in req.workflow_expecting.steps')
    .mb-sm.text-sm(ng-if='$index === 0') Must be approved by:
    .mb-sm.text-sm(ng-if='$index > 0') And then by by:
    .list-group.b.mb-sm
      .list-group-item.list-group-item-default.b0(style='{{step.status !== "active" ? "opacity: .9 !important;" : ""}} border-top-right-radius: 0 !important; border-top-left-radius: 0 !important; border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;')
        div(ng-repeat='block in step.blocks')
          div(ng-if='$index > 0')
            hr.hr.mt.mb0
            .text-center
              small OR
            hr.hr.mt0.mb-lg
          div(ng-repeat='item in block')
            div(ng-if='$index > 0') and
            h5.h4.mt0.mb-sm(ng-bind-html='item.details')
            elt-staff-list(elt-list='item.users', elt-size='28' elt-item-margin-top='' elt-item-margin-bottom='sm')
