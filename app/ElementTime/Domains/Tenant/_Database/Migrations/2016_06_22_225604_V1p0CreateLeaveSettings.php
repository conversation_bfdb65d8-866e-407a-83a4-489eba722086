<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('Settings', function (Blueprint $table) {
            // Leave Settings
            $table->boolean('staffCanUseProRataLeave')->nullable();
            $table->boolean('showProRataLeaveBalance')->nullable();
            $table->boolean('showEntitledLeaveBalance')->nullable();
            $table->boolean('showTotalLeaveBalance')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('Settings', function (Blueprint $table) {
            $table->dropColumn([
                // Leave Settings
                'staffCanUseProRataLeave',
                'showProRataLeaveBalance',
                'showEntitledLeaveBalance',
                'showTotalLeaveBalance',
            ]);
        });
    }
};
