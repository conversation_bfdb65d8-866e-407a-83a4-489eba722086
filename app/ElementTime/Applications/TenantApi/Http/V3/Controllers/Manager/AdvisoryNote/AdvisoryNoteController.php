<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V3\Controllers\Manager\AdvisoryNote;

use Element\Core\Http\JsonResponse;
use Element\ElementTime\Applications\TenantApi\Http\V3\Controllers\BaseController;
use Element\ElementTime\Applications\TenantApi\Http\V3\Resources\AdvisoryNote\AdvisoryNoteBasicResource;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;

class AdvisoryNoteController extends BaseController
{
    /** @throws \Throwable */
    public function getTimeSheetAdvisoryNotes(string $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timeSheet = TimeSheet::q()->findOrFail($id);
        $notes = $timeSheet->repository->getAllAdvisoryNotes($this->loggedUser);

        return JsonResponse::fetch(AdvisoryNoteBasicResource::collection($notes));
    }
}
