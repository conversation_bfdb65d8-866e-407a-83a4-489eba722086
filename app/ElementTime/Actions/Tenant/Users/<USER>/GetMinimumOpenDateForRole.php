<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Users\UserRole;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Users\BaseUsersAction;
use Element\ElementTime\Actions\Tenant\Users\User\GetEarliestOpenTimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

class GetMinimumOpenDateForRole extends BaseUsersAction
{
    /** @throws \Throwable */
    public function handle(UserRole $userRole, User|null $actor = null): CarbonInterface
    {
        $startDate = GetEarliestOpenTimeSheet::make()->handle(
            user: $userRole->user,
            actor: $actor,
        )?->startDate;

        if (is_null($startDate)) {
            $startDate = TenantSystemSettings::getLatestPayRun()->next->startDate->copy();
        }

        return Carbon::parse(max($startDate, $userRole->startDate, $userRole->user->startDate));
    }
}
