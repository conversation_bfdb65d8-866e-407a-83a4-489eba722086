<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->boolean('isEligibleForPenalties')->default(false)->after('isEligibleForAllowances');
        });

        if (Schema::hasColumn('User', 'isEligibleForAdditionalDuties')) {
            $users = \Illuminate\Support\Facades\DB::table('User')->get();

            foreach ($users as $user) {
                \Illuminate\Support\Facades\DB::table('User')->where('id', '=', $user->id)->update([
                    'isEligibleForPenalties' => $user->isEligibleForAdditionalDuties,
                ]);
            }
        }

        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->bigInteger('onBehalfUserRole_id')->unsigned()->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->bigInteger('onBehalfUserRole_id')->unsigned()->change();
        });

        if (Schema::hasColumn('User', 'isEligibleForPenalties')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('isEligibleForPenalties');
            });
        }
    }
};
