<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V3\Controllers\Plant;

use Element\Core\Http\JsonResponse;
use Element\ElementTime\Applications\TenantApi\Http\V3\Controllers\BaseController;
use Element\ElementTime\Applications\TenantApi\Http\V3\Resources\Plant\PlantTimeBasicResource;
use Element\ElementTime\Applications\TenantApi\Http\V3\Resources\Plant\TimeSheetDayPlantConfigResource;
use Element\ElementTime\Applications\TenantApi\Http\V3\Resources\Plant\TimeSheetPlantSummaryResource;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantItem;
use Element\ElementTime\Domains\Tenant\Plant\Traits\PlantSheetDayTimeApplicableTrait;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheetDayRepository;
use Illuminate\Database\Eloquent\Collection;

class PlantController extends BaseController
{
    use PlantSheetDayTimeApplicableTrait;

    /** @throws \Throwable */
    public function getTimeSheetDayPlantConfig(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $relations = TimeSheetDayPlantConfigResource::getRelations();

        if ($id == 'current') {
            $timesheetDay = TimeSheetDayRepository::getMyTodayRecord($relations);
        } else {
            $timesheetDay = TimeSheetDay::q()
                ->relations($relations)
                ->findOrFail($id);
        }

        return JsonResponse::fetch(TimeSheetDayPlantConfigResource::make($timesheetDay));
    }

    /** @throws \Throwable */
    public function getPlantTimesInTimeSheetDay(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        if ($id == 'current') {
            $timesheetDay = TimeSheetDayRepository::getMyTodayRecord();
        } else {
            $timesheetDay = TimeSheetDay::q()->findOrFail($id);
        }

        $entries = new Collection;

        foreach ($timesheetDay->plantTimes as $entry) {
            if (!is_null($entry->timeSheetDayTimeWork_id) || !is_null($entry->timeSheetDayTime_id)) {
                continue;
            }

            $entries->push($entry);
        }

        $entries->load(PlantTimeBasicResource::getRelations());

        return JsonResponse::fetch(PlantTimeBasicResource::collection($entries));
    }

    /** @throws \Throwable */
    public function getTimesheetPlantSummary(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timesheet = TimeSheet::q()
            ->relations(TimeSheetPlantSummaryResource::getRelations())
            ->findOrFail($id);

        $this->validateAuthorization($timesheet, doesAllowOwner: false);

        return JsonResponse::fetch(TimeSheetPlantSummaryResource::make($timesheet));
    }

    /** @throws \Throwable */
    public function postPlantTime(string $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timeSheetDay = TimeSheetDay::q()->findOrFail($this->i('timeSheetDay_id'));
        $plantItem = PlantItem::q()->findOrFail($this->i('plantItem_id'));

        $plantTimes = $this->i('plant_times', []);

        $this->recordPlantSheetDayTimes($plantTimes, $timeSheetDay, true, $plantItem);

        return JsonResponse::fetch(new \stdClass);
    }
}
