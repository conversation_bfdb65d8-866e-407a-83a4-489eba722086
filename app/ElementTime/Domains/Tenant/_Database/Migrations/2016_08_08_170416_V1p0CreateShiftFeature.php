<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ShiftCategory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Category data
            $table->string('name')->unique('ShiftCategory_Name_Index');
            $table->boolean('areShiftsExclusive')->nullable();
            $table->boolean('areShiftsContinuous')->nullable();
            $table->boolean('isPeriod24h')->nullable();
            $table->time('periodStartsAt')->nullable();
            $table->time('periodEndsAt')->nullable();
            $table->boolean('haveUsersMaximumWorkTime')->nullable();
            $table->float('hoursUserCanWork')->nullable();
            $table->float('hoursUserCanWorkPeriod')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('ShiftLocation', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Category data
            $table->string('name')->unique('ShiftLocation_Name_Index');

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('Shift', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Shift Data
            $table->string('name')->unique('Shift_Name_Index');
            $table->bigInteger('shiftCategory_id')->unsigned();
            $table->text('description')->nullable();
            $table->bigInteger('manager_id')->unsigned();
            $table->char('status', 1)->nullable();
            $table->char('type', 1)->nullable();

            // Fixed type fields
            $table->text('fixedTime')->nullable();
            $table->text('fixedBreaks')->nullable();

            // Variable type fields
            $table->text('variableTime')->nullable();

            // Flexible type fields
            $table->text('flexibleTime')->nullable();
            $table->float('flexibleMinimumHours')->nullable();
            $table->text('flexibleBreaks')->nullable();

            // Open type fields
            $table->float('openDuration')->nullable();
            $table->boolean('openCanEditDuration')->nullable();
            $table->float('openBreakDuration')->nullable();

            // Default days
            $table->boolean('hasDefaultDays')->nullable();
            $table->text('defaultDaysTo')->nullable();

            // Location
            $table->boolean('hasFixedLocation')->nullable();
            $table->bigInteger('shiftLocation_id')->unsigned()->nullable();
            $table->integer('minimumStaffAtLocation')->nullable();
            $table->integer('minimumStaffPerSchedule')->nullable();

            // Public Holidays
            $table->boolean('worksOnPublicHolidays')->nullable();

            // RDO
            $table->boolean('allowsRosteredTimeOff')->nullable();
            $table->bigInteger('rosteredTimeOffType_id')->unsigned()->nullable();

            // Life span
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            // Reminders
            $table->boolean('sendReminders')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('shiftCategory_id', 'shift_shiftcategory_fn')->references('id')->on('ShiftCategory')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'shift_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('shiftLocation_id', 'shift_shiftlocation_fn')->references('id')->on('ShiftLocation')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOffType_id', 'shift_rdo_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::table('Settings', function (Blueprint $table) {
            // Shift Permissions
            $table->boolean('allowsStaffToApproveScheduleChange')->nullable();
            $table->boolean('allowsManagersToScheduleDirectReports')->nullable();
            $table->boolean('allowsAllShiftGapAlerts')->nullable();
        });

        Schema::create('UserShift', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('shift_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('user_id', 'user_shift_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('shift_id', 'user_shift_shift_fn')->references('id')->on('Shift')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserShiftDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigInteger('userShift_id')->unsigned();
            $table->date('date');
            $table->primary(['userShift_id', 'date']);

            $table->char('type', 1)->nullable();
            $table->bigInteger('userLeaveType_id')->unsigned()->nullable();
            $table->bigInteger('rosteredTimeOffType_id')->unsigned()->nullable();
            $table->bigInteger('publicHoliday_id')->unsigned()->nullable();
            $table->bigInteger('ELT_PublicHoliday_id')->unsigned()->nullable();
            $table->text('value')->nullable();
            $table->boolean('isEditable')->nullable();

            $table->foreign('userShift_id', 'usershiftday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userLeaveType_id', 'usershiftday_leave_fn')->references('id')->on('UserLeaveType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOffType_id', 'usershiftday_rdo_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('ShiftCategory');
        Schema::drop('ShiftLocation');
        Schema::drop('Shift');
        Schema::table('Settings', function (Blueprint $table) {
            $table->dropColumn([
                'allowsStaffToApproveScheduleChange',
                'allowsManagersToScheduleDirectReports',
                'allowsAllShiftGapAlerts',
            ]);
        });
        Schema::drop('UserShift');
        Schema::drop('UserShiftDay');
    }
};
