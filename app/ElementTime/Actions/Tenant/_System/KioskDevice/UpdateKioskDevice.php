<?php

namespace Element\ElementTime\Actions\Tenant\_System\KioskDevice;

use Element\ElementTime\Actions\Tenant\_System\BaseSystemAction;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDevice;

class UpdateKioskDevice extends BaseSystemAction
{
    /** @throws \Throwable */
    public function handle(
        KioskDevice $kioskDevice,
        string|null $name,
        string|null $localization,
    ): KioskDevice
    {
        if (!is_null($name)) {
            $kioskDevice->name = $name;
        }

        if (!is_null($localization)) {
            $kioskDevice->localization = $localization;
        }

        // TODO: @isaac send socket
        $kioskDevice->saveOrFail();

        return $kioskDevice;
    }
}
