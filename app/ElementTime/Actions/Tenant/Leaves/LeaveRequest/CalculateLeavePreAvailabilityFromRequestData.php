<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest;

use Carbon\Carbon;
use Element\Core\Exceptions\ElementException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleZeroBased;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;

class CalculateLeavePreAvailabilityFromRequestData extends BaseLeavesAction
{
    /**
     * @throws \Throwable
     */
    public function handle(
        User $actorUser,
        array $data,

        // Out parameters - to be used in places where values are needed again after process of availability calculation
        UserRoleSchedule|null &$userRoleSchedule = null,
        UserLeaveBankType|null &$userLeaveBankType = null,
        LeaveRequest|null &$replaced = null,
    ): StructLeaveRequestPreAvailability
    {
        $scheduleData = $data['schedule'] ?? [];
        $bankTypeData = $data['bankType'] ?? [];

        $userRoleSchedule = UserRoleSchedule::q()->findOrFail($scheduleData['id'] ?? null);
        $userLeaveBankType = UserLeaveBankType::q()->findOrFail($bankTypeData['id'] ?? null);

        if (isset($data['replaced_id'])) {
            $replaced = LeaveRequest::q()->findOrFail($data['replaced_id']);
        }

        if ($userRoleSchedule->userRole->user->id !== $userLeaveBankType->bank->user->id) {
            throw new ElementException('Invalid user role schedule and bank type combination');
        }

        if (!$userLeaveBankType->bank->user->hasLeaveOptions) {
            throw new ElementException('Not allowed to take leave');
        }

        if ($userLeaveBankType->status != ActiveStatus::ID) {
            throw new ElementException('Leave type is disabled');
        }

        if (!$userLeaveBankType->canTakeLeave) {
            throw new ElementException('Leave type cannot be used to take leave');
        }

        switch ($userRoleSchedule->type) {
            case UserRoleScheduleType::WorkPattern:
                $isApplyingByDuration = ($data['isApplyingByDuration'] ?? false) === true;
                $startDate = Carbon::parse($data['startDate']);

                if ($data['isFullDay']) {
                    $endDate = Carbon::parse($data['endDate']);
                } else {
                    $startTime = Carbon::parse($data['startTime'])->toTimeString();
                    $endTime = null;
                    $duration = null;

                    if ($isApplyingByDuration) {
                        $duration = TimeDuration::parseFromHours($data['duration']);
                    } else {
                        $endTime = Carbon::parse($data['endTime'])->toTimeString();
                    }
                }

                return CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
                    userLeaveBankType: $userLeaveBankType,
                    userRoleSchedule: $userRoleSchedule,
                    isFullDay: $data['isFullDay'],
                    startDate: $startDate,
                    endDate: $endDate ?? null,
                    dailyStartTime: $startTime ?? null,
                    dailyEndTime: $endTime ?? null,
                    dailyDuration: $duration ?? null,
                    replaced: $replaced ?? null,
                    actorUser: $actorUser,
                );
            case UserRoleScheduleType::ZeroBased:
                $startDate = Carbon::parse($data['payRun']['startDate']);
                $duration = TimeDuration::parseFromHours($data['duration']);

                return CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
                    userLeaveBankType: $userLeaveBankType,
                    userRoleSchedule: $userRoleSchedule,
                    startDate: $startDate,
                    duration: $duration,
                    replaced: $replaced ?? null,
                    actorUser: $actorUser,
                );
            case UserRoleScheduleType::DurationOnly:
                $startDate = Carbon::parse($data['startDate']);
                $endDate = Carbon::parse($data['endDate']);
                $dailyDuration = TimeDuration::parseFromHours($data['dailyDuration']);

                return CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
                    userLeaveBankType: $userLeaveBankType,
                    userRoleSchedule: $userRoleSchedule,
                    startDate: $startDate,
                    endDate: $endDate,
                    dailyDuration: $dailyDuration,
                    replaced: $replaced ?? null,
                    actorUser: $actorUser,
                );
            default:
                throw new \InvalidArgumentException('Schedule type not defined or not supported: ' . $userRoleSchedule->type);
        }
    }
}
