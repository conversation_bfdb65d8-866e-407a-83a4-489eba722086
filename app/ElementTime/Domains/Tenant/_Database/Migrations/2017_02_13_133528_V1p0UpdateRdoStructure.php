<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            // Remove old fields
            if (Schema::hasColumn('RosteredTimeOffType', 'cyclePeriodAmount')) {
                $table->dropColumn('cyclePeriodAmount');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'cyclePeriodType')) {
                $table->dropColumn('cyclePeriodType');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'cyclePeriodStartsOn')) {
                $table->dropColumn('cyclePeriodStartsOn');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'receivePerPeriodAmount')) {
                $table->dropColumn('receivePerPeriodAmount');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'receivePerPeriodType')) {
                $table->dropColumn('receivePerPeriodType');
            }

            // Formula
            $table->decimal('formulaValueFrom', 16, 10);
            $table->char('formulaPeriodFrom', 1);
            $table->decimal('formulaValueTo', 16, 10);
            $table->char('formulaPeriodTo', 1);
            $table->decimal('formulaMinHours', 16, 10);
            $table->decimal('formulaMaxHours', 16, 10);

            // Relationship with OvertimeType and ToilType
            $table->boolean('hasOvertime')->default(false);
            $table->bigInteger('overtimeType_id')->unsigned()->nullable();
            $table->boolean('hasToil')->default(false);
            $table->bigInteger('toilType_id')->unsigned()->nullable();

            $table->foreign('overtimeType_id', 'rdot_ot_fn')->references('id')->on('OvertimeType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('toilType_id', 'rdot_tt_fn')->references('id')->on('ToilType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserRosteredTimeOffType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('rosteredTimeOffType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->text('comment')->nullable();
            $table->longText('scheduledDates');
            // Array format with dates (used only if related RosteredTimeOffType is Fixed):
            //  [
            //      "2017-04-05",
            //      "2017-08-02",
            //      ...
            //  ]

            $table->char('isFlexibleOrFixed', 1)->nullable();
            // Options:
            //  F => Flexible
            //  D => Fixed

            $table->char('status', 1)->default('A');
            // Options:
            //  A => Active
            //  I => Inactive

            $table->decimal('accruedBalance', 16, 10)->nullable();
            $table->decimal('committedBalance', 16, 10)->nullable();
            $table->decimal('historicBalance', 16, 10)->nullable();

            $table->foreign('rosteredTimeOffType_id', 'urtot_rtot_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'urtot_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('RosteredTimeOff', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userRosteredTimeOffType_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();
            $table->bigInteger('userShiftDay_id')->unsigned();

            $table->boolean('isScheduled')->default(false);

            $table->date('date')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->string('name')->nullable();
            $table->text('reason')->nullable();

            $table->longText('approvalWorkflowMap')->nullable(); // From Settings->workflowLeaveApproval (duplicated to keep the current map)

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userRosteredTimeOffType_id', 'rto_urtot_fn')->references('id')->on('UserRosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'rto_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShiftDay_id', 'rto_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('RosteredTimeOffApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('rosteredTimeOff_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->nullable();
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1); // The number of levels and the user type of each manager depends on Settings.workflowLeaveApproval map
            $table->text('notes')->nullable();

            $table->char('status', 1)->nullable(); // (W)aiting approval | (A)pproved | (D)isapproved

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('rosteredTimeOff_id', 'rtoapp_rto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'rtoapp_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userRosteredTimeOffType_id')->unsigned();

            $table->dateTime('entryDateTime');
            $table->char('entryType', 2);
            // Possible Values:
            // - (MA) Manually Add Balance
            // - (AA) Automatically Add Balance (relate to TimeSheet)
            // - (MR) Manually Remove Balance
            // - (CM) Commit (relate to RosteredTimeOff)
            // - (MC) Manually Cancel (relate to User [payroll officer] and another UserRosteredTimeOffTypeEntry)
            // - (AC) Automatically Cancel (relate to another UserRosteredTimeOffTypeEntry)
            $table->decimal('entryValue', 16, 10);
            $table->text('description')->nullable();

            $table->bigInteger('previous_id')->unsigned()->nullable();
            $table->decimal('previousAccruedBalance', 16, 10);
            $table->decimal('previousCommittedBalance', 16, 10)->unsigned();
            $table->decimal('previousHistoricBalance', 16, 10)->unsigned();

            $table->decimal('currentAccruedBalance', 16, 10);
            $table->decimal('currentCommittedBalance', 16, 10)->unsigned();
            $table->decimal('currentHistoricBalance', 16, 10)->unsigned();

            $table->bigInteger('timeSheet_id')->unsigned()->nullable();
            $table->bigInteger('rosteredTimeOff_id')->unsigned()->nullable();
            $table->bigInteger('userRosteredTimeOffTypeEntry_id')->unsigned()->nullable();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->foreign('userRosteredTimeOffType_id', 'urtote_rtot_fn')->references('id')->on('UserRosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('previous_id', 'urtote_prevurtote_fn')->references('id')->on('UserRosteredTimeOffTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            $table->foreign('timeSheet_id', 'urtote_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOff_id', 'urtote_rto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userRosteredTimeOffTypeEntry_id', 'urtote_urtote_fn')->references('id')->on('UserRosteredTimeOffTypeEntry')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'urtote_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UserRosteredTimeOffTypeEntry');

        Schema::dropIfExists('RosteredTimeOffApproval');

        Schema::dropIfExists('RosteredTimeOff');

        Schema::dropIfExists('UserRosteredTimeOffType');

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaValueFrom')) {
                $table->dropColumn('formulaValueFrom');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaPeriodFrom')) {
                $table->dropColumn('formulaPeriodFrom');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaValueTo')) {
                $table->dropColumn('formulaValueTo');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaPeriodTo')) {
                $table->dropColumn('formulaPeriodTo');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaMinHours')) {
                $table->dropColumn('formulaMinHours');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'formulaMaxHours')) {
                $table->dropColumn('formulaMaxHours');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'hasOvertime')) {
                $table->dropColumn('hasOvertime');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'overtimeType_id')) {
                $table->dropForeign('rdot_ot_fn');
                $table->dropColumn('overtimeType_id');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'hasToil')) {
                $table->dropColumn('hasToil');
            }
            if (Schema::hasColumn('RosteredTimeOffType', 'toilType_id')) {
                $table->dropForeign('rdot_tt_fn');
                $table->dropColumn('toilType_id');
            }
        });
    }
};
