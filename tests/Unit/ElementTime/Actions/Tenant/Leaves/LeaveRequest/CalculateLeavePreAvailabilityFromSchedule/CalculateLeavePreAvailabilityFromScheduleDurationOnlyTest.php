<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Element\Core\Support\ProcessCache;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\HolidayConditionType\HolidayConditionType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\CalendarDate;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\HolidayCondition;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayDurationOnly;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayGroupType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Domains\Type\StatusTypes\InactiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\TimeSheets\TimeSheetWorkFunctionality;

uses(TimeSheetWorkFunctionality::class);
uses(LeaveGeneralFunctionality::class);

describe('Duration-only - Calculate leave pre availability', function () {
    test('inactive user leave bank type does not return availability', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->status = InactiveStatus::ID;

        CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );
    })->throws(\InvalidArgumentException::class, 'Leave assignment cannot be used to take leave');

    test('cannot calculate with incompatible schedule type', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $userRoleSchedule->type = UserRoleScheduleType::WorkPattern;
        $userRoleSchedule->clearSave();

        CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );
    })->throws(\InvalidArgumentException::class, 'This schedule is not a duration only');

    test('cannot calculate when leave start date is not compatible with schedule availability', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $userRoleSchedule->startDate = $date->copy()->addDay();
        $userRoleSchedule->clearSave();

        CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date->copy()->addDays(7),
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );
    })->throws(\InvalidArgumentException::class, 'Leave is starting before the start of schedule assignment');

    test('cannot calculate when leave end date is not compatible with schedule availability', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $userRoleSchedule->endDate = $date->copy()->addDays(4);
        $userRoleSchedule->clearSave();

        CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date->copy()->addDays(7),
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );
    })->throws(\InvalidArgumentException::class, 'Leave is ending after the end of schedule assignment');

    test('cannot be requested on non scheduled day', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate, isScheduled: false);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $date = null;
        foreach ($days as $day) {
            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if (!$day->type->isScheduled) {
                $date = $day->date->copy();
                break;
            }
        }

        if (is_null($date)) {
            expect(true)->not()->toBeTrue('Not found non scheduled day - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $date,
            $date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );
        expect($availability->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    test('can be requested in an open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->duration)->toEqual(TimeDuration::parseFromHours(1))
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
        ;
    });

    it('calculating leave starting on non scheduled day adjusts calculation start date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $nonScheduledStartDate = null;
        $scheduledNextDate = null;
        foreach ($days as $index => $day) {
            if (!isset($days[$index + 1])) {
                break;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            $nextDay = $days[$index + 1];
            if (
                $day->type instanceof ScheduledPayRunPeriodDayDurationOnly && !$day->type->isScheduled
                && $nextDay->type instanceof ScheduledPayRunPeriodDayDurationOnly && $nextDay->type->isScheduled
            ) {
                $nonScheduledStartDate = $day->date->copy();
                $scheduledNextDate = $days[$index + 1]->date->copy();
            }
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $nonScheduledStartDate,
            $scheduledNextDate,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->duration)->toEqual(TimeDuration::parseFromHours(1))
            ->and($availability->startDate)->toEqual($scheduledNextDate)
            ->and($availability->endDate)->toEqual($scheduledNextDate)
            ->and($availability->doesHaveDayTimeAdjustments)->toBeTrue()
        ;
    });

    test('cannot be requested on public holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        $calendarDate = CalendarDate::getByDate($scheduledDayData->date);
        $publicHoliday = new PublicHoliday;
        $publicHoliday->date = $scheduledDayData->date->copy();
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();

        $availabilityAfterPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availabilityAfterPublicHolidayCreation->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    test('cannot be requested on special holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, [
            ['IS NOT NULL', 'User.payGroupType_id']
        ], date: $payRun->startDate);

        if (is_null($user) || is_null($days) || count($days) < 1) {
            expect(true)->not()->toBeTrue('Not found user or scheduled day');
        }

        if (is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            expect(true)->not()->toBeTrue('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        $calendarDate = CalendarDate::getByDate($scheduledDayData->date->copy());
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $scheduledDayData->date->copy();
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType->name]]];
        $holidayCondition->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();

        $availabilityAfterHolidayCreation = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date,
            $scheduledDayData->date,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availabilityAfterHolidayCreation->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    test('cannot be requested if sum of placed leave hours and new request duration exceeds fixed duration of day', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $maxDuration = TimeDuration::parseFromHours(20);
        $dailyDuration = TimeDuration::parseFromHours(11);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        /** @var ScheduledPayRunPeriodDayDurationOnly $scheduledPayRunPeriodDayDurationOnly */
        $scheduledPayRunPeriodDayDurationOnly = $scheduledDayData->type;
        $scheduledPayRunPeriodDayDurationOnly->maxDuration = $maxDuration->copy();
        $scheduledPayRunPeriodDayDurationOnly->clearSave();

        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->doesHaveDailyFixedHours = true;
        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->dailyMaxDuration = $maxDuration->copy();
        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->clearSave();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
                $userLeaveBankType,
                $userRoleSchedule,
                $scheduledDayData->date->copy(),
                $scheduledDayData->date->copy(),
                dailyDuration: $dailyDuration,
                actorUser: $actorUser,
            ),
            actorUser: $actorUser,
        );
        $preAvailability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        expect($preAvailability->canBeRequested)->toBeFalse()
            ->and($preAvailability->errorMessages)->toHaveCount(1);
        // TODO: Validate type of error
    });

    test('cannot be requested if sum of worked hours hours and new request duration exceeds 24 hours', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $dailyDuration = TimeDuration::parseFromHours(13);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        /** @var ScheduledPayRunPeriodDayDurationOnly $scheduledPayRunPeriodDayDurationOnly */
        $scheduledPayRunPeriodDayDurationOnly = $scheduledDayData->type;
        $scheduledPayRunPeriodDayDurationOnly->maxDuration = TimeDuration::parseFromHours(99);
        $scheduledPayRunPeriodDayDurationOnly->clearSave();

        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->doesHaveDailyFixedHours = false;
        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->dailyMaxDuration = TimeDuration::parseFromHours(99);
        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->dailyMinDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayDurationOnly->scheduledPayRunPeriodDay->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->durationOnly->clearSave();

        PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
                $userLeaveBankType,
                $userRoleSchedule,
                $scheduledDayData->date->copy(),
                $scheduledDayData->date->copy(),
                dailyDuration: $dailyDuration,
                actorUser: $actorUser,
            ),
            actorUser: $actorUser,
        );
        $preAvailability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDayData->date->copy(),
            $scheduledDayData->date->copy(),
            dailyDuration: $dailyDuration,
            actorUser: $actorUser,
        );

        expect($preAvailability->canBeRequested)->toBeFalse()
            ->and($preAvailability->errorMessages)->toHaveCount(1);
        // TODO: Validate type of error
    });

    test('move start date if does start on non scheduled day', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $expectedStartDate = null;
        $endDate = null;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = TimeDuration::zero();

        foreach ($days as $day) {
            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if ($day->type->isScheduled && !is_null($startDate)) {
                if (is_null($expectedStartDate)) {
                    $expectedStartDate = $day->date->copy();
                } elseif (is_null($endDate)) {
                    $endDate = $day->date->copy();
                }
                $expectedDuration->add($dailyDuration->copy());
            } elseif (!$day->type->isScheduled && is_null($startDate)) {
                $startDate = $day->date->copy();
            }

            if (!is_null($startDate) && !is_null($expectedStartDate) && !is_null($endDate)) {
                break;
            }
        }

        if (is_null($startDate)) {
            expect(true)->not()->toBeTrue('Not found non scheduled day - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration->copy(),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->not()->toEqual($startDate)
            ->and($availability->startDate)->toEqual($expectedStartDate)
            ->and($availability->endDate)->toEqual($endDate)
            ->and($availability->duration)->toEqual($expectedDuration);
    });

    test('move start date if does start on non public holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $expectedStartDate = null;
        $endDate = null;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = TimeDuration::zero();

        foreach ($days as $day) {
            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if ($day->type->isScheduled) {
                if (is_null($startDate)) {
                    $startDate = $day->date;
                    $expectedDuration->add($dailyDuration->copy());
                } elseif (is_null($expectedStartDate)) {
                    $expectedStartDate = $day->date;
                    $expectedDuration->add($dailyDuration->copy());
                } elseif (is_null($endDate)) {
                    $endDate = $day->date;
                    break;
                }
            }
        }

        $availabilityBeforeHolidayCreation = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration->copy(),
            actorUser: SuperUserFlag::getOne()
        );

        $calendarDate = CalendarDate::getByDate($startDate->copy());
        $publicHoliday = new PublicHoliday;
        $publicHoliday->date = $startDate->copy();
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();


        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration->copy(),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availabilityBeforeHolidayCreation->startDate)->not()->toEqual($availability->startDate)
            ->and($availability->startDate)->not()->toEqual($startDate)
            ->and($availability->startDate)->toEqual($expectedStartDate)
            ->and($availability->endDate)->toEqual($endDate)
            ->and($availability->duration)->toEqual($expectedDuration);
    });

    test('move start date if does start on non special holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $expectedStartDate = null;
        $endDate = null;
        $dailyDuration = TimeDuration::parseFromHours(4);
        $expectedDuration = TimeDuration::zero();

        foreach ($days as $day) {
            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if ($day->type->isScheduled) {
                if (is_null($startDate)) {
                    $startDate = $day->date;
                    $expectedDuration->add($dailyDuration->copy());
                } elseif (is_null($expectedStartDate)) {
                    $expectedStartDate = $day->date;
                    $expectedDuration->add($dailyDuration->copy());
                } elseif (is_null($endDate)) {
                    $endDate = $day->date;
                    break;
                }
            }
        }

        $availabilityBeforeHolidayCreation = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration,
            actorUser: SuperUserFlag::getOne()
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $startDate;
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType?->name]]];
        $holidayCondition->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: $dailyDuration,
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availabilityBeforeHolidayCreation->startDate)->not()->toEqual($availability->startDate)
            ->and($availability->startDate)->not()->toEqual($startDate)
            ->and($availability->startDate)->toEqual($expectedStartDate)
            ->and($availability->endDate)->toEqual($endDate)
            ->and($availability->duration)->toEqual($expectedDuration)
        ;
    });

    test('move end date if does start on non scheduled day', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $expectedEndDate = null;
        $endDate = null;

        foreach ($days as $day) {
            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if ($day->type->isScheduled) {
                if (is_null($startDate)) {
                    $startDate = $day->date->copy();
                }

                $expectedEndDate = $day->date->copy();
            } else {
                if (is_null($endDate)) {
                    $endDate = $day->date->copy();
                    break;
                }
            }
        }

        if (is_null($startDate)) {
            expect(true)->not()->toBeTrue('Not found non scheduled day - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($startDate)
            ->and($availability->endDate)->not()->toEqual($endDate)
            ->and($availability->endDate)->toEqual($expectedEndDate);
    });

    test('move end date if does start on non public holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $endDate = null;
        $expectedEndDate = null;
        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if (is_null($startDate)) {
                $startDate = $day->date->copy();
            } elseif (is_null($expectedEndDate)) {
                $expectedEndDate = $day->date->copy();
            } elseif (is_null($endDate)) {
                $endDate = $day->date->copy();
            }
        }

        if (is_null($startDate)) {
            expect(true)->not()->toBeTrue('Not found non scheduled day - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $calendarDate = CalendarDate::getByDate($endDate->copy());
        $publicHoliday = new PublicHoliday;
        $publicHoliday->date = $endDate->copy();
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($startDate)
            ->and($availability->endDate)->not()->toEqual($endDate)
            ->and($availability->endDate)->toEqual($expectedEndDate);
    });

    test('move end date if does start on non special holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::DurationOnly, date: $payRun->startDate);
        $publicHolidays = PublicHoliday::q()->many();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $startDate = null;
        $endDate = null;
        $expectedEndDate = null;
        foreach ($days as $day) {
            if (!$day->type->isScheduled) {
                continue;
            }

            if (!is_null($publicHolidays->firstWhere('date', '=', $day->date))) {
                continue;
            }

            if (is_null($startDate)) {
                $startDate = $day->date->copy();
            } elseif (is_null($expectedEndDate)) {
                $expectedEndDate = $day->date->copy();
            } elseif (is_null($endDate)) {
                $endDate = $day->date->copy();
            }
        }

        if (is_null($startDate)) {
            expect(true)->not()->toBeTrue('Not found non scheduled day - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $calendarDate = CalendarDate::getByDate($endDate);
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $endDate;
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType->name]]];
        $holidayCondition->clearSave();

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();
        $userRoleSchedule->holidayCalculations->doesWorkOnHolidays = false;
        $userRoleSchedule->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            $userLeaveBankType,
            $userRoleSchedule,
            $startDate,
            $endDate,
            dailyDuration: TimeDuration::parseFromHours(1),
            actorUser: SuperUserFlag::getOne()
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($startDate)
            ->and($availability->endDate)->not()->toEqual($endDate)
            ->and($availability->endDate)->toEqual($expectedEndDate);
    });
});
