<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\Carbon;
use Element\Core\Support\ProcessCache;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityWorkPattern;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\CreateTimeSheetWork;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\HolidayConditionType\HolidayConditionType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\LeaveCalculationType\LeaveCalculationType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\CalendarDate;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\HolidayCondition;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShiftDay;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayGroupType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleProject;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Enums\RecordedVia;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Support\Collection;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\TimeSheets\TimeSheetWorkFunctionality;

uses(TimeSheetWorkFunctionality::class);
uses(LeaveGeneralFunctionality::class);

/*
 * @todo
 *      Test might fail if majority of record has UserRoleSchedulePatternPeriodShiftDay.isScheduled as true
 *      Workaround applied:
 *           UPDATE - Historic schedule shift:
 *               update period fixed hours if its zero by default;
 *               update daily min/max/expected or daily hours - it was all zero;
 *               ScheduleShift/Days isScheduled to true - if weekday
 *           UPDATE - Update UserRoleSchedulePatternPeriodShiftDay records
 *              $userRoleSchedulePatternPeriodShiftDay.isScheduled = $userRoleSchedulePatternPeriodShiftDay.scheduleShiftDay.isScheduled
 * */
describe('Work pattern - Calculate leave pre availability', function () {
    beforeEach(function () {
        LeaveRequest::query()->whereNotNull('LeaveRequest.userRoleSchedule_id')->delete();
    });

    it('cannot calculate leave outside of scheduled work hours', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('01:00:00', '03:00:00', $scheduledDayData->date);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;

        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = false;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->duration = $ordinaryTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    it('can calculate single part day using start and end time in an open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('09:00:00', '16:00:00', $scheduledDayData->date);
        $expectedLeaveDuration = $leaveTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;

        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->days[0]->timeSpan->getStart())->toEqual($leaveTimeSpan->getStart())
            ->and($availability->days[0]->timeSpan->getEnd())->toEqual($leaveTimeSpan->getEnd())
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($expectedLeaveDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('calculate single day leave with start and end time outside of scheduled hours adjusts according to scheduled time span', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '20:00:00', $scheduledDayData->date);
        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('00:00:00', '23:00:00', $scheduledDayData->date);
        $expectedLeaveDuration = $ordinaryTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration()->getValue());

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;

        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->doesHaveDayTimeAdjustments)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->timeSpan->getStart())->toEqual($ordinaryTimeSpan->getStart())
            ->and($availability->days[0]->timeSpan->getEnd())->toEqual($ordinaryTimeSpan->getEnd())
            ->and($availability->days[0]->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($expectedLeaveDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('deducts duration if it overflows ordinary span after breaks calculation on part day leave', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '17:00:00', $scheduledDayData->date);
        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('15:00:00', '16:00:00', $scheduledDayData->date);
        $leaveStartTime = '14:00:00';
        $duration = TimeDuration::parseFromHours(4);
        $expectedLeaveDuration = TimeDuration::parseFromHours(2);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;

        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: '14:00',
            dailyDuration: $duration,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->doesHaveDayTimeAdjustments)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->timeSpan->getStart(true))->toEqual($leaveStartTime)
            ->and($availability->days[0]->timeSpan->getEnd())->toEqual($ordinaryTimeSpan->getEnd())
            ->and($availability->days[0]->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($expectedLeaveDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate single part day in a future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '20:00:00', $scheduledDayData->date);
        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('07:00:00', '18:00:00', $scheduledDayData->date);
        $dailyFixedDuration = TimeDuration::parseFromHours(8);

        /** @var UserRoleSchedulePatternPeriodShiftDay $userRoleSchedulePatternPeriodShiftDay */
        $userRoleSchedulePatternPeriodShiftDay = $scheduledDayData->type;
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = true;
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->clearSave();

        $userRoleSchedulePatternPeriodShiftDay->isScheduled = true;
        $userRoleSchedulePatternPeriodShiftDay->duration = $dailyFixedDuration->copy();
        $userRoleSchedulePatternPeriodShiftDay->doesHaveExpectedTimeSpan = true;
        $userRoleSchedulePatternPeriodShiftDay->ordinarySpan = $ordinaryTimeSpan->copy();
        $userRoleSchedulePatternPeriodShiftDay->expectedDuration = $dailyFixedDuration->copy();
        $userRoleSchedulePatternPeriodShiftDay->expectedSpan = $expectedTimeSpan->copy();
        $userRoleSchedulePatternPeriodShiftDay->breaks = TimeSpanList::make($breakTimeSpan);
        $userRoleSchedulePatternPeriodShiftDay->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );
        $expectedLeaveStart = $leaveTimeSpan->getStartAsCarbon();
        $expectedLeaveEnd = $leaveTimeSpan->getStartAsCarbon()->copy()
            ->add($dailyFixedDuration->getValue())
            ->add($breakTimeSpan->getDuration()->getValue());

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($dailyFixedDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->duration)->toEqual($dailyFixedDuration)
            ->and($availability->days[0]->timeSpan->getStartAsCarbon())->toEqual($expectedLeaveStart)
            ->and($availability->days[0]->timeSpan->getEndAsCarbon())->toEqual($expectedLeaveEnd)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($dailyFixedDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate single full day in an open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '20:00:00', $scheduledDayData->date);
        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $dailyFixedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $expectedLeaveTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $expectedLeaveDuration = $expectedLeaveTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration()->getValue());

        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = true;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->isScheduled = true;
        $scheduledPayRunPeriodDayWorkPattern->duration = $dailyFixedDuration->copy();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $dailyFixedDuration->copy();
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make($breakTimeSpan);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->duration)->toEqual($expectedLeaveDuration)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($expectedLeaveDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('calculating leave starting on non scheduled day adjusts calculation start date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $nonScheduledStartDate = null;
        $scheduledNextDate = null;
        foreach ($days as $index => $day) {
            if (!isset($days[$index + 1])) {
                break;
            }

            $nextDay = $days[$index + 1];
            if (
                $day->type instanceof ScheduledPayRunPeriodDayWorkPattern && !$day->type->isScheduled
                && $nextDay->type instanceof ScheduledPayRunPeriodDayWorkPattern && $nextDay->type->isScheduled
            ) {
                $nonScheduledStartDate = $day->date->copy();
                $scheduledNextDate = $days[$index + 1]->date->copy();
            }

        }

        if (is_null($nonScheduledStartDate) || is_null($scheduledNextDate)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $nonScheduledStartDate,
            endDate: $scheduledNextDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->doesHaveDayTimeAdjustments)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->not()->toEqual($nonScheduledStartDate)
            ->and($availability->startDate)->toEqual($availability->endDate)
            ->and($availability->endDate)->toEqual($scheduledNextDate)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledNextDate)
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate single full day in a future payrun', function () {
        $payRun = TenantSystemSettings::getPayRunByDate(Carbon::today());
        $date = $payRun->endDate->copy()->addMonths(3);

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $dailyExpectedDuration = TimeDuration::parseFromHours(10);
        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '18:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $expectedLeaveTimeSpan = TimeSpan::make('07:00:00', '18:00:00', $scheduledDayData->date);

        /** @var UserRoleSchedulePatternPeriodShiftDay $userRoleSchedulePatternPeriodShiftDay */
        $userRoleSchedulePatternPeriodShiftDay = $scheduledDayData->type;
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->minDuration = TimeDuration::zero();
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->maxDuration = TimeDuration::parseFromHours(80);
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyExpectedDuration = $dailyExpectedDuration->copy();
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->doesHaveExpectedTimeSpan = false;
        $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->clearSave();

        $userRoleSchedulePatternPeriodShiftDay->isScheduled = true;
        $userRoleSchedulePatternPeriodShiftDay->minDuration = TimeDuration::zero();
        $userRoleSchedulePatternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $userRoleSchedulePatternPeriodShiftDay->expectedDuration = TimeDuration::parseFromHours(10);
        $userRoleSchedulePatternPeriodShiftDay->doesHaveExpectedTimeSpan = false;
        $userRoleSchedulePatternPeriodShiftDay->ordinarySpan = $ordinaryTimeSpan->copy();
        $userRoleSchedulePatternPeriodShiftDay->breaks = TimeSpanList::make($breakTimeSpan);
        $userRoleSchedulePatternPeriodShiftDay->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData->date->copy())
            ->and($availability->duration)->toEqual($dailyExpectedDuration)
            ->and($availability->days)->toHaveCount(1)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData->date->copy())
            ->and($availability->days[0]->duration)->toEqual($dailyExpectedDuration)
            ->and($availability->days[0]->timeSpan->getStartAsCarbon())->toEqual($expectedLeaveTimeSpan->getStartAsCarbon())
            ->and($availability->days[0]->timeSpan->getEndAsCarbon())->toEqual($expectedLeaveTimeSpan->getEndAsCarbon())
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($dailyExpectedDuration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate multiple days within one open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDaysData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (count($scheduledDaysData) < 2) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '18:00:00');
        $expectedTimeSpan = TimeSpan::make('09:00:00', '17:00:00');
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00');
        $dailyDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration()->getValue());

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        foreach ($scheduledDaysData as $scheduledDayDatum) {
            /** @var ScheduledPayRunPeriodDayWorkPattern $dayScheduledPayRunPeriodDayWorkPattern */
            $dayScheduledPayRunPeriodDayWorkPattern = $scheduledDayDatum->type;
            $dayScheduledPayRunPeriodDayWorkPattern->isScheduled = true;
            $dayScheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
            $dayScheduledPayRunPeriodDayWorkPattern->duration = $dailyDuration->copy();
            $dayScheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy()->setDate($scheduledDayDatum->date);
            $dayScheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy()->setDate($scheduledDayDatum->date);
            $dayScheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make($breakTimeSpan);
            $dayScheduledPayRunPeriodDayWorkPattern->clearSave();
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDaysData[0]->date,
            endDate: $scheduledDaysData[1]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDaysData[0]->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDaysData[1]->date->copy())
            ->and($availability->duration)->toEqual($dailyDuration->copy()->multiply(2))
            ->and($availability->days)->toHaveCount(2)
            ->and($availability->days[0]->date)->toEqual($scheduledDaysData[0]->date->copy())
            ->and($availability->days[1]->date)->toEqual($scheduledDaysData[1]->date->copy())
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($dailyDuration->copy()->multiply(2)))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate multiple days within one future payrun', function () {
        $payRun = TenantSystemSettings::getPayRunByDate(Carbon::today());

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);

        if (count($scheduledDayData) < 2) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '18:00:00');
        $expectedTimeSpan = TimeSpan::make('09:00:00', '17:00:00');
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00');
        $dailyDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration()->getValue());

        foreach ($scheduledDayData as $scheduledDayDatum) {
            /** @var UserRoleSchedulePatternPeriodShiftDay $userRoleSchedulePatternPeriodShiftDay */
            $userRoleSchedulePatternPeriodShiftDay = $scheduledDayDatum->type;
            $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->doesHaveExpectedTimeSpan = true;
            $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = true;
            $userRoleSchedulePatternPeriodShiftDay->duration = $dailyDuration->copy();

            $userRoleSchedulePatternPeriodShiftDay->ordinarySpan = $ordinaryTimeSpan->copy()->setDate($scheduledDayDatum->date);
            $userRoleSchedulePatternPeriodShiftDay->expectedSpan = $expectedTimeSpan->copy()->setDate($scheduledDayDatum->date);
            $userRoleSchedulePatternPeriodShiftDay->breaks = TimeSpanList::make([$breakTimeSpan], $scheduledDayDatum->date);
            $userRoleSchedulePatternPeriodShiftDay->clearSave();
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[0]->date,
            endDate: $scheduledDayData[1]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->toEqual($scheduledDayData[0]->date->copy())
            ->and($availability->endDate)->toEqual($scheduledDayData[1]->date->copy())
            ->and($availability->duration)->toEqual($dailyDuration->copy()->multiply(2))
            ->and($availability->days)->toHaveCount(2)
            ->and($availability->days[0]->date)->toEqual($scheduledDayData[0]->date->copy())
            ->and($availability->days[1]->date)->toEqual($scheduledDayData[1]->date->copy())
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($dailyDuration->copy()->multiply(2)))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate multiple days across multiple payruns', function () {
        $payRun1 = TenantSystemSettings::getEarliestOpenPayRun();
        $date1 = $payRun1->startDate;
        $payRun2 = $payRun1->next;
        $date2 = $payRun2->startDate;

        $scheduledDayData1 = Collection::make();
        $scheduledDayData2 = Collection::make();
        $tries = 0;
        $maxTries = 10;

        $userRoleSchedule = null;
        $userLeaveBankType1 = null;
        $userRoleSchedule2 = null;

        $userRoleScheduleIdsDone = [];

        while (($scheduledDayData1->count() < 1 || $scheduledDayData2->count() < 1) && $tries < $maxTries) {
            [$user1, $userRoleSchedule, $userLeaveBankType1, $days1] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
                ['NOT IN', 'UserRoleSchedule.id', $userRoleScheduleIdsDone],
            ], date: $date1);

            if (is_null($user1) || is_null($userRoleSchedule) || is_null($userLeaveBankType1) || count($days1) < 1) {
                throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $date1->toDateString());
            }

            $scheduledDayData1 = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days1);

            [$user2, $userRoleSchedule2, $userLeaveBankType2, $days2] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
                ['UserRoleSchedule.id', '=', $userRoleSchedule->id],
            ], date: $date2);
            $scheduledDayData2 = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days2);

            $tries++;
            $userRoleScheduleIdsDone[] = $userRoleSchedule->id;
        }

        if ($scheduledDayData1->count() < 1 || $scheduledDayData2->count() < 1) {
            expect(true)->not()->toBeTrue('No schedule found among 2 periods within ' . $tries . ' tries');
        }

        expect($userRoleSchedule->id)->toEqual($userRoleSchedule2->id);

        $startDate = $scheduledDayData1[0]->date->copy();
        $endDate = $scheduledDayData2[1]->date->copy();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType1,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $expectedScheduledDays = Collection::make([
            ...$scheduledDayData1,
            ...$scheduledDayData2,
        ])->filter(fn (StructScheduledPayRunPeriodDayDefaultData $data) => $data->date->isBetween($startDate, $endDate));

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->startDate)->toEqual($startDate->copy())
            ->and($availability->endDate)->toEqual($endDate->copy())
            ->and($availability->days)->toHaveCount($expectedScheduledDays->count())
            ->and($availability->days[0]->date)->toEqual($startDate)
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('errors if not scheduled in the selected period', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date, isScheduled: false);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstNonScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeFalse()
            ->and($availability->errorMessages)->toBeGreaterThan(0);
        // TODO: Validate type of error
    });

    it('errors if dates are not within schedule assignment period', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);

        $startDate = null;
        $endDate = null;
        foreach ($scheduledDayData as $key => $item) {
            $nextScheduledDayData = $scheduledDayData[$key + 1];

            if (isset($nextScheduledDayData) && $nextScheduledDayData->date->diffInDays($item->date) > 1) {
                $startDate = $item->date->copy()->addDay();
                $endDate = $startDate->copy();
                break;
            }
        }

        if (is_null($startDate) || is_null($endDate)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeFalse()
            ->and($availability->errorMessages)->toHaveCount(1);
    });

    it('errors if schedule is not work pattern', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType) || count($days) < 1) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);
        $userRoleSchedule->type = UserRoleScheduleType::DurationOnly;
        $userRoleSchedule->clearSaveOrFail();
        $startDate = $scheduledDayData[0]->date->copy();
        $endDate = $startDate->copy();

        $this->expectException(\InvalidArgumentException::class, );
        $this->expectExceptionMessage('This schedule is not a work pattern');
        CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );
    });

    it('errors if new request is full day and it clashes with any other existing leave requests in the days affected', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user?->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $scheduledDaysData = [];

        foreach ($days as $day) {
            /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
            $scheduledPayRunPeriodDayWorkPattern = $day->type;

            if ($scheduledPayRunPeriodDayWorkPattern->isScheduled) {
                $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
                $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy()->setDate($day->date);
                $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
                $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan], $day->date);
                $scheduledPayRunPeriodDayWorkPattern->clearSave();

                $scheduledDaysData[] = $day;
            }
        }

        if (count($scheduledDaysData) < 1) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user?->externalId . ' - ' . $days[0]->date->toDateString());
        }

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
                userLeaveBankType: $userLeaveBankType,
                userRoleSchedule: $userRoleSchedule,
                isFullDay: true,
                startDate: $scheduledDaysData[0]->date,
                endDate: $scheduledDaysData[0]->date,
                actorUser: SuperUserFlag::getOne(),
            ),
            actorUser: $actorUser,
        );

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDaysData[0]->date,
            endDate: $scheduledDaysData[1]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($preAvailability->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    it('errors if new request is part day and it clashes with other existing leave requests in the same time frame', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user?->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('06:00:00', '20:00:00');
        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $existingLeaveTimeSpan = TimeSpan::make('08:00', '16:00');
        $newLeaveTimeSpan = TimeSpan::make('15:00', '20:00');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        if ($scheduledPayRunPeriodDayWorkPattern->isScheduled) {
            $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
            $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan->copy()->setDate($scheduledDayData->date);
            $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy()->setDate($scheduledDayData->date);
            $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
            $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan], $scheduledDayData->date);
            $scheduledPayRunPeriodDayWorkPattern->clearSave();
        }

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
                userLeaveBankType: $userLeaveBankType,
                userRoleSchedule: $userRoleSchedule,
                isFullDay: false,
                startDate: $scheduledDayData->date,
                dailyStartTime: $existingLeaveTimeSpan->getStart(),
                dailyEndTime: $existingLeaveTimeSpan->getEnd(),
                actorUser: SuperUserFlag::getOne(),
            ),
            actorUser: $actorUser,
        );

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            dailyStartTime: $newLeaveTimeSpan->getStart(),
            dailyEndTime: $newLeaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($preAvailability->canBeRequested)->toBeFalse();
        // TODO: Validate type of error
    });

    it('returns not available in case where leave starts before scheduled start', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType) || count($days) < 1) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData[0]->date->copy();
        $endDate = $startDate->copy();
        $userRoleSchedule->startDate = $startDate->copy()->addWeek();
        $userRoleSchedule->clearSave();

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Leave is starting before the start of schedule assignment');
        CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );
    });

    it('returns not available in case where leave ends after before scheduled end', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType) || count($days) < 1) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData[0]->date->copy();
        $endDate = $scheduledDayData->last()->date->copy();
        $userRoleSchedule->endDate = $startDate->copy()->addDays(2);
        $userRoleSchedule->clearSave();

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Leave is ending after the end of schedule assignment');
        CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );
    });

    it('shows warning in case there are clashes with worked hours', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);

        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $leaveCalculations = $userRoleSchedule->leaveCalculations;
        $leaveCalculations->fullDayWeekDayType = LeaveCalculationType::FTE;
        $leaveCalculations->fullDayWeekendType = LeaveCalculationType::FTE;
        $leaveCalculations->save(Carbon::now());

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $work = CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $date->copy()->setTime(9, 0),
            end: $date->copy()->setTime(13, 0),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date)->id,
                    'start' => $date->copy()->setTime(9, 0)->toDateTimeString(),
                    'end' => $date->copy()->setTime(13, 0)->toDateTimeString(),
                    'notes' => 'First work item block',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeTrue()
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($availability->clashedWorks[0]->timeSheetWork_id)->toEqual($work->id);
    });

    it('does not show warning in case leave is applied on subsequent period of already recorded work time block', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);

        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);

        $partDayLeaveTimeSpan = TimeSpan::make('09:00', '14:00')->setDate($date);
        $timeSheetWorkBlock = TimeSpan::make('14:00', '17:00')->setDate($date);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock->getStartAsCarbon(),
            end: $timeSheetWorkBlock->getEndAsCarbon(),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock->getStart(),
                    'end' => $timeSheetWorkBlock->getEnd(),
                    'notes' => 'First work item block',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->clashedWorks)->toHaveCount(0)
        ;
    });

    it('shows warning in case leave is applied within already recorded work time block', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);

        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);

        $partDayLeaveTimeSpan = TimeSpan::make('10:00', '14:00')->setDate($date);
        $timeSheetWorkBlock = $expectedTimeSpan->copy();
        $workBlockFirstItem = TimeSpan::make($expectedTimeSpan->getStart(), $breakTimeSpan->getStart());
        $workBlockSecondItem = TimeSpan::make($breakTimeSpan->getEnd(), $expectedTimeSpan->getEnd());

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        $work = CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock->getStartAsCarbon(),
            end: $timeSheetWorkBlock->getEndAsCarbon(),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockFirstItem->getStart(),
                    'end' => $workBlockFirstItem->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'break',
                    'isScheduled' => false,
                    'start' => $breakTimeSpan->getStartAsCarbon()->toDateTimeString(),
                    'end' => $breakTimeSpan->getEndAsCarbon()->toDateTimeString(),
                    'notes' => 'break time',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockSecondItem->getStart(),
                    'end' => $workBlockSecondItem->getEnd(),
                    'notes' => 'Second work item block right after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->isFullDay)->toBeFalse()
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($availability->clashedWorks[0]->timeSheetWork_id)->toEqual($work->id);
    });

    it('Not scheduled to work on selected period if date is on public holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate->copy());

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $date = $scheduledDayData->date->copy();

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $date,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($date);
        $publicHoliday = new PublicHoliday;
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->name = 'My awesome public holiday test';
        $publicHoliday->date = $date;
        $publicHoliday->saveOrFail();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availabilityBeforeHolidayPublicHolidayCreation->canBeRequested)->toBeTrue()
            ->and($availabilityBeforeHolidayPublicHolidayCreation->errorMessages)->toHaveCount(0)
            ->and($availability->canBeRequested)->toBeFalse()
            ->and($availability->errorMessages)->toBeGreaterThan(0);
        // TODO: Validate type of error
    });

    it('moves start date if it starts on public holiday date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData->date->copy();
        $endDate = $startDate->copy()->addDays(7);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $publicHoliday = new PublicHoliday;
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->name = 'My awesome public holiday test';
        $publicHoliday->date = $startDate;
        $publicHoliday->saveOrFail();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->endDate)->toEqual($availabilityBeforeHolidayPublicHolidayCreation->endDate)
            ->and($availability->startDate)->not()->toEqual($availabilityBeforeHolidayPublicHolidayCreation->startDate);
    });

    it('moves end date if it ends on public holiday date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData->date->copy();
        $endDate = $startDate->copy()->addDays(4);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $publicHoliday = new PublicHoliday;
        $publicHoliday->calendarDate_id = $calendarDate->id;
        $publicHoliday->name = 'My awesome public holiday test';
        $publicHoliday->date = $endDate;
        $publicHoliday->saveOrFail();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($availabilityBeforeHolidayPublicHolidayCreation->startDate)
            ->and($availability->endDate)->not()->toEqual($availabilityBeforeHolidayPublicHolidayCreation->endDate);
    });

    it('Not scheduled to work on selected period if date is on special holiday', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['IS NOT NULL', 'User.payGroupType_id']
        ], date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData->date->copy();

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $startDate;
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType->name]]];
        $holidayCondition->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availabilityBeforeHolidayPublicHolidayCreation->canBeRequested)->toBeTrue()
            ->and($availabilityBeforeHolidayPublicHolidayCreation->errorMessages)->toHaveCount(0)
            ->and($availability->canBeRequested)->toBeFalse()
            ->and($availability->errorMessages)->toBeGreaterThan(0);
        // TODO: Validate type of error
    });

    it('moves start date if it starts on special holiday date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['IS NOT NULL', 'User.payGroupType_id']
        ], date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData->date->copy();
        $endDate = $startDate->copy()->addDays(7);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $startDate;
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType->name]]];
        $holidayCondition->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->endDate)->toEqual($availabilityBeforeHolidayPublicHolidayCreation->endDate)
            ->and($availability->startDate)->not()->toEqual($availabilityBeforeHolidayPublicHolidayCreation->startDate);
    });

    it('moves end date if it ends on special holiday date', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        User::query()->update(['payGroupType_id' => PayGroupType::q()->many()->last()->id]);
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['IS NOT NULL', 'User.payGroupType_id']
        ], date: $payRun->startDate);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->doesIgnorePublicHolidays = false;
        $userLeaveBankType->clearSave();

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        $startDate = $scheduledDayData->date->copy();
        $endDate = $startDate->copy()->addDays(4);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $availabilityBeforeHolidayPublicHolidayCreation = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        $calendarDate = CalendarDate::getByDate($startDate);
        $holiday = new Holiday();
        $holiday->calendarDate_id = $calendarDate->id;
        $holiday->name = 'My awesome public holiday test';
        $holiday->date = $endDate;
        $holiday->clearSave();
        $holidayCondition = new HolidayCondition;
        $holidayCondition->holiday_id = $holiday->id;
        $holidayCondition->type = HolidayConditionType::PayGroupTypeIsOneOf;
        $holidayCondition->options = ['payGroupTypes' => [['id' => $user->payGroupType_id, 'name' => $user->payGroupType->name]]];
        $holidayCondition->clearSave();
        ProcessCache::clear();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($availabilityBeforeHolidayPublicHolidayCreation->startDate)
            ->and($availability->endDate)->not()->toEqual($availabilityBeforeHolidayPublicHolidayCreation->endDate);
    });
});
