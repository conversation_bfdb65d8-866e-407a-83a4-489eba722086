<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendLeaveRequestRelatedBroadcastEvents\SendLeaveRequestRelatedBroadcastEventsWorkPattern;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork as TimeSheetWorkActions;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestAvailabilityClashedWork;
use Element\ElementTime\Domains\Tenant\Plant\Support\PlantItemHoursRecordingTypes;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkBreak;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;
use Illuminate\Database\Eloquent\Collection;

class PlaceLeaveRequestFromPreAvailabilityWorkPattern extends BasePlaceLeaveRequestFromPreAvailability
{
    protected function setLeaveData(): void
    {
        $this->leave->startDate = $this->preAvailability->startDate;
        $this->leave->endDate = $this->preAvailability->endDate;
        $this->leave->isFullDay = $this->preAvailability->isFullDay;
    }

    /** @throws \Throwable */
    protected function replaceWorkedHours(): void
    {
        $doesDeleteWork = $this->leave->isFullDay
            && $this->leave->userLeaveBankType->bank->user->settings->doesFullDayLeaveReplacesAllRecordedHours;

        $timeSheetWorkIds = array_map(fn (StructLeaveRequestAvailabilityClashedWork $clash) => $clash->timeSheetWork_id, $this->preAvailability->clashedWorks);

        $works = TimeSheetWork::q()
            ->constraints([
                ['IN', 'TimeSheetWork.id', $timeSheetWorkIds]
            ])
            ->relations([
                'timeSheetDay',
            ])
            ->many();

        /** @var TimeSheetWork $work */
        foreach ($works as $work) {
            if ($doesDeleteWork) {
                TimeSheetWorkActions\DeleteTimeSheetWork::make()->handle($work);

                continue;
            }

            $workTimeSpan = TimeSpan::make($work->start, $work->end, $work->timeSheetDay->date);
            $leaveDays = $this->leave->days->where('date', '=', $work->timeSheetDay->date);

            foreach ($leaveDays as $leaveDay) {
                $leaveTimeSpan = $leaveDay->timeSpan;

                if (!$leaveTimeSpan->doesOverlap($workTimeSpan)) {
                    continue;
                }

                if ($leaveTimeSpan->doesContainTimeSpan($workTimeSpan)) {
                    TimeSheetWorkActions\DeleteTimeSheetWork::make()->handle($work);

                    continue;
                }

                if ($workTimeSpan->doesContainTimeSpan($leaveTimeSpan, true)) {
                    $replicatedWork = $work->replicateOnlyRawProperties(['id']);

                    $this->createOrUpdateWorkRecord($work, $work->itemsWithBreaks, $leaveTimeSpan->getEndAsCarbon(), $work->end->copy());

                    $this->createOrUpdateWorkRecord($replicatedWork, $work->itemsWithBreaks, $replicatedWork->start->copy(), $leaveTimeSpan->getStartAsCarbon()->copy());

                    continue;
                }

                if ($leaveTimeSpan->doesContain($work->start)) {
                    $this->createOrUpdateWorkRecord($work, $work->itemsWithBreaks, $leaveTimeSpan->getEndAsCarbon(), $work->end->copy());

                    continue;
                }

                $this->createOrUpdateWorkRecord($work, $work->itemsWithBreaks, $work->start->copy(), $leaveTimeSpan->getStartAsCarbon());
            }
        }
    }

    /**
     * @param Collection|TimeSheetWorkItem[]|TimeSheetWorkBreak[] $itemsWithBreaks
     * @throws \Throwable
     */
    protected function createOrUpdateWorkRecord(TimeSheetWork $work, Collection|array $itemsWithBreaks, CarbonInterface $start, CarbonInterface $end): void
    {
        $itemsAndBreaksInfo = [];

        if ($start->eq($end)) {
            TimeSheetWorkActions\DeleteTimeSheetWork::make()->handle($work);

            return;
        }

        foreach ($itemsWithBreaks as $itemWithBreak) {
            if ($itemWithBreak->start->gt($end)) {
                break;
            }

            if ($itemWithBreak->end->lt($start)) {
                continue;
            }

            $itemStart = Carbon::make(max($itemWithBreak->start, $start));
            $itemEnd = Carbon::make(min($itemWithBreak->end, $end));

            if ($itemStart->gte($itemEnd)) {
                continue;
            }

            if ($itemWithBreak instanceof TimeSheetWorkItem) {
                $itemPeriod = TimeSpan::make($itemStart, $itemEnd, $start->copy());
                $itemDuration = $itemPeriod->getDuration();
                $allowances = $this->getTimeSheetAllowanceDayEntries($itemWithBreak, $itemPeriod, $itemDuration->copy());
                $plant_items = $this->getPlantSheetDayTimes($itemWithBreak, $itemDuration->copy());

                $itemsAndBreaksInfo[] = [
                    '_' => 'item',
                    'type' => $itemWithBreak->type->id(),
                    'model_id' => $itemWithBreak->model_id,
                    'group_id' => $itemWithBreak->group_id,
                    'sub_id' => $itemWithBreak->sub_id,
                    'start' => $itemStart->copy(),
                    'end' => $itemEnd->copy(),
                    'notes' => $itemWithBreak->notes,
                    'allowances' => $allowances,
                    'plant_items' => $plant_items,
                ];

                continue;
            }

            $itemsAndBreaksInfo[] = [
                '_' => 'break',
                'start' => $itemStart->copy(),
                'end' => $itemEnd->copy(),
                'notes' => $itemWithBreak->notes,
            ];
        }

        while (head($itemsAndBreaksInfo)['_'] == 'break') {
            array_shift($itemsAndBreaksInfo);
        }

        while (last($itemsAndBreaksInfo)['_'] == 'break') {
            array_pop($itemsAndBreaksInfo);
        }

        if (is_null($work->id)) {
            TimeSheetWorkActions\CreateTimeSheetWork::make()->handle(
                $work->timeSheetDay,
                $work->scheduledPayRunPeriodDay,
                $work->userRoleTimeType,
                start: $start->copy(),
                end: $end->copy(),
                itemsAndBreaksInfo: $itemsAndBreaksInfo,
            );

            return;
        }

        TimeSheetWorkActions\UpdateTimeSheetWork::make()->handle(
            $work,
            $work->scheduledPayRunPeriodDay,
            $work->userRoleTimeType,
            start: $start->copy(),
            end: $end->copy(),
            itemsAndBreaksInfo: $itemsAndBreaksInfo,
        );
    }

    protected function getPlantSheetDayTimes(TimeSheetWorkItem $timeSheetWorkItem, TimeDuration $maxDuration): array
    {
        $plantRecords = [];

        foreach ($timeSheetWorkItem->plantSheetDayTimes as $plantSheetDayTime) {
            $plantItem = $plantSheetDayTime->plantItem;
            $duration = $plantSheetDayTime->duration?->copy();
            $durationStart = $plantSheetDayTime->durationStart?->copy();
            $durationEnd = $plantSheetDayTime->durationEnd?->copy();
            $secondaryDuration = $plantSheetDayTime->secondaryDuration?->copy();
            $secondaryDurationStart = $plantSheetDayTime->secondaryDurationStart?->copy();
            $secondaryDurationEnd = $plantSheetDayTime->secondaryDurationEnd?->copy();

            if ($plantItem->doesHaveHours && !is_null($plantSheetDayTime->duration) && $plantSheetDayTime->duration->gt($maxDuration)) {
                if ($plantItem->hoursRecordingTypeClass::isType(PlantItemHoursRecordingTypes\HourMeterType::class)) {
                    $durationEnd = $plantSheetDayTime->durationStart->copy()->add($maxDuration->copy());
                } else {
                    $duration = $maxDuration->copy();
                }
            }

            if ($plantItem->doesHaveSecondaryHours && !is_null($plantSheetDayTime->secondaryDuration) && $plantSheetDayTime->secondaryDuration->gt($maxDuration)) {
                if ($plantItem->secondaryHoursRecordingTypeClass::isType(PlantItemHoursRecordingTypes\HourMeterType::class)) {
                    $secondaryDurationEnd = $plantSheetDayTime->secondaryDurationStart->copy()->add($maxDuration->copy());
                } else {
                    $secondaryDuration = $maxDuration->copy();
                }
            }

            $plantRecords[] = [
                'plantItem_id' => $plantItem->id,
                'parent_id' => $plantSheetDayTime->parentPlantSheetDayTime_id,
                'rel_type' => $plantSheetDayTime->rel_type,
                'rel_id' => $plantSheetDayTime->rel_id,
                'duration' => $duration,
                'durationStart' => $durationStart,
                'durationEnd' => $durationEnd,
                'secondaryDuration' => $secondaryDuration,
                'secondaryDurationStart' => $secondaryDurationStart,
                'secondaryDurationEnd' => $secondaryDurationEnd,
                'mileage' => $plantSheetDayTime->mileage,
                'mileageStart' => $plantSheetDayTime->mileageStart,
                'mileageEnd' => $plantSheetDayTime->mileageEnd,
                'cartage' => $plantSheetDayTime->cartage,
                'costRate' => $plantSheetDayTime->costRate,
                'description' => $plantSheetDayTime->description,
            ];
        }

        return $plantRecords;
    }

    protected function getTimeSheetAllowanceDayEntries(TimeSheetWorkItem $timeSheetWorkItem, TimeSpan $workSpan, TimeDuration $maxDuration): array
    {
        $allowances = [];

        foreach ($timeSheetWorkItem->allowanceEntries as $allowanceEntry) {
            $allowanceType = $allowanceEntry->timeSheetAllowanceDay->timeSheetAllowance->allowanceType;
            $duration = null;
            $startDateTime = null;
            $endDateTime = null;

            if ($allowanceType->rateType === 'H') {
                $duration = $allowanceEntry->duration->copy();

                if ($allowanceEntry->duration->gt($maxDuration)) {
                    $duration = $maxDuration->copy();
                }

                if ($allowanceType->requiresTime) {
                    $startDateTime = $workSpan->doesContain($allowanceEntry->startDateTime)
                        ? $allowanceEntry->startDateTime->copy()
                        : $workSpan->getStartAsCarbon()->copy();

                    $endDateTime = $workSpan->doesContain($allowanceEntry->endDateTime)
                        ? $allowanceEntry->endDateTime->copy()
                        : $workSpan->getEndAsCarbon()->copy();
                }
            }

            $allowances[] = [
                'allowanceType_id' => $allowanceType->id,
                'duration' => $duration,
                'miles' => $allowanceEntry->miles ?? null,
                'quantity' => $allowanceEntry->quantity ?? null,
                'startDateTime' => $startDateTime,
                'endDateTime' => $endDateTime,
                'notes' => $allowanceEntry->notes,
            ];
        }

        return $allowances;
    }

    protected function generateDays(): void
    {
        $leaveRequestDays = Collection::make([]);

        foreach ($this->preAvailability->days as $day) {
            $leaveDay = new LeaveRequestDay;
            $leaveDay->user_id = $this->userLeaveBankType->bank->user_id;

            $leaveDay->date = $day->date->copy();
            $leaveDay->duration = $day->duration->copy();
            $leaveDay->durationAdjusted = $day->durationAdjusted->copy();
            $leaveDay->isFullDay = $this->leave->isFullDay;
            $leaveDay->timeSpan = $day->timeSpan->copy();

            $leaveRequestDays->push($leaveDay);
        }

        $this->leave->days()->saveMany($leaveRequestDays);
    }

    protected function sendBroadcastEvents(): void
    {
        SendLeaveRequestRelatedBroadcastEventsWorkPattern::dispatch($this->leave)->afterResponse();
    }
}
