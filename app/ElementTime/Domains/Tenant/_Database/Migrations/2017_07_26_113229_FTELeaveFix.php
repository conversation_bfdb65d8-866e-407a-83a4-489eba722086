<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->decimal('hoursCalculation', 16, 10)->nullable();
        });

        Schema::table('TimeSheetItemDay', function (Blueprint $table) {
            $table->decimal('hoursCalculation', 16, 10)->nullable();
        });

        Schema::table('TimeSheetItem', function (Blueprint $table) {
            $table->decimal('cTotalHoursCalculation', 16, 10)->nullable();
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('hoursWorkedCalculation', 16, 10)->nullable();
            $table->decimal('hoursLeaveCalculation', 16, 10)->nullable();
            $table->decimal('hoursToilCalculation', 16, 10)->nullable();
            $table->decimal('hoursRosteredTimeOffCalculation', 16, 10)->nullable();
            $table->decimal('hoursAllowanceCalculation', 16, 10)->nullable();
            $table->decimal('hoursTotalCalculation', 16, 10)->nullable();
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('cTotalWorkedCalculation', 16, 10)->nullable();
            $table->decimal('cTotalAllowanceCalculation', 16, 10)->nullable();
            $table->decimal('cTotalLeaveUsedCalculation', 16, 10)->nullable();
            $table->decimal('cTotalToilUsedCalculation', 16, 10)->nullable();
            $table->decimal('cTotalRosteredTimeOffUsedCalculation', 16, 10)->nullable();
            $table->decimal('cTotalCalculation', 16, 10)->nullable();
            $table->decimal('fTotalWorkedCalculation', 16, 10)->nullable();
            $table->decimal('fTotalAllowanceCalculation', 16, 10)->nullable();
            $table->decimal('fTotalLeaveUsedCalculation', 16, 10)->nullable();
            $table->decimal('fTotalToilUsedCalculation', 16, 10)->nullable();
            $table->decimal('fTotalRosteredTimeOffUsedCalculation', 16, 10)->nullable();
            $table->decimal('fTotalCalculation', 16, 10)->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('TimeSheet', 'fTotalCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalToilUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalToilUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalToilUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalToilUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursTotalCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursAllowanceCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursRosteredTimeOffCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursToilCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursToilCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursLeaveCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursWorkedCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetItem', 'hoursCalculation')) {
            Schema::table('cTotalHoursCalculation', function (Blueprint $table) {
                $table->dropColumn('cTotalHoursCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDay', 'hoursCalculation')) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropColumn('hoursCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'hoursCalculation')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('hoursCalculation');
            });
        }
    }
};
