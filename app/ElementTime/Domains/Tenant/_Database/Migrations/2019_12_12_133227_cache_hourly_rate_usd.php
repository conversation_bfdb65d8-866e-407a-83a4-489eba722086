<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->decimal('hourlyRate', 16, 10)->nullable();
            $table->decimal('glidePeriodHourlyRate', 16, 10)->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserShiftDay', 'hourlyRate')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('hourlyRate');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'glidePeriodHourlyRate')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHourlyRate');
            });
        }
    }
};
