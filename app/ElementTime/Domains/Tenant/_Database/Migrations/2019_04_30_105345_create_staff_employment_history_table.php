<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PenaltyType

        Schema::create('UserEmploymentServiceRecord', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'uesrec_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'uesrec_auser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // General information
            $table->string('organisation')->nullable();
            $table->string('lastRole')->nullable();
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->string('hourlyRate')->nullable();
            $table->string('personalLeaveBalance')->nullable();
            $table->string('longServiceLeaveDue')->nullable();
            $table->string('longServiceLeaveValue')->nullable();
            $table->string('annualServiceLeaveDue')->nullable();
            $table->string('annualServiceLeaveValue')->nullable();
            $table->string('otherServiceLeaveDue')->nullable();
            $table->string('otherServiceLeaveValue')->nullable();
            $table->text('comments')->nullable();
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'uesrec_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: PenaltyType
    }

    public function down(): void
    {
        Schema::dropIfExists('UserEmploymentServiceRecord');
    }
};
