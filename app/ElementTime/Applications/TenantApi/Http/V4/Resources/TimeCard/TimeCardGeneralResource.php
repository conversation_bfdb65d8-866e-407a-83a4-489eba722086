<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\TimeCard;

use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\User\UserTeenyTinyWithMasterRoleResource;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Support\Facades\CurrentUser;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'TimeCardGeneralResource',
    title: 'Time-card - General data',
    required: ['id', 'user', 'isOpen', 'allowRecordingAcrossMidnight', 'canCopyFromSchedule', 'canCopyFromPreviousDay', 'canRequestLeave', 'date', 'dayOfTheWeek', 'isDurationOnly', 'nav'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 42),
        new OA\Property(property: 'user', ref: '#/components/schemas/UserTeenyTinyWithMasterRoleResource'),
        new OA\Property(property: 'isOpen', type: 'bool', example: true),
        new OA\Property(property: 'allowRecordingAcrossMidnight', type: 'bool', example: true),
        new OA\Property(property: 'canCopyFromSchedule', type: 'bool', example: true),
        new OA\Property(property: 'canCopyFromPreviousDay', type: 'bool', example: true),
        new OA\Property(property: 'canRequestLeave', type: 'bool', example: true),
        new OA\Property(property: 'date', type: 'string', example: '2023-11-07'),
        new OA\Property(property: 'dayOfTheWeek', type: 'string', example: 'Tuesday'),
        new OA\Property(property: 'isDurationOnly', type: 'boolean', example: false),
        new OA\Property(property: 'nav', required: ['previous', 'next'], properties: [
            new OA\Property(property: 'previous', required: ['id', 'date'], properties: [
                new OA\Property(property: 'id', type: 'int', example: 21),
                new OA\Property(property: 'date', type: 'string', example: '2023-11-06'),
            ], nullable: true),
            new OA\Property(property: 'next', required: ['id', 'date'], properties: [
                new OA\Property(property: 'id', type: 'int', example: 84),
                new OA\Property(property: 'date', type: 'string', example: '2023-11-08'),
            ], nullable: true),
        ]),
    ],
)]
class TimeCardGeneralResource extends TimeCardBaseResource
{

    public static $relations = [
        'timeSheet.payRunItem.user.userRoles.userRoleMasters',
        'timeSheet.payRunItem.user.userRoles.role',
    ];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var TimeSheetDay $r */
        $r = $this->getModel();

        $ts = $r->timeSheet;
        $user = CurrentUser::getUser();

        if (is_null($user)) {
            $isOpen = false;
        } else {
            $isOpen = $ts->canBeUpdated($user);
        }

        return [
            'id' => $r->id,
            'user' => UserTeenyTinyWithMasterRoleResource::make($ts->payRunItem->user),

            'isOpen' => $isOpen,
            'allowRecordingAcrossMidnight' => $ts->payRunItem->user->settings->allowRecordingAcrossMidnight,
            'canCopyFromSchedule' => $r->canCopyFromSchedule,
            'canCopyFromPreviousDay' => $r->canCopyFromPreviousDay,
            'canRequestLeave' => $isOpen && $r->timeSheet->payRunItem->user->hasLeaveOptions,

            'date' => $this->fetchDate($r->date),
            'dayOfTheWeek' => $r->date->dayName,
            'isDurationOnly' => $ts->isDurationOnly,
            'nav' => $this->fetchNav($r),
        ];
    }

    /** @throws \Throwable */
    protected function fetchNav(TimeSheetDay $r): array
    {
        $prev = $r->repository->getSiblingByDate($r->date->copy()->subDays(1));
        $next = $r->repository->getSiblingByDate($r->date->copy()->addDays(1));

        return [
            'previous' => $this->fetchNavDay($prev),
            'next' => $this->fetchNavDay($next),
        ];
    }

    protected function fetchNavDay(TimeSheetDay $item = null): array|null
    {
        if (is_null($item)) {
            return null;
        }

        return [
            'id' => $item->id,
            'date' => $this->fetchDate($item->date),
        ];
    }
}
