<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Routes\WorkRecord;

use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\WorkRecord\WorkRecordController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Routes\BaseTenantApiV4Router;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetRecordType\TimeSheetRecordType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetWorkType\TimeSheetWorkType;
use Element\ElementTime\Support\Enums\RecordedVia;
use OpenApi\Attributes as OA;

class MainRoutes extends BaseTenantApiV4Router
{
    protected string|null $defaultPrefix = 'work-record';

    protected function routes(): void
    {
        $this->get();
        $this->getId();
        $this->post();
        $this->putId();
        $this->deleteId();
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/work-record',
        operationId: 'get_api_v4_work-record',
        description: 'Return a list of work-records filtered by parameters',
        summary: 'Return a list of work-records filtered by parameters',
        tags: ['work-record'],
        parameters: [
            new OA\Parameter(name: 'timeSheetDay_id', in: 'query', schema: new OA\Schema(type: 'integer'), example: 53122),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Return the data for a record of work successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', type: 'array', items: new OA\Items(ref: '#/components/schemas/WorkRecordBasicResource')),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function get(): void
    {
        $this->router->get('', [WorkRecordController::class, 'listWorks'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/work-record/{id}',
        operationId: 'get_api_v4_work-record_{id}',
        description: 'Return the data for a record of work',
        summary: 'Return the data for a record of work',
        tags: ['work-record'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', schema: new OA\Schema(type: 'integer'), example: 1),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Return the data for a record of work successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/WorkRecordDetailsResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getId(): void
    {
        $this->router->get('{id}', [WorkRecordController::class, 'getWork'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Post(
        path: '/api/v4/work-record',
        operationId: 'post_api_v4_work-record',
        description: 'Create a work record in a timesheet day (time-card)',
        summary: 'Create a work record in a timesheet day (time-card)',
        requestBody: new OA\RequestBody(
            description: 'Work data',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'timeSheetDay_id', type: 'integer', example: 42),
                    new OA\Property(property: 'scheduledPayRunPeriodDay_id', type: 'integer', example: 42),
                    new OA\Property(property: 'userRoleTimeType_id', type: 'integer', example: 42),
                    new OA\Property(property: 'recordedVia', type: 'string', enum: RecordedVia::class, example: RecordedVia::KioskTimer),
                    new OA\Property(property: 'start', type: 'string', example: '2025-06-29 10:30:00', nullable: true),
                    new OA\Property(property: 'end', type: 'string', example: '2025-06-29 13:00:00', nullable: true),
                    new OA\Property(property: 'items_and_breaks', type: 'array', items: new OA\Items(required: ['_', 'type', 'group_type', 'group_id', 'model_type', 'model_id', 'sub_type', 'sub_id', 'duration', 'start', 'end', 'isScheduled', 'plant_items', 'allowances'], properties: [
                        new OA\Property(property: '_', type: 'string', enum: TimeSheetRecordType::class, example: TimeSheetRecordType::Item),
                        new OA\Property(property: 'type', description: 'this is only required if "_" = item', type: 'string', enum: TimeSheetWorkType::class, example: TimeSheetWorkType::Project),
                        new OA\Property(property: 'group_type', description: 'this is only required if "_" = item', type: 'string', example: 'projects', nullable: true),
                        new OA\Property(property: 'group_id', description: 'this is only required if "_" = item', type: 'integer', example: 4, nullable: true),
                        new OA\Property(property: 'model_type', description: 'this is only required if "_" = item', type: 'string', example: 'work-order'),
                        new OA\Property(property: 'model_id', description: 'this is only required if "_" = item', type: 'integer', example: 4),
                        new OA\Property(property: 'sub_type', description: 'this is only required if "_" = item', type: 'string', example: 'activity', nullable: true),
                        new OA\Property(property: 'sub_id', description: 'this is only required if "_" = item', type: 'integer', example: 4, nullable: true),
                        new OA\Property(property: 'duration', type: 'float', example: 2.5),
                        new OA\Property(property: 'start', type: 'string', example: '10:30:00', nullable: true),
                        new OA\Property(property: 'end', type: 'string', example: '13:00:00', nullable: true),
                        new OA\Property(property: 'isScheduled', description: 'this is only required if "_" = break', type: 'boolean', example: false, nullable: true),
                        new OA\Property(property: 'notes', type: 'string', example: 'this is a note', nullable: true),
                        new OA\Property(property: 'plant_items', description: 'this is only required if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/PlantSheetDayTimeRecordResource')),
                        new OA\Property(property: 'allowances', description: 'this is only required if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/AdHocAllowanceEntryResource')),
                    ])),
                ],
            ),
        ),
        tags: ['work-record'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Record created successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/WorkRecordDetailsResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function post(): void
    {
        $this->router->post('', [WorkRecordController::class, 'createWork'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Put(
        path: '/api/v4/work-record/{id}',
        operationId: 'put_api_v4_work-record_{id}',
        description: 'Update a record of work',
        summary: 'Update a record of work',
        requestBody: new OA\RequestBody(
            description: 'Work data',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'scheduledPayRunPeriodDay_id', type: 'integer', example: 42),
                    new OA\Property(property: 'userRoleTimeType_id', type: 'integer', example: 42),
                    new OA\Property(property: 'start', type: 'string', example: '2025-06-29 10:30:00'),
                    new OA\Property(property: 'end', type: 'string', example: '2025-06-29 13:00:00'),
                    new OA\Property(property: 'recordedVia', type: 'string', enum: RecordedVia::class, example: RecordedVia::AppTimeSheet),
                    new OA\Property(property: 'items_and_breaks', type: 'array', items: new OA\Items(required: ['id', '_', 'type', 'group_type', 'group_id', 'model_type', 'model_id', 'sub_type', 'sub_id', 'duration', 'start', 'end', 'isScheduled', 'plant_items', 'allowances'], properties: [
                        new OA\Property(property: 'id', type: 'integer', example: 42),
                        new OA\Property(property: '_', type: 'string', enum: TimeSheetRecordType::class, example: TimeSheetRecordType::Item),
                        new OA\Property(property: 'type', description: 'this is only required if "_" = item', type: 'string', enum: TimeSheetWorkType::class, example: TimeSheetWorkType::Project),
                        new OA\Property(property: 'group_type', description: 'this is only required if "_" = item', type: 'string', example: 'projects', nullable: true),
                        new OA\Property(property: 'group_id', description: 'this is only required if "_" = item', type: 'integer', example: 4, nullable: true),
                        new OA\Property(property: 'model_type', description: 'this is only required if "_" = item', type: 'string', example: 'work-order'),
                        new OA\Property(property: 'model_id', description: 'this is only required if "_" = item', type: 'integer', example: 4),
                        new OA\Property(property: 'sub_type', description: 'this is only required if "_" = item', type: 'string', example: 'activity', nullable: true),
                        new OA\Property(property: 'sub_id', description: 'this is only required if "_" = item', type: 'integer', example: 4, nullable: true),
                        new OA\Property(property: 'duration', type: 'float', example: 2.5),
                        new OA\Property(property: 'start', type: 'string', example: '10:30:00', nullable: true),
                        new OA\Property(property: 'end', type: 'string', example: '13:00:00', nullable: true),
                        new OA\Property(property: 'isScheduled', description: 'this is only required if "_" = break', type: 'boolean', example: false, nullable: true),
                        new OA\Property(property: 'notes', type: 'string', example: 'this is a note', nullable: true),
                        new OA\Property(property: 'plant_items', description: 'this is only required if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/PlantSheetDayTimeRecordResource')),
                        new OA\Property(property: 'allowances', description: 'this is only required if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/AdHocAllowanceEntryResource')),
                    ])),
                ],
            ),
        ),
        tags: ['work-record'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', schema: new OA\Schema(type: 'integer'), example: 1),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Record updated successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/WorkRecordDetailsResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function putId(): void
    {
        $this->router->put('{id}', [WorkRecordController::class, 'updateWork'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Delete(
        path: '/api/v4/work-record/{id}',
        operationId: 'delete_api_v4_work-record_{id}',
        description: 'Delete a record of work',
        summary: 'Delete a record of work',
        tags: ['work-record'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', schema: new OA\Schema(type: 'integer'), example: 1),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Record deleted successfully',
                content: new OA\JsonContent(
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function deleteId(): void
    {
        $this->router->delete('{id}', [WorkRecordController::class, 'deleteWork'])->middleware($this->getPrivateMiddleware());
    }
}
