<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Routes\AdHocAllowance;

use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\AdHocAllowance\AdHocAllowanceController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Routes\BaseTenantApiV4Router;
use Element\ElementTime\Support\Enums\RecordedVia;
use OpenApi\Attributes as OA;

class MainRoutes extends BaseTenantApiV4Router
{
    protected string|null $defaultPrefix = 'ad-hoc-allowance';

    protected function routes(): void
    {
        $this->getEntryId();
        $this->postEntry();
        $this->putEntryId();
        $this->deleteEntryId();
        $this->getDayIdConfig();
        $this->getDayIdEntries();
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/ad-hoc-allowance/entry/{id}',
        operationId: 'get_api_v4_ad_hoc_allowance_entry_{id}',
        description: 'Return the detailed data for ad hoc allowance entry with the ID `{id}`.<br><strong>Note:</strong> If the logged user doesn\'t have access to the entry an error will be returned.',
        summary: 'Get ad hoc allowance entry detailed data',
        tags: ['ad-hoc-allowance'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', schema: new OA\Schema(type: 'integer'), example: '42'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/AdHocAllowanceEntryResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getEntryId(): void
    {
        $this->router->get('entry/{id}', [AdHocAllowanceController::class, 'getEntry'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Post(
        path: '/api/v4/ad-hoc-allowance/entry',
        operationId: 'post_api_v4_ad_hoc_allowance_entry',
        description: 'Creates a new ad hoc allowance entry.<br><strong>Note:</strong> If the logged user doesn\'t have access to the record, an error will be returned.',
        summary: 'Create ad hoc allowance',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                required: ['timeSheetDay_id', 'allowanceType_id', 'recordedVia'],
                properties: [
                    new OA\Property(property: 'recordedVia', type: 'string', enum: RecordedVia::class, example: RecordedVia::KioskTimer),
                    new OA\Property(property: 'timeSheetDay_id', type: 'integer', example: 42),
                    new OA\Property(property: 'allowanceType_id', type: 'integer', example: 42),
                    new OA\Property(property: 'duration', type: 'float', example: 1, nullable: true),
                    new OA\Property(property: 'startDateTime', type: 'string', example: '2023-07-11 09:00', nullable: true),
                    new OA\Property(property: 'endDateTime', type: 'string', example: '2023-07-11 10:00', nullable: true),
                    new OA\Property(property: 'miles', type: 'float', example: 80, nullable: true),
                    new OA\Property(property: 'quantity', type: 'integer', example: 1, nullable: true),
                    new OA\Property(property: 'notes', type: 'string', example: 'This is a comments field', nullable: true),
                ],
            ),
        ),
        tags: ['ad-hoc-allowance'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', example: 'S'),
                ]),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function postEntry(): void
    {
        $this->router->post('entry', [AdHocAllowanceController::class, 'createEntry'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Put(
        path: '/api/v4/ad-hoc-allowance/entry/{id}',
        operationId: 'put_api_v4_ad_hoc_allowance_entry',
        description: 'Updates an existing ad hoc allowance entry.',
        summary: 'Update ad hoc allowance',
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'startDateTime', type: 'string', example: '2023-07-11 09:00', nullable: true),
                    new OA\Property(property: 'endDateTime', type: 'string', example: '2023-07-11 10:00', nullable: true),
                    new OA\Property(property: 'duration', type: 'float', example: 1, nullable: true),
                    new OA\Property(property: 'miles', type: 'float', example: 80, nullable: true),
                    new OA\Property(property: 'quantity', type: 'integer', example: 1, nullable: true),
                    new OA\Property(property: 'notes', type: 'string', example: 'Updated comment', nullable: true),
                ],
            ),
        ),
        tags: ['ad-hoc-allowance'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', required: true, schema: new OA\Schema(type: 'integer'), example: 4242),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Success',
                content: new OA\JsonContent(properties: [
                    new OA\Property(property: 'status', type: 'string', example: 'S'),
                ]),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function putEntryId(): void
    {
        $this->router->put('entry/{id}', [AdHocAllowanceController::class, 'updateEntry'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Delete(
        path: '/api/v4/ad-hoc-allowance/entry/{id}',
        operationId: 'delete_api_v4_ad_hoc_allowance_entry_{id}',
        description: 'Delete ad hoc allowance entry with the ID `{id}`.<br><strong>Note:</strong> If the logged user doesn\'t have access to the entry an error will be returned.',
        summary: 'Delete an ad hoc allowance entry',
        tags: ['ad-hoc-allowance'],
        parameters: [
            new OA\Parameter(name: 'id', in: 'path', schema: new OA\Schema(type: 'integer'), example: '42'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function deleteEntryId(): void
    {
        $this->router->delete('entry/{id}', [AdHocAllowanceController::class, 'deleteEntry'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/ad-hoc-allowance/day/{id}/config',
        operationId: 'get_api_v4_ad_hoc_allowance_day_{id}_config',
        description: 'Return the config data for recording ad hoc allowance entries in a day with the ID `{id}`.<br><strong>Note:</strong> If the logged user doesn\'t have access to the day an error will be returned.',
        summary: 'Get ad hoc allowance config data for day',
        tags: ['ad-hoc-allowance'],
        parameters: [
            new OA\Parameter(name: 'id', description: 'This is the ID of a time-card or a day in the time-sheet', in: 'path', schema: new OA\Schema(type: 'integer|string'), example: 'current'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', ref: '#/components/schemas/AdHocAllowanceDayConfigResource'),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getDayIdConfig(): void
    {
        $this->router->get('day/{id}/config', [AdHocAllowanceController::class, 'getDayConfig'])->middleware($this->getPrivateMiddleware());
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/ad-hoc-allowance/day/{id}/entries',
        operationId: 'get_api_v4_ad_hoc_allowance_day_{id}_entries',
        description: 'Return the list of recorded ad hoc allowance entries in a day with the ID `{id}`.<br><strong>Note:</strong> If the logged user doesn\'t have access to the day an error will be returned.',
        summary: 'Get ad hoc allowance entries for a day',
        tags: ['ad-hoc-allowance'],
        parameters: [
            new OA\Parameter(name: 'id', description: 'This is the ID of a time-card or a day in the time-sheet', in: 'path', schema: new OA\Schema(type: 'integer|string'), example: 'current'),
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: 'OK',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(property: 'content', type: 'array', items: new OA\Items(ref: '#/components/schemas/AdHocAllowanceEntryResource')),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getDayIdEntries(): void
    {
        $this->router->get('day/{id}/entries', [AdHocAllowanceController::class, 'getDayEntries'])->middleware($this->getPrivateMiddleware());
    }
}
