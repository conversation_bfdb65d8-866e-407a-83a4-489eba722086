<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\ElementTime\Tenant\Applications\TenantSite\LeaveDetails;

use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeavePeriodInstanceType\LeavePeriodInstanceType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\PayrollOfficerFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;
use Tests\ElementTime\Tenant\Applications\TenantSite\TenantSiteTestCase;
use Tests\ElementTime\Traits\Tenant\HasAuth;

class LeaveDetailsTest extends TenantSiteTestCase
{
    use HasAuth;

    protected static function getServicePath(): string
    {
        return 'services/protected/leave-details';
    }

    protected function getRelevantLeaveTypes(): array
    {
        $resp = $this->runGetOnService('types');

        return $resp->json('content');
    }

    public function testDoesMatchFirstPeriod()
    {
        $this->loginAs(PayrollOfficerFlag::getOne());
        $leaveTypes = $this->getRelevantLeaveTypes();
        $typeThreshold = intval(count($leaveTypes) / 2);
        $pastPeriodsCount = 0;

        $leaveTypeParams = [];
        foreach ($leaveTypes as $leaveType) {
            $leaveTypeKey = $leaveType['type'];

            if (!isset($leaveTypeParams[$leaveTypeKey])) {
                $leaveTypeParams[$leaveTypeKey] = [];
            }

            if ($leaveTypeKey !== "accrued" && count($leaveTypeParams[$leaveTypeKey]) < $typeThreshold) {
                $leaveTypeParams[$leaveTypeKey][] = $leaveType['id'];
            }
        }

        $periodTypes = [
            ['type' => LeavePeriodInstanceType::CalendarYear, 'settings' => []],
            ['type' => LeavePeriodInstanceType::FinancialYear, 'settings' => []],
            ['type' => LeavePeriodInstanceType::FloatingPeriod, 'settings' => ['numWeeks' => 52]],
        ];

        foreach ($periodTypes as $periodTypeEntry) {
            $periodType = $periodTypeEntry['type'];
            $periodInstanceTypeSettings = $periodTypeEntry['settings'];

            $params = [
                'doesShowOwn' => false,
                'pastPeriodsCount' => $pastPeriodsCount,
                'periodInstanceType' => $periodType->id(),
                'periodInstanceTypeSettings' => $periodInstanceTypeSettings,
                'types' => $leaveTypeParams,
            ];

            $response = $this->runGetOnService('count-instances?'. http_build_query($params));
            $response->assertJsonFragment(['status' => 'S']);
            $responseContent = $response->json('content');

            $targetPeriods = $periodType->buildPeriod($pastPeriodsCount);

            foreach ($responseContent['data'] as $datum) {
                foreach ($datum['types'] as $userLeaveBankTypeEntry) {
                    /** @var UserLeaveBankType $userLeaveBankType */
                    $userLeaveBankType = UserLeaveBankType::q()->findOrFail($userLeaveBankTypeEntry['userLeaveBankType_id']);
                    $leaveRequests = $userLeaveBankType->leaveRequests()
                        ->join('Workflow', function (JoinClause $join) {
                            $join->on('Workflow.rel_type', 'LIKE', DB::raw('"%LeaveRequest"'));
                            $join->on('Workflow.rel_id', '=', 'LeaveRequest.id');
                        })
                        ->where(function (\Illuminate\Database\Eloquent\Builder $query) use ($targetPeriods) {
                            $query->where(DB::raw('CAST(LeaveRequest.startDateTime as DATE)'), '>=', $targetPeriods->start->toDateString());
                            $query->where(DB::raw('CAST(LeaveRequest.startDateTime as DATE)'), '<=', $targetPeriods->end->toDateString());
                            $query->whereIn('Workflow.status', [
                                NewType::ID,
                                SubmittedType::ID,
                                PartiallyApprovedType::ID,
                                ApprovedType::ID,
                            ]);
                        })
                        ->groupBy(['LeaveRequest.id']);

                    $this->assertEquals($userLeaveBankType->bank->user->fullName, $datum['userName']);
                    $this->assertEquals($leaveRequests->get()->count(), $userLeaveBankTypeEntry['current']['instances']);
                    $this->assertEquals(
                        etime_number($leaveRequests->get()->sum('calculatedTotalHours'), 2),
                        $userLeaveBankTypeEntry['current']['totalHours'],
                    );
                    $this->assertEquals(
                        etime_number($leaveRequests->get()->average('calculatedTotalHours'), 2),
                        $userLeaveBankTypeEntry['current']['averageHours'],
                    );

                    $attachmentCount = 0;
                    /** @var LeaveRequest $leaveRequest */
                    foreach ($leaveRequests->get() as $leaveRequest) {
                        $attachmentCount += $leaveRequest->attachmentFiles->count();
                    }

                    $this->assertEquals($attachmentCount, $userLeaveBankTypeEntry['current']['attachments']);
                }
            }
        }

    }
}
