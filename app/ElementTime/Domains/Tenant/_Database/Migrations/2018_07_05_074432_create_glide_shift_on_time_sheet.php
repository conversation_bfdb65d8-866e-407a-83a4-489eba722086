<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Remove D_E_P_R_E_C_A_T_E_D fields

        if (Schema::hasColumn('TimeSheet', 'cTotalHoursMap')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalHoursMap');
            });
        }

        if (Schema::hasColumn('User', 'allowsAutomaticPayRateIncrease')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('allowsAutomaticPayRateIncrease');
            });
        }

        //endregion

        //region Table: UserShiftDay

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->boolean('glideRequireTimeBlock')->nullable();
        });

        //endregion Table: UserShiftDay

        //region Table: TimeSheet

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->boolean('isGlide')->default(false);
            $table->char('glideDurationType', 1)->nullable()->default(null);
        });

        //endregion Table: TimeSheet

        //region Table: TimeSheetDay

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->boolean('isGlide')->nullable()->default(false);
            $table->char('glideDurationType', 1)->nullable()->default(null);

            $table->date('glidePeriodStart')->nullable()->default(null);
            $table->date('glidePeriodEnd')->nullable()->default(null);
            $table->decimal('glidePeriodHoursWorked', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursLeave', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursToil', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursRosteredTimeOff', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursAllowance', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodMilesAllowance', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodValueAllowance', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursMore', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursLess', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursTotal', 16, 10)->nullable()->default(0);
            $table->decimal('glidePeriodHoursExpected', 16, 10)->nullable()->default(0);

            $table->boolean('glideDayIsOn')->nullable()->default(false);
            $table->decimal('glideDayAverageHoursExpected', 16, 10)->nullable()->default(0);
            $table->decimal('glideDayMinHoursExpected', 16, 10)->nullable()->default(0);
            $table->decimal('glideDayMaxHoursExpected', 16, 10)->nullable()->default(0);
            $table->dateTime('glideDayExpectedStartDateTime')->nullable()->default(null);
            $table->dateTime('glideDayExpectedEndDateTime')->nullable()->default(null);
        });

        //endregion Table: TimeSheetDay
    }

    public function down(): void
    {
        //region Table: TimeSheetDay
        if (Schema::hasColumn('TimeSheetDay', 'glideDayExpectedEndDateTime')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayExpectedEndDateTime');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glideDayExpectedStartDateTime')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayExpectedStartDateTime');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glideDayMaxHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMaxHoursExpected');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glideDayMinHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMinHoursExpected');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glideDayAverageHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayAverageHoursExpected');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glideDayIsOn')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayIsOn');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExpected');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursTotal')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursTotal');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursLess')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursLess');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursMore')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursMore');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValueAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValueAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodMilesAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodMilesAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursRosteredTimeOff')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursRosteredTimeOff');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursToil')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursToil');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursLeave')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursLeave');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursWorked')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursWorked');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodEnd')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodEnd');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodStart')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodStart');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glideDurationType')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDurationType');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'isGlide')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('isGlide');
            });
        }
        // endregion Table: TimeSheetDay

        // region Table: TimeSheet
        if (Schema::hasColumn('TimeSheet', 'glideDurationType')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('glideDurationType');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'isGlide')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('isGlide');
            });
        }
        //endregion Table: TimeSheet

        //region Table: UserShiftDay

        if (Schema::hasColumn('UserShiftDay', 'glideRequireTimeBlock')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideRequireTimeBlock');
            });
        }

        //endregion Table: UserShiftDay
    }
};
