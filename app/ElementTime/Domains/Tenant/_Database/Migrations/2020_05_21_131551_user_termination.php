<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->unsignedBigInteger('terminationActorUser_id')->nullable()->after('endDate');
            $table->foreign('terminationActorUser_id', 'user_tuser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->string('terminationActorName')->nullable()->after('terminationActorUser_id');
            $table->longText('terminationComments')->nullable()->after('terminationActorName');
            $table->dateTime('terminationDateTime')->nullable()->after('terminationComments');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'terminationDateTime')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('terminationDateTime');
            });
        }
        if (Schema::hasColumn('User', 'terminationComments')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('terminationComments');
            });
        }
        if (Schema::hasColumn('User', 'terminationActorName')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('terminationActorName');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM User WHERE Key_name=\'user_tuser_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropForeign('user_tuser_fn');
            });
        }
        if (Schema::hasColumn('User', 'terminationActorUser_id')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('terminationActorUser_id');
            });
        }
    }
};
