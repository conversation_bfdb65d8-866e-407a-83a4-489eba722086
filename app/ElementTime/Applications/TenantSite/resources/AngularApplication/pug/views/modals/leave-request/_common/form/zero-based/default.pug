div
  .row
    .col-xs-12(ng-class='{"col-md-6": !!$ctrl.form.data.payRun}')
      .form-group(elt-help-text="modals.leave-request|fields.pay-run")
        label.label-control Select payrun

        .lead.m0.bb(ng-if='!!$ctrl.form.specific.options.payRun.isLoading') Loading...

        div(ng-if='!$ctrl.form.specific.options.payRun.isLoading && $ctrl.form.specific.options.payRun.data')
          ui-select(ng-if='$ctrl.form.specific.options.payRun.data.length > 1' ng-model='$ctrl.form.data.payRun' theme='bootstrap' reset-search-input='true' ng-change='$ctrl.form.specific.$events.onChangePayRun()' ng-required='true' ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting')
            ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.label}}
            ui-select-choices(repeat='object in $ctrl.form.specific.options.payRun.data | filter : $select.search')
              div(ng-bind-html='object.labelHtml | highlight: $select.search')

          .lead.m0.bb(ng-if='$ctrl.form.specific.options.payRun.data.length === 1') {{$ctrl.form.data.payRun.label}}

    .col-xs-12.col-md-6(ng-if='!!$ctrl.form.data.payRun')
      .form-group
        label(for='Leave_duration') Leave duration (hours)
        input.form-control.pl.pr(
          ng-model="$ctrl.form.data.duration"
          ng-required='true'
          ng-change='$ctrl.form.specific.$events.onChangeDuration()'
          ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
          type='text'
          id='Leave_duration'
          name='duration'
          input-mask
          input-options='{"name": "number", "min": 0, "max": 100, "allowMinus": false}'
        )
