<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Support\Helpers\ELT_Database;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PayRunType

        Schema::table('PayRunType', function (Blueprint $table) {
            $table->tinyInteger('dueDayForTimeSheets')->default(0);
        });

        //endregion Table: PayRunType

        //region Table: PayRun

        Schema::table('PayRun', function (Blueprint $table) {
            $table->date('closingDate')->nullable();
        });

        //endregion Table: PayRun

        //region Apply changes on records for table PayRun

        $payRuns = DB::connection(ELT_Database::$tenantConnectionName)
            ->table('PayRun')
            ->whereNull('closingDate')
            ->get();
        foreach ($payRuns as $payRun) {
            DB::connection(ELT_Database::$tenantConnectionName)
                ->table('PayRun')
                ->where('id', '=', $payRun->id)
                ->update([
                    'closingDate' => $payRun->dueDate,
                ]);
        }

        //endregion Apply changes on records for table PayRun
    }

    public function down(): void
    {
        //region Table: PayRun

        if (Schema::hasColumn('PayRun', 'closingDate')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('closingDate');
            });
        }

        //endregion Table: PayRun

        //region Table: PayRunType

        if (Schema::hasColumn('PayRunType', 'dueDayForTimeSheets')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('dueDayForTimeSheets');
            });
        }

        //endregion Table: PayRunType
    }
};
