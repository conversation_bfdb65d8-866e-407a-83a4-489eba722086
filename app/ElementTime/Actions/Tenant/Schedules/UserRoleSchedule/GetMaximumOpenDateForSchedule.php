<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule;

use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Users\BaseUsersAction;
use Element\ElementTime\Actions\Tenant\Users\UserRole\GetMaximumOpenDateForRole;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;

class GetMaximumOpenDateForSchedule extends BaseUsersAction
{
    /** @throws \Throwable */
    public function handle(UserRoleSchedule $userRoleSchedule): CarbonInterface|null
    {
        $maxDate = $userRoleSchedule->endDate?->copy();
        $maxUserRoleDate = GetMaximumOpenDateForRole::make()->handle($userRoleSchedule->userRole);

        if (!is_null($maxUserRoleDate) && (is_null($maxDate) || $maxUserRoleDate->lt($maxDate))) {
            $maxDate = $maxUserRoleDate->copy();
        }

        return $maxDate;
    }
}
