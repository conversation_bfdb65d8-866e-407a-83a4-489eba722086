<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest;

use Element\Core\Exceptions\NullValueException;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Illuminate\Database\Eloquent\Collection;

class LeaveSyncAttachments extends BaseLeavesAction
{
    /**
     * @param Collection<MediaFile>|MediaFile[] $mediaFiles
     * @throws NullValueException
     */
    public function handle(LeaveRequest $leaveRequest, Collection|array $mediaFiles): LeaveRequest
    {
        if (is_null($leaveRequest->id)) {
            throw new NullValueException('Request not saved');
        }

        $leaveRequest->attachmentFiles()->sync($mediaFiles);
        $leaveRequest->load(['attachmentFiles']);

        return $leaveRequest;
    }
}
