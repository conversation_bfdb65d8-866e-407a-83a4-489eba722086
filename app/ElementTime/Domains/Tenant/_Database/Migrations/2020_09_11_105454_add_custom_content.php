<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: CustomContent

        Schema::create('CustomContent', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('model_type')->nullable();
            $table->unsignedBigInteger('model_id')->nullable();
            $table->string('slug');
            $table->string('type'); // ID from type class
            $table->unique(['model_type', 'model_id', 'slug']);

            // Details
            $table->boolean('isActive')->default(false);
            $table->text('title')->nullable();
            $table->longText('content')->nullable();
            $table->text('url')->nullable();

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: CustomContent

        //region Table: CustomContentAttachmentFile

        Schema::create('CustomContentAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('mediaFile_id');
            $table->foreign('mediaFile_id', 'ccaf_mf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('customContent_id');
            $table->foreign('customContent_id', 'ccaf_lr_fn')->references('id')->on('CustomContent')->onUpdate('cascade')->onDelete('cascade');

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: CustomContentAttachmentFile
    }

    public function down(): void
    {
        Schema::dropIfExists('CustomContentAttachmentFile');
        Schema::dropIfExists('CustomContent');
    }
};
