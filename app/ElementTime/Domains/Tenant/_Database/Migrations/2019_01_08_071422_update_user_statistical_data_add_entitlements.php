<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Statistical Data update

        Schema::table('User', function (Blueprint $table) {
            $table->string('statsGender')->nullable();
            $table->string('statsEthnicity')->nullable();
            $table->string('statsCitizenshipType')->nullable();
            $table->string('statsQualification')->nullable();
        });

        $users = DB::table('User')->get();
        foreach ($users as $user) {
            DB::table('User')->where('id', '=', $user->id)->update([
                'statsGender' => $user->gender,
                'statsEthnicity' => $user->ethnicity,
                'statsCitizenshipType' => $user->citizenshipType,
                'statsQualification' => $user->qualification,
            ]);
        }

        //endregion Statistical Data update

        //region Table: EntitlementType

        Schema::create('EntitlementType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Details
            $table->string('name');
            $table->text('comments')->nullable();
            $table->char('status', 1)->default('A');

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: EntitlementType

        //region Table: UserEntitlement

        Schema::create('UserEntitlement', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'ue_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('entitlementType_id');
            $table->foreign('entitlementType_id', 'ue_et')->references('id')->on('EntitlementType')->onUpdate('cascade')->onDelete('cascade');

            // Details
            $table->text('comments')->nullable();
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->default('A');

            $table->timestamps();
        });

        //endregion Table: UserEntitlement
    }

    public function down(): void
    {
        //region Table: UserEntitlement

        Schema::dropIfExists('UserEntitlement');

        //endregion Table: UserEntitlement

        //region Table: EntitlementType

        Schema::dropIfExists('EntitlementType');

        //endregion Table: EntitlementType

        //region Statistical Data update

        if (Schema::hasColumn('User', 'statsQualification')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('statsQualification');
            });
        }
        if (Schema::hasColumn('User', 'statsCitizenshipType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('statsCitizenshipType');
            });
        }
        if (Schema::hasColumn('User', 'statsEthnicity')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('statsEthnicity');
            });
        }
        if (Schema::hasColumn('User', 'statsGender')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('statsGender');
            });
        }

        //endregion Statistical Data update
    }
};
