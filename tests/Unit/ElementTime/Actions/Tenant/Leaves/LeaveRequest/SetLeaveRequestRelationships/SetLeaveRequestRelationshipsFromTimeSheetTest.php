<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromTimeSheet;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRun\CreateScheduleForUserOnPayRun;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\TimeType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeGroup;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\FullTimeType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRoleRepository;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

describe('Duration only - set relationships from Timesheet', function () {
    it('does set timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;

        foreach ($leaveRequest->days as $day) {
            $day->refresh();

            /** @var TimeSheetDay $expectedTimeSheetDay */
            $expectedTimeSheetDay = $timeSheet->timeSheetDays->firstWhere('date', '=', $day->date);
            /** @var ScheduledPayRunPeriod $scheduledPayRunPeriod */
            $scheduledPayRunPeriod = $expectedScheduledPayRun->periods->firstWhere(fn (ScheduledPayRunPeriod $period) => $day->date->isBetween($period->startDate, $period->endDate, true));
            $scheduledPayRunPeriodDay = $scheduledPayRunPeriod->days->firstWhere(fn (ScheduledPayRunPeriodDay $periodDay) => $day->date->isSameDay($periodDay->date));

            expect($day->timeSheetDay_id)->toEqual($expectedTimeSheetDay->id)
                ->and($day->timeSheetPeriod_id)->toEqual($scheduledPayRunPeriod->timeSheetPeriod->id)
                ->and($day->scheduledPayRunPeriodDay_id)->toEqual($scheduledPayRunPeriodDay->id)
            ;
        }
    });

    it('does fix timeSheet_id and scheduledPayRun_id if values are not correct', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $wrongTimeSheet = TimeSheet::q()->many()->random();
        $wrongScheduledPayRun = ScheduledPayRun::q()->many()->random();

        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest->clearSaveOrFail();
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->timeSheet_id)->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($wrongScheduledPayRun->id)
        ;

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->not()->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->not()->toEqual($wrongScheduledPayRun)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;

        foreach ($leaveRequest->days as $day) {
            $day->refresh();

            /** @var TimeSheetDay $expectedTimeSheetDay */
            $expectedTimeSheetDay = $timeSheet->timeSheetDays->firstWhere('date', '=', $day->date);
            /** @var ScheduledPayRunPeriod $scheduledPayRunPeriod */
            $scheduledPayRunPeriod = $expectedScheduledPayRun->periods->firstWhere(fn (ScheduledPayRunPeriod $period) => $day->date->isBetween($period->startDate, $period->endDate, true));
            $scheduledPayRunPeriodDay = $scheduledPayRunPeriod->days->firstWhere(fn (ScheduledPayRunPeriodDay $periodDay) => $day->date->isSameDay($periodDay->date));

            expect($day->timeSheetDay_id)->toEqual($expectedTimeSheetDay->id)
                ->and($day->timeSheetPeriod_id)->toEqual($scheduledPayRunPeriod->timeSheetPeriod->id)
                ->and($day->scheduledPayRunPeriodDay_id)->toEqual($scheduledPayRunPeriodDay->id)
            ;
        }
    });
});

describe('Work pattern - set relationships from Timesheet', function () {
    it('does set timesheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;

        foreach ($leaveRequest->days as $day) {
            $day->refresh();

            /** @var TimeSheetDay $expectedTimeSheetDay */
            $expectedTimeSheetDay = $timeSheet->timeSheetDays->firstWhere('date', '=', $day->date);
            /** @var ScheduledPayRunPeriod $scheduledPayRunPeriod */
            $scheduledPayRunPeriod = $expectedScheduledPayRun->periods->firstWhere(fn (ScheduledPayRunPeriod $period) => $day->date->isBetween($period->startDate, $period->endDate, true));
            /** @var ScheduledPayRunPeriodDay $scheduledPayRunPeriodDay */
            $scheduledPayRunPeriodDay = $scheduledPayRunPeriod->days->firstWhere(fn (ScheduledPayRunPeriodDay $periodDay) => $day->date->isSameDay($periodDay->date));

            expect($day->timeSheetDay_id)->toEqual($expectedTimeSheetDay->id)
                ->and($day->timeSheetPeriod_id)->toEqual($scheduledPayRunPeriod->timeSheetPeriod->id)
                ->and($day->scheduledPayRunPeriodDay_id)->toEqual($scheduledPayRunPeriodDay->id)
                ->and($day->userRoleScheduleDay)->toEqual($scheduledPayRunPeriodDay->specific->patternPeriodShiftDay)
            ;
        }
    });

    it('does fix timesheet_id and scheduledPayRun_id from timesheet if values are not correct', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $wrongTimeSheet = TimeSheet::q()->many()->random();
        $wrongScheduledPayRun = ScheduledPayRun::q()->many()->random();

        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest->clearSaveOrFail();

        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->not()->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->not()->toEqual($wrongScheduledPayRun->id)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;

        foreach ($leaveRequest->days as $day) {
            $day->refresh();

            /** @var TimeSheetDay $expectedTimeSheetDay */
            $expectedTimeSheetDay = $timeSheet->timeSheetDays->firstWhere('date', '=', $day->date);
            /** @var ScheduledPayRunPeriod $scheduledPayRunPeriod */
            $scheduledPayRunPeriod = $expectedScheduledPayRun->periods->firstWhere(fn (ScheduledPayRunPeriod $period) => $day->date->isBetween($period->startDate, $period->endDate, true));
            /** @var ScheduledPayRunPeriodDay $scheduledPayRunPeriodDay */
            $scheduledPayRunPeriodDay = $scheduledPayRunPeriod->days->firstWhere(fn (ScheduledPayRunPeriodDay $periodDay) => $day->date->isSameDay($periodDay->date));

            expect($day->timeSheetDay_id)->toEqual($expectedTimeSheetDay->id)
                ->and($day->timeSheetPeriod_id)->toEqual($scheduledPayRunPeriod->timeSheetPeriod->id)
                ->and($day->scheduledPayRunPeriodDay_id)->toEqual($scheduledPayRunPeriodDay->id)
                ->and($day->userRoleScheduleDay)->toEqual($scheduledPayRunPeriodDay->specific->patternPeriodShiftDay)
            ;
        }
    });
});

describe('Zero based - set relationships from Timesheet', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true);
        $user = $leaveSettings->userLeaveBankType->bank->user;
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });

    it('does fix leave timeSheet_id and scheduledPayRun_id if values are not correct', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true);
        $user = $leaveSettings->userLeaveBankType->bank->user;
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $wrongTimeSheet = TimeSheet::q()->many()->random();
        $wrongScheduledPayRun = ScheduledPayRun::q()->many()->random();

        $leaveRequest->timeSheet_id = $wrongTimeSheet->id;
        $leaveRequest->scheduledPayRun_id = $wrongScheduledPayRun->id;
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $timeSheet = TimeSheet::getByDate($user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $leaveSettings->payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $leaveSettings->payRun->endDate],
            ])
            ->first();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->not()->toEqual($wrongTimeSheet->id)
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->not()->toEqual($wrongScheduledPayRun)
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('General - set relationships from Timesheet', function () {
    it('does set to at least one leave request if timesheet is not submitted', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);

        $timeSheet = TimeSheet::getByDate($workPatternLeaveSettings->user, $payRun->startDate);
        $timeSheet->workflow->status = NewType::ID;
        $timeSheet->workflow->clearSave();

        SetLeaveRequestRelationshipsFromTimeSheet::partialMock()
            ->expects('setLeaveRequestRelationships')
            ->atLeast()
            ->once();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);
    });

    it('does not set to any leave request related to approved timesheet', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);

        $timeSheet = TimeSheet::getByDate($workPatternLeaveSettings->user, $payRun->startDate);
        $timeSheet->workflow->status = ApprovedType::ID;
        $timeSheet->workflow->clearSave();

        SetLeaveRequestRelationshipsFromTimeSheet::partialMock()
            ->allows('setLeaveRequestRelationships')
            ->never();

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);
    });

    it('does set to multiple leave requests related to a single timesheet', function () {
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, isFullDay: true);
        $payRun = $workPatternLeaveSettings->payRun;
        $user = $workPatternLeaveSettings->user;
        $timeSheet = TimeSheet::getByDate($user, $payRun->startDate);
        $role = Role::q([
            ['Role.status', '=', ActiveStatus::ID],
            ['Role.id', '<>', $workPatternLeaveSettings->userRoleSchedule->userRole->role_id]
        ])->many()->random();

        $newUserRole = UserRoleRepository::assignRoleToUser(
            $role,
            $user,
            $workPatternLeaveSettings->userRoleSchedule->userRole->startDate,
            false,
            (object) [
                'doesOverrideExternalId' => false,
                'externalId' => 'test_lorem_ipsum',
                'contractedHours' => 80,
                'employeeTypeStatusType' => FullTimeType::ID,
                'costing' => [
                    'doesAllowNonStandardPlant' => false,
                    'doesAllowUserToUpdateWorkDetails' => false,
                ],
                'departmentData' => [[
                    'department_id' => Department::q()->onlyActive()->many()->random()->id,
                    'isManager' => false,
                ]],
                'masterProjectData' => [[
                    'project_id' => Project::q()->onlyActive()->many()->random()->id,
                    'isMaster' => false,
                ]],
                'payTypesData' => [[
                    'payType_id' => PayType::q()->onlyActive()->many()->random()->id,
                    'overtimeCustomAmount' => null,
                    'overtimeCustomRateType' => 'Y',
                ]],
                'roleManagerData' => [[
                    'manager_id' => User::q()->onlyActive()->many()->random()->id,
                ]],
                'timeTypesData' => [
                    [
                        'timeType_id' => TimeType::q()->onlyActive()->many()->random()->id,
                        'excessTimeGroup_id' => ExcessTimeGroup::q()->onlyActive()->many()->random()->id,
                        'isActive' => true,
                        'isMaster' => true,
                    ]
                ],
            ],
        );

        $zeroBasedUserRoleSchedule = UserRoleSchedule::addToUserRoleByData(
            $newUserRole,
            $payRun->startDate,
            [
                'type' => UserRoleScheduleType::ZeroBased->id(),
                'zeroBased' => [
                    'dailyMaxDuration' => null,
                    'doesHaveDailyMaxDuration' => false,
                    'doesHaveFixedLocation' => false,
                    'doesHaveMaxDuration' => false,
                    'maxDuration' => null,
                    'scheduleLocation_id' => null,
                ],
            ],
            true,
        );
        CreateScheduleForUserOnPayRun::make()->handle($zeroBasedUserRoleSchedule, $payRun);
        $timeSheet->load(['scheduledPayRuns']);

        $zeroBasedLeaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun, userRoleSchedule: $zeroBasedUserRoleSchedule);

        [$workPatternLeaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);
        [$zeroBasedLeaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettings);

        $expectedScheduledPayRunForWorkPattern = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedScheduledPayRunForZeroBased = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $zeroBasedUserRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle($timeSheet);
        $workPatternLeaveRequest->refresh();
        $zeroBasedLeaveRequest->refresh();

        expect($workPatternLeaveRequest->id)->toBeInt()
            ->and($zeroBasedLeaveRequest->id)->toBeInt()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForWorkPattern->id)
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForZeroBased->id)
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->not()->toEqual($zeroBasedLeaveRequest->scheduledPayRun_id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeInt()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeInt()
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($zeroBasedLeaveRequest->timeSheet_id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($timeSheet->id)
        ;
    });
});
