@extends('Reports::_views.tenant.pdf_master')

@section('title', 'Pay run '.$timeSheet->payRun->number.' - '.$timeSheet->payRun->startDate.' to '.$timeSheet->payRun->endDate.' - for '.$timeSheet->user->fullName)
@section('description', 'Individual timesheet details')
@section('keywords', 'payrun, timesheet')
@section('type', 'Individual timesheet details')

@section('header')
    <style type="text/css" media="screen,print">
        .block-with-title { display: block; position: relative; width: 100%; page-break-inside: avoid; }
        .block-title  { display: block; position: relative; width: 100%; margin: 0 0 0; font-weight: normal; font-size: 12px; line-height: 19px; text-align: right; }
        .full-block { display: block; position: relative; width: 100%; padding: 0 8px; margin: 0 0 10px; border: 1px solid #333; }
        .full-block .block-item { position: relative; width: 100%; min-height: 56px; margin: 8px 0; page-break-inside: avoid; }
        .full-block .block-item .block-left-photo { display: block; position: absolute; left: 0; top: 0; width: 56px; height: 56px; text-align: center; line-height: 56px; }
        .full-block .block-item .block-left-photo img { width: 56px; height: 56px; }
        .full-block .block-item .block-left-icon { display: block; position: absolute; left: 0; top: 0; width: 56px; height: 56px; padding: 10px; background: #fd9300; text-align: center; line-height: 36px; }
        .full-block .block-item .block-left-icon svg { fill: #fff; width: 36px; height: 36px; }
        .full-block .block-item .block-content { display: block; width: 100%; padding: 5px 0 0 68px; }
        .full-block .block-item .block-content .label { display: block; position: relative; width: 100%; font-size: 10px; line-height: 16px; }
        .full-block .block-item .block-content .value { display: block; position: relative; width: 100%; font-size: 20px; line-height: 30px; }
        .full-block .block-item .block-content hr { width: 100%; height: 1px; margin: 10px 0; padding: 0; background: #000; border: 0 none; }

        .block-table, .data-table { width: 100%; margin: 0; }
        .block-table tr, .data-table tr {  }
        .block-table tr th, .data-table tr th { padding: 0 5px; background: #edeff2; font-weight: normal; font-size: 10px; line-height: 21px; text-align: left; }
        .block-table tr th { border-top: 1px solid #000; }
        .block-table-bb tr td { border-bottom: 1px solid #000; }
        .block-table tr .th1 { width: 27%; }
        .block-table tr .th2 { width: 30%; }
        .block-table tr .th3 { width: 43%; }
        .block-calculations-table tr .th1,
        .block-calculations-table tr .th2,
        .block-calculations-table tr .th3,
        .block-calculations-table tr .th4 { width: 25%; }
        .block-table tr td, .data-table tr td { padding: 0 5px; font-weight: normal; font-size: 10px; line-height: 23px; text-align: left; vertical-align: top; }
        .data-table tr td { border-bottom: 1px solid #000; height: auto; }
        .data-table tr:last-child td { border-bottom: 0 none; }

        .data-table-details tr td { page-break-inside: avoid; }
        .data-table-details .th1 { width: 22%; }
        .data-table-details .th2 { width: 12%; }
        .data-table-details .th3 { width: 53%; }
        .data-table-details .th4 { width: 6%; }
        .data-table-details .th5 { width: 7%; }
        .data-table-details .td-full { width: 100%; padding-left: 0 !important; padding-right: 0 !important; }
        .data-table-details>tbody>tr>td { border-bottom: 1px solid #000 !important; }
        .data-table-details>tbody>tr:last-child>td { border-bottom: 0 none !important; }

        .data-table-details-internal>tbody>tr>td { border-bottom: 1px solid #DDD !important; }
        .data-table-details-internal>tbody>tr:last-child>td { border-bottom: 0 none !important; }
        .data-table-details-internal .td1 { width: 22%; }
        .data-table-details-internal .td2 { width: 12%; }
        .data-table-details-internal .td3 { width: 53%; }
        .data-table-details-internal .td4 { width: 6%; }
        .data-table-details-internal .td5 { width: 7%; }

        .data-table-details-internal-work tr td { border-bottom: 0 none !important; }
        .data-table-details-internal-work tr:last-child td { border-bottom: 0 none !important; }

        .data-table-allowances tr td { page-break-inside: avoid; }
        .data-table-allowances .th1 { width: 16%; }
        .data-table-allowances .th2 { width: 84%; }
        .data-table-allowances .th2 td { font-size: 10px; line-height: 23px; }

        .data-table-allowances-internal tr td { border-bottom: 1px solid #DDD !important; }
        .data-table-allowances-internal tr:last-child td { border-bottom: 0 none !important; }
        .data-table-allowances-internal .td1 { width: 70%; }
        .data-table-allowances-internal .td2 { width: 10%; }
        .data-table-allowances-internal .td3 { width: 20%; }

        .block-project:last-child .block-content hr { display: none; }

        .comment { color: #999; }

        .bold { font-weight: bold; }
    </style>
@endsection

@section('body')
    {{--  General data  --}}
    <div class="full-block">
        <div class="block-item block-organisation">
            <div class="block-left-icon">
                <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" xml:space="preserve">
                    <g>
                        <path d="M23,10h-5V1c0-0.6-0.4-1-1-1H7C6.4,0,6,0.4,6,1v9H1c-0.6,0-1,0.4-1,1v13h24V11C24,10.4,23.6,10,23,10z M2,22V12h4v10H2z    M8,22V11V2h8v9v11H8z M18,22V12h4v10H18z"/>
                        <rect height="2" width="1" x="4" y="13"/>
                        <rect height="2" width="1" x="4" y="16"/>
                        <rect height="2" width="1" x="19" y="13"/>
                        <rect height="2" width="1" x="19" y="16"/>
                        <rect height="2" width="1" x="10" y="4"/>
                        <rect height="2" width="1" x="10" y="7"/>
                        <rect height="2" width="1" x="10" y="10"/>
                        <rect height="2" width="1" x="10" y="13"/>
                        <rect height="2" width="1" x="10" y="16"/>
                        <rect height="2" width="1" x="13" y="4"/>
                        <rect height="2" width="1" x="13" y="7"/>
                        <rect height="2" width="1" x="13" y="10"/>
                        <rect height="2" width="1" x="13" y="13"/>
                        <rect height="2" width="1" x="13" y="16"/>
                    </g>
                </svg>
            </div>
            <div class="block-content">
                <label class="label">Organisation</label>
                <label class="value">{{ $tenant->name }}</label>

                <table class="block-table block-table-bb" width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th width="1">Fiscal&nbsp;year</th>
                            <th width="1">Pay&nbsp;run&nbsp;number</th>
                            <th>Pay&nbsp;run&nbsp;date</th>
                            @if(!is_null($timeSheet->payRun->name) && $timeSheet->payRun->name != '')
                                <th>Pay&nbsp;run&nbsp;name</th>
                            @endif
                            <th style="text-align: center;" width="1">Current&nbsp;status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $timeSheet->payRun->fiscalYear }}</td>
                            <td>{{ $timeSheet->payRun->number }}</td>
                            <td>{{ $timeSheet->payRun->startDate }}&nbsp;to&nbsp;{{ $timeSheet->payRun->endDate }}</td>
                            @if(!is_null($timeSheet->payRun->name) && $timeSheet->payRun->name != '')
                                <td>{{ $timeSheet->payRun->name }}</td>
                            @endif
                            <td style="text-align: center; background-color: {{ $timeSheet->currentStatus->color }};">{{ $timeSheet->currentStatus->name }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="block-item block-organisation">
            <div class="block-left-photo">
                <img src="{{ $timeSheet->user->image }}" alt="{{ $timeSheet->user->fullName }}">
            </div>
            <div class="block-content">
                <label class="label">Staff name</label>
                <label class="value">{{ $timeSheet->user->fullName }}</label>

                <table class="block-table" width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th class="th1">System ID</th>
                            <th class="th2">External reference ID</th>
                            <th class="th3">Date of birth</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $timeSheet->user->id }}</td>
                            <td>{{ $timeSheet->user->externalId }}</td>
                            <td>{{ $timeSheet->user->birthday }}</td>
                        </tr>
                    </tbody>
                </table>
                <table class="block-table" width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th class="th1">Role</th>
                            <th class="th2">Department</th>
                            <th class="th3">Direct Manager</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $timeSheet->user->role }}</td>
                            <td>{{ $timeSheet->user->department }}</td>
                            <td>{{ $timeSheet->user->manager }}</td>
                        </tr>
                    </tbody>
                </table>
                <table class="block-table block-table-bb" width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th class="th1">Company employment start date</th>
                            <th class="th2">Govt. employment start date</th>
                            <th class="th3">Current pay rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $timeSheet->user->startDatePermanent }}</td>
                            <td>{{ $timeSheet->user->startDateGovernment }}</td>
                            <td>{{ $timeSheet->user->payRate }}</td>
                        </tr>
                    </tbody>
                </table>
                @if (count($timeSheet->user->advisoryNotes) > 0)
                <table class="block-table block-table-bb" width="100%" cellspacing="0" cellpadding="0">
                    <thead>
                        <tr>
                            <th class="th1">Advisory notes</th>
                        </tr>
                    </thead>
                    <tbody>
                    @foreach ($timeSheet->user->advisoryNotes as $advisoryNote)
                        <tr>
                            <td>
                                <strong>{{ $advisoryNote->title }}</strong>
                                <br>
                                {{ $advisoryNote->description }}
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
                @endif
            </div>
        </div>

        <div class="block-item block-calculations">
            <div class="block-left-icon">
                <svg enable-background="new 0 0 500 500" id="Layer_1" version="1.1" viewBox="0 0 500 500"
                     xml:space="preserve" xmlns="http://www.w3.org/2000/svg">
                    <path clip-rule="evenodd" d="M100.59,431.711c0,20.078,16.263,36.34,36.342,36.34h227.137  c20.078,0,36.341-16.262,36.341-36.34V68.29c0-20.077-16.263-36.34-36.341-36.34H136.932c-20.079,0-36.342,16.264-36.342,36.34  V431.711z M166.007,77.375h168.988c11.082,0,19.987,8.905,19.987,19.991v59.959c0,11.086-8.905,19.991-19.987,19.991H166.007  c-11.086,0-19.989-8.905-19.989-19.991V97.367C146.018,86.28,154.921,77.375,166.007,77.375z M146.018,236.371  c0-12.537,10.177-22.713,22.713-22.713s22.714,10.177,22.714,22.713c0,12.538-10.178,22.715-22.714,22.715  S146.018,248.909,146.018,236.371z M227.788,236.371c0-12.537,10.177-22.713,22.713-22.713c12.536,0,22.715,10.177,22.715,22.713  c0,12.538-10.179,22.715-22.715,22.715C237.964,259.086,227.788,248.909,227.788,236.371z M309.557,236.371  c0-12.537,10.175-22.713,22.716-22.713c12.535,0,22.71,10.177,22.71,22.713c0,12.538-10.175,22.715-22.71,22.715  C319.731,259.086,309.557,248.909,309.557,236.371z M146.018,318.142c0-12.536,10.177-22.715,22.713-22.715  s22.714,10.179,22.714,22.715c0,12.537-10.178,22.711-22.714,22.711S146.018,330.679,146.018,318.142z M227.788,318.142  c0-12.536,10.177-22.715,22.713-22.715c12.536,0,22.715,10.179,22.715,22.715c0,12.537-10.179,22.711-22.715,22.711  C237.964,340.853,227.788,330.679,227.788,318.142z M309.557,318.142c0-12.536,10.175-22.715,22.716-22.715  c12.535,0,22.71,10.179,22.71,22.715c0,12.537-10.175,22.711-22.71,22.711C319.731,340.853,309.557,330.679,309.557,318.142z   M146.018,399.909c0-12.536,10.177-22.711,22.713-22.711s22.714,10.175,22.714,22.711c0,12.54-10.178,22.715-22.714,22.715  S146.018,412.449,146.018,399.909z M227.788,399.909c0-12.536,10.177-22.711,22.713-22.711c12.536,0,22.715,10.175,22.715,22.711  c0,12.54-10.179,22.715-22.715,22.715C237.964,422.624,227.788,412.449,227.788,399.909z M309.557,399.909  c0-12.536,10.175-22.711,22.716-22.711c12.535,0,22.71,10.175,22.71,22.711c0,12.54-10.175,22.715-22.71,22.715  C319.731,422.624,309.557,412.449,309.557,399.909z" fill="#FFFFFF" fill-rule="evenodd"/>
                </svg>
            </div>
            <div class="block-content">
                <label class="value">Summary</label>

                @foreach ($timeSheet->summary as $summaryBlock)
                    <table class="block-table block-calculations-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            @for ($i = 0; $i < count($summaryBlock); $i++)
                                <th class="th{{$i+1}}">{{$summaryBlock[$i]->name}}</th>
                            @endfor
                        </thead>
                        <tbody>
                            @for ($i = 0; $i < count($summaryBlock); $i++)
                                <td>{{$summaryBlock[$i]->value}}</td>
                            @endfor
                        </tbody>
                    </table>
                @endforeach
            </div>
        </div>
    </div>

    {{--  Time types totals  --}}
    @if (count($timeTypes) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Time types</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th>Time type</th>
                                <th width="1">Total&nbsp;hours&nbsp;recorded</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($timeTypes as $timeType)
                                <tr>
                                    <td>{{ $timeType->name }}</td>
                                    <td class="ar">{{ etime_round($timeType->hours, 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Projects totals  --}}
    @if (count($projects) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Projects</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th width="1">Code</th>
                                <th>Project name</th>
                                <th width="1">Total&nbsp;hours&nbsp;recorded</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($projects as $project)
                                <tr>
                                    <td>{{ $project->code }}</td>
                                    <td>{{ $project->name }}</td>
                                    <td class="ar">{{ etime_round($project->hours, 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Public holidays totals  --}}
    @if (count($publicHolidays) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Public holidays</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th width="1">Date</th>
                                <th>Public holiday</th>
                                <th width="1">Hours</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($publicHolidays as $publicHoliday)
                                <tr>
                                    <td>{{ $publicHoliday->date }}</td>
                                    <td>{{ $publicHoliday->name }}</td>
                                    <td class="ar">{{ etime_round($publicHoliday->hours, 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Leave totals  --}}
    @if ($config->hasLeaveOptions && count($leaves) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Leave</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th width="1">Code</th>
                                <th>Leave name</th>
                                <th width="1">Total&nbsp;leave&nbsp;used</th>
                                <th width="1">Total&nbsp;leave&nbsp;earnt</th>
                                <th width="1">Total&nbsp;available</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($leaves as $leave)
                                <tr>
                                    <td>{{ $leave->code }}</td>
                                    <td>{{ $leave->name }}</td>
                                    <td class="ar">{{ etime_round($leave->hoursUsed, 2) }} @if($config->doesShowLeaveObs) * @endif</td>
                                    <td class="ar">{{ etime_round($leave->hoursEarnt, 2) }} @if($config->doesShowLeaveObs) * @endif</td>
                                    <td class="ar">{{ etime_round($leave->balanceAvailable, 2) }} @if($config->doesShowLeaveObs) * @endif</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    @if($config->doesShowLeaveObs)
                    <div style="text-align: right">
                        <small>* Pay-run not finished so values can still change</small>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    {{--  Allowances totals  --}}
    @if (count($allowances) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Allowances / Penalties / Duties</h2>
            <div class="full-block">
                <div class="block-item block-allowance">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th width="1">Code</th>
                                <th>Type</th>
                                <th width="1">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($allowances as $allowance)
                                <tr>
                                    <td>{{ $allowance->code }}</td>
                                    <td>{{ $allowance->name }}</td>
                                    <td class="ar">{{ etime_money($allowance->value, false) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Daily breakdown  --}}
    @if (count($timeSheet->timeSheetDays) > 0)
        <div class="page-break"></div>

        <div class="block-with-title no-margin">
            <h2 class="block-title">Time recorded - details</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table data-table-details" width="100%" cellspacing="0" cellpadding="0">
                        <tbody>
                            @foreach($timeSheet->timeSheetDays as $day)
                                <tr><td style="background-color: {{$day->style->backgroundColor}}; color: {{$day->style->color}};" class="td-full" colspan="4">
                                    <table class="data-table data-table-details-internal" width="100%" cellspacing="0" cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="bold" style="font-size: 130%">{{ $day->date }}</td>
                                                <td class="ar bold" style="font-size: 130%">Total: {{ etime_round($day->hoursTotal, 2) }} hours</td>
                                            </tr>
                                            @foreach($day->itemDays as $itemDay)
                                                @if($itemDay->type == 'work')
                                                    <tr>
                                                        <td class="td-full" colspan="4">
                                                            <table class="data-table data-table-details-internal-work"  width="100%" cellspacing="0" cellpadding="0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td colspan="3">&nbsp;&nbsp;&bullet;&nbsp;&nbsp;{{ $itemDay->timeBlock }}&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;{{$itemDay->role}} ({{ $itemDay->timeType }})</td>
                                                                    </tr>
                                                                    @foreach($itemDay->works as $work)
                                                                        <tr>
                                                                            <td style="font-size: 85%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{{ $work->name }} - {{$work->formattedCode}}</td>
                                                                            <td style="font-size: 70%; color: orange">{{ $work->plant }}</td>
                                                                            <td class="ar" style="font-size: 85%">{{$work->hours}} hours</td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                @elseif($itemDay->type == 'holiday')
                                                    <tr>
                                                        <td>&nbsp;&nbsp;&bullet;&nbsp;&nbsp;{{ $itemDay->name }}&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{{$itemDay->role}} - {{$itemDay->formattedCode}}</td>
                                                        <td class="ar">{{ etime_round($itemDay->hours, 2) }} hours</td>
                                                    </tr>
                                                @elseif($itemDay->type == 'leave')
                                                    <tr>
                                                        <td>&nbsp;&nbsp;&bullet;&nbsp;&nbsp;{{ $itemDay->timeBlock }}&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;{{$itemDay->role}}&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{{$itemDay->formattedCode}}&nbsp;&nbsp;-&nbsp;&nbsp;{{ $itemDay->name }}</td>
                                                        <td class="ar">{{ etime_round($itemDay->hours, 2) }} hours</td>
                                                    </tr>
                                                @else
                                                    <tr>
                                                        <td>&nbsp;&nbsp;&bullet;&nbsp;&nbsp;{{ $itemDay->timeBlock }}&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;&nbsp;{{$itemDay->role}} ({{ $itemDay->timeType }})&nbsp;&nbsp;&raquo;&nbsp;&nbsp;{{$itemDay->formattedCode}} - {{ $itemDay->name }}</td>
                                                        <td class="ar">{{ etime_round($itemDay->hours, 2) }} hours</td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        </tbody>
                                    </table>
                                </td></tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    @if(count($timeSheet->timeSheetAllowances) > 0 || count($timeSheet->timeSheetPenalties))
        <div class="page-break"></div>
    @endif

    {{--  Allownaces breakdown  --}}
    @if(count($timeSheet->timeSheetAllowances) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Adhoc allowances - details</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th class="th1" width="1">Date</th>
                                <th class="th2" width="1">Code</th>
                                <th class="th3">Allowance</th>
                                <th class="th4" width="1">Type</th>
                                <th class="th5 ar" width="1">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($timeSheet->timeSheetAllowances as $day)
                                <tr>
                                    <td><strong>{{ $day->date }}</strong></td>
                                    <td>{{ $day->code }}</td>
                                    <td>{{ $day->name }}</td>
                                    <td>{{ $day->type }}</td>
                                    <td class="ar">{{ etime_money($day->value, false) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Penalties breakdown  --}}
    @if(count($timeSheet->timeSheetPenalties) > 0)
        <div class="block-with-title">
            <h2 class="block-title">Auto allowances/penalties - details</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table" width="100%" cellspacing="0" cellpadding="0">
                        <thead>
                            <tr>
                                <th class="th1" width="1">Date</th>
                                <th class="th2" width="1">Code</th>
                                <th class="th3">Allowance/Penalty</th>
                                <th class="th4 ar" width="1">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($timeSheet->timeSheetPenalties as $day)
                                <tr>
                                    <td><strong>{{ $day->date }}</strong></td>
                                    <td>{{ $day->code }}</td>
                                    <td>{{ $day->name }}</td>
                                    <td class="ar">{{ etime_money($day->value, false) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    {{--  Workflow  --}}
    @if(!is_null($timeSheet->timeline) && count($timeSheet->timeline) > 0)
        <div class="page-break"></div>

        <div class="block-with-title">
            <h2 class="block-title">Workflow</h2>
            <div class="full-block">
                <div class="block-item">
                    <table class="data-table data-table-workflow" width="100%" cellspacing="0" cellpadding="0">
                        <tbody>
                        @foreach($timeSheet->timeline as $entry)
                            <tr>
                                <td class="td1">
                                    {{ $entry->description }}
                                    @if(!is_null($entry->reason) && strlen($entry->reason) > 0)
                                        <br>
                                        <span class="comment">{{ $entry->reason }}</span>
                                    @endif
                                </td>
                                <td class="td2 ar">at {{ $entry->dateTime }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

@endsection
