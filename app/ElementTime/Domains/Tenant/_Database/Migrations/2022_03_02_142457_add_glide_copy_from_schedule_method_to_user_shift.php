<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Schedules\Support\GlideCopyFromScheduleMethods\ExpectedAndAverageHoursMethod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserShift

        Schema::table('UserShift', function (Blueprint $table) {
            $table->string('glideCopyFromScheduleMethod')->default(ExpectedAndAverageHoursMethod::ID)->after('maximumDailyHours');
        });

        //endregion Table: UserShift
    }

    public function down(): void
    {
        //region Table: UserShift

        if (Schema::hasColumn('UserShift', 'glideCopyFromScheduleMethod')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glideCopyFromScheduleMethod');
            });
        }

        //endregion Table: UserShift
    }
};
