<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod;

use Carbon\CarbonInterface;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\NotImplementedException;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay\GetMissingDurationForScheduledDay;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay\GetOrdinaryGapsInScheduledDayWorkPattern;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\CreateTimeSheetWork;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetHoliday;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetPeriod;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleTimeType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Illuminate\Database\Eloquent\Collection;
use JetBrains\PhpStorm\ObjectShape;

class CompleteTimeSheetPeriodMissingHoursWithWork extends BaseTimeSheetsAction
{
    private array $gaps = [];
    private TimeSheetPeriod $timeSheetPeriod;
    private UserRoleTimeType $userRoleTimeType;

    private string $workType;
    private string|null $group_id;
    private string $model_id;
    private string|null $sub_id;
    private string $notes;

    /** @throws \Throwable */
    public function handle(
        TimeSheetPeriod $timeSheetPeriod,
        UserRoleTimeType $userRoleTimeType,
        string $workType,
        string|null $group_id,
        string $model_id,
        string|null $sub_id,
        string $notes = '',
    ): void
    {
        $scheduleType = $timeSheetPeriod->scheduleType;

        if ($scheduleType->is(UserRoleScheduleType::ZeroBased)) {
            throw new InvalidArgumentException("Action not supported for zero based schedule type.");
        }

        CalculateTimeSheetPeriodRelatedRecords::make()->handle(timeSheetPeriod: $timeSheetPeriod);

        $this->timeSheetPeriod = $timeSheetPeriod;
        $this->userRoleTimeType = $userRoleTimeType;
        $this->workType = $workType;
        $this->group_id = $group_id;
        $this->model_id = $model_id;
        $this->sub_id = $sub_id;
        $this->notes = $notes;

        $userRoleSchedule = $timeSheetPeriod->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule;
        $holidays = $timeSheetPeriod->timeSheetHolidays;
        $user = $timeSheetPeriod->timeSheet->payRunItem->user;

        $leaveRequestDaysFromAllRoles = $this->getRelevantLeaveRequestDays($timeSheetPeriod->timeSheet->payRunItem->user_id, $timeSheetPeriod->startDate, $timeSheetPeriod->endDate);
        $leaveRequestDaysFromSameRole = $leaveRequestDaysFromAllRoles->filter(fn (LeaveRequestDay $leaveRequestDay) => $leaveRequestDay->leaveRequest->userRoleSchedule_id === $userRoleSchedule->id);

        /** @var Collection|ScheduledPayRunPeriodDay[] $scheduledDays */
        $scheduledDays = $timeSheetPeriod->scheduledPayRunPeriod->days->filter(fn (ScheduledPayRunPeriodDay $day) => $day->isScheduled())->values();
        $missingDuration = GetTimeSheetPeriodMissingDuration::make()->handle($timeSheetPeriod);

        foreach ($scheduledDays as $scheduledDay) {
            $dayGapRecord = $this->getOrSetGapRecord($scheduledDay->date);
            $dayHolidays = $holidays->filter(fn (TimeSheetHoliday $holiday) => $holiday->timeSheetDay->date->isSameDay($scheduledDay->date));
            $dayLeaveRequestDaysFromSameRole = $leaveRequestDaysFromSameRole->where('date', '=', $scheduledDay->date);
            $dayLeaveRequestDayFromAllRoles = $leaveRequestDaysFromAllRoles->where('date', '=', $scheduledDay->date);

            $dayGapRecord->duration = GetMissingDurationForScheduledDay::make()->handle(
                day: $scheduledDay,
                holidays: $dayHolidays,
                leaveRequestDays: $dayLeaveRequestDaysFromSameRole,
            );

            $dayGapRecord->ordinaryGaps = $scheduleType->isDurationOnly()
                ? TimeSpanList::empty()
                : GetOrdinaryGapsInScheduledDayWorkPattern::make()->handle(
                    day: $scheduledDay,
                    leaveRequestDays: $dayLeaveRequestDaysFromSameRole,
                    holidays: $dayHolidays,
                    works: $scheduledDay->timeSheetDay->works,
                );

            $dayGapRecord->maxDuration = $this
                ->getAvailableOrdinaryDurationForDay(
                    user: $user,
                    day: $scheduledDay,
                    leaveRequestDayFromAllRoles: $dayLeaveRequestDayFromAllRoles,
                    dayHolidays: $dayHolidays,
                    doesWorkOnHolidays: $userRoleSchedule->holidayCalculations->doesWorkOnHolidays,
                )
                ->deduct($dayGapRecord->duration);

            $missingDuration->deduct($dayGapRecord->duration);
        }

        $scheduledDayIndex = $scheduledDays->count() - 1;

        while ($missingDuration->gt0() && isset($scheduledDays[$scheduledDayIndex])) {
            $scheduledDay = $scheduledDays[$scheduledDayIndex];
            $gapEntry = $this->getOrSetGapRecord($scheduledDay->date);

            $totalGapsDuration = $scheduleType->isDurationOnly()
                ? TimeDuration::oneDay()
                : $gapEntry->ordinaryGaps->getDuration();

            $missingDurationToAdd = TimeDuration::min(
                $gapEntry->maxDuration,
                $missingDuration->copy(),
                $totalGapsDuration,
            );

            $gapEntry->duration->add($missingDurationToAdd);
            $missingDuration->deduct($missingDurationToAdd);

            $scheduledDayIndex--;
        }

        $scheduledDayIndex = $scheduledDays->count() - 1;
        $carryOverDuration = TimeDuration::zero();

        while (isset($scheduledDays[$scheduledDayIndex])) {
            $scheduledDay = $scheduledDays[$scheduledDayIndex];
            $date = $scheduledDay->date->copy();
            $dayData = $this->getOrSetGapRecord($date);

            $remainingAvailableDuration = $dayData->maxDuration->copy()->deduct($dayData->duration);

            if ($remainingAvailableDuration->gt($carryOverDuration)) {
                $dayData->duration->add($carryOverDuration->copy());
                $carryOverDuration = TimeDuration::zero();
            } else {
                $dayData->duration->add($remainingAvailableDuration->copy());
                $carryOverDuration->deduct($remainingAvailableDuration->copy());
            }

            $carryOverDuration->add($this->completeMissingHoursForDay(date: $date, dayData: $dayData));

            $scheduledDayIndex--;
        }
    }

    /** @throws \Throwable  */
    #[ObjectShape([
        'duration' => TimeDuration::class,
        'maxDuration' => TimeDuration::class,
        'ordinaryGaps' => TimeSpanList::class,
    ])]
    private function getOrSetGapRecord(CarbonInterface $date): \stdClass
    {
        $key = $date->toDateString();

        if (!isset($this->gaps[$key])) {
            $entry = new \stdClass();
            $entry->duration = TimeDuration::make();
            $entry->maxDuration = TimeDuration::make();
            $entry->ordinaryGaps = TimeSpanList::empty();

            $this->gaps[$key] = $entry;
        }

        return $this->gaps[$key];
    }

    //region --\\   Data fetching | filter methods   //--

    private function getRelevantLeaveRequestDays(int $user_id, CarbonInterface $startDate, CarbonInterface $endDate): Collection|array
    {
        return LeaveRequestDay::q(constraints: [
            ['User.id', '=', $user_id],
            ['LeaveRequestDay.date', '>=' , $startDate->toDateString()],
            ['LeaveRequestDay.date', '<=' , $endDate->toDateString()],
            ['IN', 'Workflow.status', [
                WorkflowStatusTypes\NewType::ID,
                WorkflowStatusTypes\SubmittedType::ID,
                WorkflowStatusTypes\PartiallyApprovedType::ID,
                WorkflowStatusTypes\ApprovedType::ID,
            ]],
        ])->many();
    }

    /**
     * @param Collection<LeaveRequestDay>|LeaveRequestDay[] $leaveRequestDayFromAllRoles
     * @param Collection<TimeSheetHoliday>|TimeSheetHoliday[] $dayHolidays
     * @throws \Throwable
     */
    private function getAvailableOrdinaryDurationForDay(
        User $user,
        ScheduledPayRunPeriodDay $day,
        Collection|array $leaveRequestDayFromAllRoles,
        Collection|array $dayHolidays,
        bool $doesWorkOnHolidays,
    ): TimeDuration
    {
        // TODO: Do this properly when introduce part day holidays
        if (!$doesWorkOnHolidays && $dayHolidays->count() > 0) {
            return TimeDuration::zero();
        }

        $userMaxDuration = $user->settings->doesHaveMaximumHoursPerDay
            ? TimeDuration::parseFromHours($user->settings->maximumHoursPerDay)
            : TimeDuration::oneDay();

        $scheduledDayMaxDuration = TimeDuration::min($day->getMaxDuration(), $userMaxDuration);

        foreach ($day->timeSheetDay->works as $timeSheetWork) {
            foreach ($timeSheetWork->items as $item) {
                if ($day->id === $timeSheetWork->scheduledPayRunPeriodDay_id) {
                    $scheduledDayMaxDuration->deduct($item->getOrdinaryDuration());
                }
            }

            $userMaxDuration->deduct($timeSheetWork->workDuration);
        }

        foreach ($leaveRequestDayFromAllRoles as $leaveRequestDay) {
            $durationToDeduct = $leaveRequestDay->duration;

            if ($leaveRequestDay->leaveRequest->userRoleSchedule_id === $day->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule_id) {
                $scheduledDayMaxDuration->deduct($durationToDeduct);
            }

            $userMaxDuration->deduct($durationToDeduct);
        }

        return TimeDuration::min($scheduledDayMaxDuration, $userMaxDuration);
    }

    //endregion --\\   Data fetching | filter methods   //--

    //region --\\   Complete missing hours methods   //--

    /** @throws \Throwable */
    private function completeMissingHoursForDay(CarbonInterface $date, \stdClass $dayData): TimeDuration
    {
        $scheduleType = $this->timeSheetPeriod->scheduleType;
        $scheduledPayRunPeriodDay = $this->timeSheetPeriod->scheduledPayRunPeriod->days->first(fn (ScheduledPayRunPeriodDay $day) => $day->date->isSameDay($date));
        $timeSheetDay = $this->timeSheetPeriod->timeSheet->timeSheetDays->first(fn (TimeSheetDay $day) => $day->date->isSameDay($date));
        $missingDuration = $dayData->duration;

        if ($missingDuration->lte0()) {
            return TimeDuration::zero();
        }

        $itemInfo = [
            '_' => 'item',
            'type' => $this->workType,
            'group_id' => $this->group_id,
            'model_id' => $this->model_id,
            'sub_id' => $this->sub_id,
            'notes' => $this->notes,
        ];

        $timeSheetWork = match ($scheduleType) {
            UserRoleScheduleType::WorkPattern => $this->createWorkPatternTimeSheetWork(
                timeSheetDay: $timeSheetDay,
                dayData: $dayData,
                scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
                duration: $missingDuration->copy(),
                itemInfo: $itemInfo,
            ),
            UserRoleScheduleType::DurationOnly => $this->createDurationOnlyTimesheetWork(
                timeSheetDay: $timeSheetDay,
                scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
                duration: $missingDuration->copy(),
                itemsAndBreaksInfo: $itemInfo,
            ),

            default => throw new NotImplementedException("Not available for - " . $scheduleType->id() . ' schedules'),
        };

        $missingDuration->deduct($timeSheetWork->sumDuration);

        if ($dayData->ordinaryGaps->count() > 0) {
            return $this->completeMissingHoursForDay(date: $date, dayData: $dayData);
        }

        return $missingDuration;
    }

    /** @throws \Throwable */
    private function createWorkPatternTimeSheetWork(
        TimeSheetDay $timeSheetDay,
        \stdClass $dayData,
        ScheduledPayRunPeriodDay $scheduledPayRunPeriodDay,
        TimeDuration $duration,
        array $itemInfo,
    ): TimeSheetWork
    {
        if ($dayData->ordinaryGaps->count() < 1) {
            throw new InvalidArgumentException("Unable to find available schedule block for day - " . $timeSheetDay->date->toDateString());
        }

        $largestTimeBlock = $dayData->ordinaryGaps->pluckLongestSpan();
        $workDuration = TimeDuration::min($largestTimeBlock->getDuration(), $duration->copy());
        $timeSpan = TimeSpan::createFromStartAndDuration($largestTimeBlock->getStart(), $workDuration)->setDate($timeSheetDay->date);

        return CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $this->userRoleTimeType,
            start: $timeSpan->getStartAsCarbon(),
            end: $timeSpan->getEndAsCarbon(),
            itemsAndBreaksInfo: [array_merge([
                'start' =>  $timeSpan->getStartAsCarbon(),
                'end' =>  $timeSpan->getEndAsCarbon(),
                'duration' => $workDuration,
            ], $itemInfo)],

            doesPreventAllAdditionalCalculations: true,
            doesRunCalculationsAsync: true,
        );
    }

    /** @throws \Throwable */
    private function createDurationOnlyTimesheetWork(
        TimeSheetDay $timeSheetDay,
        ScheduledPayRunPeriodDay $scheduledPayRunPeriodDay,
        TimeDuration $duration,
        array $itemsAndBreaksInfo,
    ): TimeSheetWork
    {
        return CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $this->userRoleTimeType,
            itemsAndBreaksInfo: [array_merge([
                'duration' => $duration->copy(),
            ], $itemsAndBreaksInfo)],
            doesPreventAllAdditionalCalculations: true,
            doesPreventBroadcasting: true,
        );
    }

    //endregion --\\   Complete missing hours methods   //--
}
