<?php

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk;

use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'KioskDeviceRequestBasicResource',
    title: 'Kiosk Device Request data',
    required: ['id', 'deviceId', 'ipAddress', 'name', 'status', 'kioskDevice_id'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'deviceId', type: 'string', example: 'unique-device-id'),
        new OA\Property(property: 'ipAddress', type: 'string', example: 'device-ipAddress'),
        new OA\Property(property: 'name', type: 'string', example: 'kiosk front door'),
        new OA\Property(property: 'status', required: ['id', 'title'], properties: [
            new OA\Property(property: 'id', type: 'string', example: 'approved'),
            new OA\Property(property: 'title', type: 'string', example: 'Approved'),
        ]),
        new OA\Property(property: 'kioskDevice', ref: '#/components/schemas/KioskDeviceBasicWithKeyPairResource', description: 'this only returns if status = approved', nullable: true),
    ],
)]
class KioskDeviceRequestBasicResource extends Resource
{
    public static $relations = [];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var KioskDeviceRequest $r */
        $r = $this->getModel();

        $ret = [
            'id' => $r->id,
            'deviceId' => $r->deviceId,
            'ipAddress' => $r->ipAddress,
            'name' => $r->name,
            'status' => $r->status->toArray(),
        ];

        if (!is_null($r->kioskDevice_id) && $r->kioskDevice->isActive) {
            $ret['kioskDevice'] = KioskDeviceBasicWithKeyPairResource::make($r->kioskDevice);
        }

        return $ret;
    }
}
