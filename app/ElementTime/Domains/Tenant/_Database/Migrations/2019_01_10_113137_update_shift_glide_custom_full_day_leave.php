<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Shift

        Schema::table('Shift', function (Blueprint $table) {
            $table->decimal('glidePublicHolidaysCustomRate', 16, 10)->nullable()->after('glidePublicHolidaysRateType');

            $table->char('glidePublicHolidaysRateTypeWeekend', 1)->default('A')->after('glidePublicHolidaysCustomRate');
            $table->decimal('glidePublicHolidaysCustomRateWeekend', 16, 10)->nullable()->after('glidePublicHolidaysRateTypeWeekend');

            $table->char('glideLeaveRateType', 1)->default('A')->after('glidePublicHolidaysCustomRateWeekend');
            $table->decimal('glideLeaveCustomRate', 16, 10)->nullable()->after('glideLeaveRateType');

            $table->char('glideLeaveRateTypeWeekend', 1)->default('A')->after('glideLeaveCustomRate');
            $table->decimal('glideLeaveCustomRateWeekend', 16, 10)->nullable()->after('glideLeaveRateTypeWeekend');
        });

        //endregion Table: Shift

        //region Table: UserShift

        Schema::table('UserShift', function (Blueprint $table) {
            $table->char('glidePublicHolidaysRateType', 1)->default('A')->after('endDate');
            $table->decimal('glidePublicHolidaysCustomRate', 16, 10)->nullable()->after('glidePublicHolidaysRateType');

            $table->char('glidePublicHolidaysRateTypeWeekend', 1)->default('A')->after('glidePublicHolidaysCustomRate');
            $table->decimal('glidePublicHolidaysCustomRateWeekend', 16, 10)->nullable()->after('glidePublicHolidaysRateTypeWeekend');

            $table->char('glideLeaveRateType', 1)->default('A')->after('glidePublicHolidaysCustomRateWeekend');
            $table->decimal('glideLeaveCustomRate', 16, 10)->nullable()->after('glideLeaveRateType');

            $table->char('glideLeaveRateTypeWeekend', 1)->default('A')->after('glideLeaveCustomRate');
            $table->decimal('glideLeaveCustomRateWeekend', 16, 10)->nullable()->after('glideLeaveRateTypeWeekend');
        });

        //endregion Table: UserShift
    }

    public function down(): void
    {
        //region Table: UserShift

        if (Schema::hasColumn('UserShift', 'glideLeaveCustomRateWeekend')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveCustomRateWeekend');
            });
        }
        if (Schema::hasColumn('UserShift', 'glideLeaveRateTypeWeekend')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveRateTypeWeekend');
            });
        }
        if (Schema::hasColumn('UserShift', 'glideLeaveCustomRate')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveCustomRate');
            });
        }
        if (Schema::hasColumn('UserShift', 'glideLeaveRateType')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveRateType');
            });
        }
        if (Schema::hasColumn('UserShift', 'glidePublicHolidaysCustomRateWeekend')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysCustomRateWeekend');
            });
        }
        if (Schema::hasColumn('UserShift', 'glidePublicHolidaysRateTypeWeekend')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysRateTypeWeekend');
            });
        }
        if (Schema::hasColumn('UserShift', 'glidePublicHolidaysCustomRate')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysCustomRate');
            });
        }
        if (Schema::hasColumn('UserShift', 'glidePublicHolidaysRateType')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysRateType');
            });
        }

        //endregion Table: UserShift

        //region Table: Shift

        if (Schema::hasColumn('Shift', 'glideLeaveCustomRateWeekend')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveCustomRateWeekend');
            });
        }
        if (Schema::hasColumn('Shift', 'glideLeaveRateTypeWeekend')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveRateTypeWeekend');
            });
        }
        if (Schema::hasColumn('Shift', 'glideLeaveCustomRate')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveCustomRate');
            });
        }
        if (Schema::hasColumn('Shift', 'glideLeaveRateType')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideLeaveRateType');
            });
        }
        if (Schema::hasColumn('Shift', 'glidePublicHolidaysCustomRateWeekend')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysCustomRateWeekend');
            });
        }
        if (Schema::hasColumn('Shift', 'glidePublicHolidaysRateTypeWeekend')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysRateTypeWeekend');
            });
        }
        if (Schema::hasColumn('Shift', 'glidePublicHolidaysCustomRate')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysCustomRate');
            });
        }

        //endregion Table: Shift
    }
};
