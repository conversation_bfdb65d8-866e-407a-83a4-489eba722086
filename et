#!/usr/bin/env bash

#
#       _                           _  _____ ___ __  __ _____
#   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
#  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
# |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
#  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
#
# @link https://www.elementtime.com
# @copyright 2025 Adroit Creations
#

set -o errexit

#region ---\\ CONFIG //---

Color_Off='\033[0m'       # Text Reset

Black='\033[0;30m'        # Black
Red='\033[0;31m'          # Red
Green='\033[0;32m'        # Green
Yellow='\033[0;33m'       # Yellow
Blue='\033[0;34m'         # Blue
Purple='\033[0;35m'       # Purple
Cyan='\033[0;36m'         # Cyan
White='\033[0;37m'        # White

On_Black='\033[40m'       # Black
On_Red='\033[41m'         # Red
On_Green='\033[42m'       # Green
On_Yellow='\033[43m'      # Yellow
On_Blue='\033[44m'        # Blue
On_Purple='\033[45m'      # Purple
On_Cyan='\033[46m'        # Cyan
On_White='\033[47m'       # White

IBlack='\033[0;90m'       # Black
IRed='\033[0;91m'         # Red
IGreen='\033[0;92m'       # Green
IYellow='\033[0;93m'      # Yellow
IBlue='\033[0;94m'        # Blue
IPurple='\033[0;95m'      # Purple
ICyan='\033[0;96m'        # Cyan
IWhite='\033[0;97m'       # White

BIBlack='\033[1;90m'      # Black
BIRed='\033[1;91m'        # Red
BIGreen='\033[1;92m'      # Green
BIYellow='\033[1;93m'     # Yellow
BIBlue='\033[1;94m'       # Blue
BIPurple='\033[1;95m'     # Purple
BICyan='\033[1;96m'       # Cyan
BIWhite='\033[1;97m'      # White

On_IBlack='\033[0;100m'   # Black
On_IRed='\033[0;101m'     # Red
On_IGreen='\033[0;102m'   # Green
On_IYellow='\033[0;103m'  # Yellow
On_IBlue='\033[0;104m'    # Blue
On_IPurple='\033[0;105m'  # Purple
On_ICyan='\033[0;106m'    # Cyan
On_IWhite='\033[0;107m'   # White

Bold=$(tput bold)
Normal=$(tput sgr0)

MYSQL_S3_PATH=ac-share-int-prod/elementtime/dumps

#endregion ---\\ CONFIG //---

#region ---\\ Internal functions //---

# -----------------------------------------------------------------------------
# Helper functions start with _ and aren't listed in this script's help menu.
# -----------------------------------------------------------------------------

function _signature {
  echo -e "${Yellow}╔══════════════════════════════════════════════════════════════════════════════╗"
  echo -e "║ ◎                                                                          ◎ ║"
  echo -e "║               _                           _  _____ ___ __  __ _____          ║"
  echo -e "║           ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|         ║"
  echo -e "║          / _ \ |/ _ \ '_ \` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|           ║"
  echo -e "║         |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___          ║"
  echo -e "║          \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|         ║"
  echo -e "║                                                                              ║"
  echo -e "║ ◎                                                                          ◎ ║"
  echo -e "╚══════════════════════════════════════════════════════════════════════════════╝${Color_Off}"
  if [ "$1" ]; then
    echo -e ${BIBlue}-\>\> $1 ${Color_Off}
    echo -e
  fi
}

function _dc {
  docker compose exec et_www "${@}"
}

function _permissions:light {
   echo "- Root folder..."
  _dc chown 1000:1000 .
   echo "- Storage folder..."
  _dc chmod -R 777 storage
   echo "- Bootstrap cache folder..."
  _dc chmod -R o+w bootstrap/cache
}

function _permissions:long {
  _permissions:light
   echo "- All others..."
  _dc find . -name '.?*' -prune -o -exec chown 1000:1000 {} +;
}

function _dcmysql() {
  docker compose exec et_database "${@}"
}

function _mysql:query:run {
  _dcmysql bash -c "MYSQL_PWD=123 mysql -uroot -Bse \"${1}\""
}

function _mysql:run:refresh {
  FILE=${1}
  DB=${2}

  echo "Deleting old database..."
  _mysql:query:run "DROP DATABASE ${DB}"
  echo "Creating old database..."
  _mysql:query:run "CREATE DATABASE ${DB}"
  echo "Copying file ${FILE}..."
  _dcmysql rm -rf "/var/db/${FILE}"
  docker compose cp "./local-tmp/${FILE}" "et_database:/var/db/${DB}.sql"
  echo "Importing file ${FILE} into database ${DB}..."
  _dcmysql bash -c "MYSQL_PWD=123 mysql -uroot ${DB} < /var/db/${DB}.sql"
  echo "Cleaning all the mess..."
  _dcmysql rm -rf "/var/db/${DB}.sql"
}

#endregion ---\\ Internal functions //---

#region --\\\ Admin commands ///--

function _upload:dump {
  FILE=${1}

  _signature Upload\ dump\ file

  echo "Uploading file \"${FILE}\"..."
  aws s3 cp "./local-tmp/${FILE}" "s3://${MYSQL_S3_PATH}/${FILE}"
}

function _download:dump {
  FILE=${1}

  _signature Download\ dump\ file

  echo "Getting file \"$FILE\"..."
  rm -rf "./local-tmp/${FILE}"
  aws s3 cp "s3://${MYSQL_S3_PATH}/${FILE}" "./local-tmp/${FILE}"
}

#endregion --\\\ Admin commands ///--

#region ---\\ MongoDB commands //---

function mongosh {
  _signature mongosh
  docker compose exec et_docdb mongosh --username root --password 123
}

#endregion ---\\ MongoDB commands //---

#region ---\\ Redis commands //---

function redis-cli {
  _signature Redis\ CLI
  docker compose exec et_redis redis-cli
}

#endregion ---\\ Redis commands //---

#region ---\\ MySQL commands //---

function mysql {
  _signature MySQL
  _dcmysql mysql -uroot -p123
}

function mysql:dbs:list {
  _signature List\ of\ all\ databases
  _mysql:query:run "SHOW DATABASES"
}

function mysql:refresh {
  mysql:refresh:local "${1}"
}

function mysql:refresh:local {
  _signature Refresh\ database\ from\ local\ dump\ file
  _mysql:run:refresh "${1}" "${2}"
  echo "All done. Have fun!"
}

function mysql:refresh:s3 {
  _signature Refresh\ database\ from\ dump\ file\ on\ S3

  TENANT=${1}
  FILE=${2}
  DB=${3:-${1}}

  _download:dump "$FILE"
  _mysql:run:refresh "$FILE" "$DB"

  tenant:migrate "$TENANT"
  admin:migrate
  cache:clear

  echo "All done. Have fun!"
}

function mysql:list:s3 {
  _signature List\ of\ all\ current\ files\ available\ on\ S3\ for\ download

  aws s3 ls s3://${MYSQL_S3_PATH} --summarize --recursive --no-paginate --human-readable

  echo -e
  echo -e "${Yellow}Remember: to refresh a database using a file available in S3 run:\n${BIWhite}et mysql:refresh:s3 <${BIGreen}tenant-slug${BIWhite}> <${BIGreen}file.sql${BIWhite}> <${BIGreen}dbName${BIWhite}>${Color_Off}\n"
}

#endregion ---\\ MySQL commands //---

#region ---\\ Host instance commands ///--

function bash {
  _signature Bash
  _dc bash
}

function zsh {
  _signature Shell
  _dc zsh
}

function shell {
  _signature Shell
  _dc zsh
}

function crontab {
  _signature crontab
  _dc crontab -e
}

#endregion ---\\ Host instance commands ///--

#region ---\\ Application commands //---

function artisan {
  _dc php artisan "${@}"
}

function a {
  _dc php artisan "${@}"
}

function permissions:reset {
  _signature Update\ folder\ permissions
  _permissions:long
}

function cache:clear {
  _signature Cache\ clear
  echo "- Config cache..."
  artisan config:clear >> /dev/null 2>&1
  echo "- App cache..."
  artisan cache:clear >> /dev/null 2>&1
  echo "- View cache..."
  artisan view:clear >> /dev/null 2>&1
  echo "- Optimize cache..."
  artisan optimize:clear >> /dev/null 2>&1
  echo "- Reports cache..."
  artisan elt-gm:report:clear-cache --all >> /dev/null 2>&1
  echo "- Tenant cache..."
  artisan elementtime:tenant:clear-cache >> /dev/null 2>&1 || echo -e "- ${Red}Error on clearing tenant cache - check tenant DB exists${Color_Off}"
  echo "- Updating folder permissions..."
  _permissions:long
  echo -e
  echo -e "${Green}Done. All cache cleared${Color_Off}"
  echo -e
}

function cc {
  cache:clear
}

function admin:migrate {
  artisan elementtime:admin:migrate --yes
}

function admin:mm {
  artisan make:migration "${@}" --path=app/ElementTime/Domains/Admin/_Database/Migrations
  _dc chown 1000:1000  app/ElementTime/Domains/Admin/_Database/Migrations -R
}

function tenant:migrate {
  artisan elementtime:tenant:migrate "${@}" --yes
}

function tenant:update {
  artisan elementtime:tenant:update "${@}"
}

function tenant:mm {
  artisan make:migration "${@}" --path=app/ElementTime/Domains/Tenant/_Database/Migrations
  _dc chown 1000:1000  app/ElementTime/Domains/Tenant/_Database/Migrations -R
}

#endregion ---\\ Application commands //---

#region ---\\ General aliases commands ///--

function s {
  make dev/start
}

function r {
  make dev/rebuild
}

function g {
  make dev/gulp
}

function l {
  make dev/pm2-log
}

function p {
  make dev/pm2-restart-all
}

#endregion ---\\ General aliases commands ///--

function help {
  printf "Usage: %s ${IWhite}<task> ${IPurple}[args]\n\n${IYellow}Available tasks:${IGreen}\n" "${0}"

  compgen -A function | grep -v "^_" | sed "s/^/   - /"
}

function default {
  help
}

"${@:-default}"
