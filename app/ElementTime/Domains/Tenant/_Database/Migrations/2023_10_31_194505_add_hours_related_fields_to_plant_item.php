<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Plant\Support\PlantItemHoursRecordingTypes\DurationType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('PlantItem', function (Blueprint $table) {
            $table->string('hoursLabel')->nullable()->after('isHoursRequired');
            $table->string('hoursRecordingType')->default(DurationType::ID)->after('hoursLabel');
            $table->decimal('hoursCost', 16, 10)->nullable()->after('hoursRecordingType');

            $table->boolean('doesHaveSecondaryHours')->default(false)->after('hoursCost');
            $table->boolean('isSecondaryHoursRequired')->default(false)->after('doesHaveSecondaryHours');
            $table->string('secondaryHoursLabel')->nullable()->after('isSecondaryHoursRequired');
            $table->string('secondaryHoursRecordingType')->default(DurationType::ID)->after('secondaryHoursLabel');
            $table->decimal('secondaryHoursCost', 16, 10)->nullable()->after('secondaryHoursRecordingType');
        });

        Schema::table('PlantSheetDayTime', function (Blueprint $table) {
            $table->integer('minutesStart')->nullable()->after('minutes');
            $table->integer('minutesEnd')->nullable()->after('minutesStart');
            $table->integer('secondaryMinutes')->nullable()->after('minutesEnd');
            $table->integer('secondaryMinutesStart')->nullable()->after('secondaryMinutes');
            $table->integer('secondaryMinutesEnd')->nullable()->after('secondaryMinutesStart');
        });
    }

    public function down(): void
    {
        // region --\\ PlantSheetDayTime //--

        if (Schema::hasColumn('PlantSheetDayTime', 'minutesStart')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('minutesStart');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'minutesEnd')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('minutesEnd');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'secondaryMinutes')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('secondaryMinutes');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'secondaryMinutesStart')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('secondaryMinutesStart');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'secondaryMinutesEnd')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('secondaryMinutesEnd');
            });
        }

        // endregion --\\ PlantSheetDayTime //--

        // region --\\ PlantItem // --
        if (Schema::hasColumn('PlantItem', 'hoursLabel')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('hoursLabel');
            });
        }

        if (Schema::hasColumn('PlantItem', 'hoursRecordingType')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('hoursRecordingType');
            });
        }

        if (Schema::hasColumn('PlantItem', 'hoursCost')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('hoursCost');
            });
        }

        if (Schema::hasColumn('PlantItem', 'doesHaveSecondaryHours')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('doesHaveSecondaryHours');
            });
        }

        if (Schema::hasColumn('PlantItem', 'isSecondaryHoursRequired')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('isSecondaryHoursRequired');
            });
        }

        if (Schema::hasColumn('PlantItem', 'secondaryHoursLabel')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('secondaryHoursLabel');
            });
        }

        if (Schema::hasColumn('PlantItem', 'secondaryHoursRecordingType')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('secondaryHoursRecordingType');
            });
        }

        if (Schema::hasColumn('PlantItem', 'secondaryHoursCost')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('secondaryHoursCost');
            });
        }

        // endregion --\\ PlantItem // --
    }
};
