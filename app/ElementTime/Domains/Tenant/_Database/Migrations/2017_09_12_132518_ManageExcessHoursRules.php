<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Settings
        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('requiresCommentOnExcessTime')->default(true);
        });
        //endregion

        //region ToilType
        Schema::table('ToilType', function (Blueprint $table) {
            $table->decimal('allowedNegativeBalance', 16, 10)->default(8);
        });
        //endregion

        //region User
        Schema::table('User', function (Blueprint $table) {
            $table->boolean('canSelectExcessTimeRule')->default(true);
            $table->char('excessTimeMethod', 1)->nullable();
        });

        /** @var \stdClass[] $users */
        $users = DB::table('User')->get();
        foreach ($users as $user) {
            try {
                if ($user->hasTOIL) {
                    DB::table('User')->where('id', '=', $user->id)->update([
                        'excessTimeMethod' => 'T',
                    ]);
                } elseif ($user->hasOvertime) {
                    DB::table('User')->where('id', '=', $user->id)->update([
                        'excessTimeMethod' => 'O',
                    ]);
                }
            } catch (Throwable) {
            }
        }
        //endregion

        //region UserToilType
        Schema::table('UserToilType', function (Blueprint $table) {
            $table->boolean('isSpecial')->default(false);
        });
        //endregion

        //region TimeSheet changes
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('totalExcessTimeHours', 16, 10)->default(0);
            $table->decimal('totalToilHours', 16, 10)->default(0);
            $table->decimal('totalOvertimeHours', 16, 10)->default(0);
            $table->decimal('totalOvertimeValue', 16, 10)->default(0);
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->boolean('hasSubmitIssues')->default(false);
            $table->boolean('hasApproveIssues')->default(false);
            $table->boolean('hasUnSubmitIssues')->default(false);
            $table->boolean('hasExcessHours')->default(false);
        });

        Schema::create('TimeSheetDayExcessTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetDay_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('originalTimeSheetDayExcessTime_id')->unsigned()->nullable();
            $table->bigInteger('overtimeType_id')->unsigned()->nullable();
            $table->bigInteger('userToilType_id')->unsigned()->nullable();

            $table->char('ruleType', 1)->default('D');
            $table->decimal('baseHours', 16, 10)->default(0);
            $table->text('comment')->nullable();
            $table->char('status', 1)->default('W');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetDay_id', 'tsdet_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'tsdet_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('originalTimeSheetDayExcessTime_id', 'tsdet_otsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('overtimeType_id', 'tsdet_ot_fn')->references('id')->on('OvertimeType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilType_id', 'tsdet_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetDayExcessTimeRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetDayExcessTime_id')->unsigned();
            $table->bigInteger('overtimeType_id')->unsigned()->nullable();
            $table->bigInteger('userToilType_id')->unsigned()->nullable();

            $table->boolean('isPaidOut')->default(true);
            $table->longText('timeBlocks')->nullable();
            $table->decimal('baseHours', 16, 10)->default(0);
            $table->decimal('calculatedHours', 16, 10)->default(0);
            $table->decimal('calculatedValue', 16, 10)->default(0);
            $table->char('calculationType', 1)->nullable();
            $table->longText('formula')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetDayExcessTime_id', 'tsdetr_tsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('overtimeType_id', 'tsdetr_ot_fn')->references('id')->on('OvertimeType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilType_id', 'tsdetr_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetDayExcessTimeAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('timeSheetDayExcessTime_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mediaFile_id', 'tsdetaf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheetDayExcessTime_id', 'tsdetaf_tsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('TimeSheetDayExcessTimeApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetDayExcessTime_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->default('M');
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1);
            $table->text('notes')->nullable();
            $table->char('status', 1)->default('W');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetDayExcessTime_id', 'tsdeta_tsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('manager_id', 'tsdeta_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('TimeSheetDayExcessTimeStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetDayExcessTime_id')->unsigned();
            $table->bigInteger('otherTimeSheetDayExcessTime_id')->unsigned()->nullable();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();
            $table->bigInteger('timeSheetDayExcessTimeApproval_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();
            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetDayExcessTime_id', 'tsdetsh_tsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('otherTimeSheetDayExcessTime_id', 'tsdetsh_otsdet_fn')->references('id')->on('TimeSheetDayExcessTime')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'tsdetsh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheetDayExcessTimeApproval_id', 'tsdetsh_tsdeta_fn')->references('id')->on('TimeSheetDayExcessTimeApproval')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region Update notes on TimeSheetItemDays
        DB::table('TimeSheetItemDay')->where('hours', '>', 0)->where('notes', '=', '')->update([
            'notes' => '...',
        ]);
        //endregion
    }

    public function down(): void
    {
        //region TimeSheet changes
        Schema::dropIfExists('TimeSheetDayExcessTimeStatusHistory');
        Schema::dropIfExists('TimeSheetDayExcessTimeApproval');
        Schema::dropIfExists('TimeSheetDayExcessTimeAttachmentFile');
        Schema::dropIfExists('TimeSheetDayExcessTimeRule');
        Schema::dropIfExists('TimeSheetDayExcessTime');

        if (Schema::hasColumn('TimeSheetDay', 'hasExcessHours')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hasExcessHours');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hasUnSubmitIssues')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hasUnSubmitIssues');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hasApproveIssues')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hasApproveIssues');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hasSubmitIssues')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hasSubmitIssues');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'totalOvertimeValue')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalOvertimeValue');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'totalOvertimeHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalOvertimeHours');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'totalToilHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalToilHours');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'totalExcessTimeHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalExcessTimeHours');
            });
        }
        //endregion

        //region UserToilType
        if (Schema::hasColumn('UserToilType', 'isSpecial')) {
            Schema::table('UserToilType', function (Blueprint $table) {
                $table->dropColumn('isSpecial');
            });
        }
        //endregion

        //region User
        if (Schema::hasColumn('User', 'excessTimeMethod')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('excessTimeMethod');
            });
        }

        if (Schema::hasColumn('User', 'canSelectExcessTimeRule')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('canSelectExcessTimeRule');
            });
        }
        //endregion

        //region ToilType
        if (Schema::hasColumn('ToilType', 'allowedNegativeBalance')) {
            Schema::table('ToilType', function (Blueprint $table) {
                $table->dropColumn('allowedNegativeBalance');
            });
        }
        //endregion

        //region Settings
        if (Schema::hasColumn('Settings', 'requiresCommentOnExcessTime')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('requiresCommentOnExcessTime');
            });
        }
        //endregion
    }
};
