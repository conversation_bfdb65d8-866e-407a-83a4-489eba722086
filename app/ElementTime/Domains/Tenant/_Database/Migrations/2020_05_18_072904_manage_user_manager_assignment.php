<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('UserManager', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'uman_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('manager_id');
            $table->foreign('manager_id', 'uman_man_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            // Other data
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'uman_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->date('startDate');
            $table->date('endDate')->nullable();
            $table->text('comments')->nullable();

            // Timestamps
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UserManager');
    }
};
