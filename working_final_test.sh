#!/bin/bash
#
# Working Final Test Suite - Simplified and Reliable
#

set -euo pipefail

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m'

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

log_test() {
    echo -e "${GREEN}[TEST]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_scenario() {
    echo -e "${PURPLE}[SCENARIO]${NC} $*"
}

pass_test() {
    local test_name="$1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    log_test "✓ PASS: $test_name"
}

fail_test() {
    local test_name="$1"
    local reason="$2"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    log_error "✗ FAIL: $test_name - $reason"
}

# Setup simple mock AWS
setup_simple_mock() {
    local mock_dir="/tmp/simple_mock_aws"
    mkdir -p "$mock_dir"
    
    cat > "${mock_dir}/aws" << 'EOF'
#!/bin/bash
case "$1" in
    "sts") echo '{"UserId":"test","Account":"************","Arn":"arn:aws:iam::************:user/test"}' ;;
    "autoscaling")
        case "$2" in
            "describe-auto-scaling-groups")
                if [[ "$*" =~ --auto-scaling-group-names ]]; then
                    asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                    echo '{"AutoScalingGroups":[{"DesiredCapacity":0,"AutoScalingGroupName":"'$asg_name'"}]}'
                else
                    echo '{"AutoScalingGroups":[
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"},
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-b-14si"},
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-g-14si"},
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-cmd-b-14si"},
                        {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-cmd-g-14si"}
                    ]}'
                fi ;;
            "update-auto-scaling-group")
                asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                echo "Mock: Updated ASG $asg_name" ;;
        esac ;;
    "elbv2")
        case "$2" in
            "describe-load-balancers")
                lb_name=$(echo "$*" | grep -o 'alb-etime-[^[:space:]]*' | head -1)
                echo '{"LoadBalancers":[{"LoadBalancerArn":"arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/'$lb_name'/123"}]}' ;;
            "describe-listeners")
                if [[ "$*" =~ --query.*Weight.*100.*TargetGroupArn ]]; then
                    # For fresh deployment, return a default Green target group ARN to simulate no current Blue traffic
                    echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/123"
                elif [[ "$*" =~ --query.*ListenerArn ]]; then
                    echo "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/123"
                else
                    echo '{"Listeners":[{"ListenerArn":"arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/123","Port":443}]}'
                fi ;;
            "describe-target-groups")
                if [[ "$*" =~ --query.*contains.*TargetGroupName ]]; then
                    # Extract target group name and return appropriate ARN
                    if [[ "$*" =~ etime-tg-test-skt-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-b-14si/123"
                    elif [[ "$*" =~ etime-tg-test-skt-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/123"
                    elif [[ "$*" =~ etime-tg-test-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-b-14si/123"
                    elif [[ "$*" =~ etime-tg-test-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-g-14si/123"
                    elif [[ "$*" =~ etime-tg-test-cmd-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-b-14si/123"
                    elif [[ "$*" =~ etime-tg-test-cmd-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-g-14si/123"
                    else
                        echo ""
                    fi
                else
                    echo '{"TargetGroups":[{"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/test/123","TargetGroupName":"test"}]}'
                fi ;;
            "describe-target-health") echo "2" ;;
            "modify-listener") echo "Mock: Modified listener" ;;
        esac ;;
esac
EOF
    
    chmod +x "${mock_dir}/aws"
    export PATH="${mock_dir}:${PATH}"
}

# Test 1: Basic functionality
test_basic_functionality() {
    log_scenario "Testing Basic Functionality"
    
    # Test help (ignore exit code, just check output)
    local help_output
    help_output=$(./deploy_final_optimized.sh --help 2>&1 || true)
    if echo "$help_output" | grep -q "Usage:"; then
        pass_test "Help functionality"
    else
        fail_test "Help functionality" "Help not displayed"
    fi
    
    # Test parameter validation
    if ./deploy_final_optimized.sh -e invalid 2>/dev/null; then
        fail_test "Parameter validation" "Should reject invalid environment"
    else
        pass_test "Parameter validation"
    fi
    
    # Test blue-green component
    if ./deploy_blue_green_final_optimized.sh -e test -t skt -n b -s 14si 2>&1 | grep -q "ASG deployment completed"; then
        pass_test "Blue-green component deployment"
    else
        fail_test "Blue-green component deployment" "Component deployment failed"
    fi
}

# Test 2: Fresh deployment logic
test_fresh_deployment() {
    log_scenario "Testing Fresh Deployment Logic"
    
    local output
    output=$(timeout 15 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    
    # Debug output for troubleshooting
    echo "DEBUG: Fresh deployment output (last 10 lines):"
    echo "$output" | tail -10

    if echo "$output" | grep -q "Fresh deployment detected\|setting default.*Blue"; then
        pass_test "Fresh deployment detection"
    else
        fail_test "Fresh deployment detection" "Not detected"
    fi

    if echo "$output" | grep -q "current=g, next=b"; then
        pass_test "Fresh deployment color logic (current=g, next=b)"
    else
        fail_test "Fresh deployment color logic" "Wrong colors"
    fi

    if echo "$output" | grep -q "Deployment completed successfully"; then
        pass_test "Fresh deployment completion"
    else
        fail_test "Fresh deployment completion" "Did not complete"
    fi
}

# Test 3: Socket-first dependency
test_socket_first_dependency() {
    log_scenario "Testing Socket-First Dependency"
    
    local output
    output=$(timeout 15 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    
    if echo "$output" | grep -q "Waiting for Socket instances.*priority\|Socket instances.*healthy.*priority"; then
        pass_test "Socket priority dependency"
    else
        fail_test "Socket priority dependency" "Not found"
    fi

    if echo "$output" | grep -q "Socket is healthy.*Normal\|Socket.*healthy.*checking Normal"; then
        pass_test "Socket-first sequence"
    else
        fail_test "Socket-first sequence" "Wrong sequence"
    fi
}

# Test 4: Health check configuration
test_health_check_config() {
    log_scenario "Testing Health Check Configuration"
    
    local output
    output=$(timeout 15 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    
    if echo "$output" | grep -q "10s intervals\|10 second\|10-second"; then
        pass_test "Health check interval (10s)"
    else
        fail_test "Health check interval" "Not 10s"
    fi

    if echo "$output" | grep -q "90min timeout\|90 minute\|90-minute"; then
        pass_test "Health check timeout (90min)"
    else
        fail_test "Health check timeout" "Not 90min"
    fi
}

# Test 5: Error handling
test_error_handling() {
    log_scenario "Testing Error Handling"
    
    # Test error handling by checking cleanup function exists
    if grep -q "cleanup_failed_deployment" deploy_final_optimized.sh; then
        pass_test "Error handling functions present"
    else
        fail_test "Error handling functions" "Cleanup functions not found"
    fi
}

# Test 6: Performance
test_performance() {
    log_scenario "Testing Performance"
    
    local start_time=$(date +%s)
    timeout 15 ./deploy_final_optimized.sh -e test -v 14si -s 0 >/dev/null 2>&1 || true
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    if [[ $duration -lt 10 ]]; then
        pass_test "Performance (${duration}s)"
    else
        fail_test "Performance" "Too slow: ${duration}s"
    fi
}

# Test 7: Signal handling
test_signal_handling() {
    log_scenario "Testing Signal Handling"
    
    # Start deployment and kill it
    timeout 3 ./deploy_final_optimized.sh -e test -v 14si -s 10 >/dev/null 2>&1 &
    local pid=$!
    sleep 1
    kill -TERM $pid 2>/dev/null || true
    wait $pid 2>/dev/null || true
    
    # Check no background processes remain
    if pgrep -f "deploy.*optimized" >/dev/null; then
        fail_test "Signal handling" "Background processes remain"
    else
        pass_test "Signal handling cleanup"
    fi
}

# Test 8: Individual components
test_individual_components() {
    log_scenario "Testing Individual Components"
    
    local components=("skt" "normal" "cmd")
    for component in "${components[@]}"; do
        if ./deploy_blue_green_final_optimized.sh -e test -t "$component" -n b -s 14si 2>&1 | grep -q "ASG deployment completed for $component"; then
            pass_test "$component component deployment"
        else
            fail_test "$component component deployment" "Failed"
        fi
    done
}

# Generate final report
generate_report() {
    local total=$((TESTS_PASSED + TESTS_FAILED))
    
    echo
    echo "=========================================="
    echo "         FINAL TEST REPORT"
    echo "=========================================="
    echo
    echo "Total Tests: $total"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    echo
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
        echo "The optimized deployment scripts are production-ready!"
        echo
        echo "✅ Fresh deployment logic working"
        echo "✅ Socket-first dependency implemented"
        echo "✅ Health check timing optimized (10s intervals, 90min timeout)"
        echo "✅ Error handling comprehensive"
        echo "✅ Signal handling working"
        echo "✅ Performance excellent"
        echo "✅ All components functional"
        return 0
    else
        echo -e "${RED}❌ $TESTS_FAILED TESTS FAILED ❌${NC}"
        echo "Please review failing tests before production deployment."
        return 1
    fi
}

# Cleanup
cleanup() {
    rm -rf /tmp/simple_mock_aws
    pkill -f "deploy.*optimized" 2>/dev/null || true
}

# Main test runner
main() {
    log_test "Starting Working Final Test Suite"
    
    setup_simple_mock
    
    test_basic_functionality
    test_fresh_deployment
    test_socket_first_dependency
    test_health_check_config
    test_error_handling
    test_performance
    test_signal_handling
    test_individual_components
    
    cleanup
    generate_report
}

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    cd "$(dirname "${BASH_SOURCE[0]}")"
    main "$@"
fi
