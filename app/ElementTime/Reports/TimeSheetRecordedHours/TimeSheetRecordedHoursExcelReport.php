<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\TimeSheetRecordedHours;

use Carbon\Carbon;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheet\TimeSheetRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;
use PhpOffice\PhpSpreadsheet\Style\Border;

class TimeSheetRecordedHoursExcelReport extends TenantExcelReport
{
    use HasPayRunParameters {
        setData as _iSetData;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\AuditorFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const TIME = '~30sec';
    const REPORT_SLUG = 'time-sheet-recorded-hours';
    const TYPE_SLUG = 'time-sheet-recorded-hours.xls';
    const DOES_CACHE_REPORT = false;

    /** @var string|TimeSheetRecordedHoursReportConfig */
    public $c = TimeSheetRecordedHoursReportConfig::class;

    protected User|null $user;

    protected function setData(array $info): void
    {
        $this->_iSetData($info);
        $this->user = User::q()->findOrFail($info['idUser'] ?? 0);
    }

    protected function buildReportData(): void
    {
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->setSheetHeading($sheet);
        $this->setSheetData($sheet);

        $this->sheets = [
            $sheet
        ];
    }

    private function setSheetHeading(\stdClass $sheet): void
    {
        $sheetHeading = new \stdClass();
        $sheetHeading->type = 'data-table';
        $sheetHeading->columns = [
            $this->createColumn(label: 'Payrun', span: 3),
            $this->createColumn(label: 'User', span: 3),
            $this->createColumn(label: 'Date', span: 2),
        ];

        $sheetHeading->data = [[
            'config' => [],
            'data' => [
                $this->parseDate($this->payRun->startDate) . ' - ' . $this->parseDate($this->payRun->endDate),
                $this->user->externalId . ' - ' . $this->user->fullName,
                $this->parseDate(Carbon::today()),
            ],
        ]];

        $sheet->data[] = $sheetHeading;
    }

    /** @throws \Throwable */
    private function setSheetData($sheet): void
    {
        $timeSheet = TimeSheetRepository::getFromUserByDate($this->user, $this->payRun->startDate);

        $timeSheet->load([
            'timeSheetDayTimes',
            /// TODO
        ]);

        $times = $timeSheet->timeSheetDayTimes;

        $leaveRequestDays = LeaveRequestDay::q()
            ->constraints([
                ['User.id', '=', $this->user->id],
                ['IN', 'Workflow.status', [
                    SubmittedType::ID,
                    PartiallyApprovedType::ID,
                    ApprovedType::ID,
                ]],
                ['LeaveRequestDay.date', '>=', $this->payRun->startDate->toDateString()],
                ['LeaveRequestDay.date', '<=', $this->payRun->endDate->toDateString()],
            ])
            ->many();

        $allItems = Collection::make($times);

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $allItems = $times->push($leaveRequestDay);
        }

        $allItems = $allItems->sortBy(function (TimeSheetDayTime|LeaveRequestDay $item) {
            if ($item instanceof LeaveRequestDay) {
                return $item->date->format('Ymd');
            }

            if ($item instanceof TimeSheetDayTime) {
                return $item->timeSheetDay->date->format('Ymd');
            }

            return null;
        });

        $rowIndex = 0;

        /// TODO: Don't forget to add leave - not only time

        foreach ($allItems as $item) {
            $sheet->data[] = static::getSeparator();
            $rowIndex += 1;

            if ($item instanceof TimeSheetDayTime) {
                $this->makeTimeRecord($sheet, $item, $rowIndex);
            } else {
                $this->makeLeaveRecord($sheet, $item, $rowIndex);
            }
        }
    }

    private static function getSeparator(): \stdClass
    {
        $separator = new \stdClass;
        $separator->type = 'separator';
        $separator->height = 40;

        return $separator;
    }

    private function makeTimeRecord($sheet, TimeSheetDayTime $time, int $rowIndex): void
    {
        $headerTable = new \stdClass();
        $headerTable->type = 'data-table';
        $headerTable->noSeparator = true;
        $headerTable->config = new \stdClass();
        $headerTable->config->noLabel = true;
        $headerTable->columns = [
            $this->createColumn(''), // Date
            $this->createColumn(''), // Start time
            $this->createColumn(''), // End time
            $this->createColumn(''), // Total hours
            $this->createColumn('', span: 4), // Role
        ];

        $headerTable->data = [[
            'config' => [
                'customBorder' => Border::BORDER_NONE,
                'font-weight' => 'bold',
                'color' => $rowIndex % 2 === 0
                    ? 'c6e0b4'
                    : 'f8cbad',
            ],

            'data' => [
                $this->parseDate($time->timeSheetDay->date), // Date
                !is_null($time->startDateTime) ? $this->parseTime($time->startDateTime) : '-', // Start time
                !is_null($time->endDateTime) ? $this->parseTime($time->endDateTime) : '-', // End time
                $time->minutes->toHours(2) . ' hours', // Total hours
                $time->userRoleTimeType->userRole->repository->getExternalId() . ' ' . $time->roleName, // Role
            ],
        ]];

        $sheet->data[] = $headerTable;

        $subTable = new \stdClass();
        $subTable->type = 'data-table';
        $subTable->noSeparator = true;
        $subTable->config = new \stdClass();
        $subTable->columns = [
            $this->createColumn('Worked hours', align: 'right'),
            $this->createColumn('Cost code'),
            $this->createColumn('Costing details'),
            $this->createColumn('Plant code'),
            $this->createColumn('Plant details'),
            $this->createColumn('Plant hours', align: 'right'),
            $this->createColumn('Plant mileage', align: 'right'),
            $this->createColumn('Plant cartage', align: 'right'),
        ];
        $subTable->data = [];

        $remainingMinutes = $time->minutes->copy();

        foreach ($time->works as $work) {
            $subTable->data[] = [
                'config' => [],
                'data' => [
                    $work->minutes->toHours(2), // Worked hours
                    $work->formattedCode, // Cost code
                    $work->itemName, // Costing details
                    '', // Plant code
                    '', // Plant details
                    '', // Plant hours
                    '', // Plant mileage
                    '', // Plant cartage
                ],
            ];

            $remainingMinutes->sub($work->minutes);

            foreach ($work->plantTimes as $plantTime) {
                $subTable->data[] = [
                    'config' => [],
                    'data' => [
                        '', // Worked hours
                        $plantTime->formattedCode, // Cost code
                        $work->itemName, // Costing details
                        $plantTime->plantItem->plantNumber ?? $plantTime->plantItem->externalId, // Plant code
                        $plantTime->itemName, // Plant details
                        $plantTime->plantItem->doesHaveHours && !is_null($plantTime->minutes) ? $plantTime->minutes->toHours(2) : '', // Plant hours
                        $plantTime->plantItem->doesHaveMileage && !is_null($plantTime->mileage) ? $plantTime->plantItem->mileageUnitClass::getMaskedValue($plantTime->mileage) : '', // Plant mileage
                        $plantTime->plantItem->doesHaveCartage && !is_null($plantTime->cartage) ? $plantTime->plantItem->cartageUnitClass::getMaskedValue($plantTime->cartage) : '', // Plant cartage
                    ],
                ];
            }
        }

        if ($remainingMinutes->gt(0)) {
            $subTable->data[] = [
                'config' => [],
                'data' => [
                    $remainingMinutes->toHours(2), // Worked hours
                    $time->formattedCode, // Cost code
                    $time->itemName, // Costing details
                    '', // Plant code
                    '', // Plant details
                    '', // Plant hours
                    '', // Plant mileage
                    '', // Plant cartage
                ],
            ];

            foreach ($time->plantTimes as $plantTime) {
                if (!is_null($plantTime->timeSheetDayTimeWork_id)) {
                    continue;
                }

                $subTable->data[] = [
                    'config' => [],
                    'data' => [
                        '', // Worked hours
                        $plantTime->formattedCode, // Cost code
                        $time->itemName, // Costing details
                        $plantTime->plantItem->plantNumber ?? $plantTime->plantItem->externalId, // Plant code
                        $plantTime->itemName, // Plant details
                        $plantTime->plantItem->doesHaveHours && !is_null($plantTime->minutes) ? $plantTime->minutes->toHours(2) : '', // Plant hours
                        $plantTime->plantItem->doesHaveMileage && !is_null($plantTime->mileage) ? $plantTime->plantItem->mileageUnitClass::getMaskedValue($plantTime->mileage) : '', // Plant mileage
                        $plantTime->plantItem->doesHaveCartage && !is_null($plantTime->cartage) ? $plantTime->plantItem->cartageUnitClass::getMaskedValue($plantTime->cartage) : '', // Plant cartage
                    ],
                ];
            }
        }

        $sheet->data[] = $subTable;
    }

    /**
     * @throws \Throwable
     */
    private function makeLeaveRecord($sheet, LeaveRequestDay $leaveRequestDay, int $rowIndex): void
    {
        $headerTable = new \stdClass();
        $headerTable->type = 'data-table';
        $headerTable->noSeparator = true;
        $headerTable->config = new \stdClass();
        $headerTable->config->noLabel = true;
        $headerTable->columns = [
            $this->createColumn(''), // Date
            $this->createColumn(''), // Start time
            $this->createColumn(''), // End time
            $this->createColumn(''), // Total hours
            $this->createColumn('', span: 2), // Role
            $this->createColumn(''), // Leave type name
            $this->createColumn(''), // Leave status
        ];

        $headerTable->data = [[
            'config' => [
                'customBorder' => Border::BORDER_NONE,
                'font-weight' => 'bold',
                'color' => $rowIndex % 2 === 0
                    ? 'c6e0b4'
                    : 'f8cbad',
            ],

            'data' => [
                $this->parseDate($leaveRequestDay->date), // Date
                !is_null($leaveRequestDay->timeSpan) ? $this->parseTime($leaveRequestDay->timeSpan->getStartAsCarbon()) : '-', // Start time
                !is_null($leaveRequestDay->timeSpan) ? $this->parseTime($leaveRequestDay->timeSpan->getEndAsCarbon()) : '-', // End time
                $leaveRequestDay->duration->getTotalHours(2) . ' hours', // Total hours
                $leaveRequestDay->leaveRequest->userRoleSchedule->userRole->repository->getExternalId() . ' ' . $leaveRequestDay->leaveRequest->userRoleSchedule->userRole->role->name, // Role
                $leaveRequestDay->leaveRequest->typeName, // Leave type name
                $leaveRequestDay->leaveRequest->workflow->statusClass::NAME, // Leave status
            ],
        ]];

        $sheet->data[] = $headerTable;

        $subTable = new \stdClass();
        $subTable->type = 'data-table';
        $subTable->noSeparator = true;
        $subTable->config = new \stdClass();
        $subTable->columns = [
            $this->createColumn('Leave hours', align: 'right'),
            $this->createColumn('Cost code'),
            $this->createColumn('Costing details'),
            $this->createColumn('', span: 5),
        ];

        $subTable->data = [[
            'config' => [],
            'data' => [
                $leaveRequestDay->duration->getTotalHours(2), // Worked hours
                $leaveRequestDay->leaveRequest->formattedCode, // Cost code
                $leaveRequestDay->leaveRequest->userRoleSchedule->userRole->getMasterUserRoleProject($leaveRequestDay->date)->name, // Costing details
                '', // None
            ],
        ]];

        $sheet->data[] = $subTable;
    }
}
