<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays;

use Carbon\CarbonInterface;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShift;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShiftDay;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDefaultData;

class GetScheduledDaysWorkPattern extends BaseGetScheduledDays
{
    /** @throws \Throwable */
    protected function makeDayActual(
        UserRoleSchedule $userRoleSchedule,
        ScheduledPayRunPeriodDay $dayData,
        ScheduledPayRunPeriod $scheduledPayRunPeriod,
    ): StructScheduledDayWorkPattern
    {
        if (!$scheduledPayRunPeriod->specific instanceof ScheduledPayRunPeriodWorkPattern) {
            throw new InvalidArgumentException('Incompatible scheduledPayRunPeriod parameter');
        }

        /** @var ScheduledPayRunPeriodDayWorkPattern $dayDataDetails */
        $dayDataDetails = $dayData->specific;
        $hasFixedHours = $dayDataDetails->hasFixedHours;

        $ret = $this->makeStructScheduledDayWorkPattern($dayData->date->copy(), $hasFixedHours, $dayDataDetails);
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;
        $ret->fteRatio = $scheduledPayRunPeriod->specific->getFteRatio();
        $ret->periodData = $scheduledPayRunPeriod;

        return $ret;
    }

    /** @throws \Throwable */
    protected function makeDayDefault(
        UserRoleSchedule $userRoleSchedule,
        StructScheduledPayRunPeriodDayDefaultData $dayData,
        StructScheduledPayRunPeriodDefaultData $specificPeriod,
    ): StructScheduledDayWorkPattern
    {
        /** @var UserRoleSchedulePatternPeriodShiftDay $dayDataDetails */
        $dayDataDetails = $dayData->type;
        $hasFixedHours = $dayDataDetails->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours;

        /** @var UserRoleSchedulePatternPeriodShift $type */
        $type = $specificPeriod->type;

        $ret = $this->makeStructScheduledDayWorkPattern($dayData->date->copy(), $hasFixedHours, $dayDataDetails);
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;
        $ret->fteRatio = $type->getFteRatio();
        $ret->periodData = $specificPeriod;

        return $ret;
    }

    // --------------------------------------

    /** @throws \Throwable */
    protected function makeStructScheduledDayWorkPattern(
        CarbonInterface $date,
        bool $hasFixedHours,
        UserRoleSchedulePatternPeriodShiftDay|ScheduledPayRunPeriodDayWorkPattern $dayData,
    ): StructScheduledDayWorkPattern
    {
        $ret = new StructScheduledDayWorkPattern;

        $ret->date = $date;
        $ret->isScheduled = $dayData->isScheduled;
        $ret->isOrdinary = $dayData->isOrdinary;
        $ret->isRdo = $dayData->isRdo;

        $ret->hasFixedHours = $hasFixedHours;
        $ret->duration = $dayData->duration ?? TimeDuration::zero();
        $ret->minDuration = $dayData->minDuration ?? TimeDuration::zero();
        $ret->maxDuration = $dayData->maxDuration ?? TimeDuration::zero();
        $ret->expectedDuration = $dayData->expectedDuration ?? TimeDuration::zero();

        $ret->ordinarySpan = $dayData->ordinarySpan;
        $ret->doesHaveExpectedTimeSpan = $dayData->doesHaveExpectedTimeSpan;
        $ret->expectedSpan = $dayData->expectedSpan;
        $ret->breaks = TimeSpanList::make($dayData->breaks ?? []);

        return $ret;
    }
}
