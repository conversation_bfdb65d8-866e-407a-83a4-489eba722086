<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Feature\ElementTime\Applications\TenantSite\Leaves\LeaveRequestOptions;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\GetUserLeaveBankTypeOptionsForLeaveRequest;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\GetUserRoleScheduleOptionsForLeaveRequest;
use Element\ElementTime\Actions\Tenant\PayRuns\PayRun\GetOpenAndFuturePayRunsForSchedule;
use Element\ElementTime\Domains\Tenant\Leaves\Resources\LeaveRequest\LeaveRequestProcess\PayRunOptionDropDownResource;
use Element\ElementTime\Domains\Tenant\Leaves\Resources\LeaveRequest\LeaveRequestProcess\UserLeaveBankTypeOptionDropDownResource;
use Element\ElementTime\Domains\Tenant\Leaves\Resources\LeaveRequest\LeaveRequestProcess\UserRoleScheduleOptionDropDownResource;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleManager;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\PayrollOfficerFlag;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Feature\ElementTime\Applications\TenantSite\_Traits\UserRoleScheduleBuilderTrait;

function getServiceUrl($prefix = '/', string $suffix = ''): string
{
    return $prefix . 'services/protected/leave-request/options' . $suffix;
}

function getUserRoleSchedule(int $user_id = null): UserRoleSchedule|null
{
    $minDate = TenantSystemSettings::getEarliestOpenPayRun()?->startDate;

    $constraints = [
        ['Role.status', '=', ActiveStatus::ID],
        ...UserRole::availabilityConstraints()->notPast($minDate)->build()
    ];

    if (!is_null($user_id)) {
        $constraints[] = ['User.id', '=', $user_id];
    }

    return UserRoleSchedule::q()
        ->constraints($constraints)
        ->order('RAND()')
        ->first();
}

uses(UserRoleScheduleBuilderTrait::class);

describe('UserRoleSchedule options', function () {
    it('does not allow not logged user', function () {
        $urlSuffix = '/schedule';
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertStatus(401)
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 401, 'message' => 'Unauthorized'],
                ]
            ])
        ;
    });

    it('does not find result if invalid user_id is provided', function () {
        $this->loginAs(PayrollOfficerFlag::class);
        $payload = ['user_id' => 0];
        $urlSuffix = '/schedule?' . http_build_query($payload);

        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 2010, 'message' => 'Record not found: ID is invalid'],
                ]
            ])
        ;
    });

    it('does match expected structure from resource', function () {
        /** @var UserRoleSchedule $userRoleSchedule */
        $userRoleSchedule = null;
        $remainingAttempts = 10;

        while ($remainingAttempts > 0 && is_null($userRoleSchedule)) {
            $userRoleSchedule = getUserRoleSchedule();
            $remainingAttempts--;
        }

        $this->loginAs(PayrollOfficerFlag::class);
        $payload = ['user_id' => $userRoleSchedule->userRole->user_id];
        $urlSuffix = '/schedule?' . http_build_query($payload);
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));
        $expectedUserRoleSchedules = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($userRoleSchedule->userRole->user, SuperUserFlag::getOne());
        $sampleResource = json_decode(UserRoleScheduleOptionDropDownResource::collection($expectedUserRoleSchedules)->toJson(), true);

        $response
            ->assertJsonFragment(['status' => 'S'])
            ->assertJsonFragment($sampleResource)
        ;
    });

    it('does list only schedule that actor can view', function () {
        $this->loginAs(PayrollOfficerFlag::class);

        $staff = User::q()->onlyActive()->many()
            ->filter(function (User $user) {
                $uniqueRoleManagerIds = $user->getActiveUserRoles()
                    ->map(fn (UserRole $userRole) => $userRole->repository->getActiveUserRoleManager()?->manager_id)
                    ->filter(fn ($manager_id) => !!$manager_id)
                    ->unique();

                return $uniqueRoleManagerIds->count() > 0;
            })
            ->random();

        if ($staff->count() < 1) {
            throw new \Exception('Incomplete test environment - this test does require at least 1 staff with two roles, each role with different role manager');
        }

        /** @var UserRole $activeUserRole */
        $activeUserRole = $staff->getActiveUserRoles()->random();
        $activeUserRoleManager = $activeUserRole->getActiveAssignments(models: [UserRoleManager::class])->first();
        $payload = ['user_id' => $activeUserRole->user_id];
        $urlSuffix = '/schedule?' . http_build_query($payload);
        $responseFromPayrollOfficer = $this->get(getServiceUrl(suffix: $urlSuffix));

        CurrentUser::clearResolvedInstances();
        $this->loginAsUser($activeUserRoleManager->manager);

        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response->assertJsonFragment(['status' => 'S']);
        expect($response->json(['content']))->not()->toHaveSameSize($responseFromPayrollOfficer->json(['content']))
            ->and(count($responseFromPayrollOfficer->json(['content'])))->toBeGreaterThan(count($response->json(['content'])))
        ;
    });
});

describe('UserLeaveBankType options', function () {
    it('does not allow not logged user', function () {
        $urlSuffix = '/bank-type';
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertStatus(401)
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 401, 'message' => 'Unauthorized'],
                ],
            ])
        ;
    });
    
    it('does not find any records if wrong userRoleSchedule_id is provided', function () {
        $this->loginAs(PayrollOfficerFlag::class);
        $payload = ['userRoleSchedule_id' => 0];
        $urlSuffix = '/bank-type?' . http_build_query($payload);

        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 2010, 'message' => 'Record not found: ID is invalid'],
                ],
            ])
        ;
    });

    it('does match expected structure from resource', function () {
        /** @var UserRoleSchedule $userRoleSchedule */
        $userRoleSchedule = null;
        $remainingAttempts = 10;

        while ($remainingAttempts > 0 && is_null($userRoleSchedule)) {
            $userRoleSchedule = getUserRoleSchedule();
            $remainingAttempts--;
        }

        $payrollStaff = PayrollOfficerFlag::getOne();
        $this->loginAsUser($payrollStaff);
        $payload = ['userRoleSchedule_id' => $userRoleSchedule->id];
        $urlSuffix = '/bank-type?' . http_build_query($payload);

        $response = $this->get(getServiceUrl(suffix: $urlSuffix));
        $expectedUserLeaveBankTypes = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle(
            user: $userRoleSchedule->userRole->user,
            actor: $payrollStaff
        );
        $sampleResource = json_decode(UserLeaveBankTypeOptionDropDownResource::make($expectedUserLeaveBankTypes[0])->toJson(), true);

        $response
            ->assertJsonFragment(['status' => 'S'])
            ->assertJsonFragment($sampleResource)
        ;
    });

});

describe('Payrun options', function () {
    it('does not allow not logged user', function () {
        $urlSuffix = '/pay-run';
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertStatus(401)
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 401, 'message' => 'Unauthorized'],
                ]
            ])
        ;
    });

    it('does not find any record from invalid userRoleSchedule_id', function () {
        $this->loginAs(PayrollOfficerFlag::class);
        $payload = ['userRoleSchedule_id' => 0];
        $urlSuffix = '/pay-run?' . http_build_query($payload);
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));

        $response
            ->assertJsonFragment([
                'status' => 'E',
                'errors' => [
                    ['code' => 2010, 'message' => 'Record not found: ID is invalid'],
                ],
            ])
        ;
    });

    it('does match with expected resource structure', function () {
        $this->loginAs(PayrollOfficerFlag::class);
        $userRoleSchedule = UserRoleSchedule::q()
            ->constraints([
                ['UserRoleSchedule.type', '=', UserRoleScheduleType::ZeroBased]
            ])
            ->order('UserRoleSchedule.id DEsc')
            ->first();
        $payload = ['userRoleSchedule_id' => $userRoleSchedule->id];
        $urlSuffix = '/pay-run?' . http_build_query($payload);
        $response = $this->get(getServiceUrl(suffix: $urlSuffix));
        $expectedPayRuns = GetOpenAndFuturePayRunsForSchedule::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            actor: SuperUserFlag::getOne(),
        );
        $sample = json_decode(PayRunOptionDropDownResource::make($expectedPayRuns[0])->toJson(), true);

        $response
            ->assertJsonFragment(['status' => 'S'])
            ->assertJsonFragment($sample)
        ;
    });
});
