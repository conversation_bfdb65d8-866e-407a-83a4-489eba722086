<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Support\Domains\Type\StatusTypes\InactiveStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('___I_CivicaAuthorityWorkOrders71StatusMap', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegrationModule_id');
            $table->foreign('externalIntegrationModule_id', '_i_cawo71sm_eim_fn')
                ->references('id')
                ->on('ExternalIntegrationModule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->string('code');
            $table->string('description');
            $table->char('status')->default(InactiveStatus::ID);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('___I_CivicaAuthorityWorkOrders71StatusMap');
    }
};
