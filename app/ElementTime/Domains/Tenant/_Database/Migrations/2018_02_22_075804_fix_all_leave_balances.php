<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('UserToilTypeEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->char('entryType', 2);
            /*
             * MA => Manually Add Balance
             * AA => Automatically Add Balance (relate to TimeSheet [field timeSheet_id] / TimeSheetDay [field timeSheetDay_id])
             * MR => Manually Deduct Balance
             * CM => Commit (Transfer from Accrued to Committed) (relate to waiting to approve/approved future Toil [field toil_id])
             * UC => Uncommit (Transfer from Committed to Accrued) (relate to declined or cancelled Toil [field toil_id])
             * TU => Use as TOIL (relate to approved past Toil [field toil_id])
             * OP => Paid as Overtime (relate to PayRunItem [field payRunItem_id])
             * MC => Manually Cancel (relate to cancelled [another] UserToilTypeEntry field [cancelledUserToilTypeEntry_id])
             * EX => Expired (relate to another UserToilTypeEntry field [expiredUserToilTypeEntry_id])
             */

            $table->bigInteger('userToilType_id')->unsigned();
            $table->foreign('userToilType_id', 'utte_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime');

            $table->decimal('entryBalance', 16, 10)->nullable();

            $table->decimal('entryValue', 16, 10)->nullable();

            $table->bigInteger('previous_id')->unsigned()->nullable();
            $table->foreign('previous_id', 'utte_prevutte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('set null');
            $table->decimal('previousAccruedBalance', 16, 10)->default(0);
            $table->decimal('previousCommittedBalance', 16, 10)->default(0);
            $table->decimal('previousHistoricBalance', 16, 10)->default(0);

            $table->decimal('currentAccruedBalance', 16, 10)->default(0);
            $table->decimal('currentCommittedBalance', 16, 10)->default(0);
            $table->decimal('currentHistoricBalance', 16, 10)->default(0);

            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            $table->boolean('isDeleted')->default(false);

            $table->bigInteger('manager_id')->unsigned()->nullable(); // Payroll officer that manually managed balance
            $table->foreign('manager_id', 'utte_m_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // Specific fields

            /*
             * Validity of balance ( MA | AA )
             */
            $table->dateTime('validityDateTime')->nullable();

            /*
             * Balance and value still available for this entry - if used it will be deducted based on dates ( MA | AA )
             */
            $table->decimal('currentBalance', 16, 10)->nullable();
            $table->decimal('currentValue', 16, 10)->nullable();

            /*
             * TimeSheet that TOIL was earnt ( MA | AA )
             */
            $table->bigInteger('timeSheet_id')->unsigned()->nullable();
            $table->foreign('timeSheet_id', 'utte_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');

            /*
             * TimeSheetDay that TOIL was earnt ( MA | AA )
             */
            $table->bigInteger('timeSheetDay_id')->unsigned()->nullable();
            $table->foreign('timeSheetDay_id', 'utte_tsid_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('restrict');

            /*
             * UserToilTypeEntry that was cancelled ( MC )
             */
            $table->bigInteger('cancelledUserToilTypeEntry_id')->unsigned()->nullable();
            $table->foreign('cancelledUserToilTypeEntry_id', 'utte_cautte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            /*
             *  UserToilTypeEntry that was expired ( EX )
             */
            $table->bigInteger('expiredUserToilTypeEntry_id')->unsigned()->nullable();
            $table->foreign('expiredUserToilTypeEntry_id', 'utte_eutte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            /*
             *  UserToilTypeEntry that was undone [uncommitted] ( UC )
             */
            $table->bigInteger('committedUserToilTypeEntry_id')->unsigned()->nullable();
            $table->foreign('committedUserToilTypeEntry_id', 'utte_coutte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            /*
             *  UserToilTypeEntry that was used or paid ( US | OP )
             */
            $table->bigInteger('usedUserToilTypeEntry_id')->unsigned()->nullable();
            $table->foreign('usedUserToilTypeEntry_id', 'utte_uoutte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            /*
             *  UserToilTypeEntry that was returned ( US | OP )
             */
            $table->bigInteger('returnedUserToilTypeEntry_id')->unsigned()->nullable();
            $table->foreign('returnedUserToilTypeEntry_id', 'utte_routte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            /*
             * Future Toil that committed this balance ( CM )
             * Declined or cancelled Toil that uncommitted this balance ( UC )
             * Past approved Toil that has used this balance ( US )
             */
            $table->bigInteger('toil_id')->unsigned()->nullable();
            $table->foreign('toil_id', 'utte_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');

            /*
             * PayRunItem associated to balance paid as Overtime ( OP )
             */
            $table->bigInteger('payRunItem_id')->unsigned()->nullable();
            $table->foreign('payRunItem_id', 'utte_pri_fn')->references('id')->on('PayRunItem')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserToilTypeEntryCommitted', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilTypeEntry_id')->unsigned();
            $table->bigInteger('userToilTypeEntryAccrued_id')->unsigned();

            $table->decimal('balance', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->nullable();

            $table->foreign('userToilTypeEntry_id', 'uttec_utte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilTypeEntryAccrued_id', 'uttec_uttea_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserToilTypeEntryRemoved', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilTypeEntry_id')->unsigned();
            $table->bigInteger('userToilTypeEntryAccrued_id')->unsigned();

            $table->decimal('balance', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->nullable();

            $table->foreign('userToilTypeEntry_id', 'utter_utte_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilTypeEntryAccrued_id', 'utter_uttea_fn')->references('id')->on('UserToilTypeEntry')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserToilType', function (Blueprint $table) {
            $table->decimal('committedBalance', 16, 10)->default(0);
            $table->decimal('committedValue', 16, 10)->default(0);
        });

        //region Add IN entries

        // $in_map = [];
        // if (Schema::hasTable('UserToilTypeBalanceIn')) {
        //     $balanceInEntries = DB::table('UserToilTypeBalanceIn')->get();
        //     foreach ($balanceInEntries as $balanceInEntry) {
        //         $in_map[] = [
        //             'old' => $balanceInEntry->id,
        //             'new' => DB::table('UserToilTypeEntry')->insertGetId([
        //                 'entryType' => $balanceInEntry->isManual ? 'MA' : 'AA',
        //                 'userToilType_id' => $balanceInEntry->userToilType_id,
        //                 'entryDateTime' => $balanceInEntry->entryDateTime,
        //                 'currentDateTime' => $balanceInEntry->currentDateTime,
        //                 'entryBalance' => $balanceInEntry->entryBalance,
        //                 'entryValue' => $balanceInEntry->entryValue,
        //                 'description' => $balanceInEntry->description,
        //                 'comments' => $balanceInEntry->comments,
        //                 'isDeleted' => $balanceInEntry->isDeleted,
        //                 'manager_id' => $balanceInEntry->payrollOfficer_id,
        //                 'validityDateTime' => $balanceInEntry->validityDateTime, // MA | AA
        //                 'currentBalance' => $balanceInEntry->currentBalance, // MA | AA
        //                 'currentValue' => $balanceInEntry->currentValue, // MA | AA
        //                 'timeSheet_id' => $balanceInEntry->timeSheet_id, // MA | AA
        //                 'timeSheetDay_id' => $balanceInEntry->timeSheetDay_id, // MA | AA
        //                 'cancelledUserToilTypeEntry_id' => null, // MC
        //                 'expiredUserToilTypeEntry_id' => null, // EX
        //                 'committedUserToilTypeEntry_id' => null, // UC
        //                 'toil_id' => null, // CM | UC | TU
        //                 'payRunItem_id' => null, // OP
        //             ]),
        //         ];
        //     }
        // }

        //endregion

        //region Add LOST entries

        // $lost_map = [];
        // if (Schema::hasTable('UserToilTypeBalanceLost')) {
        //     $balanceLostEntries = DB::table('UserToilTypeBalanceLost')->get();
        //     foreach ($balanceLostEntries as $balanceLostEntry) {
        //         $cancelledUserToilTypeEntry_id = null;
        //         $expiredUserToilTypeEntry_id = null;
        //
        //         $item = array_where($in_map, function ($v, $k) use ($balanceLostEntry) {
        //             if ($k >= 0) {
        //                 return $v['old'] == $balanceLostEntry->userToilTypeBalanceIn_id;
        //             }
        //
        //             return false;
        //         });
        //
        //         if ($balanceLostEntry->isManual) {
        //             $cancelledUserToilTypeEntry_id = $item['new'];
        //         } else {
        //             $expiredUserToilTypeEntry_id = $item['new'];
        //         }
        //
        //         $lost_map[] = [
        //             'old' => $balanceLostEntry->id,
        //             'new' => DB::table('UserToilTypeEntry')->insertGetId([
        //                 'entryType' => $balanceLostEntry->isManual ? 'MC' : 'EX',
        //                 'userToilType_id' => $balanceLostEntry->userToilType_id,
        //                 'entryDateTime' => $balanceLostEntry->entryDateTime,
        //                 'currentDateTime' => $balanceLostEntry->currentDateTime,
        //                 'entryBalance' => $balanceLostEntry->entryBalance,
        //                 'entryValue' => $balanceLostEntry->entryValue,
        //                 'description' => $balanceLostEntry->description,
        //                 'comments' => $balanceLostEntry->comments,
        //                 'isDeleted' => $balanceLostEntry->isDeleted,
        //                 'manager_id' => $balanceLostEntry->payrollOfficer_id,
        //                 'validityDateTime' => null, // MA | AA
        //                 'currentBalance' => null, // MA | AA
        //                 'currentValue' => null, // MA | AA
        //                 'timeSheet_id' => null, // MA | AA
        //                 'timeSheetDay_id' => null, // MA | AA
        //                 'cancelledUserToilTypeEntry_id' => $cancelledUserToilTypeEntry_id, // MC
        //                 'expiredUserToilTypeEntry_id' => $expiredUserToilTypeEntry_id, // EX
        //                 'committedUserToilTypeEntry_id' => null, // UC
        //                 'toil_id' => null, // CM | UC | TU
        //                 'payRunItem_id' => null, // OP
        //             ]),
        //         ];
        //     }
        // }

        //endregion

        //region Add USE entries

        // $use_map = [];
        // if (Schema::hasTable('UserToilTypeBalanceUse')) {
        //     $balanceUseEntries = DB::table('UserToilTypeBalanceUse')->get();
        //     foreach ($balanceUseEntries as $balanceUseEntry) {
        //         $use_map[] = [
        //             'old' => $balanceUseEntry->id,
        //             'new' => DB::table('UserToilTypeEntry')->insertGetId([
        //                 'entryType' => 'CM',
        //                 'userToilType_id' => $balanceUseEntry->userToilType_id,
        //                 'entryDateTime' => $balanceUseEntry->entryDateTime,
        //                 'currentDateTime' => $balanceUseEntry->currentDateTime,
        //                 'entryBalance' => $balanceUseEntry->entryBalance,
        //                 'entryValue' => $balanceUseEntry->entryValue,
        //                 'description' => $balanceUseEntry->description,
        //                 'comments' => $balanceUseEntry->comments,
        //                 'isDeleted' => false,
        //                 'manager_id' => $balanceUseEntry->payrollOfficer_id,
        //                 'validityDateTime' => null, // MA | AA
        //                 'currentBalance' => null, // MA | AA
        //                 'currentValue' => null, // MA | AA
        //                 'timeSheet_id' => null, // MA | AA
        //                 'timeSheetDay_id' => null, // MA | AA
        //                 'cancelledUserToilTypeEntry_id' => null, // MC
        //                 'expiredUserToilTypeEntry_id' => null, // EX
        //                 'committedUserToilTypeEntry_id' => null, // UC
        //                 'toil_id' => $balanceUseEntry->toil_id, // CM | UC | TU
        //                 'payRunItem_id' => null, // OP
        //             ]),
        //         ];
        //     }
        // }

        //endregion

        //region Add NxN relationship between IN and USE entries

        // if (Schema::hasTable('UserToilTypeBalanceUse_UserToilTypeBalanceIn')) {
        //     $x_records = DB::table('UserToilTypeBalanceUse_UserToilTypeBalanceIn')->get();
        //     foreach ($x_records as $x_record) {
        //         $item_in = array_first(array_where($in_map, function ($v, $k) use ($x_record) {
        //             if ($k >= 0) {
        //                 return $v['old'] == $x_record->userToilTypeBalanceIn_id;
        //             }
        //
        //             return false;
        //         }));
        //
        //         $item_use = array_first(array_where($use_map, function ($v, $k) use ($x_record) {
        //             if ($k >= 0) {
        //                 return $v['old'] == $x_record->userToilTypeBalanceUse_id;
        //             }
        //
        //             return false;
        //         }));
        //
        //         DB::table('UserToilTypeEntryCommitted')->insert([
        //             'userToilTypeEntry_id' => $item_use['new'],
        //             'userToilTypeEntryAccrued_id' => $item_in['new'],
        //             'balance' => $x_record->balance,
        //             'value' => $x_record->value,
        //         ]);
        //     }
        // }

        //endregion

        //region Add UNUSE entries

        $unuse_map = [];
        // if (Schema::hasTable('UserToilTypeBalanceUnUse')) {
        //     $balanceUnUseEntries = DB::table('UserToilTypeBalanceUnUse')->get();
        //     foreach ($balanceUnUseEntries as $balanceUnUseEntry) {
        //         $item = array_first(array_where($use_map, function ($v, $k) use ($balanceUnUseEntry) {
        //             if ($k >= 0) {
        //                 return $v['old'] == $balanceUnUseEntry->userToilTypeBalanceUse_id;
        //             }
        //
        //             return false;
        //         }));
        //
        //         $unuse_map[] = [
        //             'old' => $balanceUnUseEntry->id,
        //             'new' => DB::table('UserToilTypeEntry')->insertGetId([
        //                 'entryType' => 'UC',
        //                 'userToilType_id' => $balanceUnUseEntry->userToilType_id,
        //                 'entryDateTime' => $balanceUnUseEntry->entryDateTime,
        //                 'currentDateTime' => $balanceUnUseEntry->currentDateTime,
        //                 'entryBalance' => $balanceUnUseEntry->entryBalance,
        //                 'entryValue' => $balanceUnUseEntry->entryValue,
        //                 'description' => $balanceUnUseEntry->description,
        //                 'comments' => $balanceUnUseEntry->comments,
        //                 'isDeleted' => false,
        //                 'manager_id' => $balanceUnUseEntry->payrollOfficer_id,
        //                 'validityDateTime' => null, // MA | AA
        //                 'currentBalance' => null, // MA | AA
        //                 'currentValue' => null, // MA | AA
        //                 'timeSheet_id' => null, // MA | AA
        //                 'timeSheetDay_id' => null, // MA | AA
        //                 'cancelledUserToilTypeEntry_id' => null, // MC
        //                 'expiredUserToilTypeEntry_id' => null, // EX
        //                 'committedUserToilTypeEntry_id' => $item['new'], // UC
        //                 'toil_id' => $balanceUnUseEntry->toil_id, // CM | UC | TU
        //                 'payRunItem_id' => null, // OP
        //             ]),
        //         ];
        //     }
        // }

        //endregion

        Schema::table('PayRunItem', function (Blueprint $table) {
            $table->boolean('areLeaveBalancesAccrued')->default(false);
        });

        Schema::table('PayRun', function (Blueprint $table) {
            $table->boolean('areLeaveBalancesAccruing')->default(false);
            $table->boolean('areLeaveBalancesAccrued')->default(false);
        });

        // $payRuns = DB::table('PayRun')
        //     ->orWhere('status', '=', 'F')
        //     ->orWhere('processingDateTime', '<=', \Carbon\Carbon::now()->toDateTimeString())
        //     ->get();
        // foreach ($payRuns as $payRun) {
        //     $payRunItems = DB::table('PayRunItem')->where('payRun_id', '=', $payRun->id)->get();
        //     foreach ($payRunItems as $payRunItem) {
        //         $timeSheet = DB::table('TimeSheet')->where('payRunItem_id', '=', $payRunItem->id)->first();
        //         if (!is_null($timeSheet)) {
        //             if (in_array($timeSheet->status, ['A', 'P'])) {
        //                 DB::table('TimeSheet')->where('id', '=', $timeSheet->id)->update([
        //                     'status' => 'F',
        //                 ]);
        //             } elseif (!in_array($timeSheet->status, ['A', 'P', 'F'])) {
        //                 DB::table('TimeSheet')->where('id', '=', $timeSheet->id)->update([
        //                     'status' => 'X',
        //                 ]);
        //             }
        //         }
        //
        //         DB::table('PayRunItem')->where('id', '=', $payRunItem->id)->where('areLeaveBalancesAccrued', '=', 0)->update([
        //             'areLeaveBalancesAccrued' => 1,
        //         ]);
        //     }
        //
        //     DB::table('PayRun')->where('id', '=', $payRun->id)->where(function ($query) {
        //         /** @var \Illuminate\Database\Query\Builder $query */
        //         $query->orWhere('status', '<>', 'F')->orWhere('areLeaveBalancesAccrued', '=', '0');
        //     })->update([
        //         'status' => 'F',
        //         'areLeaveBalancesAccrued' => 1,
        //     ]);
        // }

        // $lastPayRun = DB::table('PayRun')->where('status', '=', 'F')->where('areLeaveBalancesAccrued', '=', 1)->orderBy('endDate', 'DESC')->first();

        //region committed balances for UserLeaveTypes
        // DB::table('UserLeaveType')->update([
        //     'committedBalance' => 0,
        // ]);
        // $userLeaveTypes = DB::table('UserLeaveType')->get();
        // foreach ($userLeaveTypes as $userLeaveType) {
        //     $leaves = DB::table('Leave')
        //         ->where('userLeaveType_id', '=', $userLeaveType->id)
        //         ->where('status', '=', 'A')
        //         ->where('endDateTime', '>', Carbon::parse($lastPayRun->endDate)->endOfDay())
        //         ->get();
        //     $hours = 0;
        //     foreach ($leaves as $leave) {
        //         $hours += $leave->hours;
        //     }
        //
        //     if ($hours > 0) {
        //         DB::table('UserLeaveType')->where('id', '=', $userLeaveType->id)->update([
        //             'committedBalance' => $hours,
        //         ]);
        //     }
        //
        //     $entry = DB::table('UserLeaveTypeEntry')->where('userLeaveType_id', '=', $userLeaveType->id)->orderBy('entryDateTime', 'DESC')->first();
        //     if (!is_null($entry)) {
        //         DB::table('UserLeaveTypeEntry')->where('id', '=', $entry->id)->update([
        //             'previousCommittedBalance' => $hours,
        //             'currentCommittedBalance' => $hours,
        //         ]);
        //     }
        // }
        //endregion

        //region committed balances for UserToilTypes
        // DB::table('UserToilType')->update([
        //     'committedBalance' => 0,
        // ]);
        // $userToilTypes = DB::table('UserToilType')->get();
        // foreach ($userToilTypes as $userToilType) {
        //     $leaves = DB::table('Toil')
        //         ->where('userToilType_id', '=', $userToilType->id)
        //         ->where('status', '=', 'A')
        //         ->where('endDateTime', '>', Carbon::parse($lastPayRun->endDate)->endOfDay())
        //         ->get();
        //     $hours = 0;
        //     foreach ($leaves as $leave) {
        //         $hours += $leave->hours;
        //     }
        //
        //     if ($hours > 0) {
        //         DB::table('UserToilType')->where('id', '=', $userToilType->id)->update([
        //             'committedBalance' => $hours,
        //         ]);
        //     }
        //
        //     $entry = DB::table('UserToilTypeEntry')->where('userToilType_id', '=', $userToilType->id)->orderBy('entryDateTime', 'DESC')->first();
        //     if (!is_null($entry)) {
        //         DB::table('UserToilTypeEntry')->where('id', '=', $entry->id)->update([
        //             'previousCommittedBalance' => $hours,
        //             'currentCommittedBalance' => $hours,
        //         ]);
        //     }
        // }
        //endregion

        //region committed balances for UserRosteredTimeOffTypes
        // DB::table('UserRosteredTimeOffType')->update([
        //     'committedBalance' => 0,
        // ]);
        // $userRosteredTimeOffTypes = DB::table('UserRosteredTimeOffType')->get();
        // foreach ($userRosteredTimeOffTypes as $userRosteredTimeOffType) {
        //     $leaves = DB::table('RosteredTimeOff')
        //         ->where('userRosteredTimeOffType_id', '=', $userRosteredTimeOffType->id)
        //         ->where('status', '=', 'A')
        //         ->where('endDateTime', '>', Carbon::parse($lastPayRun->endDate)->endOfDay())
        //         ->get();
        //     $hours = 0;
        //     foreach ($leaves as $leave) {
        //         $hours += $leave->hours;
        //     }
        //
        //     if ($hours > 0) {
        //         DB::table('UserRosteredTimeOffType')->where('id', '=', $userRosteredTimeOffType->id)->update([
        //             'committedBalance' => $hours,
        //         ]);
        //     }
        //
        //     $entry = DB::table('UserRosteredTimeOffTypeEntry')->where('userRosteredTimeOffType_id', '=', $userRosteredTimeOffType->id)->orderBy('entryDateTime', 'DESC')->first();
        //     if (!is_null($entry)) {
        //         DB::table('UserRosteredTimeOffTypeEntry')->where('id', '=', $entry->id)->update([
        //             'previousCommittedBalance' => $hours,
        //             'currentCommittedBalance' => $hours,
        //         ]);
        //     }
        // }
        //endregion
    }

    public function down(): void
    {
        if (Schema::hasColumn('PayRun', 'areLeaveBalancesAccruing')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('areLeaveBalancesAccruing');
            });
        }

        if (Schema::hasColumn('PayRun', 'areLeaveBalancesAccrued')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('areLeaveBalancesAccrued');
            });
        }

        if (Schema::hasColumn('PayRunItem', 'areLeaveBalancesAccrued')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('areLeaveBalancesAccrued');
            });
        }

        if (Schema::hasColumn('UserToilType', 'committedBalance')) {
            Schema::table('UserToilType', function (Blueprint $table) {
                $table->dropColumn('committedBalance');
            });
        }

        if (Schema::hasColumn('UserToilType', 'committedValue')) {
            Schema::table('UserToilType', function (Blueprint $table) {
                $table->dropColumn('committedValue');
            });
        }

        Schema::dropIfExists('UserToilTypeEntryRemoved');
        Schema::dropIfExists('UserToilTypeEntryCommitted');
        Schema::dropIfExists('UserToilTypeEntry');
    }
};
