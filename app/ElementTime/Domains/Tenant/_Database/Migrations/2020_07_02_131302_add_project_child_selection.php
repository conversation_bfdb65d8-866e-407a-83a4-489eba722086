<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Project
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Project MODIFY COLUMN isOpenToPayGroups TINYINT(1) NOT NULL DEFAULT \'0\' AFTER validTo');

        Schema::table('Project', function (Blueprint $table) {
            $table->boolean('doesAllowTimeToParent')->default(true)->after('budgetAlertPercentage');
        });
        //endregion Table: Project
    }

    public function down(): void
    {
        //region Table: Project
        if (Schema::hasColumn('Project', 'doesAllowTimeToParent')) {
            Schema::table('Project', function (Blueprint $table) {
                $table->dropColumn('doesAllowTimeToParent');
            });
        }
        //endregion Table: Project
    }
};
