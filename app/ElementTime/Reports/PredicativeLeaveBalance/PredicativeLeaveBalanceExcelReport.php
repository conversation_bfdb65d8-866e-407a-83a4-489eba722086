<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\PredicativeLeaveBalance;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBank;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayRunType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleDepartment;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\IndicativeLeave\Traits\IndicativeLeaveDataTrait;
use Element\ElementTime\Support\Facades\TenantOrganisationSettings;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Support\Collection as SupportCollection;

class PredicativeLeaveBalanceExcelReport extends TenantExcelReport
{
    use IndicativeLeaveDataTrait;

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'predicative-leave-balance';
    const TYPE_SLUG = 'predicative-leave-balance.xls';

    public $c = PredicativeLeaveBalanceReportConfig::class;

    protected PayRunType|null $payRunType = null;
    protected PayRun|null $firstPayRun = null;
    protected PayRun|null $lastPayRun = null;

    /**
     * @inheritDoc
     */
    protected function setData(array $info): void
    {
        $this->config->title = $this->c::NAME;
        $this->payRunType = PayRunType::q()->findOrFail($info['payRunType_id']);

        $firstPayRun = $this->payRunType->getCurrentPayRun();
        while ($firstPayRun->isFinished()) {
            $firstPayRun = $firstPayRun->next;
        }

        $this->firstPayRun = $firstPayRun;
        $this->lastPayRun = $this->payRunType->getPayRunByDate(Carbon::parse($info['endDate']));
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$doesCache) {
            $this->reportValidity = Carbon::now()->subSeconds(1);
        }

        $this->reportValidity = Carbon::tomorrow()->startOfDay();

        return $this;
    }

    protected function getStartDate(): Carbon
    {
        return $this->firstPayRun->startDate->copy();
    }

    protected function getEndDate(): Carbon
    {
        return $this->lastPayRun->endDate->copy()->addDay();
    }

    public function getReplacedString(string $string): string
    {
        $string = parent::getReplacedString($string);

        if (!is_null($this->firstPayRun)) {
            $string = Str::replace('{{firstPayRun.startDate}}', $this->firstPayRun->startDate->format('d-m-Y'), $string);
            $string = Str::replace('{{firstPayRun.endDate}}', $this->firstPayRun->endDate->format('d-m-Y'), $string);
        }

        if (!is_null($this->lastPayRun)) {
            $string = Str::replace('{{lastPayRun.startDate}}', $this->lastPayRun->startDate->format('d-m-Y'), $string);
            $string = Str::replace('{{lastPayRun.endDate}}', $this->lastPayRun->endDate->format('d-m-Y'), $string);
        }

        return $string;
    }

    /** @throws \Throwable */
    protected function buildReportData(): void
    {
        $this->sheets = [];

        $sourceData = $this->getData();

        foreach ($sourceData as $datum) {
            $this->sheets[] = $this->buildSheet($datum);
        }
    }

    /**
     * @param object{name: string, bankTypes: UserLeaveBankType[]} $data
     * @throws \Throwable
     */
    private function buildSheet(object $data): \stdClass
    {
        $this->validateSheet($sheet);
        $this->fetchSheetSettings($sheet, $data);
        $this->fetchSheetTitle($sheet, $data);
        $this->fetchSheetData($sheet, $data);

        return $sheet;
    }

    /** @param object{name: string, bankTypes: UserLeaveBankType[]} $data */
    private function fetchSheetSettings(\stdClass $sheet, object $data): void
    {
        $sheet->label = Str::cleanMaxSize($data->name, 30);
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    /** @param object{name: string, bankTypes: UserLeaveBankType[]} $data */
    private function fetchSheetTitle(\stdClass $sheet, object $data): void
    {
        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = 'Predicative ' . $data->name . ' from ' . $this->firstPayRun->startDate->format(TenantSystemSettings::o()->dateFormat) . ' to ' . $this->lastPayRun->endDate->format(TenantSystemSettings::o()->dateFormat);
        $sheet->data[] = $title;
    }

    /**
     * @param object{name: string, bankTypes: UserLeaveBankType[]} $data
     * @throws \Throwable
     */
    private function fetchSheetData(\stdClass $sheet, object $data): void
    {
        $table = new \stdClass;
        $table->type = 'data-table';
        $table->parentColumns = [
            $this->createParentColumn('Staff details', 8),
            $this->createParentColumn('Current totals (' . $this->firstPayRun->startDate->format(TenantSystemSettings::o()->dateFormat) . ')', 5),
            $this->createParentColumn('Predicted totals (' . $this->lastPayRun->endDate->format(TenantSystemSettings::o()->dateFormat) . ')', 5),
        ];
        $table->columns = [
            // Staff details
            $this->createColumn('Ref'),
            $this->createColumn('Last name'),
            $this->createColumn('First name'),
            $this->createColumn('Status'),
            $this->createColumn('Council start date'),
            $this->createColumn('Role name'),
            $this->createColumn('Manager name'),
            $this->createColumn('Department ext ref'),
            $this->createColumn('Department name'),
            $this->createColumn('Currently hourly rate'),

            // Today's current totals
            $this->createFloatColumn('Pro rata balance'),
            $this->createFloatColumn('Available balance'),
            $this->createFloatColumn('Cap'),
            $this->createFloatColumn('Excess balance'),
            $this->createFloatColumn('Taken this year until now'),

            // Predicated totals
            $this->createFloatColumn('Committed leave to end date'),
            $this->createFloatColumn('Pro rata balance'),
            $this->createFloatColumn('Available balance'),
            $this->createFloatColumn('Exceeded balance'),
            $this->createFloatColumn('Committed leave after end date'),
        ];
        $table->data = $this->getSheetData($data->bankTypes);

        $sheet->data[] = $table;
    }

    /**
     * @param SupportCollection|UserLeaveBankType[] $bankTypes
     * @throws \Throwable
     */
    private function getSheetData(SupportCollection|array $bankTypes): array
    {
        $tableData = [];
        $endDate = $this->getEndDate();

        foreach ($bankTypes as $userLeaveBankType) {
            if (!$userLeaveBankType->bank->user->hasLeaveOptions) {
                continue;
            }

            $tableData[] = [
                'config' => [],
                'data' => array_merge(
                    $this->fetchUserRowData($userLeaveBankType->bank->user, $endDate),
                    $this->fetchCurrentLeaveBankData($userLeaveBankType),
                    $this->fetchPredicatedBalanceOnDay($userLeaveBankType, $endDate),
                ),
            ];
        }

        return $tableData;
    }

    private function fetchUserRowData(User $user, CarbonInterface $date): array
    {
        $userRole = $user->getMasterUserRole($date);

        return [
            $user->externalId,
            $user->nameLast,
            $user->nameFirst,
            $user->statusClass::NAME,
            $user->startDate->format(TenantSystemSettings::o()->dateFormat),
            $userRole?->role->name ?? '',
            $user->currentManager?->fullName ?? '',
            $this->getDepartmentExternalId($userRole, $date),
            $this->getDepartmentName($userRole, $date),
            $userRole?->getActiveUserRolePayType($date)?->getHourlyAmount() ?? '-',
        ];
    }

    /** @throws \Throwable */
    private function fetchCurrentLeaveBankData(UserLeaveBankType $userLeaveBankType): array
    {
        $bank = $userLeaveBankType->bank;
        $proRataBalance = $bank->totalAccruedBalance;

        $availableBalance = $bank->totalAvailableBalance + $bank->totalCommittedBalance;
        if ($userLeaveBankType->doesAllowStaffToUseProRata) {
            $availableBalance += $proRataBalance;
        }

        $cap = $this->getBankCap($bank);

        return [
            etime_round($proRataBalance, 2),
            etime_round($availableBalance, 2),
            !is_null($cap) ? etime_round($cap, 2) : null,
            !is_null($cap) ? ($availableBalance > $cap ? etime_round($availableBalance - $cap, 2) : 0) : null,
            $this->getTakenThisYear($userLeaveBankType),
        ];
    }

    /**
     * @throws \Throwable
     */
    private function fetchPredicatedBalanceOnDay(UserLeaveBankType $userLeaveBankType, Carbon|CarbonInterface $date): array
    {
        $committed = $this->getPartialCommittedBalance($userLeaveBankType->bank, $date);
        $proRataBalance = $userLeaveBankType->bank->repository->getAccruedBalance($date);
        $availableBalance = $userLeaveBankType->bank->repository->getAvailableBalance($date);

        if ($userLeaveBankType->doesAllowStaffToUseProRata) {
            $availableBalance += $proRataBalance;
        }

        $cap = $this->getBankCap($userLeaveBankType->bank);

        $predictedExceededBalance = !is_null($cap) ? ($availableBalance > $cap ? $availableBalance - $cap : 0) : null;

        $totalCommittedAfterEndDate = $userLeaveBankType->bank->repository->getCommittedBalance($date);

        return [
            etime_round($committed, 2),
            etime_round($proRataBalance, 2),
            etime_round($availableBalance, 2),
            etime_round($predictedExceededBalance, 2),
            etime_round($totalCommittedAfterEndDate, 2),
        ];
    }

    private function getPartialCommittedBalance(UserLeaveBank $bank, CarbonInterface $date): float
    {
        return $bank->repository->getTakenBalance($date) - $bank->repository->getTakenBalance($this->getStartDate());
    }

    private function getDepartmentExternalId(UserRole|null $userRole, CarbonInterface $date): string
    {
        if (is_null($userRole)) {
            return '-';
        }

        return $userRole->userRoleDepartments
            ->filter(fn(UserRoleDepartment $userRoleDepartment) => $userRoleDepartment->availability($date)->isCurrent())
            ->map(fn(UserRoleDepartment $userRoleDepartment) => $userRoleDepartment->department->externalId)
            ->join(', ');
    }

    private function getDepartmentName(UserRole|null $userRole, CarbonInterface $date): string
    {
        if (is_null($userRole)) {
            return '-';
        }

        return $userRole->userRoleDepartments
            ->filter(fn(UserRoleDepartment $userRoleDepartment) => $userRoleDepartment->availability($date)->isCurrent())
            ->map(fn(UserRoleDepartment $userRoleDepartment) => $userRoleDepartment->department->name)
            ->join(', ');
    }

    //region --\\   Leave related data methods   //--

    /**
     * @throws \Throwable
     */
    private function getBankCap(UserLeaveBank $bank): float|null
    {
        if (!$bank->repository->doesHaveCap()) {
            return null;
        }

        return $bank->repository->getCapAmount();
    }

    protected function getTakenThisYear(UserLeaveBankType $bankType): float|null
    {
        $ret = 0;

        foreach ($this->getLeaveRequestsFromBankType($bankType) as $leaveRequest) {
            if ($leaveRequest->endDateTime->gte($this->getStartDate()->copy()->startOfDay())) {
                continue;
            }

            if ($leaveRequest->endDateTime->lt(Carbon::parse(TenantOrganisationSettings::getValue('fiscalYearStart')))) {
                continue;
            }

            $ret += $leaveRequest->calculatedTotalHours;
        }

        return etime_round($ret, 2);
    }

    //endregion --\\   Leave related data methods   //--
}
