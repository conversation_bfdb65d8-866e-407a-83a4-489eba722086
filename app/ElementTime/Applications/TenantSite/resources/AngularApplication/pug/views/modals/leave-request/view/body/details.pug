.row.mb-lg(style='line-height: 0.8')
  .col-xs-12
    label Period:
    .text-md {{ $ctrl.view.data.period }}

.row.mb-lg(style='line-height: 0.8')
  .col-xs-12
    label Total hours taken:
    .text-md
      span {{ $ctrl.view.data.durationAdjusted | _Elt$DurationFormat: $root.durationDisplayFormat.value }}
      i.fa.fa-clock-o.ml-sm.text-blue(ng-class='{"opacity-50": $root.durationDisplayFormat.value === "human"}' ng-click='$root.durationDisplayFormat.toggle()')
    .h4.text-thin.mt-sm.mb0(ng-if='$ctrl.view.data.duration !== $ctrl.view.data.durationAdjusted')
      | ({{$ctrl.view.data.duration | _Elt$DurationFormat: $root.durationDisplayFormat.value}} scheduled hours)

.row.mb-lg(ng-if='$ctrl.view.data.doesShowBalances' style='line-height: 0.8')
  .col-xs-12
    label Estimated remaining balance after leave is taken:
    .text-md
      span {{ $ctrl.view.data.balanceTotal | _Elt$DurationFormat: $root.durationDisplayFormat.value }}
      i.fa.fa-clock-o.ml-sm.text-blue(ng-class='{"opacity-50": $root.durationDisplayFormat.value === "human"}' ng-click='$root.durationDisplayFormat.toggle()')
    .h4.text-thin.mt-sm.mb0(ng-if='!!$ctrl.view.data.doesShowBalancesProRata')
      | ({{$ctrl.view.data.balanceAvailable | _Elt$DurationFormat: $root.durationDisplayFormat.value}} available + {{$ctrl.view.data.balanceAccrued | _Elt$DurationFormat: $root.durationDisplayFormat.value}} pro-rata)

.row.mb-lg(ng-if='!!$ctrl.view.data.reason && $ctrl.view.data.reason.length > 0' style='line-height: 0.8')
  .col-xs-12
    label Reason:
    .text-md(ng-bind-html='$ctrl.view.data.reason | _Elt$NoteContent')

.row.mb-lg(ng-if='(!!$ctrl.view.data.attachment_files && $ctrl.view.data.attachment_files.length > 0) || !!$ctrl.view.data.canAddAttachment')
  .col-xs-12
    label Attached files:
    .list-group.m0(ng-if='!!$ctrl.view.data.attachment_files && $ctrl.view.data.attachment_files.length > 0')
      .list-group-item.list-group-item-default(ng-repeat="file in $ctrl.view.data.attachment_files" style="padding-top: 0px !important; padding-bottom: 0px !important;")
        a.pt.pb.full-width(href='{{file.downloadUrl}}')
          i.fa.fa-download
          span.ml
            | {{!!file.name && file.name.length > 0 ? file.name : 'Attachment ' + ($index + 1)}}
            span(ng-if='!!file.user && !!file.user.fullName') &nbsp;|&nbsp;by&nbsp;
              strong {{file.user.fullName}}
            span &nbsp;at&nbsp;{{file.dateTime | _Elt$DateTimeFormat : true : false}}

    div(ng-if='!!$ctrl.view.data.canAddAttachment' ng-class='{"mt-lg": !!$ctrl.view.data.attachment_files && $ctrl.view.data.attachment_files.length > 0}')
      .row(ng-if='!$ctrl.view.attachments.isOpen')
        .col-xs-12
          button.btn.btn-default.pl-xl.pr-xl(type='button' ng-click='$ctrl.view.attachments.open()')
            i.fa.fa-paperclip.mr
            | Attach new file

      .row(ng-if='!!$ctrl.view.attachments.isOpen')
        .col-xs-12
          form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$ctrl.view.attachments.submit()' name='$ctrl.view.attachments.form')
            .panel.panel-default
              .panel-heading
                h4.h4.m0 Attach new file
              .panel-body
                .row
                  .col-xs-12.col-md-6
                    elt-attachment-file(id='LeaveAttach_attachmentFile' elt-label='Attachment file' name='attach_attachmentFile' ng-model='$ctrl.view.attachments.data.attachmentFile' elt-file-options='{{settings.form.LeaveRequest.attachmentFileOptions}}')
                  .col-xs-12.col-md-6.form-horizontal(ng-if='!!$ctrl.leave.data.hasSplitParts')
                    label &nbsp;
                    elt-toggle(ng-model='$ctrl.view.attachments.data.doesLinkAttachment' elt-name='' elt-label='Link attachment to requests also linked to this request' elt-help-text='')
                .row
                  .col-xs-12
                    hr.mt0.mb-lg
                .row.visible-sm.visible-md.visible-lg
                  .col-xs-12.text-right
                    button.btn.btn-default.ml(type='button' ng-click='$ctrl.view.attachments.close()' ng-disabled='!!$ctrl.view.attachments.isSubmitting')
                      | Cancel
                    button.btn.btn-success.ml(type='submit' ng-disabled='!!$ctrl.view.attachments.isSubmitting')
                      | Save
                .row.visible-xs
                  .col-xs-6
                    button.btn.btn-default.btn-block(type='button' ng-click='$ctrl.view.attachments.close()' ng-disabled='!!$ctrl.view.attachments.isSubmitting')
                      | Cancel
                  .col-xs-6
                    button.btn.btn-success.btn-block(type='submit' ng-disabled='!!$ctrl.view.attachments.isSubmitting')
                      | Save
