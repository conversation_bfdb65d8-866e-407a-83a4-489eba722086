div(ng-if='!$ctrl.view.approving.isOpen && !!$ctrl.view.data.workflow_expecting && !!$ctrl.view.data.workflow_expecting.steps && $ctrl.view.data.workflow_expecting.steps.length > 0')
  .row
    hr.mt0.mb-lg

  .row.mb-lg
    .col-xs-12(ng-repeat='step in $ctrl.view.data.workflow_expecting.steps')
      label.mb-sm(ng-if='$index === 0') Must be approved by:
      label.mb-sm(ng-if='$index > 0') And then by by:
      .list-group.b.mb-sm
        .list-group-item.list-group-item-default.b0(style='{{step.status !== "active" ? "opacity: .9 !important;" : ""}} border-top-right-radius: 0 !important; border-top-left-radius: 0 !important; border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;')
          div(ng-repeat='block in step.blocks')
            div(ng-if='$index > 0')
              hr.hr.mt.mb0
              .text-center
                small OR
              hr.hr.mt0.mb-lg
            div(ng-repeat='item in block')
              div(ng-if='$index > 0') and
              h5.h4.mt0.mb-sm(ng-bind-html='item.details')
              elt-staff-list(elt-list='item.users', elt-size='28' elt-item-margin-top='' elt-item-margin-bottom='sm')

div(ng-if='!!$ctrl.view.approving.isOpen')
  .row
    hr.mt0.mb-lg

  .row
    .col-xs-12
      form.form-validate(role='form' method='POST' action='javascript:' name='$ctrl.view.approving.form' ng-submit='$ctrl.view.approving.submit()')
        .panel.panel-default
          .panel-heading
            h4.h4.m0 Approve / decline leave request
          .panel-body
            .row
              .col-xs-12
                .form-group
                  label.label-control(for='LeaveApproveDecline_inputNotes') Your comments / notes
                  textarea.form-control(id='LeaveApproveDecline_inputNotes' ng-model='$ctrl.view.approving.data.notes' ng-change='$ctrl.view.approving.$events.onChangeNotes()')
            .row
              .col-xs-12
                button.btn.btn-success.mr(ng-if='!!$ctrl.view.approving.canApprove' type='submit' ng-click='$ctrl.view.approving.approve()' ng-disabled='!!$ctrl.view.approving.isSubmitting')
                  | Approve
                button.btn.btn-danger.mr(ng-if='!!$ctrl.view.approving.canDecline' type='submit' ng-click='$ctrl.view.approving.decline()' ng-disabled='!!$ctrl.view.approving.isSubmitting')
                  | Decline

                span.pull-right.text-danger.pt-sm(ng-if='!$ctrl.view.data.isThereEnoughBalance') Due to other recent approved requests the user does not have enough balance for you to now approve this request.

div(ng-if='!!$ctrl.view.cancel.isOpen')
  .row
    hr.mt0.mb-lg

  .row
    .col-xs-12
      form.form-validate(role='form' method='POST' action='javascript:' name='$ctrl.view.cancel.form' ng-submit='$ctrl.view.cancel.submit()')
        .panel.panel-default
          .panel-heading
            h4.h4.m0 Cancel leave request
          .panel-body
            .row
              .col-xs-12
                .form-group
                  label.label-control(for='LeaveCancel_inputNotes') Your comments / notes
                  textarea.form-control(id='LeaveCancel_inputNotes' ng-model='$ctrl.view.cancel.data.notes' required='required')

            .row.mb-lg(ng-if='!!$ctrl.view.data.staffCoveringLeave')
              .col-xs-12
                .m0.p0.list-group
                  .list-group-item.list-group-item-warning.text-warning
                    h4.h4.m0 {{ $ctrl.view.data.staffCoveringLeave.fullName }} is acting on higher duties on the period of this leave request.
                    p.mb0.text-warning You might want to let them know that this leave is being cancelled.

            .row
              .col-xs-12
                button.btn.btn-danger.mr(type='submit' ng-disabled='!!$ctrl.view.cancel.isSubmitting')
                  | Cancel request
                button.btn.btn-default(type='button' ng-click='$ctrl.view.cancel.close()' ng-disabled='!!$ctrl.view.cancel.isSubmitting')
                  | Don't cancel
