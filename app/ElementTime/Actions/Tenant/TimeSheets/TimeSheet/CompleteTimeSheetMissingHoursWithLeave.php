<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet;

use Element\Core\Exceptions\ElementException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromTimeSheet;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod\CompleteTimeSheetPeriodMissingHoursWithLeave;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;

class CompleteTimeSheetMissingHoursWithLeave extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheet $timeSheet,
        UserLeaveBankType $userLeaveBankType,
        User $actorUser,
        string $reason
    ): void
    {
        if (!$timeSheet->canAutoCompleteMissingHoursWithLeave) {
            throw new UnauthorizedActionException([
                'action' => 'complete-missing-hours-with-leave',
                'model' => 'timesheet',
                'reason' => 'Not allowed to complete timesheet missing hours with leave requests'
            ]);
        }
        if ($userLeaveBankType->bank->user_id !== $timeSheet->payRunItem->user_id) {
            throw new ElementException('Invalid timesheet and bank type combination');
        }

        if (!$userLeaveBankType->bank->user->hasLeaveOptions) {
            throw new ElementException('Not allowed to take leave');
        }

        if ($userLeaveBankType->status != ActiveStatus::ID) {
            throw new ElementException('Leave type is disabled');
        }

        if (!$userLeaveBankType->canTakeLeave) {
            throw new ElementException('Leave type cannot be used to take leave');
        }

        if (!$userLeaveBankType->doesAllowToBeUsedForCompleteMissingHours) {
            throw new ElementException('Leave type cannot be used to complete missing hours');
        }

        foreach ($timeSheet->timeSheetPeriods as $timeSheetPeriod) {
            if ($timeSheetPeriod->scheduleType->is(UserRoleScheduleType::ZeroBased)) {
                continue;
            }

            CompleteTimeSheetPeriodMissingHoursWithLeave::make()->handle(
                period: $timeSheetPeriod,
                userLeaveBankType: $userLeaveBankType,
                actorUser: $actorUser,
                reason: $reason,
            );
        }

        SetLeaveRequestRelationshipsFromTimeSheet::make()->handle(timeSheet: $timeSheet);
        CalculateTimeSheet::make()->handle(timeSheet: $timeSheet, setModelTaskDependant: true);
        $timeSheet->repository->updateIssues();
        $timeSheet->sendBroadcast();
    }
}
