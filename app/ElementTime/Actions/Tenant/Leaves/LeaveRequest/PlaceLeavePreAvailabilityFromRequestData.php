<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest;

use Element\Core\Exceptions\ElementException;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityZeroBased;
use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Database\Eloquent\Collection;

class PlaceLeavePreAvailabilityFromRequestData extends BaseLeavesAction
{
    /**
     * @throws \Throwable
     */
    public function handle(User $actorUser, array $data): LeaveRequest
    {
        if (!isset($data['$calculated']) || !$data['$calculated']) {
            throw new ElementException('Leave still not calculated');
        }

        $availability = CalculateLeavePreAvailabilityFromRequestData::make()->handle(
            actorUser: $actorUser,
            data: $data,
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            replaced: $replacedLeaveRequest,
        );

        if (!$availability->canBeRequested) {
            throw new ElementException('Cannot request leave');
        }

        if ($availability->doesHaveDayTimeAdjustments) {
            throw new ElementException('Please recalculate leave before applying');
        }

        $reason = $this->getReasonFromRequest($data);
        $attachments = $this->getAttachmentsFromRequestData($data);

        $doesApprove = $data['doesApprove'] ?? false;;
        $recordedVia = RecordedVia::DEFAULT;

        if (isset($data['recordedVia'])) {
            $recordedVia = RecordedVia::from($data['recordedVia']);
        }

        $action = match($userRoleSchedule->type) {
            UserRoleScheduleType::WorkPattern => PlaceLeaveRequestFromPreAvailabilityWorkPattern::make(),
            UserRoleScheduleType::ZeroBased => PlaceLeaveRequestFromPreAvailabilityZeroBased::make(),
            UserRoleScheduleType::DurationOnly => PlaceLeaveRequestFromPreAvailabilityDurationOnly::make(),
            default => throw new \InvalidArgumentException('Schedule type not defined or not supported: ' . $userRoleSchedule->type),
        };

        return $action->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: $availability,
            reason: $reason,
            attachments: $attachments,
            replaced: $replacedLeaveRequest,
            actorUser: $actorUser,
            doesApprove: $doesApprove,
            recordedVia: $recordedVia,
        );
    }

    protected function getReasonFromRequest(array $data): string|null
    {
        $reason = null;

        if (isset($data['reason'])) {
            $reason = $data['reason'];
        }

        return $reason;
    }

    /** @throws \Throwable */
    protected function getAttachmentsFromRequestData(array $data): Collection
    {
        $attachmentFile = null;
        $attachments = Collection::make();

        if (isset($data['attachmentFile'])) {
            $attachmentFile = $data['attachmentFile'];
        }

        if (is_array($attachmentFile) && isset($attachmentFile['id'])) {
            $mediaFile = MediaFile::q()->findOrFail($attachmentFile['id']);

            if (!is_null($mediaFile)) {
                $mediaFile->name = $attachmentFile['name'] ?? 'Leave attachment';
                $mediaFile->saveOrFail();
                $attachments->push($mediaFile);
            }
        }

        return $attachments;
    }
}
