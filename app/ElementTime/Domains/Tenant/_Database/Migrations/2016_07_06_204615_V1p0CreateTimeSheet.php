<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('TimeSheet', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('user_id')->unsigned();

            // Calculated fields (kept to improve performance when recovering data)
            $table->float('cTotalWorked')->nullable();
            $table->float('cTotalAllowance')->nullable();
            $table->float('cTotalHoursLess')->nullable();
            $table->float('cTotalOvertime')->nullable();
            $table->float('cTotalToilUsed')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id', 'timesheet_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->char('type', 1)->nullable(); // Values:
            //  P - Project (Project relationship)
            //  A - Activity (ProjectActivity relationship)
            //  L - LeaveType (LeaveType relationship)
            //  W - Allowance (AllowanceType relationship)
            //  R - Rostered Time Off (RosteredTimeOffType relationship)
            //  T - TOIL (No relationship)

            // Items relationships
            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();
            $table->bigInteger('leaveType_id')->unsigned()->nullable();
            $table->bigInteger('allowanceType_id')->unsigned()->nullable();
            $table->bigInteger('rosteredTimeOffType_id')->unsigned()->nullable();

            // Notes
            $table->longText('quarterNotes')->nullable();
            $table->longText('monthNotes')->nullable();
            $table->longText('fourWeeksNotes')->nullable();
            $table->longText('fortnightNotes')->nullable();
            $table->longText('weekNotes')->nullable();

            // Days
            $table->longText('map')->nullable();

            // Calculated fields (kept to improve performance when recovering data)
            $table->float('cTotalHours')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'timesheetitem_timesheet_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('project_id', 'timesheetitem_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('projectActivity_id', 'timesheetitem_pactivity_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leaveType_id', 'timesheetitem_leavetype_fn')->references('id')->on('LeaveType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('allowanceType_id', 'timesheetitem_alltype_fn')->references('id')->on('AllowanceType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOffType_id', 'timesheetitem_rdo_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->nullable();
            $table->dateTime('dueDateTime')->nullable();

            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1); // The number of levels and the user type of each manager depends on Settings.workflowTimesheetApproval map
            $table->string('notes')->nullable();

            $table->char('status', 1)->nullable(); // (W)aiting to be approved | (A)pproved | (D)isapproved

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'timesheetapproval_timesheet_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'timesheetapproval_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('TimeSheet');
        Schema::drop('TimeSheetItem');
        Schema::drop('TimeSheetApproval');
    }
};
