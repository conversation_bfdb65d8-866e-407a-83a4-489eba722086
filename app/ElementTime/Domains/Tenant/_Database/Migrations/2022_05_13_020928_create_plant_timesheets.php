<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PlantSheet

        Schema::create('PlantSheet', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();

            $table->unsignedBigInteger('payRun_id');
            $table->foreign('payRun_id', 'ps_pr_fn')
                ->references('id')->on('PayRun')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('plantItem_id');
            $table->foreign('plantItem_id', 'ps_pi_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->integer('minutesTotal')->nullable();
            $table->decimal('cartageTotal', 16, 10)->nullable();
            $table->decimal('mileageTotal', 16, 10)->nullable();
            $table->decimal('mileageStart', 16, 10)->nullable();
            $table->decimal('mileageEnd', 16, 10)->nullable();
            $table->decimal('costRateTotal', 16, 10)->nullable();

            $table->timestamps();
        });

        //endregion Table: PlantSheet

        //region Table: PlantSheetDayTime

        Schema::table('PlantSheetDayTime', function (Blueprint $table) {
            $table->unsignedBigInteger('plantSheet_id')->nullable()->after('id');
            $table->foreign('plantSheet_id', 'psdt_ps_fn')
                ->references('id')->on('PlantSheet')
                ->onUpdate('cascade')->onDelete('restrict');
        });

        //endregion Table: PlantSheetDayTime
    }

    public function down(): void
    {
        //region Table: PlantSheetDayTime

        try {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropForeign('psdt_ps_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('PlantSheetDayTime', 'plantSheet_id')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('plantSheet_id');
            });
        }

        //endregion Table: PlantSheetDayTime

        Schema::dropIfExists('PlantSheet');
    }
};
