<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        // First, do a fix on Leave (probably I was crazy when I related userShift_id field to UserLeaveType table on V1p0ModifyLeaveStructure class ^_^)
        Schema::table('Leave', function (Blueprint $table) {
            $table->dropForeign('leave_us_fn');
            $table->foreign('userShift_id', 'leave_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('User', function (Blueprint $table) {
            $table->boolean('hasRosteredTimeOff')->default(true);
        });

        Schema::create('TimeSheetMessage', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('user_id')->unsigned()->nullable();

            $table->string('name')->nullable();
            $table->string('title')->nullable();
            $table->text('message');
            $table->dateTime('dateTime');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'tsm_timesheet')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'tsm_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('TimeSheetMessageView', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->bigInteger('timeSheetMessage_id')->unsigned();
            $table->bigInteger('user_id')->unsigned()->nullable();

            $table->longText('dateTimes');

            $table->boolean('viewed')->default(true);

            $table->foreign('timeSheetMessage_id', 'tsmv_tsm')->references('id')->on('TimeSheetMessage')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'tsmv_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->boolean('automaticallyUsesToil')->default(false);
        });

        Schema::create('ToilType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->boolean('allowsToPayOnTermination')->default(true);
            $table->boolean('allowsToPayOnRequest')->default(false);
            $table->boolean('allowsToPayAfterExpiry')->default(false);
            $table->decimal('expiryInNumber', 16, 10)->nullable();
            $table->char('expiryInType', 1)->nullable(); // Options: (H)our | (D)ay | (W)eek

            $table->char('calculationType', 1)->default('S'); // Options:
            // (S)tays the same
            // Changes depending on the (T)ime of day it is earnt
            // Changes based on how (M)uch TOIL is earnt over time
            $table->longText('formula')->nullable(); // Format:
            /*
                If calculationType is S
                    {
                        "ratioTo": 1 // float
                    }

                If calculationType is T
                    [
                        {
                            "startTime: "06:00", // time
                            "endTime": "18:00", // time
                            "ratioTo": 1, // float
                        },
                        {
                            "startTime: "18:00", // time
                            "endTime": "06:00", // time
                            "ratioTo": 1, // float
                        }
                    ]
                If calculationType is M
                    [
                        {
                            "hours": 4, // float
                            "ratioTo": 1, // float
                        },
                        {
                            "hours": 3, // time
                            "ratioTo": 1, // float
                        },
                        {
                            "hours": null, // last must be null
                            "ratioTo": 1, // float
                        }
                    ]
            */

            $table->boolean('appliesBufferBeforeAccrued')->default(false);
            $table->decimal('minutesAsBuffer', 16, 10)->nullable()->default(null);

            $table->boolean('roundsUp')->default(false);
            $table->boolean('roundsDown')->default(false);
            $table->integer('roundMinutes')->nullable(); // Options: 5, 10, 15, 30 and 60

            $table->string('accountCode')->nullable();
            $table->bigInteger('project_id')->unsigned()->nullable();

            $table->char('autoApplies', 1)->default('D'); // Options:
            // After worker exceeds (D)aily scheduled hours
            // After worker exceeds (W)eekly scheduled hours
            // After worker exceeds (P)ay run scheduled hours
            // After worker exceeds <hour> (H)ours per <period> period
            $table->decimal('autoApplyHours')->nullable();
            $table->char('autoApplyPeriod', 1)->nullable(); // Options:
            // (D)ay
            // (W)eek
            // (P)ay run

            $table->boolean('earntOnPublicHoliday')->default(true);
            $table->boolean('earntOnRegionalPublicHoliday')->default(true);
            $table->boolean('earntOnOrganisationPublicHoliday')->default(true);
            $table->boolean('earntOnSaturdays')->default(true);
            $table->boolean('earntOnSundays')->default(true);

            $table->decimal('limitBalance', 16, 10)->nullable();
            $table->char('ifLimitReached', 1)->default('M'); // Options:
            // Do (N)othing
            // Send Alert to (M)anager
            // Send Alert to (P)ayroll officer
            // Send (A)lert to Manager and Payroll Officer

            $table->char('status', 1);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('project_id', 'toiltype_project')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::table('TimeSheetItem', function (Blueprint $table) {
            $table->bigInteger('toilType_id')->unsigned()->nullable();
            $table->foreign('toilType_id', 'timesheetitem_toiltype_fn')->references('id')->on('ToilType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserToilType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('toilType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->default('A');
            // Options:
            //  A => Active
            //  I => Inactive

            $table->decimal('availableTotalBalance', 16, 10);
            $table->decimal('availableLess4WeeksBalance', 16, 10);
            $table->decimal('available4To12WeeksBalance', 16, 10);
            $table->decimal('availableMore12WeeksBalance', 16, 10);
            $table->decimal('lostBalance', 16, 10);
            $table->decimal('usedAsToilBalance', 16, 10);
            $table->decimal('usedAsOvertimeBalance', 16, 10);
            $table->decimal('historicalBalance', 16, 10);

            $table->decimal('availableTotalValue', 16, 10);
            $table->decimal('availableLess4WeeksValue', 16, 10);
            $table->decimal('available4To12WeeksValue', 16, 10);
            $table->decimal('availableMore12WeeksValue', 16, 10);
            $table->decimal('lostValue', 16, 10);
            $table->decimal('usedAsToilValue', 16, 10);
            $table->decimal('usedAsOvertimeValue', 16, 10);
            $table->decimal('historicalValue', 16, 10);

            $table->foreign('toilType_id', 'user_toiltype_tt_fn')->references('id')->on('ToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'user_toiltype_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('UserToilTypeBalanceIn', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilType_id')->unsigned();

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime');
            $table->dateTime('validityDateTime')->nullable();

            $table->boolean('isManual')->default(false);

            $table->decimal('entryBalance', 16, 10);
            $table->decimal('currentBalance', 16, 10);

            $table->decimal('entryValue', 16, 10);
            $table->decimal('currentValue', 16, 10);

            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            $table->bigInteger('timeSheet_id')->unsigned()->nullable(); // TimeSheet that TOIL was earnt
            $table->bigInteger('timeSheetDay_id')->unsigned()->nullable(); // TimeSheetDay that TOIL was earnt
            $table->bigInteger('payrollOfficer_id')->unsigned()->nullable(); // Payroll officer that manually managed balance

            $table->foreign('userToilType_id', 'uttbi_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheet_id', 'uttbi_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheetDay_id', 'uttbi_tsid_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('payrollOfficer_id', 'uttbi_po_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('UserToilTypeBalanceUse', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilType_id')->unsigned();

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime');

            $table->boolean('isManual')->default(false);
            $table->char('type', 1)->default('T');
            // Options:
            //  - T => TOIL
            //  - O => Overtime

            $table->decimal('entryBalance', 16, 10);
            $table->decimal('entryValue', 16, 10);

            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            $table->bigInteger('timeSheetItemDay_id')->unsigned()->nullable(); // TimeSheetItemDay that TOIL was used
            $table->bigInteger('payrollOfficer_id')->unsigned()->nullable(); // Payroll officer that manually managed balance

            $table->foreign('userToilType_id', 'uttbu_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('payrollOfficer_id', 'uttbu_po_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('UserToilTypeBalanceUse_UserToilTypeBalanceIn', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('userToilTypeBalanceUse_id')->unsigned();
            $table->bigInteger('userToilTypeBalanceIn_id')->unsigned();

            $table->foreign('userToilTypeBalanceUse_id', 'uttbu_uttbi_uttbu_fn')->references('id')->on('UserToilTypeBalanceUse')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilTypeBalanceIn_id', 'uttbu_uttbi_uttbi_fn')->references('id')->on('UserToilTypeBalanceIn')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserToilTypeBalanceUse_TimeSheetItemDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('userToilTypeBalanceUse_id')->unsigned();
            $table->bigInteger('timeSheetItemDay_id')->unsigned();

            $table->foreign('userToilTypeBalanceUse_id', 'uttbu_tsid_uttbu_fn')->references('id')->on('UserToilTypeBalanceUse')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheetItemDay_id', 'uttbu_tsid_tsid_fn')->references('id')->on('TimeSheetItemDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserToilTypeBalanceLost', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilType_id')->unsigned();
            $table->bigInteger('userToilTypeBalanceIn_id')->unsigned(); // Balance related

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime');

            $table->boolean('isManual')->default(false);

            $table->decimal('entryBalance', 16, 10);
            $table->decimal('entryValue', 16, 10);

            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            $table->bigInteger('payrollOfficer_id')->unsigned()->nullable(); // Payroll officer that manually managed balance

            $table->foreign('userToilType_id', 'uttbl_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilTypeBalanceIn_id', 'uttbl_uttbi_fn')->references('id')->on('UserToilTypeBalanceIn')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('payrollOfficer_id', 'uttbl_po_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('Toil', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userToilType_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->string('name')->nullable();
            $table->text('reason')->nullable();

            $table->longText('approvalWorkflowMap')->nullable(); // From Settings->workflowLeaveApproval (duplicated to keep the current map)

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userToilType_id', 'toil_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'toil_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('ToilDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('toil_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();
            $table->bigInteger('userShiftDay_id')->unsigned();
            $table->date('date');

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->char('status', 1)->nullable();

            $table->foreign('toil_id', 'toilday_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'toilday_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'toilday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShiftDay_id', 'toilday_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('ToilApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('toil_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->nullable();
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1); // The number of levels and the user type of each manager depends on Settings.workflowLeaveApproval map
            $table->text('notes')->nullable();

            $table->char('status', 1)->nullable(); // (W)aiting approval | (A)pproved | (D)isapproved

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('toil_id', 'toilapproval_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'toilapproval_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserToilTypeBalanceUse', function (Blueprint $table) {
            $table->bigInteger('toil_id')->unsigned();
            $table->foreign('toil_id', 'uttbu_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserToilTypeBalanceUse', 'toil_id')) {
            Schema::table('UserToilTypeBalanceUse', function (Blueprint $table) {
                $table->dropForeign('uttbu_toil_fn');
                $table->dropColumn('toil_id');
            });
        }

        Schema::dropIfExists('ToilApproval');

        Schema::dropIfExists('ToilDay');

        Schema::dropIfExists('Toil');

        Schema::dropIfExists('UserToilTypeBalanceLost');

        Schema::dropIfExists('UserToilTypeBalanceUse_TimeSheetItemDay');

        Schema::dropIfExists('UserToilTypeBalanceUse_UserToilTypeBalanceIn');

        Schema::dropIfExists('UserToilTypeBalanceUse');

        Schema::dropIfExists('UserToilTypeBalanceIn');

        Schema::dropIfExists('UserToilType');

        if (Schema::hasColumn('TimeSheetItem', 'toilType_id')) {
            Schema::table('TimeSheetItem', function (Blueprint $table) {
                $table->dropForeign('timesheetitem_toiltype_fn');
                $table->dropColumn('toilType_id');
            });
        }

        Schema::dropIfExists('ToilType');

        if (Schema::hasColumn('TimeSheet', 'automaticallyUsesToil')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('automaticallyUsesToil');
            });
        }

        Schema::dropIfExists('TimeSheetMessageView');

        Schema::dropIfExists('TimeSheetMessage');

        if (Schema::hasColumn('User', 'hasRosteredTimeOff')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasRosteredTimeOff');
            });
        }
    }
};
