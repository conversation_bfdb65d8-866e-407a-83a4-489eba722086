<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('ModelIssue', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('model_type');
            $table->unsignedBigInteger('model_id');
            $table->string('type'); // ID from type class

            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();
            $table->string('otherRel_type')->nullable();
            $table->unsignedBigInteger('otherRel_id')->nullable();

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'missue_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // General data
            $table->string('level'); // ID from level  class - Options: stopper / warning / info
            $table->string('workflowStatus')->nullable(); // Options: null (used for other than workflow) / workflow status type ID
            $table->integer('priority')->default(0); // 0 to 10
            $table->longText('details')->nullable();
            $table->text('description')->nullable();
            $table->dateTime('dueDateTime')->nullable();
            $table->boolean('isRelevant')->default(true);

            // Workflow data
            $table->boolean('isSolved')->default(false);
            $table->unsignedBigInteger('solvedByUser_id')->nullable();
            $table->foreign('solvedByUser_id', 'missue_sbu_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->dateTime('solvedDateTime')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ModelIssue');
    }
};
