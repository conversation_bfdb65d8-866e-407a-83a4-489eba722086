<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Department

        Schema::table('Department', function (Blueprint $table) {
            $table->char('status', 1)->default('A');

            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id', 'department_parent_fn')->references('id')->on('Department')->onUpdate('cascade')->onDelete('cascade');
        });

        //endregion Table: Department

        //region Table: UserDepartment

        Schema::create('UserDepartment', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('department_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('userRole_id');

            $table->date('startDate')->nullable()->default(null);
            $table->date('endDate')->nullable()->default(null);
            $table->string('comments')->nullable();
            $table->boolean('isManager')->default(false);
            $table->char('status', 1)->default('A');

            $table->foreign('department_id', 'ud_department_fn')->references('id')->on('Department')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'ud_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('userRole_id', 'ud_userrole_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();
        });

        //endregion Table: UserDepartment
    }

    public function down(): void
    {
        //region Table: UserDepartment

        Schema::dropIfExists('UserDepartment');

        //endregion Table: UserDepartment

        //region Table: Department

        try {
            Schema::table('Department', function (Blueprint $table) {
                $table->dropForeign('department_parent_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('Department', 'parent_id')) {
            Schema::table('Department', function (Blueprint $table) {
                $table->dropColumn('parent_id');
            });
        }
        if (Schema::hasColumn('Department', 'status')) {
            Schema::table('Department', function (Blueprint $table) {
                $table->dropColumn('status');
            });
        }

        //endregion Table: Department
    }
};
