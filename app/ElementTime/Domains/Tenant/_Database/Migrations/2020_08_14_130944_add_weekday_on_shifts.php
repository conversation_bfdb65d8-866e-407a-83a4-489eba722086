<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ShiftDay

        Schema::table('ShiftDay', function (Blueprint $table) {
            $table->integer('weekDay')->default(0)->after('number');
            $table->integer('weekNumber')->default(1)->after('weekDay');
        });

        //endregion Table: ShiftDay

        //region Table: UserShiftDay

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->boolean('isOn')->default(false)->after('date');
            $table->string('label')->nullable()->after('isOn');
            $table->boolean('hasExpectedSchedule')->default(false)->after('glideRequireTimeBlock');
            $table->integer('expectedMinutes')->nullable()->after('hasExpectedSchedule');
        });

        if (Schema::hasColumn('UserShiftDay', 'leave_id')) {
            \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN leave_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER isOnLeave;');
            if (Schema::hasColumn('UserShiftDay', 'rosteredTimeOffType_id')) {
                \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN rosteredTimeOffType_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER leave_id;');
            }
        } elseif (Schema::hasColumn('UserShiftDay', 'rosteredTimeOffType_id')) {
            \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN rosteredTimeOffType_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER isOnLeave;');
        }

        //endregion Table: UserShiftDay

        //region Table: UserShiftDayTime

        Schema::table('UserShiftDayTime', function (Blueprint $table) {
            $table->dateTime('startDateTime')->nullable()->after('label');
            $table->dateTime('endDateTime')->nullable()->after('startDateTime');
        });

        //endregion Table: UserShiftDayTime

        //region Table: UserShiftDayTimeBreak

        Schema::table('UserShiftDayTimeBreak', function (Blueprint $table) {
            $table->dateTime('startDateTime')->nullable()->after('label');
            $table->dateTime('endDateTime')->nullable()->after('startDateTime');
        });

        //endregion Table: UserShiftDayTimeBreak
    }

    public function down(): void
    {
        //region Table: UserShiftDayTimeBreak

        if (Schema::hasColumn('UserShiftDayTimeBreak', 'startDateTime')) {
            Schema::table('UserShiftDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTimeBreak', 'endDateTime')) {
            Schema::table('UserShiftDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('endDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTimeBreak', 'startTime')) {
            Schema::table('UserShiftDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('startTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTimeBreak', 'endTime')) {
            Schema::table('UserShiftDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('endTime');
            });
        }

        //endregion Table: UserShiftDayTimeBreak

        //region Table: UserShiftDayTime

        if (Schema::hasColumn('UserShiftDayTime', 'startDateTime')) {
            Schema::table('UserShiftDayTime', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTime', 'endDateTime')) {
            Schema::table('UserShiftDayTime', function (Blueprint $table) {
                $table->dropColumn('endDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTime', 'startTime')) {
            Schema::table('UserShiftDayTime', function (Blueprint $table) {
                $table->dropColumn('startTime');
            });
        }

        if (Schema::hasColumn('UserShiftDayTime', 'endTime')) {
            Schema::table('UserShiftDayTime', function (Blueprint $table) {
                $table->dropColumn('endTime');
            });
        }


        //endregion Table: UserShiftDayTime

        //region Table: UserShiftDay

        if (Schema::hasColumn('UserShiftDay', 'label')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('label');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'isOn')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('isOn');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'expectedMinutes')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('expectedMinutes');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'hasExpectedSchedule')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('hasExpectedSchedule');
            });
        }

        //endregion Table: UserShiftDay

        //region Table: ShiftDay

        if (Schema::hasColumn('ShiftDay', 'weekDay')) {
            Schema::table('ShiftDay', function (Blueprint $table) {
                $table->dropColumn('weekDay');
            });
        }

        if (Schema::hasColumn('ShiftDay', 'weekNumber')) {
            Schema::table('ShiftDay', function (Blueprint $table) {
                $table->dropColumn('weekNumber');
            });
        }

        //endregion Table: ShiftDay
    }
};
