<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: WorkOrderTypeClassificationItem

        Schema::create('WorkOrderTypeClassificationItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotci_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('workOrderClassificationItem_id');
            $table->foreign('workOrderClassificationItem_id', 'wotci_woci_fn')
                ->references('id')->on('WorkOrderClassificationItem')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedTinyInteger('level');

            $table->timestamps();

            $table->unique(['workOrderType_id', 'level'], 'wotci_tp_lvl_uq');
            $table->unique(['workOrderType_id', 'workOrderClassificationItem_id'], 'wotci_tp_ct_uq');
        });

        //endregion Table: WorkOrderTypeClassificationItem
    }

    public function down(): void
    {
        Schema::dropIfExists('WorkOrderTypeClassificationItem');
    }
};
