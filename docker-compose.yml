services:
  et_www:
    build:
      context: ./
      dockerfile: ./docker/www/Dockerfile
    ports:
      - '80:80'
      - '3000:80'
    expose:
      - 9003
    volumes:
      - .:/var/www/elementtime
      - ./docker/www/volumes/elementtime.ini:/usr/local/etc/php/conf.d/elementtime.ini
    networks:
      - internal
    depends_on:
      - et_database
      - et_redis
      - et_docdb
  et_database:
    image: mysql/mysql-server:8.0
    environment:
      MYSQL_ROOT_PASSWORD: '123'
      MYSQL_ROOT_HOST: '%'
    command: --max-sp-recursion-depth=255
    ports:
      - '33063:3306'
    expose:
      - '33063'
    volumes:
      - et-database:/var/lib/mysql
      - ./docker/mysql/0_init.sql:/docker-entrypoint-initdb.d/0_init.sql
    networks:
      - internal
    restart: always
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123" ]
      interval: 2s
      timeout: 5s
      retries: 5
  et_redis:
    image: redis:7.0-alpine
    command: redis-server
    ports:
      - '63793:6379'
    expose:
      - '63793'
    volumes:
      - et-redis:/data
    networks:
      - internal
  et_docdb:
    image: mongo:7.0
    environment:
      MONGO_INITDB_DATABASE: 'etgeneral'
      MONGO_INITDB_ROOT_USERNAME: 'root'
      MONGO_INITDB_ROOT_PASSWORD: '123'
    ports:
      - '27018:27017'
    expose:
      - '27018'
    volumes:
      - et-docdb70-config:/data/configdb
      - et-docdb70-data:/data/db
    networks:
      - internal

volumes:
  et-database:
  et-redis:
  et-docdb70-config:
  et-docdb70-data:

networks:
  internal:
    driver: bridge
