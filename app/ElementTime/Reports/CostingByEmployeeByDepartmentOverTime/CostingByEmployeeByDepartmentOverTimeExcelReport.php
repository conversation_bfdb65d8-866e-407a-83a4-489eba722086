<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\CostingByEmployeeByDepartmentOverTime;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShift;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes;
use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyRuleCalculationMethods\OffsetMethod;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTimeWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRuleWork;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CostingByEmployeeByDepartmentOverTimeExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const TYPE_SLUG = 'costing-by-employee-by-department-over-time';
    const REPORT_SLUG = 'costing-by-employee-by-department-over-time.xls';

    /** @var CostingByEmployeeByDepartmentOverTimeReportConfig */
    public $c = CostingByEmployeeByDepartmentOverTimeReportConfig::class;

    protected Department|null $department = null;
    protected bool $doesIncludeSubDepartments = false;
    protected float $totalStaffCount = 0;

    protected function setData(array $info): void
    {
        $this->_iSetData($info);

        if (isset($info['idDepartment'])) {
            $this->department = Department::q()->find($info['idDepartment']);

            if (isset($info['doesIncludeSubDepartments']) && strlen($info['doesIncludeSubDepartments']) > 0) {
                $this->doesIncludeSubDepartments = !!$info['doesIncludeSubDepartments'];
            }
        }
    }

    public function getReplacedString(string $string): string
    {
        $string = $this->_iGetReplacedString($string);

        if (!is_null($this->department)) {
            $string = Str::replace('{{department.id}}', $this->department->id . ($this->doesIncludeSubDepartments ? '-s' : ''), $string);
            $string = Str::replace('{{department.name}}', $this->department->name . ($this->doesIncludeSubDepartments ? ' and sub departments' : ''), $string);
        } else {
            $string = Str::replace('{{department.id}}', '0', $string);
            $string = Str::replace('{{department.name}}', 'All departments', $string);
        }

        return $string;
    }

    /** @throws InvalidArgumentException */
    protected function buildReportData(): void
    {
        $startDate = $this->getStartDate();
        $endDate = $this->getEndDate();
        $payRunItems = $this->collectReportData($startDate, $endDate, $this->department, $this->doesIncludeSubDepartments);

        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetTotals($sheet, $this->totalStaffCount);
        $this->fetchSheetData($sheet, $payRunItems, $startDate, $endDate);

        $this->sheets = [
            $sheet,
        ];
    }

    /**
     * @return Collection|PayRunItem[]
     */
    private function collectReportData(Carbon|CarbonInterface $startDate, Carbon|CarbonInterface $endDate, Department|null $department, bool $doesIncludeSubDepartments = false): Collection|array
    {
        if (!is_null($department)) {
            $users = $department->repository->getAllStaff($doesIncludeSubDepartments, $doesIncludeSubDepartments, $endDate, $startDate);
        } else {
            $users = User::q()
                ->constraints([
                    ['User.id', '>', 1],
                    [
                        [
                            ['IS NULL', 'User.startDate', 'OR'],
                            ['User.startDate', '=', '0000-00-00', 'OR'],
                            ['User.startDate', '<=', $endDate->toDateString(), 'OR'],
                        ],
                    ],
                    [
                        [
                            ['IS NULL', 'User.endDate', 'OR'],
                            ['User.endDate', '=', '0000-00-00', 'OR'],
                            ['User.endDate', '>=', $startDate->toDateString(), 'OR'],
                        ],
                    ],
                ])
                ->many();
        }

        $staffIds = [];
        foreach ($users as $user) {
            $staffIds[] = $user->id;
        }
        $this->totalStaffCount = count($staffIds);

        $payRunItems = PayRunItem::q()
            ->constraints([
                ['PayRun.startDate', '>=', $startDate],
                ['PayRun.endDate', '<=', $endDate],
                ['IN', 'PayRunItem.user_id', $staffIds],
                ['IS NOT NULL', 'TimeSheet.id'],
            ])
            ->many();

        $payRunItems->loadMissing([
            'user.employeeType',
            'user.userPayTypes.payType',
            'user.userRoles' => function (Builder $query) use ($startDate, $endDate) {
                $query->where(function (Builder $subQuery) use ($startDate) {
                    $subQuery->orWhereNull('UserRole.endDate');
                    $subQuery->orWhere('UserRole.endDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserRole.endDate', '>', $startDate);
                });
                $query->where(function (Builder $subQuery) use ($endDate) {
                    $subQuery->orWhereNull('UserRole.startDate');
                    $subQuery->orWhere('UserRole.startDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserRole.startDate', '<', $endDate);
                });
            },
            'user.userRoles.userPayTypes.payType.payBand',
            'user.userRoles.userDepartments' => function (Builder $query) use ($startDate, $endDate) {
                $query->where(function (Builder $subQuery) use ($startDate) {
                    $subQuery->orWhereNull('UserDepartment.endDate');
                    $subQuery->orWhere('UserDepartment.endDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserDepartment.endDate', '>', $startDate);
                });
                $query->where(function (Builder $subQuery) use ($endDate) {
                    $subQuery->orWhereNull('UserDepartment.startDate');
                    $subQuery->orWhere('UserDepartment.startDate', '=', '0000-00-00');
                    $subQuery->orWhere('UserDepartment.startDate', '<', $endDate);
                });
            },
            'user.userRoles.userDepartments.department',
            'user.userRoles.userRoleManagers.manager',
            'user.userRoles.user',
            'user.userRoles.role',
            'user.userRoles.userShifts.userShiftDays' => function (Builder $query) use ($startDate, $endDate) {
                $query->whereBetween('UserShiftDay.date', [$startDate, $endDate]);
            },
            'user.userRoles.userShifts.userShiftDays.leaveRequestDays' => function (Builder $query) use ($startDate, $endDate) {
                $query->whereBetween('LeaveRequestDay.date', [$startDate, $endDate]);
            },
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.works.penaltyCalculationItems.calculation.penalty.userRolePenaltyRule.penaltyRule',
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.rule.item',
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.works.excessTimeItemRuleWorks.splitItems',
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.penalties.userRolePenaltyRule.penaltyRule',
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.rule.item',
            'user.userRoles.userShifts.userShiftDays.timeSheetDayTimes.excessTimeWorks.splitItems',
            'user.userRoles.userShifts.userShiftDays.userShift.user.userHigherDuties.payType',
            'user.userRoles.userShifts.userShiftDays.leaveRequestDays',
            'timeSheet.timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries',
            'timeSheet.penaltySets.userPenaltyType',
        ]);

        return $payRunItems;
    }

    private function fetchSheetTotals(\stdClass $sheet, float $count): void
    {
        $table = new \stdClass();
        $table->type = 'data-table';

        $table->columns = [
            $this->createColumn('Date/time', span: 3),
            $this->createColumn('Span of report', span: 4),
            $this->createColumn('Number of staff', span: 3),
        ];

        $table->data = [[
            'config' => [],
            'data' => [
                $this->parseDate(Carbon::now()),
                'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear . ' to PayRun #' . $this->payRunEnd->number . ' ' . $this->payRunEnd->fiscalYear,
                $count,
            ],
        ]];

        $sheet->data[] = $table;
    }

    /**
     * @param Collection|PayRunItem[] $payRunItems
     * @throws InvalidArgumentException
     */
    private function fetchSheetData(\stdClass $sheet, Collection|array $payRunItems, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = [
            $this->createParentColumn('', 1),
            $this->createParentColumn('Staff information', 10),
            $this->createParentColumn('Worked hours', 2),
            $this->createParentColumn('Leave', 2),
            $this->createParentColumn('Public holidays', 2),
            $this->createParentColumn('Excess time', 8),
            $this->createParentColumn('Penalties', 2),
            $this->createParentColumn('Allowances'),
            $this->createParentColumn('Totals', 2),
        ];
        $table->columns = [
            // Start [Staff information]
            $this->createColumn('#'),
            $this->createColumn('Ext ref'),
            $this->createColumn('User Name'),
            $this->createColumn('Employee type'),
            $this->createColumn('Role name'),
            $this->createColumn('Role status'),
            $this->createColumn('Role manager'),
            $this->createColumn('Department'),
            $this->createColumn('Pay rate name'),
            $this->createColumn('Pay rate code'),
            $this->createFloatColumn('Payrate hourly amount'),
            // Start [Worked hours]
            $this->createFloatColumn('Total ordinay hours (worked)'),
            $this->createFloatColumn('Cost of ordinary hours'),
            // Start [Leave]
            $this->createFloatColumn('Total hours leave'),
            $this->createFloatColumn('Cost of leave'),
            // Start [Public holidays]
            $this->createFloatColumn('Total hours public holiday'),
            $this->createFloatColumn('Cost of public holidays'),
            // Start [Excess time]
            $this->createFloatColumn('Total hours paid overtime'),
            $this->createFloatColumn('Cost of paid overtime'),
            $this->createFloatColumn('Total hours overtime accrued'),
            $this->createFloatColumn('Cost of overtime accrued'),
            $this->createFloatColumn('Total hours overtime additional hours'),
            $this->createFloatColumn('Cost of overtime additional'),
            $this->createFloatColumn('Total hours overtime unpaid'),
            $this->createFloatColumn('Cost of overtime unpaid'),
            // Start [Penalties]
            $this->createFloatColumn('Hours of penalties'),
            $this->createFloatColumn('Cost of penalties'),
            // Start [Allowances]
            $this->createFloatColumn('Cost of allowances'),
            // Start [Totals]
            $this->createFloatColumn('Total hours'),
            $this->createFloatColumn('Total cost'),
        ];
        $table->data = [];
        $entries = [];

        foreach ($payRunItems as $payRunItem) {
            if (is_null($payRunItem->timeSheet) || $payRunItem->timeSheet->repository->isExcluded()) {
                continue;
            }

            $user = $payRunItem->user;

            foreach ($user->userRoles as $userRole) {
                $this->fetchDataFromUserRole($entries, $user, $userRole, $startDate, $endDate);
            }

            $this->fetchDataFromTimeSheet($entries, $payRunItem->timeSheet, $endDate, $user);
        }

        foreach ($entries as  $i => $entry) {
            $table->data[] = [
                'config' => [],
                'data' => [
                    $i + 1, // #

                    // Staff information
                    $entry->externalId, // Ext ref
                    $entry->fullName, // User Name
                    $entry->employeeType, // Employee type
                    $entry->role, // Role name
                    $entry->isRoleActive, // Role status
                    $entry->roleManagerName, // Role manager
                    $entry->departmentNames, // Department
                    $entry->payTypeName, // Pay rate name
                    $entry->payRateCode, // Pay rate code
                    $entry->payRateHourlyAmount, // Payrate hourly amount

                    // Worked hours
                    $entry->totalOrdinaryHours, // Total ordinay hours (worked)
                    $entry->totalOrdinaryCost, // Cost of ordinary hours

                    // Leave
                    $entry->totalLeaveHours, // Total hours leave
                    $entry->totalLeaveCost, // Cost of leave

                    // Public holidays
                    $entry->totalPublicHolidaysHours, // Total hours public holiday
                    $entry->totalPublicHolidaysCost, // Cost of public holidays

                    // Excess time
                    $entry->totalPaidOvertimeHours, // Total hours paid overtime
                    $entry->totalPaidOvertimeCost, // Cost of paid overtime
                    $entry->totalAccruedOvertimeHours, // Total hours overtime accrued
                    $entry->totalAccruedOvertimeCost, // Cost of overtime accrued
                    $entry->totalAdditionalOvertimeHours, // Total hours overtime additional hours
                    $entry->totalAdditionalOvertimeCost, // Total hours overtime unpaid
                    $entry->totalUnpaidOvertimeHours, // Cost of overtime additional
                    $entry->totalUnpaidOvertimeCost, // Cost of overtime unpaid

                    // Penalties
                    $entry->totalPenaltyHours, // Hours of penalties
                    $entry->totalPenaltyCost, // Cost of penalties

                    // Allowances
                    $entry->totalAllowanceCost, // Cost of allowances

                    // Totals
                    $entry->totalOrdinaryHours + $entry->totalLeaveHours + $entry->totalPublicHolidaysHours + $entry->totalPaidOvertimeHours + $entry->totalAccruedOvertimeHours + $entry->totalAdditionalOvertimeHours + $entry->totalUnpaidOvertimeHours + $entry->totalPenaltyHours, // 'Total hours'
                    $entry->totalOrdinaryCost + $entry->totalLeaveCost + $entry->totalPublicHolidaysCost + $entry->totalPaidOvertimeCost + $entry->totalAccruedOvertimeCost + $entry->totalAdditionalOvertimeCost + $entry->totalUnpaidOvertimeCost + $entry->totalPenaltyCost + $entry->totalAllowanceCost, // 'Total cost'
                ],
            ];
        }

        $sheet->data[] = $table;
    }

    private static function getOrSetRecordEntry(array &$entries, User $user, int $userRole_id): \stdClass
    {
        $key = $user->id . '_' . $userRole_id;

        if (!isset($entries[$key])) {
            $newEntry = new \stdClass();
            $newEntry->id = null;
            $newEntry->externalId = null;
            $newEntry->fullName = null;
            $newEntry->employeeType = null;
            $newEntry->role = null;
            $newEntry->isRoleActive = null;
            $newEntry->roleManagerName = null;
            $newEntry->departmentNames = null;
            $newEntry->payTypeName = null;
            $newEntry->payRateCode = null;
            $newEntry->payRateHourlyAmount = null;
            $newEntry->totalOrdinaryHours = null;
            $newEntry->totalOrdinaryCost = null;
            $newEntry->totalLeaveHours = null;
            $newEntry->totalLeaveCost = null;
            $newEntry->totalPublicHolidaysHours = null;
            $newEntry->totalPublicHolidaysCost = null;
            $newEntry->totalPaidOvertimeHours = null;
            $newEntry->totalPaidOvertimeCost = null;
            $newEntry->totalAccruedOvertimeHours = null;
            $newEntry->totalAccruedOvertimeCost = null;
            $newEntry->totalAdditionalOvertimeHours = null;
            $newEntry->totalAdditionalOvertimeCost = null;
            $newEntry->totalUnpaidOvertimeHours = null;
            $newEntry->totalUnpaidOvertimeCost = null;
            $newEntry->totalPenaltyHours = null;
            $newEntry->totalPenaltyCost = null;
            $newEntry->totalAllowanceCost = null;
            $newEntry->totalHours = null;
            $newEntry->totalCost = null;
            $entries[$key] = $newEntry;
        }

        return $entries[$key];
    }

    private function getDepartmentNames(UserRole $userRole, CarbonInterface $date): string
    {
        $userDepartmentNames = [];

        foreach($userRole->repository->getActiveUserDepartments($date) as $userDepartment) {
            $userDepartmentNames[] = $userDepartment->department->name;
        }

        return implode(',', $userDepartmentNames);
    }

    private function fetchDataFromTimeSheet(array &$entries, TimeSheet $timeSheet, CarbonInterface $endDate, User $user): void
    {
        foreach ($timeSheet->penaltySets as $penaltySet) {
            if ($penaltySet->totalValue <= 0) {
                continue;
            }

            if (is_null($penaltySet->userPenaltyType->userShift_id)) {
                $userRole = $user->repository->getMasterUserRole($endDate);
                $record = static::getOrSetRecordEntry($entries, $user, $userRole->id);
            } else {
                $record = static::getOrSetRecordEntry($entries, $user, $penaltySet->userPenaltyType->userShift->userRole_id);
            }

            $record->totalAllowanceCost += $penaltySet->totalValue;
        }

        foreach($timeSheet->timeSheetAllowances as $timeSheetAllowance) {
            $allowanceUserRole = null;
            foreach ($timeSheetAllowance->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
                if (!is_null($allowanceUserRole)) {
                    break;
                }

                foreach ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries as $timeSheetAllowanceDayEntry) {
                    if (!is_null($timeSheetAllowanceDayEntry->timeSheetDayTime_id)) {
                        $allowanceUserRole = $timeSheetAllowanceDayEntry->timeSheetDayTime->userShiftDay->userShift->userRole;
                    }

                    if (!is_null($allowanceUserRole)) {
                        break;
                    }
                }
            }

            if (is_null($allowanceUserRole)) {
                $allowanceUserRole = $user->repository->getMasterUserRole($endDate);
            }

            if (!is_null($allowanceUserRole)) {
                $record = static::getOrSetRecordEntry($entries, $user, $allowanceUserRole->id);
                $record->totalAllowanceCost += $timeSheetAllowance->totalValue;
            }
        }
    }

    /**
     * @throws InvalidArgumentException
     */
    private function fetchDataFromUserRole(array &$entries, User $user, UserRole $userRole, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        $userShifts = [];

        foreach ($userRole->userShifts as $userShift) {
            if ($userShift->isActiveOnTime($startDate) || $userShift->isActiveOnTime($endDate)) {
                $userShifts[] = $userShift;
            }
        }

        if (count($userShifts) < 1) {
            return;
        }

        $record = static::getOrSetRecordEntry($entries, $user, $userRole->id);

        $record->id = $user->id;
        $record->externalId = $userRole->repository->getExternalId();
        $record->fullName = $user->fullName;
        $record->employeeType = $user->employeeType->name;
        $record->role = $userRole->role->name; // Role name
        $record->isRoleActive = $userRole->isActiveOnTime($endDate) ? 'Active' : 'Inactive'; // Role status
        $record->roleManagerName = $userRole->repository->getManager($endDate)?->fullName; // Role manager
        $record->departmentNames = $this->getDepartmentNames($userRole, $endDate); // Department

        $userPayType = $userRole->repository->getUserPayType($endDate);
        $record->payTypeName = $userPayType->payTypeName; // Pay rate name
        $record->payRateCode = is_null($userPayType->payType) // Pay rate code
            ? 'Custom'
            : $userPayType->payType->externalId;
        $record->payRateHourlyAmount = $userPayType->getHourlyAmount(); // Pay rate hourly amount

        foreach($userShifts as $userShift) {
            $this->fetchDataFromUserShift($record, $userShift, $startDate, $endDate);
        }
    }

    private function fetchDataFromUserShift(\stdClass &$record, UserShift $userShift, CarbonInterface $startDate, CarbonInterface $endDate): void
    {
        foreach ($userShift->userShiftDays as $userShiftDay) {
            if (!$userShiftDay->date->isBetween($startDate, $endDate)) {
                continue;
            }

            foreach ($userShiftDay->leaveRequestDays as $leaveRequestDay) {
                $this->fetchDataFromLeaveRequestDay($record, $leaveRequestDay, $userShiftDay);
            }

            foreach ($userShiftDay->timeSheetDayTimes as $timeSheetDayTime) {
                $this->fetchDataFromTime($record, $timeSheetDayTime, $userShiftDay);
            }
        }
    }

    private function fetchDataFromLeaveRequestDay(\stdClass $record, LeaveRequestDay $leaveRequestDay, UserShiftDay $userShiftDay): void
    {
        $rate = $userShiftDay->hourlyRate;

        if ($userShiftDay->isOnHigherDuty) {
            if ($userShiftDay->higherDuty->doesApplyDutyRateToAllLeave) {
                $rate = $userShiftDay->higherDuty->payType->getHourlyAmount();
            }
        }

        $record->totalLeaveHours += $leaveRequestDay->actualHours;
        $record->totalLeaveCost += ($leaveRequestDay->actualHours * $rate);
    }

    private function fetchDataFromTime(\stdClass $record, TimeSheetDayTime|TimeSheetDayTimeWork $time, UserShiftDay $userShiftDay): void
    {
        if ($time->recordTypeClass::IS_HOLIDAY) {
            $record->totalPublicHolidaysHours += $time->hours;
            $record->totalPublicHolidaysCost += ($time->hours * $userShiftDay->hourlyRate);

            return;
        }

        $actual = $time->hours;
        foreach ($time->penalties as $penalty) {
            $penaltyHours = $penalty->minutes->toHours();

            if ($penalty->userRolePenaltyRule->penaltyRule->calculationMethodClass::isType(OffsetMethod::class)) {
                $actual -= $penaltyHours;
            }

            $record->totalPenaltyHours += $penaltyHours;
            $record->totalPenaltyCost += $penalty->value;
        }

        foreach ($time->excessTimeWorks as $excessTimeItemRuleWork) {
            static::fetchDataFromExcessTimeItemRuleWork($record, $excessTimeItemRuleWork, $actual, $userShiftDay->hourlyRateOvertime);
        }

        $record->totalOrdinaryHours += $actual;
        $record->totalOrdinaryCost += ($actual * $userShiftDay->hourlyRate);
    }

    private static function fetchDataFromExcessTimeItemRuleWork(\stdClass $record, TimeSheetExcessTimeItemRuleWork $work, float &$parentHours, float $overtimeRate): void
    {
        if (!$work->rule->item->isActive) {
            return;
        }

        foreach ($work->splitItems as $splitItem) {
            if ($splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\PaidHoursType::class)) {
                $record->totalPaidOvertimeHours += $splitItem->adjustedHours;
                $record->totalPaidOvertimeCost += ($overtimeRate * $splitItem->adjustedHours);
            } elseif ($splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\AdditionalHoursType::class)) {
                $record->totalAdditionalOvertimeHours += $splitItem->actualHours;
                $record->totalAdditionalOvertimeCost += ($overtimeRate * $splitItem->actualHours);
            } elseif ($splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\UnpaidHoursType::class)) {
                $record->totalUnpaidOvertimeHours += $splitItem->actualHours;
                $record->totalUnpaidOvertimeCost += 0;
            } elseif ($splitItem->ruleTypeClass::isType(ExcessTimeRuleTypes\AccruedHoursType::class)) {
                $record->totalAccruedOvertimeHours += $splitItem->actualHours;
                $record->totalAccruedOvertimeCost += 0;
            }

            $parentHours -= $splitItem->actualHours;
        }
    }
}
