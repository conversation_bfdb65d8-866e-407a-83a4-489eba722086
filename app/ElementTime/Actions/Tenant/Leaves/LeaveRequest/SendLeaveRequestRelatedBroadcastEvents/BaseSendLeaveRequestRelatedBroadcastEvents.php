<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendLeaveRequestRelatedBroadcastEvents;

use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;

abstract class BaseSendLeaveRequestRelatedBroadcastEvents extends BaseLeavesAction
{
    protected LeaveRequest $leave;
    protected UserRoleSchedule $userRoleSchedule;

    public function handle(LeaveRequest $leaveRequest): void
    {
        $this->leave = $leaveRequest;
        $this->userRoleSchedule = $leaveRequest->userRoleSchedule;

        $this->sendBroadcastEvents();
    }

    public function asJob(LeaveRequest $leaveRequest): void
    {
        $this->handle($leaveRequest);
    }

    //  -------------------

    /**
     * @todo - Work-pattern and duration-only
     * @todo - Timesheet events
     * @todo - Timecard events
     */
    protected function sendBroadcastEvents(): void
    {
        /// TODO
    }
}
