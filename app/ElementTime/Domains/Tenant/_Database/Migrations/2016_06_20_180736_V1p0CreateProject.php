<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('Project', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            // Project Structure
            $table->string('externalId')->nullable();
            $table->string('name')->nullable();
            $table->string('projectCode')->nullable();
            $table->bigInteger('owner_id')->unsigned()->nullable();
            $table->date('validFrom')->nullable();
            $table->date('validTo')->nullable();

            // Project Budget
            $table->char('budgetType', 1)->nullable();
            $table->float('budgetAmount')->nullable();
            $table->boolean('hasBudgetAlert')->default(null);
            $table->float('budgetAlertPercentage')->nullable();

            $table->char('status', 1)->default(null);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('owner_id', 'project_owner_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('Project_PayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('project_id')->unsigned();
            $table->bigInteger('payGroupType_id')->unsigned();

            $table->primary(['project_id', 'payGroupType_id']);
            $table->foreign('project_id', 'project_paygrouptype_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payGroupType_id', 'project_paygrouptype_paygrouptype_fn')->references('id')->on('PayGroupType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('Project_Department', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('project_id')->unsigned();
            $table->bigInteger('department_id')->unsigned();

            $table->primary(['project_id', 'department_id']);
            $table->foreign('project_id', 'project_department_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('department_id', 'project_department_department_fn')->references('id')->on('Department')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('Project');
        Schema::drop('Project_PayGroupType');
        Schema::drop('Project_Department');
    }
};
