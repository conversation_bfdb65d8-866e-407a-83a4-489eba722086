<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\Kiosk;

use Element\ElementTime\Actions\Tenant\_System\KioskDeviceRequest\CreateKioskDeviceRequest;
use Element\ElementTime\Actions\Tenant\_System\KioskDeviceRequest\ResolveKioskDeviceRequest;
use Element\ElementTime\Applications\TenantApi\ApiBroker\ApiBroker;
use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\BaseController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk\KioskDeviceBasicResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk\KioskDeviceRequestBasicResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\TenantApiV4Response;
use Element\ElementTime\Domains\Tenant\_System\Enums\KioskDeviceRequestStatus\KioskDeviceRequestStatus;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;

class KioskController extends BaseController
{
    /** @throws \Throwable */
    public function getDevice(string|null $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        return TenantApiV4Response::fetch(KioskDeviceBasicResource::make(
            ApiBroker::getInstance()->getKioskDevice(),
        ));
    }

    /** @throws \Throwable */
    public function createDeviceRequest(string|null $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $deviceId = $this->i('deviceId', null, 'string');
        $ipAddress = $this->i('ipAddress', null, 'string');
        $deviceName = $this->i('deviceName', null, 'string');

        $deviceRequest = CreateKioskDeviceRequest::make()->handle($deviceId, $ipAddress, $deviceName);

        return TenantApiV4Response::fetch(KioskDeviceRequestBasicResource::make($deviceRequest));
    }

    /** @throws \Throwable */
    public function getDeviceRequest(string|null $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $deviceRequest = KioskDeviceRequest::q()->findOrFail($id);

        return TenantApiV4Response::fetch(KioskDeviceRequestBasicResource::make($deviceRequest));
    }

    /** @throws \Throwable */
    public function cancelDeviceRequest(string|null $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $deviceRequest = KioskDeviceRequest::q()->findOrFail($id);
        $deviceRequest = ResolveKioskDeviceRequest::make()->handle($deviceRequest, KioskDeviceRequestStatus::Cancelled);

        return TenantApiV4Response::fetch(KioskDeviceRequestBasicResource::make($deviceRequest));
    }
}
