<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('Deduction', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Deduction Details
            $table->string('name')->nullable();
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();

            // Rules
            $table->char('rateType', 1)->nullable();
            $table->float('deductionAmount')->nullable();
            $table->boolean('canChangeRateType')->nullable();
            $table->char('madeOn', 1)->nullable();
            $table->boolean('canChangeAmount')->nullable();
            $table->boolean('isDonation')->nullable();
            $table->boolean('isLinkedToTax')->nullable();
            $table->float('protectedThresholdAmount')->nullable();
            $table->char('protectedThresholdType', 1)->nullable();

            // Other Details
            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();
            $table->char('isPayableTo', 1)->nullable();
            $table->bigInteger('fundProject_id')->unsigned()->nullable();
            $table->bigInteger('fundProjectActivity_id')->unsigned()->nullable();
            $table->bigInteger('fundOrganisation_id')->unsigned()->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('incrementRate_id', 'deduction_incrementrate_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('project_id', 'deduction_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('projectActivity_id', 'deduction_projectactivity_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('fundProject_id', 'deduction_fundproject_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('fundProjectActivity_id', 'deduction_fundprojectactivity_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('fundOrganisation_id', 'deduction_fundprorgtanisation_fn')->references('id')->on('FundOrganisation')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::drop('Deduction');
    }
};
