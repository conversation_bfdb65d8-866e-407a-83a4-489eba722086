<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays;

use Carbon\CarbonInterface;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDefaultData;

class GetScheduledDaysDurationOnly extends BaseGetScheduledDays
{
    protected function makeDayActual(
        UserRoleSchedule $userRoleSchedule,
        ScheduledPayRunPeriodDay $dayData,
        ScheduledPayRunPeriod $scheduledPayRunPeriod,
    ): StructScheduledDayDurationOnly
    {
        /** @var ScheduledPayRunPeriodDayDurationOnly $dayDataDetails */
        $dayDataDetails = $dayData->specific;
        $hasFixedHours =
            $scheduledPayRunPeriod->specific instanceof ScheduledPayRunPeriodDurationOnly
            && $scheduledPayRunPeriod->specific->doesHaveFixedHours;

        $ret = $this->makeStructScheduledDayDurationOnly($dayData->date->copy(), $hasFixedHours, $dayDataDetails);
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;
        $ret->fteRatio = $scheduledPayRunPeriod->specific->getFteRatio();
        $ret->periodData = $scheduledPayRunPeriod;

        return $ret;
    }

    protected function makeDayDefault(
        UserRoleSchedule $userRoleSchedule,
        StructScheduledPayRunPeriodDayDefaultData $dayData,
        StructScheduledPayRunPeriodDefaultData $specificPeriod,
    ): StructScheduledDayDurationOnly
    {
        /** @var object{doesHaveFixedHours: bool, duration: TimeDuration, minDuration: TimeDuration, maxDuration: TimeDuration} $dayDataDetails */
        $dayDataDetails = $dayData->type;
        $hasFixedHours = $dayDataDetails->doesHaveFixedHours;

        $ret = $this->makeStructScheduledDayDurationOnly($dayData->date->copy(), $hasFixedHours, $dayDataDetails);
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;
        $ret->fteRatio = $userRoleSchedule->durationOnly->getFteRatio();
        $ret->periodData = $specificPeriod->type;

        return $ret;
    }

    // --------------------------------------

    protected function makeStructScheduledDayDurationOnly(
        CarbonInterface $date,
        bool $hasFixedHours,
        ScheduledPayRunPeriodDayDurationOnly|\stdClass $dayData,
    ): StructScheduledDayDurationOnly
    {
        $ret = new StructScheduledDayDurationOnly;

        $ret->date = $date;
        $ret->isScheduled = $dayData->isScheduled;
        $ret->isRdo = $dayData->isRdo;

        $ret->hasFixedHours = $hasFixedHours;
        $ret->duration = $dayData->duration ?? TimeDuration::zero();
        $ret->minDuration = $dayData->minDuration ?? TimeDuration::zero();
        $ret->maxDuration = $dayData->maxDuration ?? TimeDuration::zero();

        return $ret;
    }
}
