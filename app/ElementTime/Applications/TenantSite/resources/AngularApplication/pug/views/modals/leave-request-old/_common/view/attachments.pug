.row(ng-if='!!req.attachment_files && req.attachment_files.length > 0')
  hr.hr.m0
  .col-xs-12.mb
    h5.h5.mt-lg.mb-sm Attached files
    .list-group.mb-sm
      .list-group-item.list-group-item-default(ng-repeat="file in req.attachment_files" style="padding-top: 0px !important; padding-bottom: 0px !important;")
        a.pt.pb.full-width(href='{{file.downloadUrl}}')
          i.fa.fa-download
          span.ml
            | {{!!file.name && file.name.length > 0 ? file.name : 'Attachment ' + ($index + 1)}}
            span(ng-if='!!file.user && !!file.user.fullName') &nbsp;|&nbsp;by&nbsp;
              strong {{file.user.fullName}}
            span &nbsp;at&nbsp;{{file.dateTime | _Elt$DateTimeFormat : true : false}}