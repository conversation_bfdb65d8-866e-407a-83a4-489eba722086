.row.mb-lg
  .col-xs-12(ng-class='{"col-md-8": !!$ctrl.view.data.isFromAutoComplete}')
    label.label.label-lg.block.p.m0(class='label-{{$ctrl.view.data.status.color}}') {{$ctrl.view.data.status.name}}

  .col-xs-12.visible-xs.visible-sm(ng-if='!!$ctrl.view.data.isFromAutoComplete')
    .mt-lg
  .col-xs-12.col-md-4(ng-if='!!$ctrl.view.data.isFromAutoComplete')
    label.label.label-lg.block.p.m0.label-green-bordered(ng-if='true') Requested by auto-complete
