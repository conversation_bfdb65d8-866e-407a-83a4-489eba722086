<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod;

use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriod\ScheduledPayRunPeriodSpecificModelContract;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodDurationOnly;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodWorkPattern;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetPeriod;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;

/*
 * @todo - Sum of total hours of leave on TimeSheetPeriod should be calculated and stored in TimeSheetPeriod ?
 */
class GetTimeSheetPeriodMissingDuration extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(TimeSheetPeriod $timeSheetPeriod): TimeDuration
    {
        // TODO: Check what else needs to be done here

        $minDuration = match ($timeSheetPeriod->scheduleType) {
            UserRoleScheduleType::WorkPattern => static::getMinDurationForScheduledPayRunPeriodWorkPattern($timeSheetPeriod->scheduledPayRunPeriod->specific),
            UserRoleScheduleType::DurationOnly => static::getMinDurationForScheduledPayRunPeriodDurationOnly($timeSheetPeriod->scheduledPayRunPeriod->specific),
            default => TimeDuration::zero(),
        };

        $nonApprovedLeaveStatuses = [
            WorkflowStatusTypes\NewType::ID,
            WorkflowStatusTypes\SubmittedType::ID,
            WorkflowStatusTypes\PartiallyApprovedType::ID,
        ];

        $minDuration
            ->deduct($timeSheetPeriod->durationOrdinary)
            ->deduct($timeSheetPeriod->durationPublicHolidayTaken)
            ->deduct($timeSheetPeriod->repository->calculateRecordedLeaveDuration($nonApprovedLeaveStatuses));

        return $minDuration;
    }

    /// ------------------------------------------

    private static function getMinDurationForScheduledPayRunPeriodWorkPattern(ScheduledPayRunPeriodSpecificModelContract|ScheduledPayRunPeriodWorkPattern $payRunPeriodWorkPattern): TimeDuration
    {
        $payRunPeriodWorkPattern->loadMissing(['patternPeriodShift.scheduleShift']);

        if ($payRunPeriodWorkPattern->doesHaveFixedHours) {
            return $payRunPeriodWorkPattern->duration->copy();
        }

        return $payRunPeriodWorkPattern->minDuration->copy();
    }

    private static function getMinDurationForScheduledPayRunPeriodDurationOnly(ScheduledPayRunPeriodSpecificModelContract|ScheduledPayRunPeriodDurationOnly $payRunPeriodDurationOnly): TimeDuration
    {
        $payRunPeriodDurationOnly->loadMissing(['scheduledPayRunPeriod.scheduledPayRun.userRoleSchedule.durationOnly']);

        if ($payRunPeriodDurationOnly->doesHaveFixedHours) {
            return $payRunPeriodDurationOnly->duration->copy();
        }

        return $payRunPeriodDurationOnly->minDuration->copy();
    }
}
