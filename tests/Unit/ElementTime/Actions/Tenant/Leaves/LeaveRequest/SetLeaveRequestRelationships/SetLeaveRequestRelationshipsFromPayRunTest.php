<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships\SetLeaveRequestRelationshipsFromPayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Enums\PayRunStatus\PayRunStatus;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

/**
 * TODO
 *  [ ] - Add general case covering payrun with multiple leave, maybe with different type of leave and each leave with different expectations
 */
describe('Duration only - set relationships from Payrun', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh();

        $payRun = TenantSystemSettings::getPayRunByDate($leaveRequest->startDate);
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Work pattern - set relationships from Payrun', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $payRun = TenantSystemSettings::getPayRunByDate($leaveRequest->startDate);
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('Zero based - set relationships from Payrun', function () {
    it('does set leave timeSheet_id and scheduledPayRun_id', function () {
        $leaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true);
        [$leaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($leaveSettings);
        $leaveRequest = $leaveRequest->refresh()->syncChanges();

        $payRun = TenantSystemSettings::getPayRunByDate($leaveRequest->startDate);
        $timeSheet = TimeSheet::getByDate($leaveSettings->user, $leaveRequest->startDate);
        $expectedScheduledPayRun = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();

        expect($leaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($leaveRequest->timeSheet_id)->toBeNull();

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);

        $leaveRequest->refresh();

        expect($leaveRequest->timeSheet_id)->toBeInt()
            ->and($leaveRequest->timeSheet_id)->toEqual($timeSheet->id)
            ->and($leaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($leaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRun->id)
        ;
    });
});

describe('General - set relationships from Payrun', function () {
    it('does set to at least one leave request if payrun is not closed', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);

        SetLeaveRequestRelationshipsFromPayRun::partialMock()
            ->expects('setLeaveRequestRelationships')
            ->atLeast()
            ->once();

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);
    });

    it('does not set to any leave request if payrun is closed', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);

        $payRun->status = PayRunStatus::Finished;
        $payRun->clearSaveOrFail();

        SetLeaveRequestRelationshipsFromPayRun::partialMock()
            ->allows('setLeaveRequestRelationships')
            ->never();

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);
    });

    it('does set to multiple leave request across different staff with different schedule types', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $durationOnlyLeaveSettings = $this->l_lg_getDurationOnlyLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $zeroBasedLeaveSettings = $this->l_lg_getZeroBasedLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $expectedScheduledPayRunForDurationOnly = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $durationOnlyLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedTimeSheetForDurationOnly = TimeSheet::getByDate($durationOnlyLeaveSettings->user, $payRun->startDate);

        $expectedScheduledPayRunForWorkPattern = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedTimeSheetForWorkPattern = TimeSheet::getByDate($workPatternLeaveSettings->user, $payRun->startDate);

        $expectedScheduledPayRunForZeroBased = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $zeroBasedLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedTimeSheetForZeroBased = TimeSheet::getByDate($zeroBasedLeaveSettings->user, $payRun->startDate);

        [$durationOnlyLeaveRequest] = $this->l_lg_placeDurationOnlyLeaveRequest($durationOnlyLeaveSettings);
        [$workPatternLeaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);
        [$zeroBasedLeaveRequest] = $this->l_lg_placeZeroBasedLeaveRequest($zeroBasedLeaveSettings);

        expect($durationOnlyLeaveRequest->id)->toBeInt()
            ->and($durationOnlyLeaveRequest->timeSheet_id)->toBeNull()
            ->and($durationOnlyLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($workPatternLeaveRequest->timeSheet_id)->toBeNull()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toBeNull()
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);

        $durationOnlyLeaveRequest->refresh();
        $workPatternLeaveRequest->refresh();
        $zeroBasedLeaveRequest->refresh();

        expect($durationOnlyLeaveRequest->scheduledPayRun_id)->toBeInt()
            ->and($durationOnlyLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForDurationOnly->id)
            ->and($durationOnlyLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForDurationOnly->id)
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForWorkPattern->id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForWorkPattern->id)
            ->and($zeroBasedLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForZeroBased->id)
            ->and($zeroBasedLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForZeroBased->id)
        ;
    });

    it('does not affect leave on next payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $workPatternLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun);
        $expectedScheduledPayRunForWorkPattern = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.userRoleSchedule_id', '=', $workPatternLeaveSettings->userRoleSchedule->id],
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
            ])
            ->first();
        $expectedTimeSheetForWorkPattern = TimeSheet::getByDate($workPatternLeaveSettings->user, $payRun->startDate);
        $nextPayRunLeaveSettings = $this->l_lg_getWorkPatternLeaveSettings(onFuturePayrun: false, doesApprove: true, payRun: $payRun->next);

        [$workPatternLeaveRequest] = $this->l_lg_placeWorkPatternLeaveRequest($workPatternLeaveSettings);
        [$nextPayRunLeave] = $this->l_lg_placeWorkPatternLeaveRequest($nextPayRunLeaveSettings);

        expect($workPatternLeaveRequest->timeSheet_id)->toBeNull()
            ->and($workPatternLeaveRequest->scheduledPayRun_id)->toBeNull()
            ->and($nextPayRunLeave->timeSheet_id)->toBeNull()
            ->and($nextPayRunLeave->scheduledPayRun_id)->toBeNull()
        ;

        SetLeaveRequestRelationshipsFromPayRun::make()->handle($payRun);

        $workPatternLeaveRequest->refresh();

        expect($workPatternLeaveRequest->scheduledPayRun_id)->toEqual($expectedScheduledPayRunForWorkPattern->id)
            ->and($workPatternLeaveRequest->timeSheet_id)->toEqual($expectedTimeSheetForWorkPattern->id)
            ->and($nextPayRunLeave->timeSheet_id)->toBeNull()
            ->and($nextPayRunLeave->scheduledPayRun_id)->toBeNull()
        ;
    });
});
