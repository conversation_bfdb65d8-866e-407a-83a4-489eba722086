<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Plant;

use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\CustomContent\CustomContentResource;
use Element\ElementTime\Domains\Tenant\Plant\Models\UserRolePlantClass;
use Element\ElementTime\Domains\Tenant\Plant\Structures\PlantItemAssignment;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'PlantAssignmentConfigResource',
    title: 'Plant item config resource',
    required: ['rel_id', 'rel_type', 'plantItem_id', 'name', 'relationshipType', 'durationConfig', 'secondaryDurationConfig', 'mileageConfig', 'cartageConfig', 'costRateConfig', 'custom_content'],
    properties: [
        new OA\Property(property: 'rel_id', type: 'integer', example: 4),
        new OA\Property(property: 'rel_type', type: 'string', example: UserRolePlantClass::class),
        new OA\Property(property: 'plantItem_id', type: 'integer', example: 6),
        new OA\Property(property: 'name', type: 'string', example: 'Truck'),
        new OA\Property(property: 'relationshipType', properties: [
            new OA\Property(property: 'id', type: 'string', example: 'independent'),
            new OA\Property(property: 'name', type: 'string', example: 'Independent'),
        ]),
        new OA\Property(property: 'durationConfig', required: ['isOn'], properties: [
            new OA\Property(property: 'isOn', type: 'bool', example: true),
            new OA\Property(property: 'isRequired', description: 'only if isOn is true', type: 'bool', example: true, nullable: true),
            new OA\Property(property: 'label', description: 'only if isOn is true', type: 'string', example: 'Hours', nullable: true),
            new OA\Property(property: 'recordingType', description: 'only if isOn is true', required: ['id', 'title'], properties: [
                new OA\Property(property: 'id', type: 'string', example: 'duration'),
                new OA\Property(property: 'title', type: 'string', example: 'Duration'),
            ], nullable: true),
        ]),
        new OA\Property(property: 'secondaryDurationConfig', required: ['isOn'], properties: [
            new OA\Property(property: 'isOn', type: 'bool', example: true),
            new OA\Property(property: 'isRequired', description: 'only if isOn is true', type: 'bool', example: true, nullable: true),
            new OA\Property(property: 'label', description: 'only if isOn is true', type: 'string', example: 'Usage hours', nullable: true),
            new OA\Property(property: 'recordingType', description: 'only if isOn is true', required: ['id', 'title'], properties: [
                new OA\Property(property: 'id', type: 'string', example: 'hour-meter'),
                new OA\Property(property: 'title', type: 'string', example: 'Hour meter'),
            ], nullable: true),
        ]),
        new OA\Property(property: 'mileageConfig', required: ['isOn'], properties: [
            new OA\Property(property: 'isOn', type: 'bool', example: true),
            new OA\Property(property: 'isRequired', description: 'only if isOn is true', type: 'bool', example: true, nullable: true),
            new OA\Property(property: 'label', description: 'only if isOn is true', type: 'string', example: 'Mileage', nullable: true),
            new OA\Property(property: 'recordingType', description: 'only if isOn is true', required: ['id', 'title'], properties: [
                new OA\Property(property: 'id', type: 'string', example: 'distance'),
                new OA\Property(property: 'title', type: 'string', example: 'Distance'),
            ], nullable: true),
            new OA\Property(property: 'unit', description: 'only if isOn is true', required: ['id', 'title', 'abbreviation'], properties: [
                new OA\Property(property: 'id', type: 'integer', example: 'kilometres'),
                new OA\Property(property: 'title', type: 'string', example: 'Kilometres (Km)'),
                new OA\Property(property: 'abbreviation', type: 'string', example: 'km'),
            ], nullable: true),
        ]),
        new OA\Property(property: 'cartageConfig', required: ['isOn'], properties: [
            new OA\Property(property: 'isOn', type: 'bool', example: true),
            new OA\Property(property: 'isRequired', description: 'only if isOn is true', type: 'bool', example: true, nullable: true),
            new OA\Property(property: 'label', description: 'only if isOn is true', type: 'string', example: 'Cartage', nullable: true),
            new OA\Property(property: 'unit', description: 'only if isOn is true', required: ['id', 'title', 'abbreviation'], properties: [
                new OA\Property(property: 'id', type: 'string', example: 'kilograms'),
                new OA\Property(property: 'title', type: 'string', example: 'Kilograms (Kg)'),
                new OA\Property(property: 'abbreviation', type: 'string', example: 'kg'),
            ], nullable: true),
        ]),
        new OA\Property(property: 'costRateConfig', required: ['isOn'], properties: [
            new OA\Property(property: 'isOn', type: 'bool', example: true),
            new OA\Property(property: 'isRequired', description: 'only if isOn is true', type: 'bool', example: true, nullable: true),
            new OA\Property(property: 'label', description: 'only if isOn is true', type: 'string', example: 'Cost rate', nullable: true),
            new OA\Property(property: 'unit', description: 'only if isOn is true', required: ['id', 'title', 'abbreviation'], properties: [
                new OA\Property(property: 'id', type: 'string', example: 'kilograms'),
                new OA\Property(property: 'title', type: 'string', example: 'Dollars ($)'),
                new OA\Property(property: 'abbreviation', type: 'string', example: 'kg'),
            ], nullable: true),
        ]),
        new OA\Property(property: 'custom_content', ref: '#/components/schemas/CustomContentResource'),
    ],
)]
class PlantAssignmentConfigResource extends Resource
{
    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var PlantItemAssignment $r */
        $r = $this->getModel();

        $plantItem = $r->plantItem;

        $ret = [
            'rel_id' => $r->assignment->id,
            'rel_type' => get_class($r->assignment),
            'plantItem_id' => $plantItem->id,
            'name' => $plantItem->title,
            'relationshipType' => $plantItem->relationshipTypeClass::getMyConfig(),

            'durationConfig' => [
                'isOn' => $plantItem->doesHaveHours,
            ],

            'secondaryDurationConfig' => [
                'isOn' => $plantItem->doesHaveSecondaryHours,
            ],

            'mileageConfig' => [
                'isOn' => $plantItem->doesHaveMileage,
            ],

            'cartageConfig' => [
                'isOn' => $plantItem->doesHaveCartage,
            ],

            'costRateConfig' => [
                'isOn' => $plantItem->doesHaveCostRate,
            ],

            'custom_content' => CustomContentResource::make($plantItem->getCustomContentBySlug('recording-as-owner')),
        ];

        if ($plantItem->doesHaveHours) {
            $ret['durationConfig']['isRequired'] = $plantItem->isHoursRequired;
            $ret['durationConfig']['label'] = $plantItem->hoursLabel;
            $ret['durationConfig']['recordingType'] = [
                'id' => $plantItem->hoursRecordingTypeClass::ID,
                'title' => $plantItem->hoursRecordingTypeClass::NAME,
            ];
        }

        if ($plantItem->doesHaveSecondaryHours) {
            $ret['secondaryDurationConfig']['isRequired'] = $plantItem->isSecondaryHoursRequired;
            $ret['secondaryDurationConfig']['label'] = $plantItem->secondaryHoursLabel;
            $ret['secondaryDurationConfig']['recordingType'] = [
                'id' => $plantItem->secondaryHoursRecordingTypeClass::ID,
                'title' => $plantItem->secondaryHoursRecordingTypeClass::NAME,
            ];
        }

        if ($plantItem->doesHaveMileage) {
            $ret['mileageConfig']['isRequired'] = $plantItem->isMileageRequired;
            $ret['mileageConfig']['label'] = $plantItem->mileageLabel;
            $ret['mileageConfig']['recordingType'] = [
                'id' => $plantItem->mileageRecordingTypeClass::ID,
                'title' => $plantItem->mileageRecordingTypeClass::NAME,
            ];
            $ret['mileageConfig']['unit'] = [
                'id' => $plantItem->mileageUnitClass::ID,
                'title' => $plantItem->mileageUnitClass::NAME,
                'abbreviation' => $plantItem->mileageUnitClass::ABBREVIATION,
            ];
        }

        if ($plantItem->doesHaveCartage) {
            $ret['cartageConfig']['isRequired'] = $plantItem->isCartageRequired;
            $ret['cartageConfig']['label'] = $plantItem->cartageLabel;
            $ret['cartageConfig']['unit'] = [
                'id' => $plantItem->cartageUnitClass::ID,
                'title' => $plantItem->cartageUnitClass::NAME,
                'abbreviation' => $plantItem->cartageUnitClass::ABBREVIATION,
            ];
        }

        if ($plantItem->doesHaveCostRate) {
            $ret['costRateConfig']['isRequired'] = $plantItem->isCostRateRequired;
            $ret['costRateConfig']['label'] = $plantItem->costRateLabel;
            $ret['costRateConfig']['unit'] = $plantItem->costRateUnit->toArray();
        }

        return $ret;
    }
}
