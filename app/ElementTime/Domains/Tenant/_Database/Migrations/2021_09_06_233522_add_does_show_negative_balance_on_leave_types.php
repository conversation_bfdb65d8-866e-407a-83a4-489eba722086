<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('doesShowNegativeBalance')->default(true)->after('hasNegativeBalancesAllowed');
        });

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->boolean('doesShowNegativeBalance')->default(true)->after('defaultComment');
        });

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesShowNegativeExcessTimeBalance')->default(true)->after('maximumNegativeAllowedExcessTimeBalance');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('LeaveType', 'doesShowNegativeBalance')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doesShowNegativeBalance');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'doesShowNegativeBalance')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doesShowNegativeBalance');
            });
        }

        if (Schema::hasColumn('Settings', 'doesShowNegativeExcessTimeBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesShowNegativeExcessTimeBalance');
            });
        }
    }
};
