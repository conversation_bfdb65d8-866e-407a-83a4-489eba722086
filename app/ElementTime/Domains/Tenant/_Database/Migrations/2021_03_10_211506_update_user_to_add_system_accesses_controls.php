<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->boolean('hasPayrollOfficerAccess')->default(false)->after('id');
            $table->boolean('hasAuditAccess')->default(false)->after('hasPayrollOfficerAccess');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'hasPayrollOfficerAccess')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasPayrollOfficerAccess');
            });
        }
        if (Schema::hasColumn('User', 'hasAuditAccess')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasAuditAccess');
            });
        }
    }
};
