<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('UserTimelineEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->string('type');

            $table->dateTime('dateTime');
            $table->string('title')->nullable();
            $table->longText('description')->nullable();
            $table->longText('notes')->nullable();

            $table->string('timeline_rel_type');
            $table->unsignedBigInteger('timeline_rel_id');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id', 'ute_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'ute_auser')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            $table->unique(['user_id', 'type', 'timeline_rel_type', 'timeline_rel_id', 'dateTime'], 'utentry_item');
        });

        Schema::create('UserTimelineEntryComment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('userTimelineEntry_id');
            $table->unsignedBigInteger('actorUser_id')->nullable();

            $table->dateTime('dateTime');
            $table->string('title')->nullable();
            $table->longText('description')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userTimelineEntry_id', 'utec_ute')->references('id')->on('UserTimelineEntry')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'utec_auser')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserNote', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('actorUser_id')->nullable();

            $table->dateTime('dateTime');
            $table->string('title')->nullable();
            $table->longText('description')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id', 'un_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'un_auser')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserNoteComment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('userNote_id');
            $table->unsignedBigInteger('actorUser_id')->nullable();

            $table->dateTime('dateTime');
            $table->string('title')->nullable();
            $table->longText('description')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userNote_id', 'unc_un')->references('id')->on('UserNote')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'unc_auser')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('Notification', function (Blueprint $table) {
            $table->unsignedBigInteger('userNote_id')->nullable();
            $table->foreign('userNote_id', 'notification_un')->references('id')->on('UserNote')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM Notification WHERE Key_name=\'notification_un\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropForeign('notification_un');
            });
        }

        if (Schema::hasColumn('Notification', 'userNote_id')) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropColumn('userNote_id');
            });
        }

        Schema::dropIfExists('UserNoteComment');
        Schema::dropIfExists('UserNote');
        Schema::dropIfExists('UserTimelineEntryComment');
        Schema::dropIfExists('UserTimelineEntry');
    }
};
