<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        //region Old notification tables

        try {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropForeign('upr_rn_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropForeign('upr_sn_fn');
            });
        } catch (Throwable) {
        }

        Schema::dropIfExists('_OldNotification');
        Schema::dropIfExists('_OldNotificationType');
        Schema::dropIfExists('_OldReminderType');

        //endregion Old notification tables

        //region TimeSheet fields

        if (Schema::hasColumn('TimeSheet', 'hoursScheduled')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursScheduled');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursRecordedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRecordedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimePaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaid');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeUnpaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeAccrued')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccrued');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursPublicHoliday')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursPublicHoliday');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursMissing')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursMissing');
            });
        }

        //endregion TimeSheet fields

        //region TimeSheetDay fields

        if (Schema::hasColumn('TimeSheetDay', 'hoursWorkedCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursLeaveCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeAccrued')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccrued');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeUnpaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeUsedCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursRosteredTimeOffCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursPublicHoliday')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursPublicHoliday');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursAllowanceCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursMore')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursMore');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursLess')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursTotalCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExpected');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursWorked')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursWorked');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursLeave')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursLeave');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeAccrued')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeAccrued');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimePaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeUnpaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeUsed')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeUsed');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursRosteredTimeOff')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursRosteredTimeOff');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursMore')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursMore');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursLess')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursTotal')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursTotal');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExpected');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glideDayAverageHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayAverageHoursExpected');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glideDayMinHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMinHoursExpected');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glideDayMaxHoursExpected')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMaxHoursExpected');
            });
        }

        //endregion TimeSheetDay fields

        //region Other Timesheet-related fields

        if (Schema::hasColumn('TimeSheetAllowanceDay', 'hours')) {
            Schema::table('TimeSheetAllowanceDay', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }

        if (Schema::hasColumn('TimeSheetAllowanceDayEntry', 'hours')) {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTime', 'totalHours')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('totalHours');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTimeBlock', 'hours')) {
            Schema::table('TimeSheetExcessTimeBlock', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'totalHoursPaid')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('totalHoursPaid');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'totalHoursAccrued')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('totalHoursAccrued');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'totalHoursUnpaid')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('totalHoursUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheetExcessTimeItemRule', 'hours')) {
            Schema::table('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }

        //endregion Other Timesheet-related fields

        //region LeaveRequest-related fields

        if (Schema::hasColumn('LeaveRequest', 'actualTotalHours')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('actualTotalHours');
            });
        }

        if (Schema::hasColumn('LeaveRequest', 'glideDailyHours')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('glideDailyHours');
            });
        }

        if (Schema::hasColumn('LeaveRequestDay', 'actualHours')) {
            Schema::table('LeaveRequestDay', function (Blueprint $table) {
                $table->dropColumn('actualHours');
            });
        }

        if (Schema::hasColumn('LeaveRequestDayBreak', 'hours')) {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }

        try {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropForeign('lrdb_usdb_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('LeaveRequestDayBreak', 'userShiftDayBreak_id')) {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropColumn('userShiftDayBreak_id');
            });
        }

        //endregion LeaveRequest-related fields

        //region UserShift-related tables and fields

        Schema::dropIfExists('UserShiftDayBreak');

        if (Schema::hasColumn('UserShiftDay', 'startDateTime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'startDateTime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'endDateTime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('endDateTime');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'glideDayIsOn')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDayIsOn');
            });
        }

        if (Schema::hasColumn('Shift', 'fixedTime')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('fixedTime');
            });
        }

        if (Schema::hasColumn('Shift', 'fixedBreaks')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('fixedBreaks');
            });
        }

        if (Schema::hasColumn('Shift', 'variableTime')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('variableTime');
            });
        }

        if (Schema::hasColumn('Shift', 'flexibleTime')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('flexibleTime');
            });
        }

        if (Schema::hasColumn('Shift', 'flexibleMinimumHours')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('flexibleMinimumHours');
            });
        }

        if (Schema::hasColumn('Shift', 'flexibleBreaks')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('flexibleBreaks');
            });
        }

        if (Schema::hasColumn('Shift', 'flexibleAllowChangeHours')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('flexibleAllowChangeHours');
            });
        }

        if (Schema::hasColumn('Shift', 'glideTime')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideTime');
            });
        }

        if (Schema::hasColumn('Shift', 'glideDays')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideDays');
            });
        }

        if (Schema::hasColumn('Shift', 'hasDefaultDays')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('hasDefaultDays');
            });
        }

        if (Schema::hasColumn('Shift', 'defaultDaysTo')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('defaultDaysTo');
            });
        }

        if (Schema::hasColumn('Shift', 'allowsRosteredTimeOff')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('allowsRosteredTimeOff');
            });
        }

        try {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropForeign('shift_rdo_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('Shift', 'rosteredTimeOffType_id')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffType_id');
            });
        }

        if (Schema::hasColumn('Shift', 'rosteredTimeOffType')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffType');
            });
        }

        //endregion UserShift-related tables and fields

        //region
    }

    public function down(): void
    {
        //
    }
};
