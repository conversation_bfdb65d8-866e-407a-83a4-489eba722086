<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Carbon\Carbon;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\GetUserRoleScheduleOptionsForLeaveRequest;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\TimeType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeGroup;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\FullTimeType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRoleRepository;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\OrdinaryUserFlag;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Domains\Type\StatusTypes\InactiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

describe('Get user role schedules from user', function () {
    it('does not return option with inactive role association', function () {
        $userRoleSchedule = UserRoleSchedule::q()->current()->first();
        $user = $userRoleSchedule->userRole->user;
        $actor = SuperUserFlag::getOne();

        $schedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        $userRoleSchedule->userRole->role->status = InactiveStatus::ID;
        $userRoleSchedule->userRole->role->clearSave();

        $newSchedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        expect($newSchedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->toBeNull()
            ->and($schedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->not()->toBeNull();
    });

    it('does not return option with inactive user role association', function () {
        $userRoleSchedule = UserRoleSchedule::q()->current()->first();
        $user = $userRoleSchedule->userRole->user;
        $actor = SuperUserFlag::getOne();

        $schedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        $userRoleSchedule->userRole->startDate = Carbon::today()->subYear();
        $userRoleSchedule->userRole->endDate = $userRoleSchedule->userRole->startDate->addDay();
        $userRoleSchedule->userRole->clearSave();

        $newSchedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        expect($newSchedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->toBeNull()
            ->and($schedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->not()->toBeNull();
    });

    it('does not return option with inactive user role schedule association', function () {
        $userRoleSchedule = UserRoleSchedule::q()->current()->first();
        $user = $userRoleSchedule->userRole->user;
        $actor = SuperUserFlag::getOne();

        $schedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        $userRoleSchedule->startDate = Carbon::today()->subYear();
        $userRoleSchedule->endDate = $userRoleSchedule->startDate->addDay();
        $userRoleSchedule->clearSave();

        $newSchedulesOptions = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $actor);

        expect($newSchedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->toBeNull()
            ->and($schedulesOptions->firstWhere('id', '=', $userRoleSchedule->id))->not()->toBeNull();
    });

    it('returns only schedules that actor user can access', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $userRoleSchedule = UserRoleSchedule::q()
            ->current()
            ->order('RAND()')
            ->first();
        $userRole = $userRoleSchedule->userRole;
        $user = $userRole->user;
        $staff2 = User::q()->constraints([
            ['User.id', '<>', $user->id],
            OrdinaryUserFlag::getConstraints(),
        ])->order('RAND()')->first();
        $role = Role::q()->constraints([['Role.id', '<>', $userRole->role->id]])->onlyActive()->order('RAND()')->first();

        $newUserRole = UserRoleRepository::assignRoleToUser(
            $role,
            $user,
            $userRole->startDate,
            false,
            (object) [
                'doesOverrideExternalId' => false,
                'externalId' => 'test_lorem_ipsum',
                'contractedHours' => 80,
                'employeeTypeStatusType' => FullTimeType::ID,
                'costing' => [
                    'doesAllowNonStandardPlant' => false,
                    'doesAllowUserToUpdateWorkDetails' => false,
                ],
                'departmentData' => [[
                    'department_id' => Department::q()->onlyActive()->many()->random()->id,
                    'isManager' => false,
                ]],
                'masterProjectData' => [[
                    'project_id' => Project::q()->onlyActive()->many()->random()->id,
                    'isMaster' => false,
                ]],
                'payTypesData' => [[
                    'payType_id' => PayType::q()->onlyActive()->many()->random()->id,
                    'overtimeCustomAmount' => null,
                    'overtimeCustomRateType' => 'Y',
                ]],
                'roleManagerData' => [[
                    'manager_id' => $staff2->id,
                ]],
                'timeTypesData' => [
                    [
                        'timeType_id' => TimeType::q()->onlyActive()->many()->random()->id,
                        'excessTimeGroup_id' => ExcessTimeGroup::q()->onlyActive()->many()->random()->id,
                        'isActive' => true,
                        'isMaster' => true,
                    ]
                ],
            ],
        );

        UserRoleSchedule::addToUserRoleByData(
            $newUserRole,
            $payRun->startDate,
            [
                'type' => UserRoleScheduleType::ZeroBased->id(),
                'zeroBased' => [
                    'dailyMaxDuration' => null,
                    'doesHaveDailyMaxDuration' => false,
                    'doesHaveFixedLocation' => false,
                    'doesHaveMaxDuration' => false,
                    'maxDuration' => null,
                    'scheduleLocation_id' => null,
                ],
            ],
            true,
        );
        $user->load(['userRoles']);

        $optionsForRoleManager = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, $staff2);
        $optionsForSuperUser = GetUserRoleScheduleOptionsForLeaveRequest::make()->handle($user, SuperUserFlag::getOne());

        expect($optionsForSuperUser->count())->toBeGreaterThan($optionsForRoleManager->count());
    });
});
