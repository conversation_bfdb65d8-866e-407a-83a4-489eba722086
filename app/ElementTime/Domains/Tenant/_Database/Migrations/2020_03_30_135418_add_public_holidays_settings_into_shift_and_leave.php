<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserShift
        Schema::table('UserShift', function (Blueprint $table) {
            $table->boolean('doesWorkOnPublicHolidays')->default(false)->after('endDate');
        });
        //endregion Table: UserShift

        //region Table: TimeSheetDay
        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('hoursPublicHoliday', 16, 10)->nullable()->default(null)->after('hoursRosteredTimeOffCalculation');
        });
        //endregion Table: TimeSheetDay

        //region Table: TimeSheet
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('hoursPublicHoliday', 16, 10)->nullable()->default(null)->after('hoursRosteredTimeOffUsedCalculation');
        });
        //endregion Table: TimeSheet

        //region Table: LeaveType
        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('doesIgnorePublicHolidays')->default(true)->after('doDeductBreaks');
        });
        //endregion Table: LeaveType

        //region Table: UserLeaveBankType
        Schema::table('UserLeaveBankType', function (Blueprint $table) {
            $table->boolean('doesIgnorePublicHolidays')->nullable()->default(true)->after('canScheduleLeave');
        });
        //endregion Table: UserLeaveBankType

        //region Table: LeaveRequest
        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->boolean('doesIgnorePublicHolidays')->default(true)->after('originalSplit_id');
        });
        //endregion Table: LeaveRequest

        //region Table: TimeSheetItemDayTime
        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->decimal('amountDeductedFromPublicHolidays', 16, 10)->default(0.0)->after('leaveRequestDay_id');
        });
        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheetItemDay
        Schema::table('TimeSheetItemDay', function (Blueprint $table) {
            $table->decimal('amountDeductedFromPublicHolidays', 16, 10)->default(0.0)->after('hoursCalculation');
        });
        //endregion Table: TimeSheetItemDay
    }

    public function down(): void
    {
        //region Table: TimeSheetItemDay
        if (Schema::hasColumn('TimeSheetItemDay', 'amountDeductedFromPublicHolidays')) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropColumn('amountDeductedFromPublicHolidays');
            });
        }
        //endregion Table: TimeSheetItemDay

        //region Table: TimeSheetItemDayTime
        if (Schema::hasColumn('TimeSheetItemDayTime', 'amountDeductedFromPublicHolidays')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('amountDeductedFromPublicHolidays');
            });
        }
        //endregion Table: TimeSheetItemDayTime

        //region Table: LeaveRequest
        if (Schema::hasColumn('LeaveRequest', 'doesIgnorePublicHolidays')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('doesIgnorePublicHolidays');
            });
        }
        //endregion Table: LeaveRequest

        //region Table: UserLeaveBankType
        if (Schema::hasColumn('UserLeaveBankType', 'doesIgnorePublicHolidays')) {
            Schema::table('UserLeaveBankType', function (Blueprint $table) {
                $table->dropColumn('doesIgnorePublicHolidays');
            });
        }
        //endregion Table: UserLeaveBankType

        //region Table: LeaveType
        if (Schema::hasColumn('LeaveType', 'doesIgnorePublicHolidays')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doesIgnorePublicHolidays');
            });
        }
        //endregion Table: LeaveType

        //region Table: TimeSheet
        if (Schema::hasColumn('TimeSheet', 'hoursPublicHoliday')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursPublicHoliday');
            });
        }
        //endregion Table: TimeSheet

        //region Table: TimeSheetDay
        if (Schema::hasColumn('TimeSheetDay', 'hoursPublicHoliday')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursPublicHoliday');
            });
        }
        //endregion Table: TimeSheetDay

        //region Table: UserShift
        if (Schema::hasColumn('UserShift', 'doesWorkOnPublicHolidays')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('doesWorkOnPublicHolidays');
            });
        }
        //endregion Table: UserShift
    }
};
