<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Shift
        // Move d_e_p_r_e_c_a_t_e_d columns
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN fixedTime TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN fixedBreaks TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN variableTime TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN flexibleTime TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN flexibleMinimumHours DOUBLE(8,2) DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN flexibleBreaks TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN flexibleAllowChangeHours TINYINT(1) NOT NULL DEFAULT \'0\' AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideTime LONGTEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideDays LONGTEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN hasDefaultDays TINYINT(1) NOT NULL DEFAULT \'0\' AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN defaultDaysTo TEXT DEFAULT NULL AFTER deleted_at;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN worksOnPublicHolidays TINYINT(1) NOT NULL DEFAULT \'0\' AFTER deleted_at;');

        // Move active fields to relevant position
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideDurationType CHAR(1) NOT NULL DEFAULT \'D\' AFTER type;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideDuration DECIMAL(16,10) NOT NULL DEFAULT \'7.6\' AFTER glideDurationType;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideRequireTimeBlock TINYINT(1) NOT NULL DEFAULT \'0\' AFTER glideDuration;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glidePublicHolidaysRateType CHAR(1) NOT NULL DEFAULT \'A\' AFTER glideRequireTimeBlock;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glidePublicHolidaysCustomRate DECIMAL(16,10) DEFAULT NULL AFTER glidePublicHolidaysRateType;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glidePublicHolidaysRateTypeWeekend CHAR(1) NOT NULL DEFAULT \'A\' AFTER glidePublicHolidaysCustomRate;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glidePublicHolidaysCustomRateWeekend DECIMAL(16,10) DEFAULT NULL AFTER glidePublicHolidaysRateTypeWeekend;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideLeaveRateType CHAR(1) NOT NULL DEFAULT \'A\' AFTER glidePublicHolidaysCustomRateWeekend;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideLeaveCustomRate DECIMAL(16,10) DEFAULT NULL AFTER glideLeaveRateType;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideLeaveRateTypeWeekend CHAR(1) NOT NULL DEFAULT \'A\' AFTER glideLeaveCustomRate;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN glideLeaveCustomRateWeekend DECIMAL(16,10) DEFAULT NULL AFTER glideLeaveRateTypeWeekend;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN doesDeductBreak TINYINT(1) NOT NULL DEFAULT \'0\' AFTER rosteredTimeOffType_id;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Shift MODIFY COLUMN minimumHoursToDeductBreak DECIMAL(16,10) DEFAULT NULL AFTER doesDeductBreak;');

        Schema::table('Shift', function (Blueprint $table) {
            $table->boolean('glideDoesAllowStaffToChange')->default(true)->after('glideRequireTimeBlock');
            $table->json('defaultTimeSpan')->nullable()->after('glideDuration');
        });
        //endregion Table: Shift

        //region Table: ShiftDay
        Schema::create('ShiftDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('shift_id');
            $table->foreign('shift_id', 'ssd_shift_fn')->references('id')->on('Shift')->onUpdate('cascade')->onDelete('cascade');
            $table->integer('number')->default(1);

            // General data
            $table->boolean('isOn')->default(true);
            $table->boolean('hasOverwrittenSchedule')->default(false);
            $table->string('label')->nullable();
            $table->integer('minutes')->nullable();

            $table->boolean('hasExpectedSchedule')->default(false);
            $table->integer('expectedMinutes')->nullable();
            $table->integer('minMinutes')->nullable();
            $table->integer('maxMinutes')->nullable();

            $table->timestamps();

            $table->unique(['shift_id', 'number']);
        });
        //endregion Table: ShiftDay

        //region Table: ShiftDayTime
        Schema::create('ShiftDayTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('shiftDay_id');
            $table->foreign('shiftDay_id', 'ssdt_ssd_fn')->references('id')->on('ShiftDay')->onUpdate('cascade')->onDelete('cascade');
            $table->char('type', 1)->default('A'); // A - Actual | E - Expected

            // General data
            $table->string('label')->nullable();
            $table->string('startTime')->nullable();
            $table->string('endTime')->nullable();
            $table->integer('minutes')->nullable();

            $table->timestamps();
        });
        //endregion Table: ShiftDayTime

        //region Table: ShiftDayTimeBreak
        Schema::create('ShiftDayTimeBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('shiftDayTime_id');
            $table->foreign('shiftDayTime_id', 'ssdtb_ssdt_fn')->references('id')->on('ShiftDayTime')->onUpdate('cascade')->onDelete('cascade');

            // General data
            $table->string('label')->nullable();
            $table->string('startTime')->nullable();
            $table->string('endTime')->nullable();
            $table->integer('minutes')->nullable();

            $table->timestamps();
        });
        //endregion Table: ShiftDayTimeBreak

        //region Table: UserShiftDayTime
        Schema::create('UserShiftDayTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('userShiftDay_id');
            $table->foreign('userShiftDay_id', 'usdt_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('shiftDayTime_id')->nullable();
            $table->foreign('shiftDayTime_id', 'usdt_sdt_fn')->references('id')->on('ShiftDayTime')->onUpdate('cascade')->onDelete('set null');
            $table->char('type', 1)->default('A'); // A - Actual | E - Expected

            // General data
            $table->string('label')->nullable();
            $table->integer('minutes')->nullable();

            $table->timestamps();
        });
        //endregion Table: UserShiftDayTime

        //region Table: UserShiftDayTimeBreak
        Schema::create('UserShiftDayTimeBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('userShiftDayTime_id');
            $table->foreign('userShiftDayTime_id', 'usdtb_usdt_fn')->references('id')->on('UserShiftDayTime')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('shiftDayTimeBreak_id')->nullable();
            $table->foreign('shiftDayTimeBreak_id', 'usdtb_sdtb_fn')->references('id')->on('ShiftDayTimeBreak')->onUpdate('cascade')->onDelete('set null');

            // General data
            $table->string('label')->nullable();
            $table->integer('minutes')->nullable();

            $table->timestamps();
        });
        //endregion Table: UserShiftDayTimeBreak

        //region Table: UserShiftDay
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN userShift_id BIGINT(20) UNSIGNED NOT NULL AFTER id;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN date DATE NOT NULL AFTER calendarDate_id;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN publicHoliday_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER date;');
        if (Schema::hasColumn('UserShiftDay', 'rosteredTimeOffType_id') && Schema::hasColumn('UserShiftDay', 'leave_id')) {
            \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN leave_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER isOnLeave;');
            \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN rosteredTimeOffType_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER leave_id;');
        }
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE UserShiftDay MODIFY COLUMN isEditable TINYINT(1) NOT NULL DEFAULT \'1\' AFTER id;');

        Schema::table('UserShiftDay', function (Blueprint $table) {
            // Move positions
            $table->unsignedBigInteger('shiftDay_id')->nullable()->after('userShift_id');
            $table->foreign('shiftDay_id', 'usd_sd_fn')->references('id')->on('ShiftDay')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion Table: UserShiftDay
    }

    public function down(): void
    {
        //region Table: UserShiftDay
        if (DB::select(DB::raw('SHOW KEYS FROM UserShiftDay WHERE Key_name=\'usd_sd_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropForeign('usd_sd_fn');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'shiftDay_id')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('shiftDay_id');
            });
        }
        //endregion Table: UserShiftDay

        //region Table: UserShiftDayTimeBreak
        Schema::dropIfExists('UserShiftDayTimeBreak');
        //endregion Table: UserShiftDayTimeBreak

        //region Table: UserShiftDayTime
        Schema::dropIfExists('UserShiftDayTime');
        //endregion Table: UserShiftDayTime

        //region Table: ShiftDayTimeBreak
        Schema::dropIfExists('ShiftDayTimeBreak');
        //endregion Table: ShiftDayTimeBreak

        //region Table: ShiftDayTime
        Schema::dropIfExists('ShiftDayTime');
        //endregion Table: ShiftDayTime

        //region Table: ShiftDay
        Schema::dropIfExists('ShiftDay');
        //endregion Table: ShiftDay

        //region Table: Shift
        if (Schema::hasColumn('Shift', 'glideDoesAllowStaffToChange')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideDoesAllowStaffToChange');
            });
        }
        if (Schema::hasColumn('Shift', 'defaultTimeSpan')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('defaultTimeSpan');
            });
        }
        //endregion Table: Shift
    }
};
