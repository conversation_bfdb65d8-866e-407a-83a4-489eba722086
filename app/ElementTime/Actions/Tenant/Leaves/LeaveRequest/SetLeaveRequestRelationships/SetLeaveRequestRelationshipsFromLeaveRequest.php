<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

class SetLeaveRequestRelationshipsFromLeaveRequest extends BaseSetLeaveRequestRelationships
{
    /** @throws \Throwable */
    public function handle(LeaveRequest $leaveRequest): void
    {
        $latestPayRun = TenantSystemSettings::getLatestPayRun();

        if ($latestPayRun->endDate->lt($leaveRequest->startDate)) {
            return;
        }

        $leaveRequest->loadMissing(static::getLeaveRequestRelationships());

        $userRoleSchedule = $leaveRequest->userRoleSchedule;

        $scheduledPayRun = $leaveRequest->scheduledPayRun ?? $userRoleSchedule->getScheduledPayRun($leaveRequest->startDate);
        $scheduledPayRun->loadMissing(static::getScheduledPayRunRelationships());

        $timeSheet = $leaveRequest->timeSheet ?? $scheduledPayRun->timeSheet ?? TimeSheet::getByDate($leaveRequest->userLeaveBankType->bank->user, $leaveRequest->startDate);

        $this->setLeaveRequestRelationships($leaveRequest, $scheduledPayRun, $timeSheet);
    }

    /** @throws \Throwable */
    public function asJob(LeaveRequest $leaveRequest): void
    {
        $this->handle($leaveRequest);
    }
}
