<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        /*
         * Remove old Leave structure
        */
        Schema::dropIfExists('UserLeaveTypeEntry');
        Schema::table('UserLeaveType', function (Blueprint $table) {
            if (Schema::hasColumn('UserLeaveType', 'accruedBalance')) {
                $table->dropColumn([
                    'accruedBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'accruedDate')) {
                $table->dropColumn([
                    'accruedDate',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'availableBalance')) {
                $table->dropColumn([
                    'availableBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'committedBalance')) {
                $table->dropColumn([
                    'committedBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'historicBalance')) {
                $table->dropColumn([
                    'historicBalance',
                ]);
            }
        });
        Schema::table('MsConversation', function (Blueprint $table) {
            if (Schema::hasColumn('MsConversation', 'leave_id')) {
                $table->dropForeign(
                    'msc_leave_fn'
                );
                $table->dropColumn([
                    'leave_id',
                ]);
            }
        });
        Schema::table('UserShiftDay', function (Blueprint $table) {
            if (Schema::hasColumn('UserShiftDay', 'userLeaveType_id')) {
                $table->dropForeign(
                    'usershiftday_leave_fn'
                );
                $table->dropColumn([
                    'userLeaveType_id',
                ]);
            }

            if (Schema::hasColumn('UserShiftDay', 'leave_id')) {
                $table->dropForeign(
                    'usd_leave_fn'
                );
                $table->dropColumn([
                    'leave_id',
                ]);
            }
        });
        Schema::dropIfExists('LeaveApproval');
        Schema::dropIfExists('LeaveDay');
        Schema::dropIfExists('Leave');
        Schema::table('LeaveType', function (Blueprint $table) {
            if (Schema::hasColumn('LeaveType', 'howIsPaid')) {
                $table->dropColumn([
                    'howIsPaid',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'doesCalculateOvertime')) {
                $table->dropColumn([
                    'doesCalculateOvertime',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'minimumBalanceIfPayment')) {
                $table->dropColumn([
                    'minimumBalanceIfPayment',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'isRealisedOnCustomValue')) {
                $table->dropColumn([
                    'isRealisedOnCustomValue',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'isRealisedOnCustomType')) {
                $table->dropColumn([
                    'isRealisedOnCustomType',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'applyStandDownPeriod')) {
                $table->dropColumn([
                    'applyStandDownPeriod',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'standDownPeriodValue')) {
                $table->dropColumn([
                    'standDownPeriodValue',
                ]);
            }
            if (Schema::hasColumn('LeaveType', 'standDownPeriodType')) {
                $table->dropColumn([
                    'standDownPeriodType',
                ]);
            }
        });

        /*
         * Create new Leave structure
         */
        Schema::table('LeaveType', function (Blueprint $table) {
            $table->char('howIsPaid', 1)->nullable(); // (B)asic | (A)verage
            $table->boolean('doesCalculateOvertime')->nullable();
            $table->float('minimumBalanceIfPayment')->nullable();

            $table->integer('isRealisedOnCustomValue')->nullable();
            $table->char('isRealisedOnCustomType', 1)->nullable();

            $table->boolean('applyStandDownPeriod')->nullable();
            $table->integer('standDownPeriodValue')->nullable();
            $table->char('standDownPeriodType', 1)->nullable();
        });

        Schema::create('Leave', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userLeaveType_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->string('name')->nullable();
            $table->text('reason')->nullable();

            $table->longText('approvalWorkflowMap')->nullable(); // From Settings->workflowLeaveApproval (duplicated to keep the current map)

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userLeaveType_id', 'leave_ult_fn')->references('id')->on('UserLeaveType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'leave_us_fn')->references('id')->on('UserLeaveType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('LeaveDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigInteger('leave_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();
            $table->date('date');

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->char('status', 1)->nullable();

            $table->primary(['leave_id', 'date'], 'leaveday_unique');
            $table->foreign('user_id', 'leaveday_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leave_id', 'leaveday_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'leaveday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('LeaveApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('leave_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->nullable();
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1); // The number of levels and the user type of each manager depends on Settings.workflowLeaveApproval map
            $table->text('notes')->nullable();

            $table->char('status', 1)->nullable(); // (W)aiting approval | (A)pproved | (D)isapproved

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('leave_id', 'leaveapproval_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'leaveapproval_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('MsConversation', function (Blueprint $table) {
            if (!Schema::hasColumn('MsConversation', 'leave_id')) {
                $table->bigInteger('leave_id')->unsigned()->nullable();
                $table->foreign('leave_id', 'msc_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
            }
        });

        Schema::table('UserShiftDay', function (Blueprint $table) {
            if (!Schema::hasColumn('UserShiftDay', 'leave_id')) {
                $table->bigInteger('leave_id')->unsigned()->nullable();
                $table->foreign('leave_id', 'usd_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
            }
        });

        Schema::table('UserLeaveType', function (Blueprint $table) {
            $table->decimal('accruedBalance', 16, 10);
            $table->date('accruedDate')->nullable();
            $table->decimal('availableBalance', 16, 10);
            $table->decimal('committedBalance', 16, 10)->unsigned();
            $table->decimal('historicBalance', 16, 10)->unsigned();
        });

        Schema::create('UserLeaveTypeEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userLeaveType_id')->unsigned();

            $table->dateTime('entryDateTime');
            $table->char('entryType', 2);
            // Possible Values:
            // - (MA) Manually Add Accrued
            // - (AA) Automatically Add Accrued (relate to TimeSheet)
            // - (MR) Manually Remove Accrued
            // - (ME) Manually Transfer from Accrued to Available
            // - (AE) Automatically Transfer from Accrued to Available (Fill previous accruedDate and current accruedDate)
            // - (MD) Manually Transfer from Available to Accrued
            // - (CM) Commit (Transfer from Available to Committed) (relate to waiting to approve/approved Leave)
            // - (UC) Uncommit (Transfer from Available to Committed) (relate to declined or cancelled Leave)
            // - (US) Use (relate to LeaveDay)
            // - (MC) Manually Cancel (relate to User [payroll officer] and another UserLeaveTypeEntry)
            // - (AC) Automatically Cancel (relate to another UserLeaveTypeEntry)
            $table->decimal('entryValue', 16, 10);
            $table->text('description')->nullable();

            $table->bigInteger('previous_id')->unsigned()->nullable();
            $table->decimal('previousAccruedBalance', 16, 10);
            $table->date('previousAccruedDate')->nullable();
            $table->decimal('previousAvailableBalance', 16, 10);
            $table->decimal('previousCommittedBalance', 16, 10)->unsigned();
            $table->decimal('previousHistoricBalance', 16, 10)->unsigned();

            $table->decimal('currentAccruedBalance', 16, 10);
            $table->date('currentAccruedDate')->nullable();
            $table->decimal('currentAvailableBalance', 16, 10);
            $table->decimal('currentCommittedBalance', 16, 10)->unsigned();
            $table->decimal('currentHistoricBalance', 16, 10)->unsigned();

            $table->bigInteger('timeSheet_id')->unsigned()->nullable();
            $table->bigInteger('leave_id')->unsigned()->nullable();
            $table->date('leaveDayDate')->nullable();
            $table->bigInteger('userLeaveTypeEntry_id')->unsigned()->nullable();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->foreign('userLeaveType_id', 'ulte_ult_fn')->references('id')->on('UserLeaveType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('previous_id', 'ulte_prevulte_fn')->references('id')->on('UserLeaveTypeEntry')->onUpdate('cascade')->onDelete('restrict');

            $table->foreign('timeSheet_id', 'ulte_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leave_id', 'ulte_ul_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userLeaveTypeEntry_id', 'ulte_ulte_fn')->references('id')->on('UserLeaveTypeEntry')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'ulte_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('UserLeaveTypeEntry');
        Schema::table('UserLeaveType', function (Blueprint $table) {
            if (Schema::hasColumn('UserLeaveType', 'accruedBalance')) {
                $table->dropColumn([
                    'accruedBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'accruedDate')) {
                $table->dropColumn([
                    'accruedDate',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'availableBalance')) {
                $table->dropColumn([
                    'availableBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'committedBalance')) {
                $table->dropColumn([
                    'committedBalance',
                ]);
            }
            if (Schema::hasColumn('UserLeaveType', 'historicBalance')) {
                $table->dropColumn([
                    'historicBalance',
                ]);
            }
        });
        Schema::table('MsConversation', function (Blueprint $table) {
            if (Schema::hasColumn('MsConversation', 'leave_id')) {
                $table->dropForeign(
                    'msc_leave_fn'
                );
                $table->dropColumn([
                    'leave_id',
                ]);
            }
        });
        Schema::table('UserShiftDay', function (Blueprint $table) {
            if (Schema::hasColumn('UserShiftDay', 'userLeaveType_id')) {
                $table->dropForeign(
                    'usershiftday_leave_fn'
                );
                $table->dropColumn([
                    'userLeaveType_id',
                ]);
            }

            if (Schema::hasColumn('UserShiftDay', 'leave_id')) {
                $table->dropForeign(
                    'usd_leave_fn'
                );
                $table->dropColumn([
                    'leave_id',
                ]);
            }
        });
        Schema::drop('LeaveApproval');
        Schema::drop('Leave_UserShiftDay');
        Schema::drop('Leave');
        Schema::table('LeaveType', function (Blueprint $table) {
            $table->dropColumn([
                'howIsPaid',
                'doesCalculateOvertime',
                'minimumBalanceIfPayment',
                'isRealisedOnCustomValue',
                'isRealisedOnCustomType',
                'applyStandDownPeriod',
                'standDownPeriodValue',
                'standDownPeriodType',
            ]);
        });
    }
};
