<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ImportType

        Schema::create('ImportType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();
            $table->string('type'); // From type class

            $table->string('name')->nullable();
            $table->longText('description')->nullable();
            $table->char('status', 1)->default(\Element\ElementTime\Support\Domains\Type\StatusTypes\InactiveStatus::ID);

            $table->json('settings')->nullable();

            $table->timestamps();
        });

        //endregion Table: ImportType

        //region Table: ImportTypeUser

        Schema::create('ImportTypeUser', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();

            $table->unsignedBigInteger('importType_id');
            $table->foreign('importType_id', 'itu_it_fn')
                ->references('id')->on('ImportType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'itu_u_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'itu_au_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');

            $table->boolean('canManage')->default(false);
            $table->boolean('canRun')->default(true);
            $table->boolean('canCancel')->default(false);

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ImportTypeUser

        //region Table: ImportFile

        Schema::create('ImportFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();

            $table->unsignedBigInteger('importType_id');
            $table->foreign('importType_id', 'if_it_fn')
                ->references('id')->on('ImportType')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('mediaFile_id');
            $table->foreign('mediaFile_id', 'if_mf_fn')
                ->references('id')->on('MediaFile')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'if_au_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('name');
            $table->text('comments')->nullable();

            $table->integer('totalRecords')->default(0);
            $table->integer('succeededRecords')->default(0);
            $table->integer('erroredRecords')->default(0);

            $table->json('data')->nullable();
            $table->json('settings')->nullable();
            $table->json('warnings')->nullable();
            $table->json('errors')->nullable();

            $table->timestamps();
        });

        //endregion Table: ImportFile

        //region Table: ImportFileStatus

        /** @see \Element\ElementTime\Domains\Tenant\Importing\Support\ImportFileStatuses\BaseStatus */
        Schema::create('ImportFileStatus', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();

            $table->unsignedBigInteger('importFile_id');
            $table->foreign('importFile_id', 'ifs_if_fn')
                ->references('id')->on('ImportFile')
                ->onUpdate('cascade')->onDelete('cascade');

            // For added, cancel, and other manual entries
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'ifs_au_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('status'); // ID of type class
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->json('data')->nullable();

            $table->timestamps();
        });

        //endregion Table: ImportFileStatus

        //region Table: ImportFileItem

        Schema::create('ImportFileItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();

            $table->unsignedBigInteger('importFile_id');
            $table->foreign('importFile_id', 'ifi_if_fn')
                ->references('id')->on('ImportFile')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->integer('line')->nullable();
            $table->json('data')->nullable();
            $table->json('relatedData')->nullable();
            $table->json('warnings')->nullable();
            $table->json('errors')->nullable();

            $table->string('status'); // ID of type class
            $table->string('title')->nullable();
            $table->text('description')->nullable();

            $table->dateTime('queuedDateTime')->nullable();
            $table->dateTime('startedDateTime')->nullable();
            $table->dateTime('endedDateTime')->nullable();

            $table->timestamps();
        });

        //endregion Table: ImportFileItem
    }

    public function down(): void
    {
        Schema::dropIfExists('ImportFileItem');
        Schema::dropIfExists('ImportFileStatus');
        Schema::dropIfExists('ImportFile');
        Schema::dropIfExists('ImportTypeUser');
        Schema::dropIfExists('ImportType');
    }
};
