<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('MsConversation', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->char('type', 1); // Values: (T)imeSheet | (L)eave | (N)ormal Conversation
            $table->bigInteger('timeSheet_id')->unsigned()->nullable();
            $table->bigInteger('leave_id')->unsigned()->nullable();

            $table->string('title')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'msc_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('leave_id', 'msc_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('MsMember', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('msConversation_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->dateTime('lastActivityDataTime')->nullable();
            $table->char('status', 1)->nullable(); // (A)ctive | (I)nactive

            $table->foreign('msConversation_id', 'msmember_msc_fn')->references('id')->on('MsConversation')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'msmember_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('MsMessage', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('msConversation_id')->unsigned();
            $table->bigInteger('msMember_id')->unsigned();

            $table->char('flag'); // Values: (N)ote | (W)arning | (P)roblem | (A)nswer
            $table->string('title')->nullable();
            $table->text('description');
            $table->dateTime('dateTime');

            $table->char('status', 1)->nullable();

            $table->foreign('msConversation_id', 'msm_msc_fn')->references('id')->on('MsConversation')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('msMember_id', 'msm_member_fn')->references('id')->on('MsMember')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('MsMessageView', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigInteger('msMember_id')->unsigned();
            $table->bigInteger('msMessage_id')->unsigned();
            $table->dateTime('dateTime');

            $table->primary(['msMember_id', 'msMessage_id', 'dateTime']);

            $table->foreign('msMember_id', 'msmv_member_fn')->references('id')->on('MsMember')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('msMessage_id', 'msmv_message_fn')->references('id')->on('MsMessage')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::drop('MsConversation');
        Schema::drop('MsMember');
        Schema::drop('MsMessage');
        Schema::drop('MsMessageView');
    }
};
