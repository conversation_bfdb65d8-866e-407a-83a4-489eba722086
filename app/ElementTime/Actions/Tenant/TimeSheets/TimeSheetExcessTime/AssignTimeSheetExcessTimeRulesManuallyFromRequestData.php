<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetExcessTime;

use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\MissingCompulsoryArgumentException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeRule;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AccruedHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AdditionalHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\PaidHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\UnpaidHoursType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRule;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRuleWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItemRuleWorkSplitItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;
use Element\ElementTime\Support\Database\EDB;

class AssignTimeSheetExcessTimeRulesManuallyFromRequestData extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetExcessTimeItem $timeSheetExcessTimeItem,
        array $rules,
        bool $doesHaveBonusTimeInLieu = false,
        float|null $bonusTimeInLieuAmount = null,
    ): TimeSheetExcessTime
    {
        $timeSheetExcessTime = $timeSheetExcessTimeItem->timeSheetExcessTime;

        return EDB::tenantConn()->transaction(function () use ($timeSheetExcessTimeItem, $timeSheetExcessTime, $rules, $doesHaveBonusTimeInLieu, $bonusTimeInLieuAmount) {
            $originalItem = $timeSheetExcessTimeItem;

            if (!$timeSheetExcessTimeItem->isOriginal) {
                $originalItem = $timeSheetExcessTimeItem->originalItem;
                $timeSheetExcessTimeItem->forceDelete();
            }

            if (!is_null($this->loggedUser) && !$originalItem->repository->canReplace($this->loggedUser)) {
                throw new UnauthorizedActionException([
                    'action' => 'replace',
                    'model' => 'excess-time rules',
                    'reason' => 'Permission denied',
                ]);
            }

            $newItem = new TimeSheetExcessTimeItem;
            $newItem->timeSheetExcessTime_id = $timeSheetExcessTime->id;
            $newItem->originalTimeSheetExcessTimeItem_id = $originalItem->id;
            $newItem->isManual = true;
            $newItem->user_id = $this->loggedUser?->id;
            $newItem->isPlanned = true;
            $newItem->doesHaveBonusTimeInLieu = $doesHaveBonusTimeInLieu;

            if ($newItem->doesHaveBonusTimeInLieu) {
                if (is_null($bonusTimeInLieuAmount)) {
                    throw new InvalidArgumentException('Bonus time off in lieu value is invalid');
                }

                $newItem->bonusTimeInLieuAmount = $bonusTimeInLieuAmount;
            }

            $newItem->clearSaveOrFail();

            $newItem->durationAccrued = TimeDuration::zero();
            $newItem->durationAccruedOriginal = TimeDuration::zero();
            $newItem->durationAccruedAdjusted = TimeDuration::zero();
            $newItem->durationPaid = TimeDuration::zero();
            $newItem->durationPaidOriginal = TimeDuration::zero();
            $newItem->durationPaidAdjusted = TimeDuration::zero();
            $newItem->durationUnPaid = TimeDuration::zero();
            $newItem->durationUnPaidOriginal = TimeDuration::zero();
            $newItem->durationAdditional = TimeDuration::zero();
            $newItem->durationAdditionalOriginal = TimeDuration::zero();

            $remainingTotalDuration = $timeSheetExcessTime->duration->copy();

            foreach ($rules as $rule) {
                $excessTimeRule = ExcessTimeRule::q()->findOrFail($rule['excessTimeRuleId']);

                if (
                    $excessTimeRule->doesRestrictToPayrollOfficers
                    && (
                        $this->loggedUser?->id == $timeSheetExcessTime->timeSheet->payRunItem->user_id
                        || !$this->loggedUser?->access->isPayrollOfficer(true)
                    )
                ) {
                    throw new UnauthorizedActionException([
                        'action' => 'update',
                        'model' => 'excess-time rules',
                        'reason' => 'Selected rule "'. $excessTimeRule->name .'" can only be set by your payroll officer',
                    ]);
                }

                $newRule = new TimeSheetExcessTimeItemRule;
                $newRule->timeSheetExcessTimeItem_id = $newItem->id;
                $newRule->excessTimeRule_id = $excessTimeRule->id;
                $newRule->name = $rule['name'] ?? $excessTimeRule->name;
                $newRule->comments = $rule['comments'] ?? '';
                $newRule->doesAccrueLeave = isset($rule['doesAccrueLeave']) && ($rule['doesAccrueLeave'] == 1 || $rule['doesAccrueLeave'] == '1' || $rule['doesAccrueLeave'] == true || $rule['doesAccrueLeave'] == 'true');
                $newRule->duration = TimeDuration::parseFromHours($rule['duration']);
                $newRule->durationOriginal = TimeDuration::parseFromHours($rule['durationOriginal']);
                $newRule->durationAdjusted = $newRule->repository->getAdjustedDuration();

                $newRule->saveOrFail();

                if (!isset($rule['work'])) {
                    throw new MissingCompulsoryArgumentException('work details is missing item');
                }

                $timeSheetWorkItem = TimeSheetWorkItem::q()->findOrFail($rule['work']['id']);

                $newWork = new TimeSheetExcessTimeItemRuleWork;
                $newWork->timeSheetExcessTimeItemRule_id = $newRule->id;
                $newWork->timeSheetWorkItem_id = $timeSheetWorkItem->id;
                $newWork->duration = $newRule->duration->copy();
                $newWork->durationOriginal = $newRule->durationOriginal->copy();
                $newWork->durationAdjusted = $newRule->durationAdjusted->copy();
                $newWork->saveOrFail();

                $remainingDurationOriginal = $newWork->durationOriginal->copy();
                $remainingDuration = $newWork->durationOriginal->copy();

                foreach ($excessTimeRule->splitItems as $i => $splitRuleItem) {
                    $isLast = $i === ($newWork->rule->excessTimeRule->splitItems->count() - 1);

                    $newSplitItem = new TimeSheetExcessTimeItemRuleWorkSplitItem;
                    $newSplitItem->excessTimeRuleSplitItem_id = $splitRuleItem->id;
                    $newSplitItem->timeSheetExcessTimeItemRuleWork_id = $newWork->id;

                    $multiplier = $splitRuleItem->repository->getDurationMultiplier();

                    if ($isLast) {
                        $newSplitItem->durationOriginal = $remainingDurationOriginal->copy();
                        $newSplitItem->duration = $remainingDuration->copy();
                    } else {
                        $newSplitItem->durationOriginal = $newWork->durationOriginal->copy()->multiply($multiplier);
                        $newSplitItem->duration = $newWork->duration->copy()->multiply($multiplier);
                    }

                    $newSplitItem->durationAdjusted = $newSplitItem->duration->copy()->multiply($splitRuleItem->repository->getRatio());
                    $newSplitItem->saveOrFail();

                    $remainingDurationOriginal->deduct($newSplitItem->durationOriginal);
                    $remainingDuration->deduct($newSplitItem->duration);

                    if ($splitRuleItem->ruleTypeClass::isType(AccruedHoursType::class)) {
                        $newItem->durationAccrued->add($newSplitItem->duration->copy());
                        $newItem->durationAccruedOriginal->add($newSplitItem->durationOriginal->copy());
                        $newItem->durationAccruedAdjusted->add($newSplitItem->durationAdjusted->copy());
                    } elseif ($splitRuleItem->ruleTypeClass::isType(PaidHoursType::class)) {
                        $newItem->durationPaid->add($newSplitItem->duration->copy());
                        $newItem->durationPaidOriginal->add($newSplitItem->durationOriginal->copy());
                        $newItem->durationPaidAdjusted->add($newSplitItem->durationAdjusted->copy());
                    } elseif ($splitRuleItem->ruleTypeClass::isType(UnpaidHoursType::class)) {
                        $newItem->durationUnPaid->add($newSplitItem->duration->copy());
                        $newItem->durationUnPaidOriginal->add($newSplitItem->durationOriginal->copy());
                    } elseif ($splitRuleItem->ruleTypeClass::isType(AdditionalHoursType::class)) {
                        $newItem->durationAdditional->add($newSplitItem->duration->copy());
                        $newItem->durationAdditionalOriginal->add($newSplitItem->durationOriginal->copy());
                    }

                    $remainingTotalDuration->sub($newSplitItem->durationOriginal);
                }
            }

            if ($remainingTotalDuration->neq(0)) {
                throw new \InvalidArgumentException('Total hours does not match');
            }

            $newItem->clearSaveOrFail();

            $originalItem->workflow->otherRel_id = $newItem->id;
            $originalItem->workflow->clearSaveOrFail();
            $originalItem->repository->replace($this->loggedUser, runsStatusEvent: false);
            $newItem->repository->checkWorkflow($this->loggedUser, true);

            return ApplyRelatedCalculationsPostExcessTimeChanges::make()->handle($timeSheetExcessTime);
        });
    }
}
