<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region PenaltyRule

        Schema::table('PenaltyRule', function(Blueprint $table) {
            $table->string('workOrderPrefixCode')->nullable()->after('workOrderActivityCode');
            $table->string('workOrderSuffixCode')->nullable()->after('workOrderPrefixCode');

            DB::statement('ALTER TABLE PenaltyRule MODIFY COLUMN prefixCode VARCHAR(255) NULL DEFAULT NULL AFTER activityCode;');
            DB::statement('ALTER TABLE PenaltyRule MODIFY COLUMN suffixCode VARCHAR(255) NULL DEFAULT NULL AFTER prefixCode;');
        });

        //endregion PenaltyRule

        //region PenaltyType

        Schema::table('PenaltyType', function(Blueprint $table) {
            $table->string('prefixCode')->nullable()->after('activityCode');
            $table->string('suffixCode')->nullable()->after('prefixCode');

            $table->string('workOrderPrefixCode')->nullable()->after('workOrderActivityCode');
            $table->string('workOrderSuffixCode')->nullable()->after('workOrderPrefixCode');
        });

        //endregion PenaltyType

        //region AllowanceType

        Schema::table('AllowanceType', function(Blueprint $table) {
            $table->string('prefixCode')->nullable()->after('activityCode');
            $table->string('suffixCode')->nullable()->after('prefixCode');

            $table->string('workOrderPrefixCode')->nullable()->after('workOrderActivityCode');
            $table->string('workOrderSuffixCode')->nullable()->after('workOrderPrefixCode');
        });

        //endregion AllowanceType

        //region PlantItem

        Schema::table('PlantItem', function(Blueprint $table) {
            $table->string('prefixCode')->nullable()->after('activityCode');
            $table->string('suffixCode')->nullable()->after('prefixCode');

            $table->string('workOrderPrefixCode')->nullable()->after('workOrderActivityCode');
            $table->string('workOrderSuffixCode')->nullable()->after('workOrderPrefixCode');
        });

        //endregion PlantItem
    }

    public function down(): void
    {
        //region PlantItem

        if (Schema::hasColumn('PlantItem', 'prefixCode')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('prefixCode');
            });
        }

        if (Schema::hasColumn('PlantItem', 'suffixCode')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('suffixCode');
            });
        }

        if (Schema::hasColumn('PlantItem', 'workOrderPrefixCode')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('workOrderPrefixCode');
            });
        }

        if (Schema::hasColumn('PlantItem', 'workOrderSuffixCode')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('workOrderSuffixCode');
            });
        }

        //endregion PlantItem

        //region AllowanceType

        if (Schema::hasColumn('AllowanceType', 'prefixCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('prefixCode');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'suffixCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('suffixCode');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'workOrderPrefixCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderPrefixCode');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'workOrderSuffixCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderSuffixCode');
            });
        }

        //endregion AllowanceType

        //region PenaltyType

        if (Schema::hasColumn('PenaltyType', 'prefixCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('prefixCode');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'suffixCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('suffixCode');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'workOrderPrefixCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('workOrderPrefixCode');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'workOrderSuffixCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('workOrderSuffixCode');
            });
        }

        //endregion PenaltyType

        //region PenaltyRule

        if (Schema::hasColumn('PenaltyRule', 'workOrderPrefixCode')) {
            Schema::table('PenaltyRule', function (Blueprint $table) {
                $table->dropColumn('workOrderPrefixCode');
            });
        }

        if (Schema::hasColumn('PenaltyRule', 'workOrderSuffixCode')) {
            Schema::table('PenaltyRule', function (Blueprint $table) {
                $table->dropColumn('workOrderSuffixCode');
            });
        }

        //endregion PenaltyRule
    }
};
