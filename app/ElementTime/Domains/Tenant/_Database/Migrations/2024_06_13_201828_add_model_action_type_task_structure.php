<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelTaskStatus\ModelTaskStatus;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region \\ ---  ModelActionType // ---

        Schema::create('ModelActionType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('name');
            $table->string('type');
            $table->text('description')->nullable();
            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
        });

        $this->createRelatedTable('ModelActionType', 'Model', function (Blueprint $table) {
            $table->string('model_type')->nullable();
            $table->unsignedBigInteger('model_id')->nullable();
            $table->timestamps();

            $table->unique(['modelActionType_id', 'model_type', 'model_id'], 'matm_uq');
        });

        $this->createRelatedTable('ModelActionType', 'Condition', function (Blueprint $table, string $tableName) {
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id', 'matc_matc_fn')
                ->references('id')->on($tableName)
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('type');
            $table->boolean('isNot')->default(false);
            $table->json('options');
            $table->timestamps();
        });

        $this->createRelatedTable('ModelActionType', 'Target', function (Blueprint $table, string $tableName) {
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id', 'matt_matt_fn')
                ->references('id')->on($tableName)
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('type');
            $table->boolean('isNot')->default(false);
            $table->json('options');
            $table->timestamps();
        });

        $this->createRelatedTable('ModelActionType', 'Outcome', function (Blueprint $table) {
            $table->string('type');
            $table->json('options');
            $table->timestamps();

            $table->unique(['modelActionType_id', 'type'], 'mato_type_uq');
        });

        //endregion \\ ---  ModelActionType // ---

        //region \\ ---  ModelTask // ---

        Schema::create('ModelTask', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('model_type');
            $table->unsignedBigInteger('model_id');

            $table->string('type');
            $table->json('options')->nullable();
            $table->dateTime('dueDateTime')->nullable();
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('htmlDescription')->nullable();

            $table->string('taskStatus')->default(ModelTaskStatus::Open);
            $table->timestamps();
        });

        $this->createRelatedTable('ModelTask', 'Dependant', function (Blueprint $table) {
            $table->string('dependant_type');
            $table->unsignedBigInteger('dependant_id');

            $table->string('type');
            $table->json('options');
            $table->timestamps();

            $table->unique(['modelTask_id', 'dependant_type', 'dependant_id', 'type'], 'mtd_uq');
        });

        $this->createRelatedTable('ModelTask', 'User', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'mtu_usr_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();

            $table->unique(['modelTask_id', 'user_id'], 'mtu_uq');
        });

        $this->createRelatedTable('ModelTask', 'Event', function (Blueprint $table) {
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'mte_usr_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('type');
            $table->dateTime('dateTime');
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('comments')->nullable();
            $table->timestamps();
        });

        //endregion \\ ---  ModelTask // ---
    }

    protected function createRelatedTable(string $parentTableName, string $newChildTableName, \Closure|null $assignmentAdditional = null): void
    {
        $tableName = $parentTableName . $newChildTableName;

        Schema::create($tableName, function (Blueprint $table) use ($assignmentAdditional, $parentTableName, $tableName) {
            $shortRelationName = $this->getShorterTableName($tableName) . '_' . $this->getShorterTableName($parentTableName) . '_fn';
            $foreignColumnName = lcfirst($parentTableName) . '_id';

            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger($foreignColumnName);
            $table->foreign($foreignColumnName, $shortRelationName)
                ->references('id')->on($parentTableName)
                ->onUpdate('cascade')->onDelete('cascade');

            if (!is_null($assignmentAdditional)) {
                $assignmentAdditional($table, $tableName);
            }
        });
    }

    protected function getShorterTableName($tableName): string
    {
        return preg_replace('/[a-z]/', '', $tableName);
    }

    public function down(): void
    {
        Schema::dropIfExists('ModelActionTypeOutcome');
        Schema::dropIfExists('ModelActionTypeTarget');
        Schema::dropIfExists('ModelActionTypeCondition');
        Schema::dropIfExists('ModelActionTypeModel');
        Schema::dropIfExists('ModelActionType');

        Schema::dropIfExists('ModelTaskEvent');
        Schema::dropIfExists('ModelTaskUser');
        Schema::dropIfExists('ModelTaskDependant');
        Schema::dropIfExists('ModelTask');
    }
};
