<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('CalendarDate', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->date('date');

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['date'], 'calendar_day_date_uq');
        });

        Schema::table('PublicHoliday', function (Blueprint $table) {
            $table->bigInteger('calendarDate_id')->unsigned();

            $table->dropColumn('type');
            $table->dropColumn('duration');
            $table->dropColumn('durationType');
        });

        /** @var PublicHoliday[] $phs */
        $phs = DB::table('PublicHoliday')->get();
        foreach ($phs as $ph) {
            /** @var \Element\ElementTime\Domains\Tenant\Schedules\Models\CalendarDate $calendarDate */
            $calendarDate = DB::table('CalendarDate')->where('date', '=', $ph->date)->first();
            if (is_null($calendarDate)) {
                DB::table('CalendarDate')->insert([
                    'date' => $ph->date,
                ]);

                $calendarDate = DB::table('CalendarDate')->where('date', '=', $ph->date)->first();
            }

            DB::table('PublicHoliday')->where('id', '=', $ph->id)->update([
                'calendarDate_id' => $calendarDate->id,
            ]);
        }

        Schema::table('PublicHoliday', function (Blueprint $table) {
            $table->foreign('calendarDate_id', 'ph_cdate_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('Shift_CalendarDate', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('shift_id')->unsigned();
            $table->bigInteger('calendarDate_id')->unsigned();

            $table->timestamps();

            $table->unique(['shift_id', 'calendarDate_id'], 'shiftcdate_s_cdate_uq');
            $table->foreign('shift_id', 'scdate_s_fn')->references('id')->on('Shift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('calendarDate_id', 'scdate_cdate_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->dropColumn('ELT_PublicHoliday_id');

            $table->bigInteger('calendarDate_id')->unsigned()->nullable();
        });

        /** @var TimeSheetDay[] $tsds */
        $tsds = DB::table('TimeSheetDay')->get();
        foreach ($tsds as $tsd) {
            $calendarDate = DB::table('CalendarDate')->where('date', '=', $tsd->date)->first();
            if (is_null($calendarDate)) {
                DB::table('CalendarDate')->insert([
                    'date' => $tsd->date,
                ]);

                $calendarDate = DB::table('CalendarDate')->where('date', '=', $tsd->date)->first();
            }

            DB::table('TimeSheetDay')->where('id', '=', $tsd->id)->update([
                'calendarDate_id' => $calendarDate->id,
            ]);
        }

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->foreign('calendarDate_id', 'tsd_cdate_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('TimeSheetItemDay', function (Blueprint $table) {
            $table->bigInteger('publicHoliday_id')->unsigned()->nullable();
            $table->foreign('publicHoliday_id', 'tsid_ph_fn')->references('id')->on('PublicHoliday')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->dropColumn('ELT_PublicHoliday_id');
        });

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->bigInteger('calendarDate_id')->unsigned();
        });

        /** @var UserShiftDay[] $usds */
        $usds = DB::table('UserShiftDay')->get();
        foreach ($usds as $usd) {
            $calendarDate = DB::table('CalendarDate')->where('date', '=', $usd->date)->first();
            if (is_null($calendarDate)) {
                DB::table('CalendarDate')->insert([
                    'date' => $usd->date,
                ]);

                $calendarDate = DB::table('CalendarDate')->where('date', '=', $usd->date)->first();
            }

            DB::table('UserShiftDay')->where('id', '=', $usd->id)->update([
                'calendarDate_id' => $calendarDate->id,
            ]);
        }

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->foreign('calendarDate_id', 'usd_cdate_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM UserShiftDay WHERE Key_name=\'usd_cdate_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropForeign('usd_cdate_fn');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'calendarDate_id')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('calendarDate_id');
            });
        }

        if (!Schema::hasColumn('UserShiftDay', 'ELT_PublicHoliday_id')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->bigInteger('ELT_PublicHoliday_id')->unsigned()->nullable();
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDay WHERE Key_name=\'tsid_ph_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropForeign('tsid_ph_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDay', 'publicHoliday_id')) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropColumn('publicHoliday_id');
            });
        }

        if (!Schema::hasColumn('TimeSheetDay', 'ELT_PublicHoliday_id')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->bigInteger('ELT_PublicHoliday_id')->unsigned()->nullable();
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetDay WHERE Key_name=\'tsd_cdate_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropForeign('tsd_cdate_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'calendarDate_id')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('calendarDate_id');
            });
        }

        Schema::dropIfExists('Shift_CalendarDate');

        if (!Schema::hasColumn('PublicHoliday', 'durationType')) {
            Schema::table('PublicHoliday', function (Blueprint $table) {
                $table->char('durationType', 1)->nullable();
            });
        }

        if (!Schema::hasColumn('PublicHoliday', 'duration')) {
            Schema::table('PublicHoliday', function (Blueprint $table) {
                $table->float('duration')->nullable();
            });
        }

        if (!Schema::hasColumn('PublicHoliday', 'type')) {
            Schema::table('PublicHoliday', function (Blueprint $table) {
                $table->char('type', 1)->nullable();
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM PublicHoliday WHERE Key_name=\'ph_cdate_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('PublicHoliday', function (Blueprint $table) {
                $table->dropForeign('ph_cdate_fn');
            });
        }

        if (Schema::hasColumn('PublicHoliday', 'calendarDate_id')) {
            Schema::table('PublicHoliday', function (Blueprint $table) {
                $table->dropColumn('calendarDate_id');
            });
        }

        Schema::dropIfExists('CalendarDate');
    }
};
