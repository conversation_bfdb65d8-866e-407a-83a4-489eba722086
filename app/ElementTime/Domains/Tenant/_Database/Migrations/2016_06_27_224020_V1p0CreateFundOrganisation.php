<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('FundOrganisation', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Organisation Details
            $table->boolean('hasOfficialTaxRegistration')->nullable();
            $table->string('officialTaxRegistrationNumber')->nullable();
            $table->string('name')->nullable();
            $table->string('number')->nullable();
            $table->string('bankAccount')->nullable();
            $table->char('type', 1)->nullable();
            $table->char('shouldBePaid', 1)->nullable();
            $table->string('referenceNumber')->nullable();
            $table->string('email')->nullable();
            $table->boolean('sendReportsViaEmail')->nullable();

            // Physical Address
            $table->string('physicalPostalCode')->nullable();
            $table->string('physicalBuilding')->nullable();
            $table->string('physicalStreetAddress')->nullable();
            $table->string('physicalAdditionalDetails')->nullable();
            $table->string('physicalSuburb')->nullable();
            $table->string('physicalCity')->nullable();
            $table->string('physicalState')->nullable();
            $table->string('physicalCountry')->nullable();

            // Postal Address
            $table->boolean('postalSamePhysical')->nullable();
            $table->string('postalPostalCode')->nullable();
            $table->string('postalMailingBox')->nullable();
            $table->string('postalBuilding')->nullable();
            $table->string('postalStreetAddress')->nullable();
            $table->string('postalAdditionalDetails')->nullable();
            $table->string('postalSuburb')->nullable();
            $table->string('postalCity')->nullable();
            $table->string('postalState')->nullable();
            $table->string('postalCountry')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::drop('FundOrganisation');
    }
};
