<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShiftDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetPeriod;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Database\Eloquent\Collection;

abstract class BaseSetLeaveRequestRelationships extends BaseLeavesAction
{
    /** @throws \Throwable */
    protected function setLeaveRequestRelationships(LeaveRequest $leaveRequest, ScheduledPayRun $scheduledPayRun, TimeSheet|null $timeSheet = null): void
    {
        $validScheduledPayRun = $this->getValidScheduledPayRun($leaveRequest, $scheduledPayRun);
        $validTimeSheet = $this->getValidTimeSheet($leaveRequest, $timeSheet);

        $leaveRequest->scheduledPayRun_id = $validScheduledPayRun->id;
        $leaveRequest->timeSheet_id = $validTimeSheet->id;

        if ($leaveRequest->isDirty(['timeSheet_id', 'scheduledPayRun_id'])) {
            $leaveRequest->saveOrFail(['isSafeSave' => true]);
        }

        $this->setLeaveRequestDaysRelationships($leaveRequest, $validScheduledPayRun, $validTimeSheet);

        $leaveRequest->load([
            'timeSheet',
            'scheduledPayRun',
        ]);
    }

    /** @throws \Throwable */
    protected function getValidTimeSheet(LeaveRequest $leaveRequest, TimeSheet $timeSheet = null): TimeSheet
    {
        if ($this->isTimeSheetCompatibleWithLeave($leaveRequest, $leaveRequest->timeSheet)) {
            return $leaveRequest->timeSheet;
        }

        if ($this->isTimeSheetCompatibleWithLeave($leaveRequest, $timeSheet)) {
            return $timeSheet;
        }

        return TimeSheet::getByDate($leaveRequest->userLeaveBankType->bank->user, $leaveRequest->startDate);
    }

    protected function isTimeSheetCompatibleWithLeave(LeaveRequest $leaveRequest, TimeSheet|null $timeSheet = null): bool
    {
        if (is_null($timeSheet)) {
            return false;
        }

        if ($timeSheet->payRunItem->user_id !== $leaveRequest->userLeaveBankType->bank->user_id) {
            return false;
        }

        if ($timeSheet->payRunItem->payRun->startDate->gt($leaveRequest->endDate)) {
            return false;
        }

        if ($timeSheet->payRunItem->payRun->endDate->lt($leaveRequest->startDate)) {
            return false;
        }

        return true;
    }

    /** @throws \Throwable */
    protected function getValidScheduledPayRun(LeaveRequest $leaveRequest, ScheduledPayRun $scheduledPayRun): ScheduledPayRun
    {
        if ($this->isScheduledPayRunCompatibleWithLeave($leaveRequest, $leaveRequest->scheduledPayRun)) {
            return $leaveRequest->scheduledPayRun;
        }

        if ($this->isScheduledPayRunCompatibleWithLeave($leaveRequest, $scheduledPayRun)) {
            return $scheduledPayRun;
        }

        return $leaveRequest->userRoleSchedule->getScheduledPayRun($leaveRequest->startDate);
    }

    protected function isScheduledPayRunCompatibleWithLeave(
        LeaveRequest $leaveRequest,
        ScheduledPayRun|null $scheduledPayRun = null
    ): bool
    {
        if (is_null($scheduledPayRun)) {
            return false;
        }

        if ($leaveRequest->userRoleSchedule_id !== $scheduledPayRun->userRoleSchedule_id) {
            return false;
        }

        if ($leaveRequest->startDate->gt($scheduledPayRun->endDate)) {
            return false;
        }

        if ($leaveRequest->endDate->lt($scheduledPayRun->startDate)) {
            return false;
        }

        return true;
    }

    /** @throws \Throwable */
    protected function setLeaveRequestDaysRelationships(LeaveRequest $leaveRequest, ScheduledPayRun $scheduledPayRun, TimeSheet|null $timeSheet): void
    {
        foreach ($leaveRequest->days as $day) {
            /** @var ScheduledPayRunPeriod $scheduledPayRunPeriod */
            $scheduledPayRunPeriod = $scheduledPayRun->periods->firstWhere(fn(ScheduledPayRunPeriod $period) => $day->date->isBetween($period->startDate, $period->endDate, true));

            $scheduledPayRunPeriodDay = $this->getValidScheduledPayRunPeriodDayForLeaveRequestDay($day, $scheduledPayRunPeriod);
            $timeSheetDay = $this->getValidTimeSheetDayForLeaveRequestDay($day, $scheduledPayRunPeriodDay, $timeSheet);
            $timeSheetPeriod = $this->getValidTimeSheetPeriodForLeaveRequestDay($day, $scheduledPayRunPeriod);

            if ($leaveRequest->scheduleType->is(UserRoleScheduleType::WorkPattern) && $scheduledPayRunPeriodDay->specific instanceof ScheduledPayRunPeriodDayWorkPattern) {
                $scheduledPayRunPeriodDayWorkPattern = $scheduledPayRunPeriodDay->specific;

                $day->userRoleScheduleDay_id = $scheduledPayRunPeriodDayWorkPattern->userRoleSchedulePatternPeriodShiftDay_id;
                $day->userRoleScheduleDay_type = UserRoleSchedulePatternPeriodShiftDay::class;

            }

            $day->scheduledPayRunPeriodDay_id = $scheduledPayRunPeriodDay->id;
            $day->timeSheetDay_id = $timeSheetDay->id;
            $day->timeSheetPeriod_id = $timeSheetPeriod->id;

            if ($day->isDirty(['scheduledPayRunPeriodDay_id', 'timeSheetPeriod_id', 'timeSheetDay_id', 'userRoleScheduleDay_id', 'userRoleScheduleDay_type'])) {
                $day->saveOrFail();
            }
        }
    }

    protected function getValidScheduledPayRunPeriodDayForLeaveRequestDay(LeaveRequestDay $day, ScheduledPayRunPeriod $period): ScheduledPayRunPeriodDay
    {
        $scheduledPayRunPeriodDay = $day->scheduledPayRunPeriodDay;

        if (
            is_null($scheduledPayRunPeriodDay)
            || !$scheduledPayRunPeriodDay->date->isSameDay(!$day->date)
            || $scheduledPayRunPeriodDay->scheduledPayRunPeriod_id !== $period->id
        ) {
            return $period->days->firstWhere(fn(ScheduledPayRunPeriodDay $periodDay) => $periodDay->date->isSameDay($day->date));
        }

        return $scheduledPayRunPeriodDay;
    }

    protected function getValidTimeSheetPeriodForLeaveRequestDay(LeaveRequestDay $day, ScheduledPayRunPeriod $scheduledPayRunPeriod): TimeSheetPeriod
    {
        $timeSheetPeriod = $day->timeSheetPeriod;

        if (
            is_null($timeSheetPeriod)
            || !$day->date->isBetween($timeSheetPeriod->startDate, $timeSheetPeriod->endDate, true)
            || $timeSheetPeriod->scheduledPayRunPeriod_id != $scheduledPayRunPeriod->id
        ) {
            $timeSheetPeriod = $scheduledPayRunPeriod->timeSheetPeriod;
        }

        return $timeSheetPeriod;
    }

    protected function getValidTimeSheetDayForLeaveRequestDay(LeaveRequestDay $day, ScheduledPayRunPeriodDay $periodDay, TimeSheet $timeSheet): TimeSheetDay
    {
        $timeSheetDay = $day->timeSheetDay;

        if (
            is_null($timeSheetDay)
            || !$timeSheetDay->date->isSameDay($day->date)
            || $timeSheet->id !== $timeSheetDay->timeSheet_id
        ) {
            $timeSheetDay = $periodDay->timeSheetDay ?? $timeSheet->timeSheetDays->where('date', '=', $day->date)->first();
        }

        return $timeSheetDay;
    }

    //region --\\   Methods to set relations based on User/UserRole   //--

    /** @throws \Throwable */
    protected function setRelationshipsFromUserOrUserRole(User|UserRole $origin): void
    {
        $startDate = TenantSystemSettings::getEarliestOpenPayRun()->startDate;
        $endDate = TenantSystemSettings::getLatestPayRun()->endDate;

        $userRoleSchedules = $this->getRelevantUserRoleSchedulesFromUserOrUserRole($origin);
        $scheduledPayRuns = $this->getRelevantScheduledPayRunsFromSchedules($userRoleSchedules, $startDate);
        $leaveRequests = $this->getRelevantLeaveRequestsFromSchedules($userRoleSchedules, $startDate, $endDate);


        if ($scheduledPayRuns->count() < 1 || $leaveRequests->count() < 1) {
            return;
        }

        foreach ($scheduledPayRuns as $scheduledPayRun) {
            $timeSheet = $scheduledPayRun->timeSheet;

            if (is_null($timeSheet)) {
                $timeSheet = TimeSheet::getByDate($origin instanceof User ? $origin : $origin->user, $scheduledPayRun->startDate);
            }

            if (is_null($timeSheet)) {
                continue;
            }

            foreach ($leaveRequests as $leaveRequest) {
                if ($leaveRequest->startDate->gt($scheduledPayRun->endDate)) {
                    continue;
                }

                if ($leaveRequest->endDate->lt($scheduledPayRun->startDate)) {
                    continue;
                }

                $this->setLeaveRequestRelationships($leaveRequest, $scheduledPayRun, $timeSheet);
            }
        }
    }

    /** @throws \Throwable */
    private function getRelevantUserRoleSchedulesFromUserOrUserRole(User|UserRole $origin): Collection|array
    {
        $startDate = TenantSystemSettings::getEarliestOpenPayRun()->startDate;
        $endDate = TenantSystemSettings::getLatestPayRun()->endDate;

        $constraints = [
            ['UserRoleSchedule.startDate', '<=', $endDate->toDateString()],
            [
                [
                    ['IS NULL', 'UserRoleSchedule.endDate', 'OR'],
                    ['UserRoleSchedule.endDate', '=', '0000-00-00', 'OR'],
                    ['UserRoleSchedule.endDate', '>', $startDate->toDateString(), 'OR'],
                ],
            ],
            ['BETWEEN', 'LeaveRequest.startDate', [$startDate->toDateString(), $endDate->toDateString()]],
        ];

        if ($origin instanceof User) {
            $constraints[] = ['UserRole.user_id', '=', $origin->id];
        } else {
            $constraints[] = ['UserRoleSchedule.userRole_id', '=', $origin->id];
        }

        return UserRoleSchedule::q()
            ->constraints($constraints)
            ->joins([
                ['LeaveRequest', 'LeaveRequest.userRoleSchedule_id', '=', 'UserRoleSchedule.id', 'inner'],
            ])
            ->many();
    }

    /**
     * @param Collection|UserRoleSchedule[] $userRoleSchedules
     * @return Collection<ScheduledPayRun>|ScheduledPayRun[]
     */
    private function getRelevantScheduledPayRunsFromSchedules(Collection|array $userRoleSchedules, CarbonInterface $startDate): Collection|array
    {
        $ids = $userRoleSchedules->pluck('id')->toArray();

        return ScheduledPayRun::q()
            ->constraints([
                ['IN', 'ScheduledPayRun.userRoleSchedule_id', $ids],
                ['ScheduledPayRun.startDate', '>=', $startDate->toDateString()],
                ['LeaveRequest.startDate', '>=', $startDate->toDateString()],
            ])
            ->joins([
                ['LeaveRequest', 'LeaveRequest.userRoleSchedule_id', '=', 'ScheduledPayRun.userRoleSchedule_id', 'inner'],
            ])
            ->relations(static::getScheduledPayRunRelationships())
            ->many();
    }

    /**
     * @param Collection|UserRoleSchedule[] $userRoleSchedules
     * @return Collection<LeaveRequest>|LeaveRequest[]
     */
    private function getRelevantLeaveRequestsFromSchedules(Collection|array $userRoleSchedules, CarbonInterface $startDate, CarbonInterface $endDate): Collection|array
    {
        $ids = $userRoleSchedules->pluck('id')->toArray();

        return LeaveRequest::q()
            ->constraints([
                ['Workflow.status', '=', ApprovedType::ID],
                ['IN', 'LeaveRequest.userRoleSchedule_id', $ids],
                ['LeaveRequest.startDate', '>=', $startDate->toDateString()],
                ['LeaveRequest.startDate', '<=', $endDate->toDateString()],
            ])
            ->relations(static::getLeaveRequestRelationships())
            ->many();
    }

    //endregion --\\   Methods to set relations based on User/UserRole   //--

    //region --\\   Eager load / relation methods   //--

    public static function getLeaveRequestRelationships(string $pathToLeaveRequest = ''): array
    {
        return array_map(
            fn (string $relationshipEntry) => static::setRelationshipPrefix($pathToLeaveRequest, $relationshipEntry),
            [
                'userLeaveBankType.bank.user',
                'timeSheet.payRunItem.payRun',
                'userRoleSchedule',

                ...static::getScheduledPayRunRelationships('scheduledPayRun'),
                ...static::getLeaveRequestDayRelationships('days'),
            ]
        );
    }

    public static function getLeaveRequestDayRelationships(string $pathToLeaveRequestDay = ''): array
    {
        return array_map(
            fn (string $relationshipEntry) => static::setRelationshipPrefix($pathToLeaveRequestDay, $relationshipEntry),
            [
                ...static::getScheduledPayRunPeriodDayRelationships('scheduledPayRunPeriodDay'),
                'timeSheetPeriod',
                'timeSheetDay',
            ]
        );
    }

    public static function getScheduledPayRunRelationships(string $pathToScheduledPayRun = ''): array
    {
        return array_map(
            fn (string $relationshipEntry) => static::setRelationshipPrefix($pathToScheduledPayRun, $relationshipEntry),
            [
                ...static::getScheduledPayRunPeriodRelationships('periods'),
                'timeSheet.payRunItem.payRun',
                'userRoleSchedule',
            ]
        );
    }

    public static function getScheduledPayRunPeriodRelationships(string $pathToScheduledPayRunPeriod = ''): array
    {
        return array_map(
            fn (string $relationshipEntry) => static::setRelationshipPrefix($pathToScheduledPayRunPeriod, $relationshipEntry),
            [
                'timeSheetPeriod',
                ...static::getScheduledPayRunPeriodDayRelationships('days'),
            ]
        );
    }

    public static function getScheduledPayRunPeriodDayRelationships(string $pathToScheduledPayRunPeriodDay = ''): array
    {
        return array_map(
            fn (string $relationshipEntry) => static::setRelationshipPrefix($pathToScheduledPayRunPeriodDay, $relationshipEntry),
            [
                'timeSheetDay',
                'specific',
            ]
        );
    }

    private static function setRelationshipPrefix(string $prefix, string $relationship): string
    {
        return implode(
            '.',
            array_filter([$prefix, $relationship], fn($part) => strlen($part) > 0)
        );
    }

    //endregion --\\   Eager load / relation methods   //--
}
