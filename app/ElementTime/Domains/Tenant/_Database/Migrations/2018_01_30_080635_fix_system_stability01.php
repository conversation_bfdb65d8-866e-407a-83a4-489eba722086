<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Remove D_E_P_R_E_C_A_T_E_D stuff
        //region Table: TimeSheet
        if (Schema::hasColumn('TimeSheet', 'cAccruedToilEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cAccruedToilEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalLeave')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeave');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOff')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOff');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'extraTimeOvertimeValue')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('extraTimeOvertimeValue');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'extraTimeOvertimeTypeName')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('extraTimeOvertimeTypeName');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM `TimeSheet` WHERE Key_name=\'timesheet_etot_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('timesheet_etot_fn');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'extraTimeOvertimeType_id')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('extraTimeOvertimeType_id');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'extraTimeChangeRule')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('extraTimeChangeRule');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalToilEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalToilEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'fTotalToilEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalToilEarnt');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM `TimeSheet` WHERE Key_name=\'timesheet_user_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('timesheet_user_fn');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'user_id')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('user_id');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM `TimeSheet` WHERE Key_name=\'timesheet_payrun_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('timesheet_payrun_fn');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'payRun_id')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('payRun_id');
            });
        }
        //endregion

        //region Table: TimeSheetDay
        if (Schema::hasColumn('TimeSheetDay', 'hoursOvertimeRule')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursOvertimeRule');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursToilRule')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursToilRule');
            });
        }
        //endregion

        //region Table: PayRunType
        if (Schema::hasColumn('PayRunType', 'periodStartsOn')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('periodStartsOn');
            });
        }
        if (Schema::hasColumn('PayRunType', 'processedAtTime')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('processedAtTime');
            });
        }
        if (Schema::hasColumn('PayRunType', 'processedAtDaysAfter')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('processedAtDaysAfter');
            });
        }
        if (Schema::hasColumn('PayRunType', 'dueDaysPriorForTimesheet')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('dueDaysPriorForTimesheet');
            });
        }
        //endregion

        //region Table: Settings
        if (Schema::hasColumn('Settings', 'allowsUserToRequestRemindersViaSMS')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsUserToRequestRemindersViaSMS');
            });
        }
        if (Schema::hasColumn('Settings', 'defaultRosterType')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('defaultRosterType');
            });
        }
        if (Schema::hasColumn('Settings', 'defaultHoursPerPeriod')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('defaultHoursPerPeriod');
            });
        }
        if (Schema::hasColumn('Settings', 'defaultRosterMap')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('defaultRosterMap');
            });
        }
        if (Schema::hasColumn('Settings', 'allowsTOILRates')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsTOILRates');
            });
        }
        if (Schema::hasColumn('Settings', 'tOILRate')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('tOILRate');
            });
        }
        //endregion

        //region Table: User
        if (Schema::hasColumn('User', 'hasOvertime')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasOvertime');
            });
        }
        if (Schema::hasColumn('User', 'rosterType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosterType');
            });
        }
        if (Schema::hasColumn('User', 'hoursPerPeriod')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hoursPerPeriod');
            });
        }
        if (Schema::hasColumn('User', 'rosterMap')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosterMap');
            });
        }
        if (Schema::hasColumn('User', 'tOILRate')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('tOILRate');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffValueAmount')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffValueAmount');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffValueType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffValueType');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffPeriodAmount')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffPeriodAmount');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffPeriodType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffPeriodType');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffAutomaticallyAmount')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffAutomaticallyAmount');
            });
        }
        if (Schema::hasColumn('User', 'rosteredTimeOffAutomaticallyType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('rosteredTimeOffAutomaticallyType');
            });
        }
        //endregion

        //region Table: UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'value')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('value');
            });
        }
        //endregion

        //region Table: Notification
        if (DB::select(DB::raw('SHOW KEYS FROM `Notification` WHERE Key_name=\'not_al_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropForeign('not_al_fn');
            });
        }
        if (Schema::hasColumn('Notification', 'actionLog_id')) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropColumn('actionLog_id');
            });
        }
        //endregion
        //endregion

        //region Remove duplicate TimeSheetDays and prevent new duplications
        $records = DB::select('
            SELECT
                    a.id AS aId,
                    b.id AS bId,
                    a.timeSheet_id AS aTimeSheet_id,
                    b.timeSheet_id AS bTimeSheet_id,
                    a.calendarDate_id AS aCalendarDate_id,
                    b.calendarDate_id AS bCalendarDate_id,
                    a.hoursTotal AS aHoursTotal,
                    b.hoursTotal AS bHoursTotal,
                    a.valueAllowance AS aValueAllowance,
                    b.valueAllowance AS bValueAllowance
                FROM TimeSheetDay a, TimeSheetDay b
                WHERE a.timeSheet_id = b.timeSheet_id AND a.calendarDate_id = b.calendarDate_id AND a.id <> b.id;
        ');

        $saved = [];

        foreach ($records as $record) {
            if ($record->aHoursTotal > $record->bHoursTotal || $record->aValueAllowance > $record->bValueAllowance) {
                $id = $record->bId;
                $safe = $record->aId;
            } else {
                $id = $record->aId;
                $safe = $record->bId;
            }

            if (!in_array($id, $saved)) {
                DB::delete('DELETE FROM TimeSheetDayExcessTime WHERE timeSheetDay_id = ?', [$id]);
                DB::delete('DELETE FROM TimeSheetItemDay WHERE timeSheetDay_id = ?', [$id]);
                DB::delete('DELETE FROM TimeSheetAllowanceDay WHERE timeSheetDay_id = ?', [$id]);
                DB::delete('DELETE FROM TimeSheetDay WHERE id = ?', [$id]);
                $saved[] = $safe;
            }
        }

        if (!DB::select(DB::raw('SHOW KEYS FROM `TimeSheetDay` WHERE Key_name=\'tsd_date_ts_uq\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->unique(['calendarDate_id', 'timeSheet_id'], 'tsd_date_ts_uq');
            });
        }
        //endregion

        //region Remove PayRunItem and TimeSheet duplications
        $records = DB::select('
         SELECT
                a.id AS aId,
                b.id AS bId,
                a.payRun_id AS aPayRun_id,
                b.payRun_id AS bPayRun_id,
                a.user_id AS aUser_id,
                b.user_id AS bUser_id,
                a.updated_at as aUpdated_at,
                b.updated_at as bUpdated_at
            FROM PayRunItem a, PayRunItem b
            WHERE a.user_id = b.user_id AND a.payRun_id = b.payRun_id AND a.id <> b.id;
        ');

        $saved = [];

        foreach ($records as $record) {
            if (Carbon::parse($record->aUpdated_at)->gt(Carbon::parse($record->bUpdated_at))) {
                $id = $record->bId;
                $safe = $record->aId;
            } else {
                $id = $record->aId;
                $safe = $record->bId;
            }

            if (!in_array($id, $saved)) {
                DB::delete('
                    DELETE FROM TimeSheetApproval
                    WHERE timeSheet_id IN (
                        SELECT id FROM TimeSheet WHERE payRunItem_id = ?
                    )
                ', [$id]);
                DB::delete('
                    DELETE FROM TimeSheetItem
                    WHERE timeSheet_id IN (
                        SELECT id FROM TimeSheet WHERE payRunItem_id = ?
                    )
                ', [$id]);
                DB::delete('
                    DELETE FROM TimeSheetDayExcessTime
                    WHERE timeSheetDay_id IN (
                        SELECT id FROM TimeSheetDay
                        WHERE timeSheet_id IN (
                            SELECT id FROM TimeSheet WHERE payRunItem_id = ?
                        )
                    )
                ', [$id]);
                DB::delete('
                    DELETE FROM TimeSheetDay
                    WHERE timeSheet_id IN (
                        SELECT id FROM TimeSheet WHERE payRunItem_id = ?
                    )
                ', [$id]);
                DB::delete('DELETE FROM TimeSheet WHERE payRunItem_id = ?', [$id]);
                DB::delete('DELETE FROM PayRunItem WHERE id = ?', [$id]);
                $saved[] = $safe;
            }
        }

        if (!DB::select(DB::raw('SHOW KEYS FROM `PayRunItem` WHERE Key_name=\'pri_pr_user_uq\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->unique(['payRun_id', 'user_id'], 'pri_pr_user_uq');
            });
        }

        if (!DB::select(DB::raw('SHOW KEYS FROM `TimeSheet` WHERE Key_name=\'ts_pri_uq\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->unique(['payRunItem_id'], 'ts_pri_uq');
            });
        }
        //endregion

        //region Update public holidays on time-sheets
        DB::delete('DELETE FROM TimeSheetItem WHERE type <> "A" AND type <> "P" AND type <> "L" AND type <> "R" AND type <> "T"');
        //endregion
    }

    public function down(): void
    {
        //
    }
};
