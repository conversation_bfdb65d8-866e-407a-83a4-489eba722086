<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyRuleCalculationMethods\OffsetMethod;
use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyRuleRateTypes\PaidPercentageType;
use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyRuleRelationTypes\TriggerType;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table PenaltyRule

        Schema::create('PenaltyRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name')->unique('prule_name_uq');
            $table->text('description')->nullable();

            $table->unsignedBigInteger('incrementRate_id')->nullable();
            $table->foreign('incrementRate_id', 'prule_ir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('set null');

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->string('workOrderProjectCode')->nullable();
            $table->string('workOrderCostCode')->nullable();
            $table->string('workOrderActivityCode')->nullable();
            $table->string('prefixCode')->nullable();
            $table->string('suffixCode')->nullable();

            $table->string('calculationMethod')->default(OffsetMethod::ID);
            $table->string('relationType')->default(TriggerType::ID);
            $table->string('rateType')->default(PaidPercentageType::ID);
            $table->decimal('rateAmount', 16, 10)->default(0);

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
        });

        //endregion Table PenaltyRule

        //region Table PenaltyRuleCondition

        Schema::create('PenaltyRuleCondition', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('penaltyRule_id');
            $table->foreign('penaltyRule_id', 'prulec_prule_fn')->references('id')->on('PenaltyRule')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('parentPenaltyRuleCondition_id')->nullable();
            $table->foreign('parentPenaltyRuleCondition_id', 'prulec_parent_fn')->references('id')->on('PenaltyRuleCondition')->onUpdate('cascade')->onDelete('cascade');

            $table->string('type');
            $table->json('options')->nullable();
            $table->boolean('isNot')->default(false);
            $table->integer('order')->default(1);

            $table->timestamps();
        });

        //endregion Table PenaltyRuleCondition

        //region Table UserRolePenaltyRule

        Schema::create('UserRolePenaltyRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('penaltyRule_id');
            $table->foreign('penaltyRule_id', 'uprulec_prule_fn')->references('id')->on('PenaltyRule')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'uprulec_ur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('cascade');

            $table->date('startDate');
            $table->date('endDate')->nullable();

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->string('workOrderProjectCode')->nullable();
            $table->string('workOrderCostCode')->nullable();
            $table->string('workOrderActivityCode')->nullable();
            $table->string('prefixCode')->nullable();
            $table->string('suffixCode')->nullable();

            $table->boolean('doesUseCurrentStaffPayType')->default(false);
            $table->boolean('doesUseHigherStaffPayType')->default(false);
            $table->bigInteger('payType_id')->unsigned()->nullable();
            $table->decimal('customAmount', 16, 10)->nullable();
            $table->char('customRateType', 1)->nullable();

            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table UserRolePenaltyRule

        //region Table TimeSheetDayTimePenalty

        Schema::create('TimeSheetDayTimePenalty', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheetDayTime_id');
            $table->foreign('timeSheetDayTime_id', 'tsdtp_pr_fn')->references('id')->on('TimeSheetDayTime')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('userRolePenaltyRule_id');
            $table->foreign('userRolePenaltyRule_id', 'tsdtp_urpr_fn')->references('id')->on('UserRolePenaltyRule')->onUpdate('cascade')->onDelete('restrict');

            $table->integer('minutes');
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table TimeSheetDayTimePenalty

        //region Table TimeSheetDayTimePenaltyCalculation

        Schema::create('TimeSheetDayTimePenaltyCalculation', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheetDayTimePenalty_id');
            $table->foreign('timeSheetDayTimePenalty_id', 'tsdtpc_tsdtp_fn')->references('id')->on('TimeSheetDayTimePenalty')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('originalTimeSheetDayTimePenaltyCalculation_id')->nullable();
            $table->foreign('originalTimeSheetDayTimePenaltyCalculation_id', 'tsdtpc_otsdtpc_fn')->references('id')->on('TimeSheetDayTimePenaltyCalculation')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'tsdtpc_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->string('actorName')->nullable();

            $table->boolean('isManual')->default(false);
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table TimeSheetDayTimePenaltyCalculation

        //region Table TimeSheetDayTimePenaltyCalculationItem

        Schema::create('TimeSheetDayTimePenaltyCalculationItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheetDayTimePenaltyCalculation_id');
            $table->foreign('timeSheetDayTimePenaltyCalculation_id', 'tsdtpci_tsdtpc_fn')->references('id')->on('TimeSheetDayTimePenaltyCalculation')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('timeSheetDayTimeWork_id')->nullable();
            $table->foreign('timeSheetDayTimeWork_id', 'tsdtpci_tsdtw_fn')->references('id')->on('TimeSheetDayTimeWork')->onUpdate('cascade')->onDelete('set null');

            $table->integer('minutes');
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table TimeSheetDayTimePenaltyCalculationItem
    }

    public function down(): void
    {
        Schema::dropIfExists('TimeSheetDayTimePenaltyCalculationItem');
        Schema::dropIfExists('TimeSheetDayTimePenaltyCalculation');
        Schema::dropIfExists('TimeSheetDayTimePenalty');
        Schema::dropIfExists('UserRolePenaltyRule');
        Schema::dropIfExists('PenaltyRuleCondition');
        Schema::dropIfExists('PenaltyRule');
    }
};
