<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PayRun

        Schema::table('PayRun', function (Blueprint $table) {
            $table->integer('fiscalYear')->after('payRunType_id')->nullable();
        });

        DB::statement('ALTER TABLE PayRun MODIFY COLUMN number INT(11) NOT NULL DEFAULT 1 AFTER fiscalYear');

        if (Schema::hasColumn('PayRun', 'processingDate')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('processingDate');
            });
        }

        //endregion Table: PayRun
    }

    public function down(): void
    {
        //region Table: PayRun

        if (Schema::hasColumn('PayRun', 'fiscalYear')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('fiscalYear');
            });
        }

        //endregion Table: PayRun
    }
};
