<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Settings\Models\Settings;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('sendSMSRemindersWorkflow')->nullable()->default(true);
            $table->text('sendSMSRemindersWorkflowMessage')->nullable();
            $table->boolean('sendSMSRemindersDailyTimeSheet')->nullable()->default(true);
            $table->text('sendSMSRemindersDailyTimeSheetMessage')->nullable();
            $table->boolean('sendSMSRemindersCloseTimeSheet')->nullable()->default(true);
            $table->text('sendSMSRemindersCloseTimeSheetMessage')->nullable();
            $table->boolean('allowUsersOptOutSMSReminders')->nullable()->default(true);
        });

        /** @var Settings[] $settings */
        $settings = DB::table('Settings')->get();
        foreach ($settings as $setting) {
            DB::table('Settings')
              ->where('id', $setting->id)
              ->update(
                  [
                      'sendSMSRemindersWorkflowMessage' => 'A new work item has been assigned to you in elementTIME. No time like the present, so login and deal with it. Go on.',
                      'sendSMSRemindersDailyTimeSheetMessage' => 'Where does the time go? Looks like you missed a day. Keep it simple and enter your time as you go.',
                      'sendSMSRemindersCloseTimeSheetMessage' => 'Money money money - you haven\'t submitted your time sheet yet and it closes in 4 hours. Submit it for approval now.',
                  ]
              );
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('Settings', 'sendSMSRemindersWorkflow')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersWorkflow');
            });
        }
        if (Schema::hasColumn('Settings', 'sendSMSRemindersWorkflowMessage')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersWorkflowMessage');
            });
        }
        if (Schema::hasColumn('Settings', 'sendSMSRemindersDailyTimeSheet')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersDailyTimeSheet');
            });
        }
        if (Schema::hasColumn('Settings', 'sendSMSRemindersDailyTimeSheetMessage')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersDailyTimeSheetMessage');
            });
        }
        if (Schema::hasColumn('Settings', 'sendSMSRemindersCloseTimeSheet')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersCloseTimeSheet');
            });
        }
        if (Schema::hasColumn('Settings', 'sendSMSRemindersCloseTimeSheetMessage')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('sendSMSRemindersCloseTimeSheetMessage');
            });
        }
        if (Schema::hasColumn('Settings', 'allowUsersOptOutSMSReminders')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowUsersOptOutSMSReminders');
            });
        }
    }
};
