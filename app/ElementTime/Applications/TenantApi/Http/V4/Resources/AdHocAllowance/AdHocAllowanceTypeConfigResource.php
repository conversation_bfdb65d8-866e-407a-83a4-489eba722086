<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance;

use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\CustomContent\CustomContentResource;
use Element\ElementTime\Domains\Tenant\Settings\Models\AllowanceType;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'AdHocAllowanceTypeConfigResource',
    title: 'Allowance Type Configuration',
    required: ['allowanceType_id', 'externalId', 'name', 'rateType', 'requiresTime', 'custom_content'],
    properties: [
        new OA\Property(property: 'allowanceType_id', type: 'int', example: 2),
        new OA\Property(property: 'externalId', type: 'string', example: '2'),
        new OA\Property(property: 'name', type: 'string', example: 'On call'),
        new OA\Property(property: 'rateType', type: 'string', example: 'H'),
        new OA\Property(property: 'requiresTime', type: 'bool', example: false),
        new OA\Property(property: 'custom_content', ref: '#/components/schemas/CustomContentResource'),
    ],
)]
class AdHocAllowanceTypeConfigResource extends Resource
{
    public static $relations = [
        ///
    ];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var AllowanceType $r */
        $r = $this->getModel();

        return  [
            'allowanceType_id' => $r->id,
            'externalId' => $r->externalId,
            'name' => $r->name,
            'rateType' => $r->rateType,
            'requiresTime' => $r->rateType == 'H' && $r->requiresTime,
            'custom_content' => CustomContentResource::make($r->getCustomContentBySlug('recording-as-owner')),
        ];
    }
}
