<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendLeaveRequestRelatedBroadcastEvents;

use Element\ElementTime\Domains\Tenant\Schedules\BroadcastEvents\ScheduledPayRunUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\Schedules\BroadcastEvents\UserRoleScheduleUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

class SendLeaveRequestRelatedBroadcastEventsZeroBased extends BaseSendLeaveRequestRelatedBroadcastEvents
{
    /**
     * @todo - Timesheet events
     * @todo - Timecard events
     */
    protected function sendBroadcastEvents(): void
    {
        if (!is_null($this->leave->scheduledPayRun)) {
            ScheduledPayRunUpdatedBroadcastEvent::fire($this->leave->scheduledPayRun);

            return;
        }

        $payRun = TenantSystemSettings::getPayRunByDate($this->leave->startDate);

        if (!is_null($payRun)) {
            $scheduledPayRun = ScheduledPayRun::q()
                ->constraints([
                    ['ScheduledPayRun.userRoleSchedule_id', '=', $this->userRoleSchedule->id],
                    ['ScheduledPayRun.payRun_id', '=', $payRun->id],
                ])
                ->first();

            if (!is_null($scheduledPayRun)) {
                ScheduledPayRunUpdatedBroadcastEvent::fire($scheduledPayRun);

                return;
            }
        }

        UserRoleScheduleUpdatedBroadcastEvent::fire($this->userRoleSchedule);
    }
}
