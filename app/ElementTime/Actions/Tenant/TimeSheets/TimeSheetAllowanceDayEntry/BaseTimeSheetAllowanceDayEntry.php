<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Element\Core\Exceptions\ElementException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;

class BaseTimeSheetAllowanceDayEntry extends BaseTimeSheetsAction
{
    /** @throws ElementException */
    protected function getValidatedDuration(float|TimeDuration|string|null $duration = null, TimeSheetWorkItem|null $timeSheetWorkItem = null): TimeDuration
    {
        if (
            is_null($duration)
            || (
                is_numeric($duration)
                && $duration <= 0
            ) || (
                $duration instanceof TimeDuration
                && $duration->lte(0)
            )
        ) {
            throw new ElementException('Number of hours for ad hoc allowance is missing');
        }

        $duration = TimeDuration::make($duration, 'auto');

        if (!is_null( $timeSheetWorkItem) && $duration->gt($timeSheetWorkItem->duration)) {
            throw new ElementException('Number of hours for ad hoc allowance is over the number of hours of work record');
        }

        return $duration;
    }

    /** @throws ElementException */
    protected function getValidatedMiles(float|null $miles): float
    {
        if (is_null($miles) || $miles <= 0) {
            throw new ElementException('Value of miles for ad hoc allowance is missing');
        }

        return $miles;
    }

    /** @throws ElementException */
    protected function getValidatedQuantity(float|null $quantity): float
    {
        if (is_null($quantity) || $quantity <= 0) {
            throw new ElementException('Value of quantity for ad hoc allowance is missing');
        }

        return $quantity;
    }

    /** @throws ElementException */
    protected function validateDaily(TimeSheetAllowanceDay $timeSheetAllowanceDay): void
    {
        if ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries->count() > 0) {
            throw new ElementException('More than one instance of a daily allowance in a day is not allowed');
        }
    }

    /** @throws ElementException */
    protected function getValidatedPeriod(CarbonInterface|string|null $startDateTime, CarbonInterface|string|null $endDateTime, TimeSheetWorkItem|null $timeSheetWorkItem): CarbonPeriod
    {
        if (is_null($startDateTime) || is_null($endDateTime)) {
            throw new ElementException('Start/end time for ad hoc allowance is missing');
        }

        $start = is_string($startDateTime) ? Carbon::parse($startDateTime) : $startDateTime->copy();
        $end = is_string($endDateTime) ? Carbon::parse($endDateTime) : $endDateTime->copy();

        if (
            !is_null($timeSheetWorkItem)
            && !is_null($timeSheetWorkItem->start)
            && (
                $timeSheetWorkItem->start->gt($start)
                || $timeSheetWorkItem->end->lt($end)
            )
        ) {
            throw new ElementException('Start/end time for ad hoc allowance is off the work record');
        }

        return CarbonPeriod::create($start, $end);
    }
}
