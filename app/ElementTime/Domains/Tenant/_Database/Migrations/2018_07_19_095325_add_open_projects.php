<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Project

        Schema::table('Project', function (Blueprint $table) {
            $table->boolean('isOpenToPayGroups')->default(false);
        });

        //endregion Table: Project

        //region Table: ProjectActivity

        Schema::table('ProjectActivity', function (Blueprint $table) {
            $table->boolean('isOpenToPayGroups')->default(false);
        });

        //endregion Table: ProjectActivity

        //region Table: TimeSheetItem

        Schema::table('TimeSheetItem', function (Blueprint $table) {
            $table->boolean('isOpen')->default(false);
            $table->string('customProjectCode')->nullable();
        });

        //endregion Table: TimeSheetItem

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesShowProjectCodesOnTimeSheet')->default(false);
            $table->boolean('doesAllowCustomProjectCodesOnTimeSheets')->default(false);
        });

        //endregion
    }

    public function down(): void
    {
        //region Table: Settings

        if (Schema::hasColumn('Settings', 'doesAllowCustomProjectCodesOnTimeSheets')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAllowCustomProjectCodesOnTimeSheets');
            });
        }

        if (Schema::hasColumn('Settings', 'doesShowProjectCodesOnTimeSheet')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesShowProjectCodesOnTimeSheet');
            });
        }

        //endregion Table: Settings

        //region Table: TimeSheetItem

        if (Schema::hasColumn('TimeSheetItem', 'customProjectCode')) {
            Schema::table('TimeSheetItem', function (Blueprint $table) {
                $table->dropColumn('customProjectCode');
            });
        }

        if (Schema::hasColumn('TimeSheetItem', 'isOpen')) {
            Schema::table('TimeSheetItem', function (Blueprint $table) {
                $table->dropColumn('isOpen');
            });
        }

        //endregion Table: TimeSheetItem

        //region Table: ProjectActivity

        if (Schema::hasColumn('ProjectActivity', 'isOpenToPayGroups')) {
            Schema::table('ProjectActivity', function (Blueprint $table) {
                $table->dropColumn('isOpenToPayGroups');
            });
        }

        //endregion Table: ProjectActivity

        //region Table: Project

        if (Schema::hasColumn('Project', 'isOpenToPayGroups')) {
            Schema::table('Project', function (Blueprint $table) {
                $table->dropColumn('isOpenToPayGroups');
            });
        }

        //endregion Table: Project
    }
};
