<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region -> Leave without pay - Task: https://www.wrike.com/open.htm?id=240340829

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('isWithoutPay')->default(false);
        });

        //endregion -> Leave without pay - Task: https://www.wrike.com/open.htm?id=240340829

        //region -> Negative balances allowed - Task: https://www.wrike.com/open.htm?id=194252494

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('hasNegativeBalancesAllowed')->default(false);
            $table->decimal('maxNegativeBalancesAllowed', 16, 10)->default(0);
        });

        //endregion -> Negative balances allowed - Task: https://www.wrike.com/open.htm?id=194252494
    }

    public function down(): void
    {
        //region -> Negative balances allowed - Task: https://www.wrike.com/open.htm?id=194252494

        if (Schema::hasColumn('LeaveType', 'maxNegativeBalancesAllowed')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('maxNegativeBalancesAllowed');
            });
        }
        if (Schema::hasColumn('LeaveType', 'hasNegativeBalancesAllowed')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('hasNegativeBalancesAllowed');
            });
        }

        //endregion -> Negative balances allowed - Task: https://www.wrike.com/open.htm?id=194252494

        //region -> Leave without pay - Task: https://www.wrike.com/open.htm?id=240340829

        if (Schema::hasColumn('LeaveType', 'isWithoutPay')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('isWithoutPay');
            });
        }

        //endregion -> Leave without pay - Task: https://www.wrike.com/open.htm?id=240340829
    }
};
