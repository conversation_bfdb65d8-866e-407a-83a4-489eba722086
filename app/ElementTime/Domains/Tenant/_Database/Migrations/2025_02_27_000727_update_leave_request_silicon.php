<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: LeaveRequest

        try {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropForeign('lr_us_fn');
            });
        } catch (\Throwable) {}

        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN userShift_id BIGINT UNSIGNED NULL DEFAULT 1 AFTER updated_at;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN startDateTime DATETIME NULL AFTER userShift_id;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN endDateTime DATETIME NULL AFTER startDateTime;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN glideDailyStartTime VARCHAR(255) NULL AFTER endDateTime;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN glideDailyEndTime VARCHAR(255) NULL AFTER glideDailyStartTime;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN minutes INT UNSIGNED NULL AFTER glideDailyEndTime;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN calculatedTotalHours DECIMAL(16,10) NULL AFTER minutes;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN minutesGlideDaily INT UNSIGNED NULL AFTER calculatedTotalHours;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN externalCalendarEmail TEXT NULL AFTER minutesGlideDaily;');
        DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN externalCalendarId TEXT NULL AFTER externalCalendarEmail;');

        /*
         * TODO:
         *  - Model / Config / Repository
         *  - Routes / Controllers
         *  - Calculations
         *  - Front-end
         *  - APIs
         *  - Data migration
         */
        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->string('scheduleType')->nullable()->after('userLeaveBankType_id'); // TODO: Make not nullable after all tenants are updated

            $table->unsignedBigInteger('userRoleSchedule_id')->nullable()->after('scheduleType'); // TODO: Make not nullable after all tenants are updated
            $table->foreign('userRoleSchedule_id', 'lr_urs_fn')
                ->references('id')->on('UserRoleSchedule')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('scheduledPayRun_id')->nullable()->after('userRoleSchedule_id');
            $table->foreign('scheduledPayRun_id', 'lr_spr_fn')
                ->references('id')->on('ScheduledPayRun')
                ->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('timeSheet_id')->nullable()->after('scheduledPayRun_id');
            $table->foreign('timeSheet_id', 'lr_ts_fn')
                ->references('id')->on('TimeSheet')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('leaveRequestCalculation_id')->nullable()->after('timeSheet_id');

            $table->date('startDate')->nullable()->after('isFullDay'); // TODO: Make not nullable after all tenants are updated
            $table->date('endDate')->nullable()->after('startDate'); // TODO: Make not nullable after all tenants are updated
            $table->time('duration')->nullable()->after('endDate'); // TODO: Make not nullable after all tenants are updated
            $table->time('durationAdjusted')->nullable()->after('duration'); // TODO: Make not nullable after all tenants are updated
        });

        //endregion Table: LeaveRequest

        //region Table: LeaveRequestDay

        try {
            Schema::table('LeaveRequestDay', function (Blueprint $table) {
                $table->dropForeign('lrd_us_fn');
            });
        } catch (\Throwable) {}
        try {
            Schema::table('LeaveRequestDay', function (Blueprint $table) {
                $table->dropForeign('lrd_usd_fn');
            });
        } catch (\Throwable) {}

        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN userShift_id BIGINT UNSIGNED NULL DEFAULT 1 AFTER updated_at;');
        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN userShiftDay_id BIGINT UNSIGNED NULL DEFAULT 1 AFTER userShift_id;');
        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN startDateTime DATETIME NULL AFTER userShiftDay_id;');
        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN endDateTime DATETIME NULL AFTER startDateTime;');
        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN minutes INT UNSIGNED NULL AFTER endDateTime;');
        DB::statement('ALTER TABLE LeaveRequestDay MODIFY COLUMN calculatedHours DECIMAL(16,10) NULL AFTER minutes;');

        /*
         * TODO:
         *  - Model / Config / Repository
         *  - Routes / Controllers
         *  - Calculations
         *  - Front-end
         *  - APIs
         *  - Data migration
         */
        Schema::table('LeaveRequestDay', function (Blueprint $table) {
            $table->string('userRoleScheduleDay_type')->nullable()->after('user_id');
            $table->unsignedBigInteger('userRoleScheduleDay_id')->nullable()->after('userRoleScheduleDay_type');

            $table->unsignedBigInteger('scheduledPayRunPeriodDay_id')->nullable()->after('userRoleScheduleDay_id');
            $table->foreign('scheduledPayRunPeriodDay_id', 'lrd_sprpd_fn')
                ->references('id')->on('ScheduledPayRunPeriodDay')
                ->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('timeSheetDay_id')->nullable()->after('scheduledPayRunPeriodDay_id');
            $table->foreign('timeSheetDay_id', 'lrd_tsd_fn')
                ->references('id')->on('TimeSheetDay')
                ->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('timeSheetPeriod_id')->nullable()->after('timeSheetDay_id');
            $table->foreign('timeSheetPeriod_id', 'lrd_tsp_fn')
                ->references('id')->on('TimeSheetPeriod')
                ->onUpdate('cascade')->onDelete('set null');

            $table->json('timeSpan')->nullable()->after('date');
            $table->time('duration')->nullable()->after('timeSpan'); // TODO: Make not nullable after all tenants are updated
            $table->time('durationAdjusted')->nullable()->after('duration'); // TODO: Make not nullable after all tenants are updated
            $table->json('breaks')->nullable()->after('durationAdjusted');
        });

        //endregion Table: LeaveRequestDay
    }

    public function down(): void
    {
        //region Table: LeaveRequestDay

        et_dropStuffIfExist(
            table: 'LeaveRequestDay',
            foreignKeys: [
                'lrd_sprpd_fn',
                'lrd_tsd_fn',
                'lrd_tsp_fn',
            ],
            columns: [
                'userRoleScheduleDay_type',
                'userRoleScheduleDay_id',
                'scheduledPayRunPeriodDay_id',
                'timeSheetDay_id',
                'timeSheetPeriod_id',
                'span', /// TODO - remove
                'timeSpan',
                'duration',
                'durationAdjusted',
                'breaks',
            ],
        );

        //endregion Table: LeaveRequestDay

        //region Table: LeaveRequest

        et_dropStuffIfExist(
            table: 'LeaveRequest',
            foreignKeys: [
                'lr_urs_fn',
                'lr_spr_fn',
                'lr_ts_fn',
            ],
            columns: [
                'scheduleType',
                'userRoleSchedule_id',
                'scheduledPayRun_id',
                'timeSheet_id',
                'leaveRequestCalculation_id',
                'payRunStartDate', /// TODO - remove
                'payRunEndDate', /// TODO - remove
                'startDate',
                'endDate',
                'duration',
                'durationAdjusted',
            ],
        );

        //endregion Table: LeaveRequest
    }
};
