<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: WorkflowTypeTreeApprovalStepItem

        Schema::table('WorkflowTypeTreeApprovalStepItem', function (Blueprint $table) {
            $table->boolean('areNotificationsDisabled')->default(false)->after('details');
        });

        //endregion Table: WorkflowTypeTreeApprovalStepItem

        //region Table: WorkflowStepItem

        Schema::table('WorkflowStepItem', function (Blueprint $table) {
            $table->boolean('areNotificationsDisabled')->default(false)->after('isDone');
        });

        //endregion Table: WorkflowStepItem

        //region Renaming old Notification/Reminder tables

        if (!Schema::hasTable('_OldNotificationType') && Schema::hasTable('NotificationType')) {
            Schema::rename('NotificationType', '_OldNotificationType');
        }

        if (!Schema::hasTable('_OldReminderType') && Schema::hasTable('ReminderType')) {
            Schema::rename('ReminderType', '_OldReminderType');
        }

        if (!Schema::hasTable('_OldNotification') && Schema::hasTable('Notification')) {
            Schema::rename('Notification', '_OldNotification');
        }

        //endregion Renaming old Notification/Reminder tables

        //region Table: NotificationType

        Schema::create('NotificationType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('type'); // ID from type class
            $table->unique('type', 'nottype_type_uq');

            // General Information
            $table->boolean('isActive')->default(true);
            $table->integer('order')->default(0);

            // Email
            $table->boolean('emailIsActive')->default(true);
            $table->string('emailSenderName')->nullable();
            $table->text('emailSenderEmail')->nullable();
            $table->text('emailSubject')->nullable();
            $table->longText('emailMessage')->nullable();

            // SMS
            $table->boolean('smsIsActive')->default(true);
            $table->string('smsSenderName')->nullable();
            $table->text('smsSubject')->nullable();
            $table->text('smsMessage')->nullable();

            // Push
            $table->boolean('pushIsActive')->default(true);
            $table->string('pushSenderName')->nullable();
            $table->text('pushSubject')->nullable();
            $table->text('pushMessage')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: NotificationType

        //region Table: NotificationTypeTrigger

        Schema::create('NotificationTypeTrigger', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('notificationType_id')->nullable();
            $table->foreign('notificationType_id', 'ntr_nt_fn')->references('id')->on('NotificationType')->onUpdate('cascade')->onDelete('cascade');

            // General Information
            $table->string('media'); // ID from media (email, sms or push)
            $table->string('type'); // ID from type class
            $table->longText('details')->nullable(); // JSON with rule details

            $table->timestamps();
        });

        //endregion Table: NotificationTypeTrigger

        //region Table: Notification

        Schema::create('Notification', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('notificationType_id')->nullable();
            $table->foreign('notificationType_id', 'notif_nt_fn')->references('id')->on('NotificationType')->onUpdate('cascade')->onDelete('set null');
            $table->string('slug');
            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();

            // Owner
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'notif_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // Email
            $table->boolean('hasEmail')->default(false);
            $table->string('emailDestinationName')->nullable();
            $table->text('emailDestinationEmail')->nullable();
            $table->string('emailSenderName')->nullable();
            $table->text('emailSenderEmail')->nullable();
            $table->text('emailSubject')->nullable();
            $table->longText('emailMessage')->nullable();

            // SMS
            $table->boolean('hasSms')->default(false);
            $table->string('smsDestinationName')->nullable();
            $table->string('smsDestinationMobile')->nullable();
            $table->string('smsSenderName')->nullable();
            $table->text('smsSubject')->nullable();
            $table->text('smsMessage')->nullable();

            // Push
            $table->boolean('hasPush')->default(false);
            $table->text('pushDestinationData')->nullable();
            $table->string('pushSenderName')->nullable();
            $table->text('pushSubject')->nullable();
            $table->text('pushMessage')->nullable();

            // Status
            $table->string('status')->default('new'); // ID of status class
            $table->dateTime('limitDateTime')->nullable();

            // elementSUP integration
            $table->string('supId')->nullable();
            $table->dateTime('supDateTime')->nullable();
            $table->longText('supResponses')->nullable();
            $table->string('supEmailId')->nullable();
            $table->string('supEmailStatus')->nullable(); // ID of status class
            $table->string('supSmsId')->nullable();
            $table->string('supSmsStatus')->nullable(); // ID of status class
            $table->string('supPushId')->nullable();
            $table->string('supPushStatus')->nullable(); // ID of status class

            $table->timestamps();
        });

        //endregion Table: Notification

        //region Table: NotificationTimelineEntry

        Schema::create('NotificationTimelineEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('notification_id');
            $table->foreign('notification_id', 'ntle_not_fn')->references('id')->on('Notification')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('actor_id')->nullable();
            $table->foreign('actor_id', 'ntle_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // Info
            $table->string('media')->nullable(); // email | sms | push | null
            $table->string('status')->nullable(); // ID of status class | or null if not status change entry
            $table->dateTime('dateTime');
            $table->longText('details')->nullable();
            $table->longText('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: NotificationTimelineEntry
    }

    public function down(): void
    {
        //region Table: NotificationTimelineEntry

        Schema::dropIfExists('NotificationTimelineEntry');

        //endregion Table: NotificationTimelineEntry

        //region Table: Notification

        if (Schema::hasTable('_OldNotification')) {
            Schema::dropIfExists('Notification');
        }

        //endregion Table: Notification

        //region Table: NotificationTypeTrigger

        Schema::dropIfExists('NotificationTypeTrigger');

        //endregion Table: NotificationTypeTrigger

        //region Table: NotificationType

        if (Schema::hasTable('_OldNotificationType')) {
            Schema::dropIfExists('NotificationType');
        }

        //endregion Table: NotificationType

        //region Renaming old Notification/Reminder tables

        if (Schema::hasTable('_OldNotification') && !Schema::hasTable('Notification')) {
            Schema::rename('_OldNotification', 'Notification');
        }

        if (Schema::hasTable('_OldReminderType') && !Schema::hasTable('ReminderType')) {
            Schema::rename('_OldReminderType', 'ReminderType');
        }

        if (Schema::hasTable('_OldNotificationType') && !Schema::hasTable('NotificationType')) {
            Schema::rename('_OldNotificationType', 'NotificationType');
        }

        //endregion Renaming old Notification/Reminder tables

        //region Table: WorkflowStepItem

        if (Schema::hasColumn('WorkflowStepItem', 'areNotificationsDisabled')) {
            Schema::table('WorkflowStepItem', function (Blueprint $table) {
                $table->dropColumn('areNotificationsDisabled');
            });
        }

        //endregion Table: WorkflowStepItem

        //region Table: WorkflowTypeTreeApprovalStepItem

        if (Schema::hasColumn('WorkflowTypeTreeApprovalStepItem', 'areNotificationsDisabled')) {
            Schema::table('WorkflowTypeTreeApprovalStepItem', function (Blueprint $table) {
                $table->dropColumn('areNotificationsDisabled');
            });
        }

        //endregion Table: WorkflowTypeTreeApprovalStepItem
    }
};
