<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2023 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\_System\Repositories;

use Element\Core\Support\Domains\Repositories\Repository;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;

/**
 * @method KioskDeviceRequest getModel()
 */
class KioskDeviceRequestRepository extends Repository
{
    /** @var KioskDeviceRequest */
    protected static $modelClass = KioskDeviceRequest::class;
}
