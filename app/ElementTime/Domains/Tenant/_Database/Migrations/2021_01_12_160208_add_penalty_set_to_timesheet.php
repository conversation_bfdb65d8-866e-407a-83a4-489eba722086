<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Settings\Support\PenaltyTypeRuleCategories\TimeRecordedCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: PenaltyType

        Schema::table('PenaltyType', function (Blueprint $table) {
            $table->string('ruleCategory')->default(TimeRecordedCategory::ID)->after('activityCode');
        });

        //endregion Table: PenaltyType

        //region Table: TimeSheet

        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesScheduled INT(10) UNSIGNED DEFAULT 0 AFTER payRunItem_id;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesRecorded INT(10) UNSIGNED DEFAULT 0 AFTER minutesScheduled;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesWorked INT(10) UNSIGNED DEFAULT 0 AFTER minutesRecorded;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesLeaveTaken INT(10) UNSIGNED DEFAULT 0 AFTER minutesWorked;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesRosteredTimeOffTaken INT(10) UNSIGNED DEFAULT 0 AFTER minutesLeaveTaken;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesAccruedHoursTaken INT(10) UNSIGNED DEFAULT 0 AFTER minutesRosteredTimeOffTaken;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesPublicHolidayTaken INT(10) UNSIGNED DEFAULT 0 AFTER minutesAccruedHoursTaken;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesMissing INT(10) UNSIGNED DEFAULT 0 AFTER minutesPublicHolidayTaken;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesExcessTimeAccrued INT(10) UNSIGNED DEFAULT 0 AFTER minutesMissing;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesExcessTimePaid INT(10) UNSIGNED DEFAULT 0 AFTER minutesExcessTimeAccrued;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesExcessTimeUnPaid INT(10) UNSIGNED DEFAULT 0 AFTER minutesExcessTimePaid;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN minutesAllowance INT(10) UNSIGNED DEFAULT 0 AFTER minutesExcessTimeUnPaid;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursRecorded DECIMAL(16,10) DEFAULT 0 AFTER minutesAllowance;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursWorked DECIMAL(16,10) DEFAULT 0 AFTER hoursRecorded;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursExcessTimePaidAdjusted DECIMAL(16,10) DEFAULT 0 AFTER hoursWorked;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueExcessTimePaid DECIMAL(16,10) DEFAULT 0 AFTER hoursExcessTimePaidAdjusted;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursExcessTimeAccruedAdjusted DECIMAL(16,10) DEFAULT 0 AFTER valueExcessTimePaid;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursExcessTimeUsed DECIMAL(16,10) DEFAULT 0 AFTER hoursExcessTimeAccruedAdjusted;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursLeaveEarnt DECIMAL(16,10) DEFAULT 0 AFTER hoursExcessTimeUsed;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursLeaveUsed DECIMAL(16,10) DEFAULT 0 AFTER hoursLeaveEarnt;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursRosteredTimeOffEarnt DECIMAL(16,10) DEFAULT 0 AFTER hoursLeaveUsed;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursRosteredTimeOffUsed DECIMAL(16,10) DEFAULT 0 AFTER hoursRosteredTimeOffEarnt;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN hoursAllowance DECIMAL(16,10) DEFAULT 0 AFTER hoursRosteredTimeOffUsed;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN milesAllowance DECIMAL(16,10) DEFAULT 0 AFTER hoursAllowance;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueOrdinaryHours DECIMAL(16,10) DEFAULT 0 AFTER milesAllowance;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueWorkedOrdinaryHours DECIMAL(16,10) DEFAULT 0 AFTER valueOrdinaryHours;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueLeaveTaken DECIMAL(16,10) DEFAULT 0 AFTER valueWorkedOrdinaryHours;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueAllowance DECIMAL(16,10) DEFAULT 0 AFTER valueLeaveTaken;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valuePenalty DECIMAL(16,10) DEFAULT 0 AFTER valueAllowance;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueDeduction DECIMAL(16,10) DEFAULT 0 AFTER valuePenalty;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueContribution DECIMAL(16,10) DEFAULT 0 AFTER valueDeduction;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueSpecialPay DECIMAL(16,10) DEFAULT 0 AFTER valueContribution;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN valueTax DECIMAL(16,10) DEFAULT 0 AFTER valueSpecialPay;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN recordMethod CHAR(1) DEFAULT NULL AFTER valueTax;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN doesAllowCustomProjectCodes TINYINT(1) NOT NULL DEFAULT 0 AFTER recordMethod;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN isGlide TINYINT(1) NOT NULL DEFAULT 0 AFTER doesAllowCustomProjectCodes;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN glideDurationType CHAR(1) DEFAULT NULL AFTER isGlide;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN recallRequestDateTime DATETIME DEFAULT NULL AFTER glideDurationType;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN recallRequestReason TEXT DEFAULT NULL AFTER recallRequestDateTime;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN isUpdatingDateTime DATETIME DEFAULT NULL AFTER recallRequestReason;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN created_at TIMESTAMP NULL DEFAULT NULL AFTER isUpdatingDateTime;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN updated_at TIMESTAMP NULL DEFAULT NULL AFTER created_at;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL AFTER updated_at;');

        // Move D_E_P_R_E_C_A_T_E_D fields to the bottom
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN arePenaltiesCalculating TINYINT(1) NOT NULL DEFAULT 0 AFTER deleted_at;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN arePenaltiesCalculated TINYINT(1) NOT NULL DEFAULT 0 AFTER arePenaltiesCalculating;');
        DB::statement('ALTER TABLE TimeSheet MODIFY COLUMN status CHAR(1) NULL DEFAULT NULL AFTER arePenaltiesCalculated;');

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->dateTime('penaltyCalculationsStartedDateTime')->nullable()->after('payRunItem_id');
            $table->dateTime('penaltyCalculationsFinishedDateTime')->nullable()->after('penaltyCalculationsStartedDateTime');
            $table->unsignedBigInteger('penaltyCalculationsActorUser_id')->nullable()->after('penaltyCalculationsStartedDateTime');
            $table->foreign('penaltyCalculationsActorUser_id', 'ts_pcau_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        //endregion Table: TimeSheet

        //region Table: TimeSheetPenaltySet

        Schema::create('TimeSheetPenaltySet', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheet_id');
            $table->foreign('timeSheet_id', 'tsps_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('userPenaltyType_id');
            $table->foreign('userPenaltyType_id', 'tsps_upt_fn')->references('id')->on('UserPenaltyType')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('isValid')->default(true);
            $table->dateTime('startedDateTime');
            $table->dateTime('finishedDateTime')->nullable();
            $table->decimal('totalValue', 16, 10)->default(0);

            $table->timestamps();

            $table->unique(['timeSheet_id', 'userPenaltyType_id'], 'tsps_ts_upt_uq');
        });

        //endregion Table: TimeSheetPenaltySet

        //region Table: TimeSheetPenaltySetControl

        Schema::create('TimeSheetPenaltySetControl', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenaltySet_id');
            $table->foreign('timeSheetPenaltySet_id', 'tspsc_tsps_fn')->references('id')->on('TimeSheetPenaltySet')->onUpdate('cascade')->onDelete('cascade');

            $table->string('rel_type');
            $table->unsignedBigInteger('rel_id');

            $table->dateTime('startedDateTime');
            $table->dateTime('finishedDateTime')->nullable();

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenaltySetControl

        //region Table: TimeSheetPenaltySetItem

        Schema::create('TimeSheetPenaltySetItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenaltySet_id');
            $table->foreign('timeSheetPenaltySet_id', 'tspsi_tsps_fn')->references('id')->on('TimeSheetPenaltySet')->onUpdate('cascade')->onDelete('cascade');

            $table->date('dayDate')->nullable();
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->decimal('value', 16, 10)->default(0);
            $table->decimal('valueAdjusted', 16, 10)->default(0);
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenaltySetItem

        //region Table: TimeSheetPenaltySetItemTrigger

        Schema::create('TimeSheetPenaltySetItemTrigger', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenaltySetItem_id');
            $table->foreign('timeSheetPenaltySetItem_id', 'tspsit_tspsi_fn')->references('id')->on('TimeSheetPenaltySetItem')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('penaltyTypeRule_id')->nullable();
            $table->foreign('penaltyTypeRule_id', 'tspsit_ptr_fn')->references('id')->on('PenaltyTypeRule')->onUpdate('cascade')->onDelete('set null');

            $table->string('rel_type');
            $table->unsignedBigInteger('rel_id');

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->integer('minutes')->nullable();

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenaltySetItemTrigger

        //region Table: TimeSheetPenaltySetItemAmendment

        Schema::create('TimeSheetPenaltySetItemAmendment', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetPenaltySetItem_id');
            $table->foreign('timeSheetPenaltySetItem_id', 'tspsia_tspsi_fn')->references('id')->on('TimeSheetPenaltySetItem')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'tspsia_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->dateTime('dateTime');
            $table->decimal('newValue', 16, 10);
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: TimeSheetPenaltySetItemAmendment
    }

    public function down(): void
    {
        Schema::dropIfExists('TimeSheetPenaltySetItemAmendment');
        Schema::dropIfExists('TimeSheetPenaltySetItemTrigger');
        Schema::dropIfExists('TimeSheetPenaltySetItem');
        Schema::dropIfExists('TimeSheetPenaltySetControl');
        Schema::dropIfExists('TimeSheetPenaltySet');

        //region Table: TimeSheet

        if (Schema::hasColumn('TimeSheet', 'penaltyCalculationsStartedDateTime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('penaltyCalculationsStartedDateTime');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'penaltyCalculationsFinishedDateTime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('penaltyCalculationsFinishedDateTime');
            });
        }

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('ts_pcau_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheet', 'penaltyCalculationsActorUser_id')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('penaltyCalculationsActorUser_id');
            });
        }

        //endregion Table: TimeSheet

        //region Table: PenaltyType

        if (Schema::hasColumn('PenaltyType', 'ruleCategory')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('ruleCategory');
            });
        }

        //endregion Table: PenaltyType
    }
};
