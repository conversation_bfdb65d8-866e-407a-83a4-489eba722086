<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('UserTimelineEntry', function (Blueprint $table) {
            $table->foreign('user_id', 'ute_user')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('UserTimelineEntry', function (Blueprint $table) {
                $table->dropForeign('ute_user');
            });
        } catch (Throwable) {
        }

        if (DB::select(DB::raw('SHOW KEYS FROM UserTimelineEntry WHERE Key_name=\'utentry_item\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('UserTimelineEntry', function (Blueprint $table) {
                $table->dropUnique('utentry_item');
            });
        }
    }
};
