<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendApprovalReminder;

use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Notifications\Support\NotificationTypeTypes\LeaveRequests\LeaveApprovalReminderType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowStep;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowStepItem;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowTimelineTypes\ApprovalReminderSentType;
use Illuminate\Support\Collection;

class SendApprovalReminder extends BaseLeavesAction
{
    /** @throws \Throwable */
    public function handle(User $actor, LeaveRequest $leaveRequest): void
    {
        if (!$this->canSendApprovalReminder($leaveRequest)) {
            return;
        }

        $activeWorkflowStep = $leaveRequest->repository->getFirstActiveStep();
        $notificationParameters = ['limitDateTime' => null];
        $approverNames = [];

        try {
            $notificationTargets = $this->getNotificationTargetsFromStep($activeWorkflowStep);

            foreach ($notificationTargets as $notificationTarget) {
                LeaveApprovalReminderType::notifyAsync(
                    user: $notificationTarget,
                    rel: $leaveRequest,
                    parameters: $notificationParameters
                );
                $approverNames[] = $notificationTarget->fullName;
            }
        } catch (\Throwable) {}

        $notes = 'Reminder to approve timesheet was sent';

        if (count($approverNames) > 0) {
            $notes .= ' to ' . implode(', ', $approverNames);
        }

        $notes .= ' by ' . $actor->fullName;

        $leaveRequest->repository->addWorkflowTimelineEntry(
            type: ApprovalReminderSentType::class,
            notes: $notes,
            actor: $actor,
        );
    }

    /**
     * @return Collection|User[]
     * @throws \Throwable
     */
    public function getNotificationTargetsFromStep(WorkflowStep $workflowStep): Collection|array
    {
        return $workflowStep->items
            ->filter(fn (WorkflowStepItem $stepItem) => !$stepItem->areNotificationsDisabled)
            ->filter(fn (WorkflowStepItem $stepItem) => !$stepItem->isDone)
            ->reduce(
                fn (Collection $staff, WorkflowStepItem $stepItem) => $staff->concat($stepItem->repository->getMatchingUsers()),
                Collection::make(),
            )
            ->unique();
    }

    public function canSendApprovalReminder(LeaveRequest $leaveRequest): bool
    {
        try {
            return $leaveRequest->repository->isSubmitted() || $leaveRequest->repository->isPartiallyApproved();
        } catch (\Throwable) {}

        return false;
    }
}
