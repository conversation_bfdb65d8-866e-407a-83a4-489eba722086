<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();
        Schema::table('User', function (Blueprint $table) {
            $table->text('loggedData')->nullable();
            // JSON Format:
            //  {
            //      "dateTime": "",
            //      "hash": "",
            //      "ip": "",
            //      "device": {
            //          "type": "", // Desktop / Mobile / Tablet
            //          "name": "", // Nexus, iPhone, AsusTablet...
            //      },
            //      "platform": {
            //          "name": "", // Ubuntu, Windows, OS X, iOS...
            //          "version": "" // Version number or name
            //      },
            //      "browser": {
            //          "name": "", // Chrome, Firefox
            //          "version": "" // Version number
            //      }
            //  }

            $table->longText('loginAttempts')->nullable();
            // JSON Format:
            //  [
            //      {
            //          "dateTime": "",
            //          "hash": "",
            //          "ip": "",
            //          "device": {
            //              "type": "", // Desktop / Mobile / Tablet
            //              "name": "", // Nexus, iPhone, AsusTablet...
            //          },
            //          "platform": {
            //              "name": "", // Ubuntu, Windows, OS X, iOS...
            //              "version": "" // Version number or name
            //          },
            //          "browser": {
            //              "name": "", // Chrome, Firefox
            //              "version": "" // Version number
            //          }
            //      },...
            //  ]
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'loggedData')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('loggedData');
            });
        }
        if (Schema::hasColumn('User', 'loginAttempts')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('loginAttempts');
            });
        }
    }
};
