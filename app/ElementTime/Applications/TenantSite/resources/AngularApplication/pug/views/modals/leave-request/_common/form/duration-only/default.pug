div
  .row
    .col-xs-12.col-md-4
      elt-form-date-field(
        ng-model='$ctrl.form.data.startDate'
        ng-required='true'
        ng-change='$ctrl.form.specific.$events.onChangeStartDate()'
        ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
        elt-label='Start date'
        elt-name='startDate'
        elt-date-picker-options='$ctrl.form.specific.options.startDate.datePicker'
        elt-form='$ctrl.form.form'
        elt-validation-service='$ctrl.form._v'
        elt-validations='[{validation: "required", message: "form.validate.FIELD_IS_REQUIRED"}]'
      )

    .col-xs-12.col-md-4
      elt-form-date-field(
        ng-model='$ctrl.form.data.endDate'
        ng-required='true'
        ng-change='$ctrl.form.specific.$events.onChangeEndDate()'
        ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
        elt-label='End date'
        elt-name='endDate'
        elt-date-picker-options='$ctrl.form.specific.options.endDate.datePicker'
        elt-form='$ctrl.form.form'
        elt-validation-service='$ctrl.form._v'
        elt-validations='[{validation: "required", message: "form.validate.FIELD_IS_REQUIRED"}]'
      )

    .col-xs-12.col-md-4
      .form-group
        label(for='Leave_duration') Daily duration (will apply only to scheduled days)
        input.form-control.pl.pr(
          ng-model="$ctrl.form.data.dailyDuration"
          ng-required='true'
          ng-change='$ctrl.form.specific.$events.onChangeDailyDuration()'
          ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
          type='text'
          id='Leave_duration'
          name='duration'
          input-mask
          input-options='{"name": "number", "min": 0, "max": 24, "allowMinus": false}'
        )
