<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->string('workOrderFullTitleMask')->default('[[wo_name]] - [[act_name]]')->after('fullFormattedCodeMask');
            $table->string('workOrderTitleMask')->default('[[wo_name]]')->after('workOrderFullTitleMask');
            $table->string('workOrderActivityTitleMask')->default('[[act_name]]')->after('workOrderTitleMask');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('Settings', 'workOrderFullTitleMask')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('workOrderFullTitleMask');
            });
        }

        if (Schema::hasColumn('Settings', 'workOrderTitleMask')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('workOrderTitleMask');
            });
        }

        if (Schema::hasColumn('Settings', 'workOrderActivityTitleMask')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityTitleMask');
            });
        }
    }
};
