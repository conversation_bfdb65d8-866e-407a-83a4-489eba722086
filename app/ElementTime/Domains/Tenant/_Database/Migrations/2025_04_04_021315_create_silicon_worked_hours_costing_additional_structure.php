<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('TimeSheetPenaltyItem', function (Blueprint $table) {
            $table->unsignedBigInteger('timeSheetWork_id')->nullable()->after('calculationMethod');
            $table->foreign('timeSheetWork_id', 't_tspi_tsw_fn')
                ->references('id')->on('TimeSheetWork')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->decimal('value', 16,10)->nullable()->after('durationAdjusted'); // Only for fixed hourly
        });

        /// TODO:
        ///  - Related auto-allowance structure change
        ///  - Timesheet issues
        ///  - Deprecate old structure... :(
    }

    public function down(): void
    {
        et_dropStuffIfExist(
            table: 'TimeSheetPenaltyItem',
            foreignKeys: [
                't_tspi_tsw_fn',
            ],
            columns: [
                'timeSheetWork_id',
            ],
        );
    }
};
