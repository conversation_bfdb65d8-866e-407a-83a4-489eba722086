<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\TimeCalculations;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays\GetScheduledDaysWorkPattern;
use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeaveRequestPreAvailabilityErrorType\LeaveRequestPreAvailabilityErrorType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestAvailabilityClashedWork;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestDayPreAvailability;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayWorkPattern;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeType\ModelActionTypeType;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeValidationFlag\ModelActionTypeValidationFlagLeave;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class CalculateLeavePreAvailabilityFromScheduleWorkPattern extends BaseCalculateLeavePreAvailabilityFromSchedule
{
    /**
     * @throws \Throwable
     */
    public function handle(
        UserLeaveBankType $userLeaveBankType,
        UserRoleSchedule $userRoleSchedule,
        bool $isFullDay,
        CarbonInterface $startDate,
        CarbonInterface|null $endDate = null, // Only if full day and multiple days leave request
        string|null $dailyStartTime = null, // Only if not full day
        string|null $dailyEndTime = null, // Only if not full day and applying by time frame
        TimeDuration|null $dailyDuration = null, // Only if not full day and applying by duration
        LeaveRequest|null $replaced = null,
        User|null $actorUser = null,
    ): StructLeaveRequestPreAvailability
    {
        $this->validateUserLeaveBankType($userLeaveBankType);

        if (!$userRoleSchedule->type->is(UserRoleScheduleType::WorkPattern)) {
            throw new \InvalidArgumentException('This schedule is not a work pattern');
        }

        if (
            !$isFullDay
            && (
                is_null($dailyStartTime)
                || (
                    is_null($dailyEndTime)
                    && is_null($dailyDuration)
                )
            )
        ) {
            throw new InvalidArgumentException("Partial leave requires a daily time span/duration");
        }

        if (is_null($endDate) || !$isFullDay) {
            $endDate = $startDate->copy();
        }

        $this->checkUserRoleScheduleAvailability($userRoleSchedule, $startDate, $endDate);

        if (is_null($actorUser)) {
            $actorUser = $userLeaveBankType->bank->user;
        }

        $scheduledDaysData = GetScheduledDaysWorkPattern::make()->handle($userRoleSchedule, $startDate, $endDate);
        $holidays = $this->getHolidays($userRoleSchedule, $userLeaveBankType, $startDate, $endDate);

        $relevantDaysData = $this->getRelevantDaysData(
            $userLeaveBankType,
            $userRoleSchedule,
            $scheduledDaysData,
            $holidays,
            $startDate,
            $endDate,
            $isFullDay,
            $dailyStartTime,
            $dailyEndTime,
            $dailyDuration,
        );

        $ret = StructLeaveRequestPreAvailability::make();

        if (count($relevantDaysData) < 1) {
            $ret->canBeRequested = false;
            $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::NotScheduled, 'Not scheduled to work on selected period');

            return $ret;
        }

        $period = $this->getPeriod($relevantDaysData);

        $doesHaveDayTimeAdjustments = !$startDate->isSameDay($period->start) || !$endDate->isSameDay($period->end);
        $doesHaveBreakDeductions = false;

        $ret->scheduleType = $userRoleSchedule->type;
        $ret->canBeRequested = true;
        $ret->warningMessages = [];

        $ret->userLeaveBankType_id = $userLeaveBankType->id;
        $ret->userRoleSchedule_id = $userRoleSchedule->id;
        $ret->isFullDay = $isFullDay;
        $ret->hasDays = true;

        $ret->startDate = $period->start;
        $ret->endDate = $period->end;

        $ret->days = $this->makeDays(
            $userLeaveBankType,
            $userRoleSchedule,
            $holidays,
            $isFullDay,
            $relevantDaysData,
            $doesHaveDayTimeAdjustments,
            $doesHaveBreakDeductions,
            $dailyStartTime,
            $dailyEndTime,
            $dailyDuration,
            $duration,
            $durationAdjusted,
        );

        $ret->doesHaveDayTimeAdjustments = $doesHaveDayTimeAdjustments;
        $ret->doesHaveBreakDeductions = $doesHaveBreakDeductions;

        $ret->duration = $duration;
        $ret->durationAdjusted = $durationAdjusted;
        $ret->doesHaveFteAdjustments = $duration->neq($durationAdjusted);

        $clashedLeaveRequestIds = $this->getClashedLeaveRequestIds($ret, $userRoleSchedule, $replaced);

        if (count($clashedLeaveRequestIds) > 0) {
            $ret->canBeRequested = false;
            $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::ClashedLeaveRequests, 'There is already another request(s) that would clash with this one. You need to alter the requests so they do not clash. You can do this by editing the existing request(s) or changing this one.', ['ids' => $clashedLeaveRequestIds]);

            return $ret;
        }

        $this->calculateBalances($userLeaveBankType, $replaced, $ret);
        $this->calculateNegativeBalancesAvailability($userLeaveBankType, $actorUser, $ret);

        $ret->clashedWorks = $this->getClashedWorks($ret, $userRoleSchedule);

        $model = new LeaveRequest;
        $model->scheduleType = $userRoleSchedule->type;
        $model->actor_id = $actorUser->id;
        $model->userLeaveBankType_id = $userLeaveBankType->id;
        $model->userRoleSchedule_id = $userRoleSchedule->id;
        $model->isFullDay = $isFullDay;
        $model->startDate = $ret->startDate->copy();
        $model->endDate = $ret->endDate->copy();
        $model->duration = $ret->duration;
        $model->durationAdjusted = $ret->durationAdjusted;

        $submitModelActionFlags = ModelActionTypeType::Leave->getValidationFlags(ModelActionTypeValidationFlagLeave::Submit, $model, [$userLeaveBankType->bank->user], ['days' => $ret->days]);
        $approveModelActionFlags = ModelActionTypeType::Leave->getValidationFlags(ModelActionTypeValidationFlagLeave::Approve, $model, [$userLeaveBankType->bank->user], ['days' => $ret->days]);

        $ret->doesRequireAttachmentsOnSubmit = $submitModelActionFlags->isAttachmentRequired;
        $ret->doesRequireAttachmentsOnApprove = $approveModelActionFlags->isAttachmentRequired;
        $ret->doesRequireCommentsOnSubmit = $submitModelActionFlags->isCommentRequired;
        $ret->doesRequireCommentsOnApprove = $approveModelActionFlags->isCommentRequired;

        return $ret;
    }

    // -----------------------------------

    /**
     * @param Collection|StructScheduledDayWorkPattern[] $scheduledDaysData
     * @param Collection|Holiday[]|PublicHoliday[] $holidays
     * @return Collection<StructScheduledDayWorkPattern>|StructScheduledDayWorkPattern[]
     * @throws \Throwable
     */
    protected function getRelevantDaysData(
        UserLeaveBankType $userLeaveBankType,
        UserRoleSchedule $userRoleSchedule,
        Collection|array $scheduledDaysData,
        Collection|array $holidays,
        CarbonInterface $startDate,
        CarbonInterface $endDate,
        bool $isFullDay,
        string|null $dailyStartTime,
        string|null $dailyEndTime,
        TimeDuration|null $dailyDuration,
    ): Collection|array
    {
        $ret = Collection::make();

        /*
         * TODO: Add to the mix:
         *  - Schedule settings around leave full-day calculations
         *  - Leave assignment settings around taking leave on holidays
         */

        foreach ($scheduledDaysData as $scheduledDaysDatum) {
            if (!$scheduledDaysDatum->isScheduled) {
                continue;
            }

            if ($scheduledDaysDatum->isRdo) {
                continue;
            }

            if (!$scheduledDaysDatum->date->betweenIncluded($startDate , $endDate)) {
                continue;
            }

            if (!$isFullDay) {
                $partDayLeaveTimeSpanOnDay = !is_null($dailyEndTime)
                    ? TimeSpan::createFromStartAndEnd($dailyStartTime, $dailyEndTime)->setDate($scheduledDaysDatum->date)
                    : TimeSpan::createFromStartAndDuration($dailyStartTime, $dailyDuration)->setDate($scheduledDaysDatum->date);
                $ordinarySpan = $scheduledDaysDatum->ordinarySpan->copy()->setDate($scheduledDaysDatum->date);

                if (!$ordinarySpan->doesOverlap($partDayLeaveTimeSpanOnDay)) {
                    continue;
                }
            }

            $dayHolidays = $holidays->filter(fn (PublicHoliday|Holiday $holiday) => $holiday->date->isSameDay($scheduledDaysDatum->date));

            if ($dayHolidays->contains(fn (PublicHoliday|Holiday $holiday) => $holiday instanceof PublicHoliday || $holiday->isFullDay)) {
                continue;
            }

            if ($scheduledDaysDatum->hasFixedHours) {
                $duration = $scheduledDaysDatum->duration;

                if ($duration->lte(TimeDuration::zero())) {
                    continue;
                }
            } else {
                $maxDuration = $scheduledDaysDatum->maxDuration;

                if ($maxDuration->lte(TimeDuration::zero())) {
                    continue;
                }

                if ($scheduledDaysDatum->doesHaveExpectedTimeSpan) {
                    $expectedDuration = $scheduledDaysDatum->expectedSpan->getDuration();
                } else {
                    $expectedDuration = $scheduledDaysDatum->expectedDuration;
                }

                if ($expectedDuration->lte(TimeDuration::zero())) {
                    continue;
                }
            }

            $ret->push($scheduledDaysDatum);
        }

        return $ret;
    }

    /**
     * @param Collection<StructScheduledDayWorkPattern> $scheduledDaysData
     */
    protected function getPeriod(Collection $scheduledDaysData): CarbonPeriod|null
    {
        return CarbonPeriod::createFromArray([
            $scheduledDaysData->first()->date,
            $scheduledDaysData->last()->date,
        ]);
    }

    /**
     * @param Collection<StructScheduledDayWorkPattern>|StructScheduledDayWorkPattern[] $relevantDaysData
     * @return StructLeaveRequestDayPreAvailability[]
     * @throws \Throwable
     */
    protected function makeDays(
        UserLeaveBankType $userLeaveBankType,
        UserRoleSchedule $userRoleSchedule,
        Collection|array $holidays,
        bool $isFullDay,
        Collection|array $relevantDaysData,
        bool &$doesHaveDayTimeAdjustments,
        bool &$doesHaveBreakDeductions,
        string|null $dailyStartTime = null,
        string|null $dailyEndTime = null,
        TimeDuration $dailyDuration = null,
        TimeDuration|null &$duration = null,
        TimeDuration|null &$durationAdjusted = null,
    ): array
    {
        $days = [];

        if (is_null($duration)) {
            $duration = TimeDuration::zero();
        }

        if (is_null($durationAdjusted)) {
            $durationAdjusted = TimeDuration::zero();
        }

        foreach ($relevantDaysData as $relevantDayData) {
            $fteRatio = $this->getFteRatio($isFullDay, $userLeaveBankType, $relevantDayData);
            $dayCalculation = $isFullDay
                ? $userRoleSchedule->leaveCalculations->calculateDay($relevantDayData)
                : $this->makeDayCalculation(
                    $relevantDayData,
                    $doesHaveDayTimeAdjustments,
                    $dailyStartTime,
                    $dailyEndTime,
                    $dailyDuration,
                );

            $leaveRequestDayStruct = $this->makeDayFromDayCalculation(
                $dayCalculation,
                $relevantDayData->date,
                $holidays,
                $fteRatio,
            );

            if (is_null($leaveRequestDayStruct)) {
                continue;
            }

            $duration->add($leaveRequestDayStruct->duration);
            $durationAdjusted->add($leaveRequestDayStruct->durationAdjusted);

            if ($leaveRequestDayStruct->breaks->getList()->count() > 0) {
                $doesHaveBreakDeductions = true;
            }

            $days[] = $leaveRequestDayStruct;
        }

        return $days;
    }

    /**
     * TODO - Check LeaveCalculation method - holiday/weekend/weekday
     * TODO - Overall calculation
     * @throws \Throwable
     */
    protected function makeDayFromDayCalculation(
        array $dayCalculation,
        CarbonInterface $date,
        Collection|array $holidays,
        float $fteRatio,
    ): StructLeaveRequestDayPreAvailability|null
    {
        $ret = new StructLeaveRequestDayPreAvailability;

        if ($holidays->count() > 0) {
            $allHolidaySpans = Collection::make();

            foreach ($holidays as $holiday) {
                if (!$holiday instanceof Holiday || is_null($holiday->startDateTime) || is_null($holiday->endDateTime)) {
                    continue;
                }

                if ($dayCalculation['timeSpan']->doesOverlap(TimeSpan::make($holiday->startDateTime, $holiday->endDateTime))) {
                    $allHolidaySpans->push(TimeSpan::createFromStartAndEnd($holiday->startDateTime, $holiday->endDateTime));
                }
            }

            $mergedHolidaySpans = TimeSpanList::makeFromMergedTimeSpans($allHolidaySpans);

            foreach ($mergedHolidaySpans->getList() as $holidaySpan) {
                if ($holidaySpan->doesContain($dayCalculation['timeSpan'])) {
                    return null;
                }

                if ($holidaySpan->doesContain($dayCalculation['timeSpan']->getStartAsCarbon())) {
                    $dayCalculation['timeSpan']->setStart($holidaySpan->getEndAsCarbon());
                }

                if ($holidaySpan->doesContain($dayCalculation['timeSpan']->getEndAsCarbon())) {
                    $dayCalculation['timeSpan']->setEnd($holidaySpan->getStartAsCarbon());
                }
            }

            $duration = $dayCalculation['timeSpan']->getDuration();

            foreach ($dayCalculation['breaks']->getList() as $i => $break) {
                if ($dayCalculation['timeSpan']->doesContain($break)) {
                    $duration->deduct($break->getDuration());

                    continue;
                }

                if ($dayCalculation['timeSpan']->doesContain($break->getStartAsCarbon())) {
                    $duration->deduct($break->getStartAsCarbon()->diff($dayCalculation['timeSpan']->getEndAsCarbon()));
                    $dayCalculation['timeSpan']->setEnd($break->getStartAsCarbon());
                }

                if ($dayCalculation['timeSpan']->doesContain($break->getEndAsCarbon())) {
                    $duration->deduct($dayCalculation['timeSpan']->getStartAsCarbon()->diff($break->getEndAsCarbon()));
                    $dayCalculation['timeSpan']->setStart($break->getEndAsCarbon());
                }

                $dayCalculation['breaks']->removeTimeSpan($i);
            }

            if ($duration->lte(TimeDuration::zero())) {
                return null;
            }

            $dayCalculation['duration'] = $duration;
        }

        $ret->date = $date;
        $ret->timeSpan = $dayCalculation['timeSpan'];
        $ret->duration = $dayCalculation['duration'];
        $ret->durationAdjusted = $dayCalculation['duration']->copy()->multiply($fteRatio);
        $ret->breaks = $dayCalculation['breaks'];

        return $ret;
    }

    /** @throws \Throwable */
    #[ArrayShape(['duration' => TimeDuration::class, 'timeSpan' => TimeSpan::class, 'breaks' => TimeSpanList::class])]
    protected function makeDayCalculation(
        StructScheduledDayWorkPattern $dayData,
        &$doesHaveDayTimeAdjustments,
        string|null $dailyStartTime,
        string|null $dailyEndTime,
        TimeDuration|null $dailyDuration,
    ): array
    {
        $ordinarySpan = $dayData->ordinarySpan->copy()->setDate($dayData->date);
        $originalStart = $dayData->date->copy()->setTimeFromTimeString($dailyStartTime);
        $partDayLeaveStart = $dayData->date->copy()->setTimeFromTimeString($dailyStartTime);
        $dayBreaks = $dayData->breaks->setDate($dayData->date)->duplicate();

        if (!$ordinarySpan->doesContain($partDayLeaveStart)) {
            $partDayLeaveStart = $dayData->date->copy()->setTimeFrom($ordinarySpan->copy()->getStartAsCarbon());
            $doesHaveDayTimeAdjustments = true;
        }

        foreach ($dayBreaks->getList() as $break) {
            if ($break->doesContain($partDayLeaveStart)) {
                $partDayLeaveStart = $dayData->date->copy()->setTimeFrom($break->getEndAsCarbon());
                $doesHaveDayTimeAdjustments = true;

                break;
            }
        }

        $maxDuration = $dayData->hasFixedHours
            ? $dayData->duration->copy()
            : $dayData->maxDuration->copy();

        $duration = is_null($dailyDuration) || $dailyDuration->gt($maxDuration)
            ? $maxDuration->copy()
            : $dailyDuration->copy();

        $maxEndTime = TimeCalculations::recalculateEndTimeWithBreaks(
            $partDayLeaveStart,
            $originalStart->copy()->add($duration->getValue()),
            $dayBreaks->duplicate(),
            $ordinarySpan->copy()->setDate($dayData->date)->getEndAsCarbon(),
        );

        if (!is_null($dailyEndTime)) {
            $dayCalculation = $this->calculatePartDayLeaveFromStartAndEnd($partDayLeaveStart, $dailyEndTime, $maxEndTime, $dayData);
            $doesHaveDayTimeAdjustments = $doesHaveDayTimeAdjustments || !$partDayLeaveStart->copy()->setTimeFromTimeString($dailyEndTime)->eq($dayCalculation['timeSpan']->getEndAsCarbon());
        } else {
            $dayCalculation = $this->calculatePartDayLeaveFromStartAndDuration($partDayLeaveStart, $duration, $maxEndTime, $dayData);
            $doesHaveDayTimeAdjustments = $doesHaveDayTimeAdjustments || $dayCalculation['duration']->neq($dailyDuration);
        }

        return $dayCalculation;
    }

    /** @throws \Throwable */
    #[ArrayShape(['duration' => TimeDuration::class, 'timeSpan' => TimeSpan::class, 'breaks' => TimeSpanList::class])]
    protected function calculatePartDayLeaveFromStartAndEnd(
        CarbonInterface $leaveStart,
        string $dailyEnd,
        CarbonInterface $maxEnd,
        StructScheduledDayWorkPattern $dayData,
    ): array
    {
        $leaveEnd = $leaveStart->copy()->setTimeFromTimeString($dailyEnd);
        $leaveTimeSpan = TimeSpan::createFromStartAndEnd($leaveStart, $leaveEnd)->setDate($dayData->date->copy());
        $scheduledBreaks = TimeSpanList::make();

        if ($leaveTimeSpan->getEndAsCarbon()->gt($maxEnd)) {
            $leaveTimeSpan->setEnd($maxEnd->copy());
        }

        foreach ($dayData->breaks->getList() as $break) {
            if ($break->doesContain($leaveTimeSpan->getEndAsCarbon())) {
                $leaveTimeSpan->setEnd($break->getStartAsCarbon()->copy());
            }

            if ($leaveTimeSpan->doesContain($break)) {
                $scheduledBreaks->addTimeSpan($break->copy());
            }
        }

        return [
            'timeSpan' => $leaveTimeSpan,
            'breaks' => $scheduledBreaks,
            'duration' => $leaveTimeSpan->getDuration()->deduct($scheduledBreaks->getDuration()),
        ];
    }

    /** @throws \Throwable */
    #[ArrayShape(['duration' => TimeDuration::class, 'timeSpan' => TimeSpan::class, 'breaks' => TimeSpanList::class])]
    protected function calculatePartDayLeaveFromStartAndDuration(
        CarbonInterface $start,
        TimeDuration $duration,
        CarbonInterface $maxEnd,
        StructScheduledDayWorkPattern $dayData,
    ): array
    {
        $timeSpan = TimeSpan::createFromStartAndEnd(
            $start,
            TimeCalculations::recalculateEndTimeWithBreaks($start, $start->copy()->add($duration->getValue()), $dayData->breaks, $maxEnd),
        )->setDate($dayData->date->copy());

        $breaks = TimeSpanList::make($dayData->breaks->getList()->where(fn (TimeSpan $break) => $timeSpan->doesContain($break)));

        return [
            'timeSpan' => $timeSpan,
            'duration' => $timeSpan->getDuration()->deduct($breaks->getDuration()),
            'breaks' => $breaks,
        ];
    }

    /** @return StructLeaveRequestAvailabilityClashedWork[] */
    protected function getClashedWorks(StructLeaveRequestPreAvailability $availability, UserRoleSchedule $userRoleSchedule): array
    {
        $works = TimeSheetWork::q()
            ->constraints([
                ['UserRoleTimeType.userRole_id', '=', $userRoleSchedule->userRole_id],
                ['TimeSheetDay.date', '>=', $availability->startDate->toDateTimeString()],
                ['TimeSheetDay.date', '<=', $availability->endDate->toDateTimeString()],
            ])
            ->relations([
                'userRoleTimeType.timeType',
                'items.model',
                'items.sub',
            ])
            ->many();

        if ($works->count() < 1) {
            return [];
        }

        $ret = [];

        foreach ($availability->days as $day) {
            if ($availability->isFullDay && $userRoleSchedule->userRole->user->settings->doesFullDayLeaveReplacesAllRecordedHours) {
                $clashedWorks = $works->where(fn (TimeSheetWork $work) => $work->timeSheetDay->date->isSameDay($day->date));
            } else {
                $clashedWorks = $works->where(fn (TimeSheetWork $work) => $work->start->lt($day->timeSpan->getEndAsCarbon()) && $work->end->gt($day->timeSpan->getStartAsCarbon()));
            }

            foreach ($clashedWorks as $clashedWork) {
                $newClashedWork = new StructLeaveRequestAvailabilityClashedWork;
                $newClashedWork->timeSheetWork_id = $clashedWork->id;
                $newClashedWork->timeSheetDay_id = $clashedWork->timeSheetDay_id;
                $newClashedWork->timeTypeName = $clashedWork->userRoleTimeType->timeType->name;
                $newClashedWork->date = $clashedWork->timeSheetDay->date;
                $newClashedWork->duration = $clashedWork->workDuration->copy();

                $ret[] = $newClashedWork;
            }
        }

        return $ret;
    }

    protected function getClashedLeaveRequestIds(StructLeaveRequestPreAvailability $availability, UserRoleSchedule $userRoleSchedule, LeaveRequest|null $replaced = null): array
    {
        $constraints = [
            ['LeaveRequest.userRoleSchedule_id', '=', $userRoleSchedule->id],
            ['LeaveRequestDay.date', '>=', $availability->startDate->toDateTimeString()],
            ['LeaveRequestDay.date', '<=', $availability->endDate->toDateTimeString()],
            ['IN', 'Workflow.status', [
                NewType::ID,
                SubmittedType::ID,
                PartiallyApprovedType::ID,
                ApprovedType::ID,
            ]],
        ];

        if (!is_null($replaced)) {
            $constraints[] = ['LeaveRequestDay.leaveRequest_id', '<>', $replaced->id];
        }

        $leaveRequestDays = LeaveRequestDay::q()
            ->constraints($constraints)
            ->many();

        if (count($leaveRequestDays) < 1) {
            return [];
        }

        $ids = [];

        if ($availability->isFullDay) {
            foreach ($availability->days as $day) {
                $clashedDays = $leaveRequestDays->where(fn (LeaveRequestDay $leaveRequestDay) => $leaveRequestDay->date->isSameDay($day->date));

                foreach ($clashedDays as $clashedDay) {
                    if (in_array($clashedDay->leaveRequest_id, $ids)) {
                        continue;
                    }

                    $ids[] = $clashedDay->leaveRequest_id;
                }
            }

            return $ids;
        }

        foreach ($availability->days as $day) {
            $clashedDays = $leaveRequestDays->where(fn (LeaveRequestDay $leaveRequestDay) => $day->timeSpan->doesOverlap($leaveRequestDay->timeSpan));

            foreach ($clashedDays as $clashedDay) {
                if (in_array($clashedDay->leaveRequest_id, $ids)) {
                    continue;
                }

                $ids[] = $clashedDay->leaveRequest_id;
            }
        }

        return $ids;
    }

    protected function getFteRatio(bool $isFullDay, UserLeaveBankType $userLeaveBankType, StructScheduledDayWorkPattern $dayData): float
    {
        if (!$dayData->isFte) {
            return 1;
        }

        if ($isFullDay && !$userLeaveBankType->doesApplyFteAdjustmentsOnFullDayLeave) {
            return 1;
        }

        if (!$isFullDay && !$userLeaveBankType->doesApplyFteAdjustmentsOnPartDayLeave) {
            return 1;
        }

        return $dayData->fteRatio;
    }
}
