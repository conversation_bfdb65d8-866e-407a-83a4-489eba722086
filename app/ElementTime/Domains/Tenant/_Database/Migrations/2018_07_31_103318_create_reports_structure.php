<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ReportFile

        Schema::create('ReportFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('reportType_id'); // From Admin database

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'rf_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->string('slug')->nullable();
            $table->string('fileName')->nullable();
            $table->string('fileType')->nullable();
            $table->string('downloadFileName')->nullable();
            $table->dateTime('validity');
            $table->longText('data')->nullable(); // json formatted data

            $table->timestamps();
        });

        //endregion Table: ReportFile

        //region Table: ReportFileRequest

        Schema::create('ReportFileRequest', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('reportFile_id');
            $table->foreign('reportFile_id', 'rfr_rf_fn')->references('id')->on('ReportFile')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'rfr_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->dateTime('requestDateTime')->nullable();
            $table->boolean('wasNotified')->default(false);
            $table->dateTime('notifyDateTime')->nullable();
            $table->boolean('wasDelivered')->default(false);
            $table->char('deliverType')->nullable(); // E (Email) | D (Download) | V (View)
            $table->dateTime('deliverDateTime')->nullable();

            $table->timestamps();
        });

        //endregion Table: ReportFileRequest

        //region Table: ReportFileView

        Schema::create('ReportFileView', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('reportFile_id');
            $table->foreign('reportFile_id', 'rfv_rf_fn')->references('id')->on('ReportFile')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'rfv_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->dateTime('dateTime')->nullable();
            $table->char('method', 1); // E (Email) | D (Download) | V (View)

            $table->timestamps();
        });
        //endregion Table: ReportFileView
    }

    public function down(): void
    {
        //region Table: ReportFileView

        Schema::dropIfExists('ReportFileView');

        //endregion Table: ReportFileView

        //region Table: ReportFileRequest

        Schema::dropIfExists('ReportFileRequest');

        //endregion Table: ReportFileRequest

        //region Table: ReportFile

        Schema::dropIfExists('ReportFile');

        //endregion Table: ReportFile
    }
};
