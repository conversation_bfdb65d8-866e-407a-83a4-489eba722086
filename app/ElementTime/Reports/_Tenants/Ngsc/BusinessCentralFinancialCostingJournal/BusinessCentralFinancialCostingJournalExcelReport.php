<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\_Tenants\Ngsc\BusinessCentralFinancialCostingJournal;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantSheetDayTime;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTimeWork;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class BusinessCentralFinancialCostingJournalExcelReport extends TenantExcelReport
{
    use HasPayRunParameters;

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'ngsc-business-central-financial-costing-journal';
    const TYPE_SLUG = 'ngsc-business-central-financial-costing-journal.xls';

    public $c = BusinessCentralFinancialCostingJournalReportConfig::class;
    private string $formattedCodeSeparator = '.';

    protected function buildReportData(): void
    {
        $data = static::collectReportData($this->payRun);
        $this->eagerLoadData($data);

        $payRunIdString = $this->getPayRunIdString($this->payRun);

        $this->sheets = [];
        $this->sheets[] = $this->buildOrdinaryHoursOnCostData($data, $payRunIdString);
        $this->sheets[] = $this->buildPlantHoursOnCostData($data, $payRunIdString);
    }

    private static function collectReportData(PayRun $payRun): Collection|array
    {
        return TimeSheetDayTime::q(
            constraints: [
                ['PayRunItem.payRun_id', '=', $payRun->id],
            ],
            relations: [],
            joins: [
                ['TimeSheet', 'TimeSheet.id', '=', 'TimeSheetDay.timeSheet_id', 'inner'],
                ['PayRunItem', 'PayRunItem.id', '=', 'TimeSheet.payRunItem_id', 'inner'],
                ['User', 'User.id', '=', 'PayRunItem.user_id', 'inner'],
            ],
            order: [
                'User.externalId ASC',
                'TimeSheetDay.date ASC',
            ],
        )->many();
    }

    private function eagerLoadData(Collection|array $timeSheetDayTimes): void
    {
        $timeSheetDayTimes->loadMissing([
            'userShiftDay.userShift.user',
            'works.excessTimeItemRuleWorks.rule.item.timeSheetExcessTime.items',
            'works.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'works.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'works.workOrderType.workOrderTypeActivityTypes.activityType',
            'works.activityType',
            'works.plantTimes.plantSheet.plantItem.plantClass',
            'excessTimeWorks.rule.item.timeSheetExcessTime.items',
            'model.project',
            'type.project',
            'plantTimes.plantSheet.plantItem.plantClass',
        ]);
    }

    /* @param Collection|TimeSheetDayTime[] $timeSheetDayTimes */
    private function buildOrdinaryHoursOnCostData(Collection|array $timeSheetDayTimes, string $payRunIdString): \stdClass
    {
        $this->setSheetTitle($sheet, 'Ordinary hours oncost');

        $ordinaryHoursData = new \stdClass;
        $ordinaryHoursData->type = 'data-table';
        $ordinaryHoursData->columns = [
            $this->createColumn('Posting Date'),
            $this->createColumn('Document Date'),
            $this->createColumn('Document Type'),
            $this->createColumn('Account Type'),
            $this->createColumn('Account No.'),
            $this->createColumn('GST Prod Posting Group'),
            $this->createColumn('Document No.'),
            $this->createColumn('External Document No.'),
            $this->createColumn('Description'),
            $this->createFloatColumn('Worked amount'),
            $this->createFloatColumn('Oncost amount'),
            $this->createColumn('Dimension 1'),
            $this->createColumn('Dimension 2'),
            $this->createColumn('Dimension 3'),
            $this->createColumn('Dimension 4'),
            $this->createColumn('Dimension 5'),
            $this->createColumn('Dimension 6'),
            $this->createColumn('Dimension 7'),
            $this->createColumn('Dimension 8'),
            $this->createFloatColumn('Quantity'),
        ];
        $ordinaryHoursData->data = [];
        $offsetHours = 0;
        $offsetAmount = 0;
        $offsetOnCostAmount = 0;
        $externalDocumentNo = 'eTIME ' . $payRunIdString;
        $postingDate = $this->parseDate($this->payRun->endDate);

        /** @var object{amount: float, onCostAmount: float, hours: float, description: string, date: Carbon|CarbonInterface}[] $records */
        $records = [];
        foreach ($timeSheetDayTimes as $timeSheetDayTime) {
            if (!$timeSheetDayTime->userShiftDay->userShift->userRole->timeRecordingMethodClass::HAS_WORK_ORDERS) {
                continue;
            }

            if (!is_null($timeSheetDayTime->userShiftDay->publicHoliday_id)) {
                // Anything recorded in a public holiday should be removed from report. This was requested in a task:
                // https://www.wrike.com/open.htm?id=1672768849
                continue;
            }

            $userShiftDay = $timeSheetDayTime->userShiftDay;
            $hours = $timeSheetDayTime->finalCalculatedHours;

            foreach ($timeSheetDayTime->works as $work) {
                $hours -= $work->hours;
                $workHours = $work->hours;
                foreach ($work->excessTimeItemRuleWorks as $excessTimeItemRuleWork) {
                    if (!$excessTimeItemRuleWork->rule->item->isActive) {
                        continue;
                    }

                    $workHours -= $excessTimeItemRuleWork->actualHours;
                }

                if ($workHours <= 0) {
                    continue;
                }

                $workAmount = static::getTimeSheetDayTimeAmount($workHours, $userShiftDay);
                $workOnCostAmount = static::getOnCostAmount($workAmount);
                $dayTimeWorkRecord = static::getOrSetOrdinaryHoursRecord($records, $work, $userShiftDay, $this->formattedCodeSeparator);

                $dayTimeWorkRecord->hours += $workHours;
                $dayTimeWorkRecord->amount += $workAmount;
                $dayTimeWorkRecord->onCostAmount += $workOnCostAmount;

                $offsetHours += $workHours;
                $offsetAmount += $workAmount;
                $offsetOnCostAmount += $workOnCostAmount;
            }

            foreach ($timeSheetDayTime->excessTimeWorks as $excessTimeWork) {
                if (!$excessTimeWork->rule->item->isActive) {
                    continue;
                }

                $hours -= $excessTimeWork->actualHours;
            }

            if ($hours <= 0) {
                continue;
            }

            $dayTimeAmount = static::getTimeSheetDayTimeAmount($hours, $userShiftDay);
            $dayTimeOnCostAmount = static::getOnCostAmount($dayTimeAmount);
            $dayTimeRecord = static::getOrSetOrdinaryHoursRecord($records, $timeSheetDayTime, $userShiftDay, $this->formattedCodeSeparator);

            $dayTimeRecord->hours += $hours;
            $dayTimeRecord->amount += $dayTimeAmount;
            $dayTimeRecord->onCostAmount += $dayTimeOnCostAmount;

            $offsetHours += $hours;
            $offsetAmount += $dayTimeAmount;
            $offsetOnCostAmount += $dayTimeOnCostAmount;
        }

        foreach ($records as $record) {
            $ordinaryHoursData->data[] = [
                'config' => [],
                'data' => [
                    $postingDate, // Posting date
                    $this->parseDate($record->date), // Document date
                    '', // Document type
                    'G/L Account', // Account type
                    '31104', // Account No.
                    '', // GST Prod Posting Group
                    '', // Document No.
                    $externalDocumentNo, // External Document No.
                    $record->description, // Description
                    $record->amount, // Worked Amount
                    $record->onCostAmount, // Oncost Amount
                    ...$record->formattedCodeParts, // Dimension 1 .. 4
                    '', // Dimension 5
                    '', // Dimension 6
                    '', // Dimension 7
                    '', // Dimension 8
                    $record->hours,
                ],
            ];
        }

        $ordinaryHoursData->data[] = [
            'config' => [],
            'data' => [
                $postingDate, // Posting date
                $postingDate, // Document Date
                '', // Document type
                'G/L Account', // Account type
                '31601', // Account No.
                '', // GST Prod Posting Group
                '', // Document No.
                $externalDocumentNo, // External Document No.
                'Oncost recovery ' . $payRunIdString, // Description
                -1 * $offsetAmount, // Amount
                -1 * $offsetOnCostAmount,
                'A000023', // Dimension 1
                'C000013', // Dimension 2
                '', // Dimension 3
                '', // Dimension 4
                '', // Dimension 5
                '', // Dimension 6
                '', // Dimension 7
                '', // Dimension 8
                $offsetHours,
            ],
        ];

        $sheet->data[] = $ordinaryHoursData;

        return $sheet;
    }

    private function getPayRunIdString(PayRun $payRun): string
    {
        return $payRun->number . ' ' . $this->parseDate($payRun->startDate) . '-' . $this->parseDate($payRun->endDate);
    }

    private static function getTimeSheetDayTimeAmount(float $hours, UserShiftDay $userShiftDay): float
    {
        return $hours * $userShiftDay->hourlyRate;
    }

    private static function getOnCostAmount(float $amount, float $onCostPercentage = 51.9): float
    {
        return $amount * ($onCostPercentage / 100);
    }

    private static function getPlantSheetDayTimeAmount(PlantSheetDayTime $plantSheetDayTime): float
    {
        if (is_null($plantSheetDayTime->hours)) {
            return 0;
        }

        return $plantSheetDayTime->hours;
    }

    /* @return object{amount: float, onCostAmount: float, hours: float, description: string, formattedCodeParts: string, date: Carbon|CarbonInterface} */
    private static function getOrSetOrdinaryHoursRecord(array &$records, TimeSheetDayTime|TimeSheetDayTimeWork $work, UserShiftDay $userShiftDay, string $separator): \stdClass
    {
        $date = $userShiftDay->date;
        $key = $userShiftDay->userShift->user_id . '_' . $date->toDateString() . '_' . $work->formattedCode;

        if (!isset($records[$key])) {
            $user = $userShiftDay->userShift->user;

            $newRecord = new \stdClass();
            $newRecord->amount = 0;
            $newRecord->hours = 0;
            $newRecord->onCostAmount = 0;
            $newRecord->description = $user->nameFirst . ' ' . $user->nameLast . ' ' . $user->externalId;
            $newRecord->date = $date;
            $newRecord->formattedCodeParts = static::getFormattedCodeParts($work->formattedCode, $separator);

            $records[$key] = $newRecord;
        }

        return $records[$key];
    }

    private static function getFormattedCodeParts(string $formattedCode, string $separator): array
    {
        $codeParts = explode($separator, $formattedCode);

        return [
            $codeParts[0] ?? null, // Dimension 1
            $codeParts[1] ?? null, // Dimension 2
            $codeParts[2] ?? null, // Dimension 3
            $codeParts[3] ?? null  // Dimension 4
        ];
    }

    /**
     * @param Collection|TimeSheetDayTime[] $times
     */
    private function buildPlantHoursOnCostData(Collection|array $times, string $payRunIdString): \stdClass
    {
        $this->setSheetTitle($sheet, 'Plant hours oncost');
        $plantHoursData = new \stdClass;
        $plantHoursData->type = 'data-table';
        $plantHoursData->columns = [
            $this->createColumn('Posting Date'),
            $this->createColumn('Document Date'),
            $this->createColumn('Document Type'),
            $this->createColumn('Account Type'),
            $this->createColumn('Account No.'),
            $this->createColumn('GST Prod Posting Group'),
            $this->createColumn('Document No.'),
            $this->createColumn('External Document No.'),
            $this->createColumn('Description'),
            $this->createFloatColumn('Worked Amount'),
            $this->createFloatColumn('Oncost Amount'),
            $this->createColumn('Dimension 1'),
            $this->createColumn('Dimension 2'),
            $this->createColumn('Dimension 3'),
            $this->createColumn('Dimension 4'),
            $this->createColumn('Dimension 5'),
            $this->createColumn('Dimension 6'),
            $this->createColumn('Dimension 7'),
            $this->createColumn('Dimension 8'),
            $this->createFloatColumn('Quantity'),
        ];
        $plantHoursData->data = [];
        $offsetHours = 0;
        $offsetAmount = 0;
        $offsetOnCostAmount = 0;
        $externalDocumentNo = 'eTIME ' . $payRunIdString;
        $postingDate = $this->parseDate($this->payRun->endDate);
        $records = [];

        foreach ($times as $time) {
            if (!is_null($time->userShiftDay->publicHoliday_id)) {
                // Anything recorded in a public holiday should be removed from report. This was requested in a task:
                // https://www.wrike.com/open.htm?id=1672768849
                continue;
            }

            $userShiftDay = $time->userShiftDay;
            foreach ($time->plantTimes as $plantTime) {
                if (!is_null($plantTime->timeSheetDayTimeWork_id)) {
                    continue;
                }

                $amount = static::getPlantSheetDayTimeAmount($plantTime);
                $onCostAmount = $amount * $plantTime->plantItem->hoursCost;
                $hours = $plantTime->hours;
                $record = static::getOrSetPlantHoursRecord($records, $plantTime, $userShiftDay, $this->formattedCodeSeparator);
                $record->hours += $hours;
                $record->amount += $amount;
                $record->onCostAmount += $onCostAmount;

                $offsetAmount += $amount;
                $offsetHours += $hours;
                $offsetOnCostAmount += $onCostAmount;
            }

            foreach ($time->works as $work) {
                foreach ($work->plantTimes as $workPlantTime) {
                    $workPlantAmount = static::getPlantSheetDayTimeAmount($workPlantTime);
                    $workPlantOnCostAmount = $workPlantAmount * $workPlantTime->plantItem->hoursCost;
                    $workPlantHours = $workPlantTime->hours;
                    $record = static::getOrSetPlantHoursRecord($records, $workPlantTime, $userShiftDay, $this->formattedCodeSeparator);
                    $record->amount += $workPlantAmount;
                    $record->onCostAmount += $workPlantOnCostAmount;
                    $record->hours += $workPlantHours;

                    $offsetAmount += $workPlantAmount;
                    $offsetOnCostAmount += $workPlantOnCostAmount;
                    $offsetHours += $workPlantHours;
                }
            }
        }

        foreach ($records as $record) {
            $plantHoursData->data[] = [
                'config' => [],
                'data' => [
                    $postingDate, // Posting date
                    $this->parseDate($record->date), // Document date
                    '', // Document type
                    'G/L Account', // Account type
                    '32907', // Account No.
                    '', // GST Prod Posting Group
                    '', // Document No
                    $externalDocumentNo, // External Document No.
                    $record->description, // Description
                    $record->amount, // Amount
                    $record->onCostAmount, // Oncost amount
                    ...$record->formattedCodeParts, // Dimension 1 .. 4
                    $record->externalId, // Dimension 5
                    '', // Dimension 6
                    '', // Dimension 7
                    '', // Dimension 8
                    $record->hours, // Quantity
                ],
            ];
        }

        $plantHoursData->data[] = [
            'config' => [],
            'data' => [
                $postingDate, // Posting date
                '', // Document date
                '', // Document type
                'G/L Account', // Account type
                '32908', // Account No.
                '', // GST Prod Posting Group
                '', // Document No.
                $externalDocumentNo, // External Document No.
                'Plant recovery ' . $payRunIdString, // Description
                $offsetAmount, // Amount
                $offsetOnCostAmount, // Oncost amount
                'A000023', // Dimension 1
                'C000013', // Dimension 2
                '', // Dimension 3
                '', // Dimension 4
                '', // Dimension 5
                '', // Dimension 6
                '', // Dimension 7
                '', // Dimension 8
                $offsetHours,
            ],
        ];

        $sheet->data[] = $plantHoursData;

        return $sheet;
    }

    /**
     * @return object{externalId: string, date: Carbon, amount: float, onCostAmount: float, hours: float, description: string, assetNumber: string, formattedCodeParts: string}
     */
    private static function getOrSetPlantHoursRecord(array &$records, PlantSheetDayTime $plantTime, UserShiftDay $userShiftDay, string $separator): \stdClass
    {
        $date = $userShiftDay->date;
        $key = $plantTime->plantItem->id . '_' . $userShiftDay->userShift->user_id . '_' . $date->toDateString() . '_' . $plantTime->formattedCode;

        if (!isset($records[$key])) {
            $user = $userShiftDay->userShift->user;
            $description = substr($user->externalId . ' - ' . $plantTime->plantItem->title, 0, 100);

            $newRecord = new \stdClass;
            $newRecord->externalId = $plantTime->plantItem->plantNumber;
            $newRecord->date = $userShiftDay->date;
            $newRecord->amount = 0;
            $newRecord->hours = 0;
            $newRecord->onCostAmount = 0;
            $newRecord->description = $description;
            $newRecord->assetNumber = $plantTime->plantItem->assetNumber;
            $newRecord->formattedCodeParts = static::getFormattedCodeParts($plantTime->formattedCode, $separator);

            $records[$key] = $newRecord;
        }

        return $records[$key];
    }
}
