<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ExcessTimeGroup

        Schema::table('ExcessTimeGroup', function (Blueprint $table) {
            $table->boolean('isManual')->default(true)->after('description');
        });

        //endregion Table: ExcessTimeGroup

        //region Table: ExcessTimeGroupSectionLevelRule

        Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
            $table->unsignedBigInteger('excessTimeRule_id')->nullable()->after('id');
            $table->foreign('excessTimeRule_id', 'etgslr_etr_fn')
                ->references('id')
                ->on('ExcessTimeRule')
                ->onUpdate('cascade')
                ->onDelete('restrict');

            $table->string('workOrderProjectCode')->nullable()->after('activityCode');
            $table->string('workOrderCostCode')->nullable()->after('workOrderProjectCode');
            $table->string('workOrderActivityCode')->nullable()->after('workOrderCostCode');
        });

        //endregion Table: ExcessTimeGroupSectionLevelRule

        //region Table: ExcessTimeRule

        Schema::table('ExcessTimeRule', function (Blueprint $table) {
            $table->string('workOrderProjectCode')->nullable()->after('activityCode');
            $table->string('workOrderCostCode')->nullable()->after('workOrderProjectCode');
            $table->string('workOrderActivityCode')->nullable()->after('workOrderCostCode');
        });

        //endregion Table: ExcessTimeRule

        //region Table: TimeSheetExcessTimeItemRuleWork

        Schema::create('TimeSheetExcessTimeItemRuleWork', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTimeItemRule_id');
            $table->foreign('timeSheetExcessTimeItemRule_id', 'tsetirw_tsetir_fn')
                ->references('id')->on('TimeSheetExcessTimeItemRule')
                ->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('timeSheetDayTime_id')->nullable();
            $table->foreign('timeSheetDayTime_id', 'tsetirw_tsdt_fn')
                ->references('id')->on('TimeSheetDayTime')
                ->onUpdate('cascade')->onDelete('set null');
            $table->unsignedBigInteger('timeSheetDayTimeWork_id')->nullable();
            $table->foreign('timeSheetDayTimeWork_id', 'tsetirw_tsdtw_fn')
                ->references('id')->on('TimeSheetDayTimeWork')
                ->onUpdate('cascade')->onDelete('set null');

            $table->unsignedInteger('originalMinutes');
            $table->unsignedInteger('actualMinutes');
            $table->decimal('adjustedHours', 16, 10);

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeItemRuleWork

        //region Table: PenaltyType

        Schema::table('PenaltyType', function (Blueprint $table) {
            $table->string('workOrderProjectCode')->nullable()->after('activityCode');
            $table->string('workOrderCostCode')->nullable()->after('workOrderProjectCode');
            $table->string('workOrderActivityCode')->nullable()->after('workOrderCostCode');
        });

        //endregion Table: PenaltyType
    }

    public function down(): void
    {
        //region Table: PenaltyType

        if (Schema::hasColumn('PenaltyType', 'workOrderProjectCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('workOrderProjectCode');
            });
        }
        if (Schema::hasColumn('PenaltyType', 'workOrderCostCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('workOrderCostCode');
            });
        }
        if (Schema::hasColumn('PenaltyType', 'workOrderActivityCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityCode');
            });
        }

        //endregion Table: PenaltyType

        //region Table: TimeSheetExcessTimeItemRuleWork

        Schema::dropIfExists('TimeSheetExcessTimeItemRuleWork');

        //endregion Table: TimeSheetExcessTimeItemRuleWork

        //region Table: ExcessTimeRule

        if (Schema::hasColumn('ExcessTimeRule', 'workOrderProjectCode')) {
            Schema::table('ExcessTimeRule', function (Blueprint $table) {
                $table->dropColumn('workOrderProjectCode');
            });
        }
        if (Schema::hasColumn('ExcessTimeRule', 'workOrderCostCode')) {
            Schema::table('ExcessTimeRule', function (Blueprint $table) {
                $table->dropColumn('workOrderCostCode');
            });
        }
        if (Schema::hasColumn('ExcessTimeRule', 'workOrderActivityCode')) {
            Schema::table('ExcessTimeRule', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityCode');
            });
        }

        //endregion Table: ExcessTimeRule

        //region Table: ExcessTimeGroupSectionLevelRule

        try {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropForeign('etgslr_etr_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'excessTimeRule_id')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('excessTimeRule_id');
            });
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'workOrderProjectCode')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('workOrderProjectCode');
            });
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'workOrderCostCode')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('workOrderCostCode');
            });
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'workOrderActivityCode')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityCode');
            });
        }

        //endregion Table: ExcessTimeGroupSectionLevelRule

        //region Table: ExcessTimeGroup

        if (Schema::hasColumn('ExcessTimeGroup', 'isManual')) {
            Schema::table('ExcessTimeGroup', function (Blueprint $table) {
                $table->dropColumn('isManual');
            });
        }

        //endregion Table: ExcessTimeGroup
    }
};
