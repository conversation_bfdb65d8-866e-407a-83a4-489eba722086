<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SplitLeaveRequest;

use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;

class SplitLeaveRequestWorkPattern extends BaseSplitLeaveRequest
{
    /**
     * @param LeaveRequestDay $leaveRequestDay
     * @param ScheduledPayRunPeriod|null $scheduledPayRunPeriod
     * @return LeaveRequestDay
     */
    protected function makeSplitPartLeaveRequestDay(LeaveRequestDay $leaveRequestDay, ScheduledPayRunPeriod|null $scheduledPayRunPeriod): LeaveRequestDay
    {
        $day = parent::makeSplitPartLeaveRequestDay($leaveRequestDay, $scheduledPayRunPeriod);

        $day->timeSpan = $leaveRequestDay->timeSpan->copy();
        $day->breaks = $leaveRequestDay->breaks?->duplicate();
        $day->isFullDay = $leaveRequestDay->isFullDay;

        return $day;
    }
}
