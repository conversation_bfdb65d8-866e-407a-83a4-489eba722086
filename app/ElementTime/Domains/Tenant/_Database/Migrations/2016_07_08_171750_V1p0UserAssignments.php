<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        // Modifications on table User
        Schema::table('User', function (Blueprint $table) {
            $table->float('maximumHoursPerDay')->nullable();
            $table->boolean('hasMinimumHoursBetweenShifts')->nullable();
            $table->float('minimumHoursBetweenShifts')->nullable();
        });

        // Table UserProject
        Schema::create('UserProject', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('project_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->boolean('appliesDefaultStartDateToActivities')->nullable();
            $table->boolean('appliesDefaultEndDateToActivities')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('project_id', 'up_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'up_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        // Pivot Table UserProject_ProjectActivity
        Schema::create('UserProject_ProjectActivity', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('projectActivity_id')->unsigned();
            $table->bigInteger('userProject_id')->unsigned();

            $table->date('startDate')->nullable()->default(null);
            $table->date('endDate')->nullable()->default(null);
            $table->boolean('hasProjectStartDate')->default(true);
            $table->boolean('hasProjectEndDate')->default(true);
            $table->char('status', 1)->nullable()->default('A');

            $table->primary(['projectActivity_id', 'userProject_id'], 'up_pa_primary');
            $table->foreign('projectActivity_id', 'up_pa_pa_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userProject_id', 'up_pa_up_fn')->references('id')->on('UserProject')->onUpdate('cascade')->onDelete('cascade');
        });

        // Table UserLeaveType
        Schema::create('UserLeaveType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('leaveType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('leaveType_id', 'user_leavetype_lt_fn')->references('id')->on('LeaveType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'user_leavetype_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        // Table UserAllowanceType
        Schema::create('UserAllowanceType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('allowanceType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('allowanceType_id', 'user_allowancetype_at_fn')->references('id')->on('AllowanceType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'user_allowancetype_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        // Table UserAdditionalDutyType
        Schema::create('UserAdditionalDutyType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('additionalDutyType_id')->unsigned()->nullable();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('user_id', 'user_adt_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('additionalDutyType_id', 'user_adt_adt_fn')->references('id')->on('AdditionalDutyType')->onUpdate('cascade')->onDelete('cascade');
        });

        // Table UserOvertimeType
        Schema::create('UserOvertimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('overtimeType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->nullable();

            $table->foreign('overtimeType_id', 'user_overtimetype_lt_fn')->references('id')->on('OvertimeType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'user_overtimetype_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('User', function (Blueprint $table) {
            $table->dropColumn([
                'maximumHoursPerDay',
                'hasMinimumHoursBetweenShifts',
                'minimumHoursBetweenShifts',
            ]);
        });
        Schema::drop('UserProject');
        Schema::drop('UserProject_ProjectActivity');
        Schema::drop('UserLeaveType');
        Schema::drop('UserAllowanceType');
        Schema::drop('UserAdditionalDutyType');
        Schema::drop('UserOvertimeType');
    }
};
