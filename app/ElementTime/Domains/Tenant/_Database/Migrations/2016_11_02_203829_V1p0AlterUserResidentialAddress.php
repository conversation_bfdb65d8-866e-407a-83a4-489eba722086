<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('User', function (Blueprint $table) {
            // Residential Address
            $table->boolean('residentialSame')->nullable();
            $table->string('residentialPostalCode')->nullable();
            $table->string('residentialBuilding')->nullable();
            $table->string('residentialStreetAddress')->nullable();
            $table->string('residentialAdditionalDetails')->nullable();
            $table->string('residentialSuburb')->nullable();
            $table->string('residentialCity')->nullable();
            $table->string('residentialState')->nullable();
            $table->string('residentialCountry')->nullable();
            $table->string('residentialLatitude')->nullable();
            $table->string('residentialLongitude')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('User', function (Blueprint $table) {
            $table->dropColumn([
                'residentialSame',
                'residentialPostalCode',
                'residentialBuilding',
                'residentialStreetAddress',
                'residentialAdditionalDetails',
                'residentialSuburb',
                'residentialCity',
                'residentialState',
                'residentialCountry',
                'residentialLatitude',
                'residentialLongitude',
            ]);
        });
    }
};
