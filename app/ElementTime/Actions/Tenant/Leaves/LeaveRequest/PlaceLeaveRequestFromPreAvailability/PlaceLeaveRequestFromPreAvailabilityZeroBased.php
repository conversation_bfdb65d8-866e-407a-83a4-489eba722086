<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendLeaveRequestRelatedBroadcastEvents\SendLeaveRequestRelatedBroadcastEventsZeroBased;

class PlaceLeaveRequestFromPreAvailabilityZeroBased extends BasePlaceLeaveRequestFromPreAvailability
{
    protected function setLeaveData(): void
    {
        $this->leave->startDate = $this->preAvailability->startDate;
        $this->leave->endDate = $this->preAvailability->endDate;
    }

    protected function generateDays(): void
    {
        /// Zero-based leave doesn't have days
    }

    protected function sendBroadcastEvents(): void
    {
        SendLeaveRequestRelatedBroadcastEventsZeroBased::dispatch($this->leave)->afterResponse();
    }
}
