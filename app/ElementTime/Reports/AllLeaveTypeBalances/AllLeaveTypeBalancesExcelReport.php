<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\AllLeaveTypeBalances;

use Carbon\Carbon;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\LeaveType;
use Element\ElementTime\Domains\Tenant\Settings\Models\RosteredTimeOffType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class AllLeaveTypeBalancesExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
        UserSystemAccessFlags\StaffManagerFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'all-leave-type-balances';
    const TYPE_SLUG = 'all-leave-type-balances.xls';

    /** @var string|AllLeaveTypeBalancesReportConfig */
    public $c = AllLeaveTypeBalancesReportConfig::class;

    private Carbon|null $dateTime;

    private Department|null $department;

    /** @var User[]|Collection $users */
    private array|Collection $users;

    /** @var LeaveType[]|Collection $leaveTypes */
    private array|Collection $leaveTypes;

    /** @var RosteredTimeOffType[]|Collection $rosteredTimeOffTypes */
    private array|Collection $rosteredTimeOffTypes;

    public function setData(array $info): void
    {
        $this->department = Department::q()->findOrFail($info['idDepartment']);
        $this->dateTime = Carbon::now();
    }

    public function getReplacedString(string $string):string
    {
        $string = parent::getReplacedString($string);

        if (isset($this->department) && $this->department instanceof Department) {
            // department.id
            $string = Str::replace('{{department.id}}', $this->department->id, $string);

            // department.name
            $string = Str::replace('{{department.name}}', $this->department->name, $string);
        }

        return $string;
    }

    /** @throws \Throwable */
    private function collectReportData(): void
    {
        $constraints = [
            ['User.id', '<>', 1],
            ['EmployeeType.doesHideLeave', '=', false],
            [
                [
                    ['IS NULL', 'User.startDate', 'OR'],
                    ['User.startDate', '=', '0000-00-00', 'OR'],
                    ['User.startDate', '<=', $this->dateTime->toDateString(), 'OR'],
                ],
            ],
            [
                [
                    ['IS NULL', 'User.endDate', 'OR'],
                    ['User.endDate', '=', '0000-00-00', 'OR'],
                    ['User.endDate', '>=', $this->dateTime->toDateString(), 'OR'],
                ],
            ],
            ['ActiveUserDepartment.department_id', '=', $this->department->id],
        ];

        $userIdsConstraints = $this->collectDirectReportUserIds();

        if (!empty($userIdsConstraints)) {
            $constraints[] = $userIdsConstraints;
        }

        $this->users = User::q(
            constraints: $constraints,
            joins: [
                ['EmployeeType', 'EmployeeType.id', '=', 'User.employeeType_id'],
            ],
            select: [
                'User.id',
                'User.nameFirst',
                'User.nameMiddle',
                'User.nameLast',
                'User.externalId',
                'User.employeeType_id',
                'User.startDate',
                'User.startDatePermanent',
                'User.startDateGovernment',
            ],
        )->many();

        $this->leaveTypes = LeaveType::q(
            constraints: [
                ['LeaveType.status', '=', ActiveStatus::ID],
            ],
            select: [
                'LeaveType.id',
                'LeaveType.name',
            ],
        )->many();

        $this->rosteredTimeOffTypes = RosteredTimeOffType::q(
            constraints: [
                ['RosteredTimeOffType.status', '=', ActiveStatus::ID],
            ],
            select: [
                'RosteredTimeOffType.id',
                'RosteredTimeOffType.name',
            ],
        )->many();

        $this->eagerLoadReportData();
    }

    /** @throws \Throwable */
    private function collectDirectReportUserIds(): array
    {
        $user = CurrentUser::getUser();

        if ($user->access->isPayrollOfficer(true) || $user->access->isBusinessIntelligence(true)) {
            return [];
        }

        $directReportUserIds = [$user->id];
        $reportUsersIds = $this->getAllReportUserIds($user);

        foreach ($reportUsersIds as $reportUserId) {
            $directReportUserIds[] = $reportUserId;
        }

        return ['IN', 'User.id', $directReportUserIds];
    }

    private function eagerLoadReportData(): void
    {
        $this->users->load([
            'employeeType:id,name,isFTE,payRunType_id,doesHideLeave',
            'userRoles' => function (Builder $query) {
                $query
                    ->select([
                        'id',
                        'user_id',
                        'role_id',
                        'startDate',
                        'endDate',
                    ])
                    ->where(function (Builder $query) {
                        $query
                            ->orWhereNull('UserRole.endDate')
                            ->orWhere('UserRole.endDate', '=', '0000-00-00')
                            ->orWhere('UserRole.endDate', '>=', $this->dateTime->toDateString());
                    })
                    ->where(function (Builder $query) {
                        $query
                            ->orWhereNull('UserRole.startDate')
                            ->orWhere('UserRole.startDate', '=', '0000-00-00')
                            ->orWhere('UserRole.startDate', '<=', $this->dateTime->toDateString());
                    });
            },
            'userRoles.role:id,name',
            'userRoles.userDepartments:id,department_id,user_id,userRole_id,startDate,endDate,status',
            'userRoles.userDepartments.department:id,name',

            'userLeaveBankTypes' => function (Builder $query) {
                $query->where('UserLeaveBankType.status', '=', ActiveStatus::ID);

                return $query;
            },
            'userLeaveBankTypes.type',
            'userLeaveBankTypes.bank.types' => function (Builder $query) {
                $query->where('UserLeaveBankType.status', '=', ActiveStatus::ID);

                return $query;
            },
            'userLeaveBankTypes.bank.types.type',
            'userLeaveBankTypes.bank.user:id,startDate,employeeType_id',
            'userLeaveBankTypes.bank.user.employeeType:id,payRunType_id',
            'userLeaveBankTypes.bank.user.employeeType.payRunType:id,firstPayRunStartsOn',
        ]);
    }

    /**
     * @throws \Throwable
     */
    protected function buildReportData(): void
    {
        $this->collectReportData();

        $this->fetchSheetSettings($sheet);
        $this->fetchSheetTitle($sheet);
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet);

        $this->sheets = [
            $sheet,
        ];
    }

    private function fetchSheetSettings(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $sheet->label = 'All leave balances';
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    private function fetchSheetTitle(\stdClass $sheet): void
    {
        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = $this->c::NAME;

        $sheet->data[] = $title;
    }

    private function fetchSheetTotals(\stdClass &$sheet): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';

        $data->columns = [
            $this->createColumn('Date of report'),
            $this->createColumn('Organisation'),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                $this->dateTime->format($this->dateFormat),
                $this->tenant->name,
            ],
        ]];

        $sheet->data[] = $data;
    }

    private function fetchSheetData(\stdClass $sheet): void
    {
        $data = new \stdClass();
        $data->type = 'data-table';
        $data->parentColumns = [
            $this->createColumn(label: 'Staff details', span: 8),
        ];

        $data->columns = [
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
            $this->createColumn('Employee type'),
            $this->createColumn('Start date organisation'),
            $this->createColumn('Start permanent'),
            $this->createColumn('Start government'),
        ];

        $this->includeExcessTimeAsLeaveColumns($data);

        foreach ($this->leaveTypes as $leaveType) {
            $this->includeLeaveTypeColumns($leaveType, $data);
        }

        foreach ($this->rosteredTimeOffTypes as $rosteredTimeOffType) {
            $this->includeLeaveTypeColumns($rosteredTimeOffType, $data);
        }

        $data->data = [];
        foreach ($this->users as $user) {
            $userRoles = $user->repository->getActiveUserRoles($this->dateTime);
            $roleNames = [];
            $departmentNames = [];
            foreach ($userRoles as $userRole) {
                $roleNames[] = $userRole->role->name;
                $userDepartments = $userRole->repository->getActiveUserDepartments($this->dateTime);

                if ($userDepartments->count() < 1) {
                    continue;
                }

                foreach ($userDepartments as $userDepartment) {
                    $departmentNames[] = $userDepartment->department->name;
                }
            }

            $rowData = [
                $user->externalId,
                $user->fullName,
                implode(' & ', $roleNames),
                implode(' & ', $departmentNames),
                $user->employeeType->name,
                !is_null($user->startDate) ? $user->startDate->format($this->dateFormat) : null,
                !is_null($user->startDatePermanent) ? $user->startDatePermanent->format($this->dateFormat) : null,
                !is_null($user->startDateGovernment) ? $user->startDateGovernment->format($this->dateFormat) : null,
            ];

            $this->includeExcessTimeAsLeaveData($user, $rowData);
            $this->includeLeaveTypeData($user, $this->leaveTypes, $rowData);
            $this->includeLeaveTypeData($user, $this->rosteredTimeOffTypes, $rowData);

            $data->data[] = [
                'config' => [],
                'data' => $rowData,
            ];
        }

        $sheet->data[] = $data;
    }

    private function includeLeaveTypeColumns(LeaveType|RosteredTimeOffType $type, \stdClass $data): void
    {
        $data->parentColumns[] = $this->createColumn(label: $type->name, span: 4);
        $data->columns[] = $this->createColumn('Pro-rata');
        $data->columns[] = $this->createColumn('Available');
        $data->columns[] = $this->createColumn('Committed');
        $data->columns[] = $this->createColumn('Historic');
    }

    /**
     * @param LeaveType[]|RosteredTimeOffType[]|Collection $types
     */
    private function includeLeaveTypeData(User $user, array|Collection $types, array &$data): void
    {
        foreach ($types as $typeItem) {
            $included = false;

            foreach ($user->userLeaveBankTypes as $userLeaveBankType) {
                if ($typeItem->is($userLeaveBankType->type)) {
                    $included = true;
                    $data[] = $userLeaveBankType->bank->repository->getAccruedBalance();
                    $data[] = $userLeaveBankType->bank->repository->getAvailableBalance();
                    $data[] = $userLeaveBankType->bank->repository->getCommittedBalance();
                    $data[] = $userLeaveBankType->bank->repository->getHistoricBalance();

                    break;
                }
            }

            if (!$included) {
                $data[] = 0;
                $data[] = 0;
                $data[] = 0;
                $data[] = 0;
            }
        }
    }

    private function includeExcessTimeAsLeaveColumns(\stdClass $data): void
    {
        $data->parentColumns[] = $this->createColumn(label: TenantSystemSettings::o()->customExcessTimeLeaveName, span: 3);
        $data->columns[] = $this->createColumn('Available');
        $data->columns[] = $this->createColumn('Committed');
        $data->columns[] = $this->createColumn('Historic');
    }

    private function includeExcessTimeAsLeaveData(User $user, array &$data): void
    {
        /** @var UserLeaveBankType $accruedType */
        $accruedType = $user->userLeaveBankTypes->where('type_type', '=', null)->first();

        if (!is_null($accruedType)) {
            $data[] = $accruedType->bank->repository->getAvailableBalance();
            $data[] = $accruedType->bank->repository->getCommittedBalance();
            $data[] = $accruedType->bank->repository->getHistoricBalance();
        } else {
            $data[] = 0;
            $data[] = 0;
            $data[] = 0;
        }
    }
}
