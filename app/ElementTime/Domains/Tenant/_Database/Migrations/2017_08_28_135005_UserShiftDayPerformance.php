<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Settings
        Schema::table('Settings', function (Blueprint $table) {
            $table->integer('systemForecastLimit')->default(36);
        });
        //endregion

        //region UserShift
        Schema::table('UserShift', function (Blueprint $table) {
            $table->boolean('isLocked')->default(false);
        });
        //endregion

        //region UserShiftDay
        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->boolean('isOnLeave')->nullable();
        });
        //endregion
    }

    public function down(): void
    {
        //region UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'isLocked')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('isLocked');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'isOnLeave')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('isOnLeave');
            });
        }
        //endregion

        //region UserShift
        if (Schema::hasColumn('UserShift', 'isLocked')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('isLocked');
            });
        }
        //endregion

        //region Settings
        if (Schema::hasColumn('Settings', 'systemForecastLimit')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('systemForecastLimit');
            });
        }
        //endregion
    }
};
