<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay;

use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Schedules\BaseSchedulesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetHoliday;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Illuminate\Database\Eloquent\Collection;

class GetOrdinaryGapsInScheduledDayWorkPattern extends BaseSchedulesAction
{
    /**
     * @param Collection<LeaveRequestDay>|LeaveRequestDay[]|null $leaveRequestDays
     * @param Collection<TimeSheetHoliday>|TimeSheetHoliday[]|null $holidays
     * @param Collection<TimeSheetWork>|TimeSheetWork[]|null $works
     * @throws \Throwable
     */
    public function handle(
        ScheduledPayRunPeriodDay $day,
        Collection|array $leaveRequestDays = null,
        Collection|array $holidays = null,
        Collection|array $works = null,
        bool $doesAccountBreaks = true,
    ): TimeSpanList
    {
        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $day->specific;

        if (!$scheduledPayRunPeriodDayWorkPattern instanceof ScheduledPayRunPeriodDayWorkPattern) {
            throw new \InvalidArgumentException('Only work-pattern scheduled days are allowed.');
        }

        $occupiedTimeSpanList = $doesAccountBreaks ? $scheduledPayRunPeriodDayWorkPattern->breaks->getList() : Collection::make();

        if (!$day->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->holidayCalculations->doesWorkOnHolidays) {
            $holidays = $holidays ?? $day->timeSheetHolidays;

            // TODO: Do this properly when introduce part day holidays
            if (!is_null($holidays) && $holidays->count() > 0) {
                return TimeSpanList::empty();
            }

            // foreach ($holidays as $timeSheetHoliday) {
            //     $occupiedTimeSpanList->addTimeSpan(TimeSpan::make($timeSheetHoliday->start, $timeSheetHoliday->end));
            // }
        }

        $leaveRequestDays = $leaveRequestDays ?? $this->getRelevantLeaveRequestDays($day);
        $works = $works ?? $day->timeSheetWorks;

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $occupiedTimeSpanList->push($leaveRequestDay->timeSpan);
        }

        foreach ($works as $timeSheetWork) {
            $occupiedTimeSpanList->push(TimeSpan::make($timeSheetWork->start, $timeSheetWork->end));
        }

        return $this->getSpanGaps($scheduledPayRunPeriodDayWorkPattern->ordinarySpan->copy(), $occupiedTimeSpanList);
    }

    /** @return Collection<LeaveRequestDay>|LeaveRequestDay[] */
    private function getRelevantLeaveRequestDays(ScheduledPayRunPeriodDay $day): Collection|array
    {
        $workflowStatuses = [
            WorkflowStatusTypes\NewType::ID,
            WorkflowStatusTypes\SubmittedType::ID,
            WorkflowStatusTypes\PartiallyApprovedType::ID,
            WorkflowStatusTypes\ApprovedType::ID,
        ];

        return LeaveRequestDay::q(
            constraints: [
                ['LeaveRequest.userRoleSchedule_id', '=', $day->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule_id],
                ['IN', 'Workflow.status', $workflowStatuses],
                ['LeaveRequestDay.date', '=', $day->date->toDateString()],
            ]
        )->many();
    }

    /** @throws \Throwable */
    private function getSpanGaps(TimeSpan $timeSpan, \Illuminate\Support\Collection $listToDeduct): TimeSpanList
    {
        $ret = TimeSpanList::make()->acceptContinuous();
        $span = $timeSpan->copy();

        $sortedTimeSpansToDeduct = TimeSpanList::makeFromMergedTimeSpans($listToDeduct, $timeSpan->getStartAsCarbon())
            ->getList()
            ->sort(fn (TimeSpan $prev, TimeSpan $next) => $prev->getStartAsCarbon()->lt($next->getStartAsCarbon()) ? -1 : 1);

        if ($sortedTimeSpansToDeduct->count() < 1) {
            return TimeSpanList::make($span);
        }

        foreach ($sortedTimeSpansToDeduct as $timeSpanToDeduct) {
            if (
                $span->getStartAsCarbon()->gte($timeSpanToDeduct->getEndAsCarbon())
                || $span->getEndAsCarbon()->lte($timeSpanToDeduct->getStartAsCarbon())
            ) {
                continue;
            }

            if ($timeSpanToDeduct->doesContain($span, true)) {
                return TimeSpanList::empty();
            }

            if ($span->doesContain($timeSpanToDeduct, true)) {
                if ($span->getStartAsCarbon()->notEqualTo($timeSpanToDeduct->getStartAsCarbon())) {
                    $newTimeSpan = TimeSpan::make(
                        start: $span->getStartAsCarbon(),
                        end: $timeSpanToDeduct->getStartAsCarbon(),
                        date: $timeSpan->copy()->getStartAsCarbon(),
                    );

                    $ret->addTimeSpan($newTimeSpan);
                }
                $span->setStart($timeSpanToDeduct->getEndAsCarbon());
            } elseif ($span->doesContainTime($timeSpanToDeduct->getStartAsCarbon())) {
                $span->setEnd($timeSpanToDeduct->getStartAsCarbon());
            } elseif ($span->doesContainTime($timeSpanToDeduct->getEndAsCarbon())) {
                $span->setStart($timeSpanToDeduct->getEndAsCarbon());
            }
        }

        if ($span->getStart() !== $span->getEnd()) {
            $ret->addTimeSpan($span->copy());
        }

        return $ret;
    }
}
