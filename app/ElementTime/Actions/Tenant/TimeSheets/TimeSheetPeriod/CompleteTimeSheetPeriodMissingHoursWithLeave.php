<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\NotImplementedException;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityDurationOnly;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityWorkPattern;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay\GetMissingDurationForScheduledDay;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay\GetOrdinaryGapsInScheduledDayWorkPattern;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetHoliday;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetPeriod;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Database\Eloquent\Collection;
use JetBrains\PhpStorm\ObjectShape;

class CompleteTimeSheetPeriodMissingHoursWithLeave extends BaseTimeSheetsAction
{
    private array $gaps = [];
    private TimeSheetPeriod $period;
    private UserLeaveBankType $userLeaveBankType;
    private User $actorUser;
    private string $reason;

    /** @throws \Throwable */
    public function handle(TimeSheetPeriod $period, UserLeaveBankType $userLeaveBankType, User $actorUser, string $reason): void
    {
        if ($period->scheduleType->is(UserRoleScheduleType::ZeroBased)) {
            throw new InvalidArgumentException("Action not supported for zero based schedule type.");
        }

        $this->period = $period;
        $this->userLeaveBankType = $userLeaveBankType;
        $this->actorUser = $actorUser;
        $this->reason = $reason;
        $ownerUser = $userLeaveBankType->bank->user;
        $leaveFromAllRoles = static::getRelevantLeaveRequests($period);
        $leaveRequests = $leaveFromAllRoles->where('userRoleSchedule_id', '=', $period->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule_id)->values();
        $holidays = static::getRelevantHolidays($period);

        /** @var Collection|ScheduledPayRunPeriodDay[] $scheduledDays */
        $doesWorkOnHolidays = $period->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->holidayCalculations->doesWorkOnHolidays;
        $scheduledDays = $period->scheduledPayRunPeriod->days->filter(fn (ScheduledPayRunPeriodDay $day) => $day->isScheduled())->values();
        $scheduleType = $period->scheduleType;

        CalculateTimeSheetPeriodRelatedRecords::make()->handle(timeSheetPeriod: $period);

        $missingDuration = GetTimeSheetPeriodMissingDuration::make()->handle($period);

        foreach ($scheduledDays as $scheduledDay) {
            $dayGapRecord = $this->getOrSetGapRecord($scheduledDay->date);
            $dayHolidays = $this->getHolidaysForDay($holidays, $scheduledDay->date);
            $leaveRequestDays = $this->getLeaveRequestDaysForDay($leaveRequests, $scheduledDay->date);

            $dayGapRecord->duration = GetMissingDurationForScheduledDay::make()->handle($scheduledDay, $dayHolidays, $leaveRequestDays);
            $dayGapRecord->ordinaryGaps = $scheduleType->isDurationOnly()
                ? TimeSpanList::empty()
                : GetOrdinaryGapsInScheduledDayWorkPattern::make()->handle(
                    day: $scheduledDay,
                    leaveRequestDays: $leaveRequestDays,
                    holidays: $dayHolidays,
                );

            $dayGapRecord->maxDuration = $this
                ->getAvailableOrdinaryDurationForDay(
                    user: $ownerUser,
                    day: $scheduledDay,
                    leaveRequestDaysFromAllRoles: $this->getLeaveRequestDaysForDay($leaveFromAllRoles, $scheduledDay->date),
                    dayHolidays: $dayHolidays,
                    doesWorkOnHolidays: $doesWorkOnHolidays,
                )
                ->deduct($dayGapRecord->duration);

            $missingDuration->deduct($dayGapRecord->duration);
        }

        $scheduledDayIndex = $scheduledDays->count() - 1;

        while ($missingDuration->gt0() && isset($scheduledDays[$scheduledDayIndex])) {
            $scheduledDay = $scheduledDays[$scheduledDayIndex];
            $gapEntry = $this->getOrSetGapRecord($scheduledDay->date);

            $totalGapsDuration = $scheduleType->isDurationOnly()
                ? TimeDuration::oneDay()
                : $gapEntry->ordinaryGaps->getDuration();

            $durationToIncrementOnDay = TimeDuration::min(
                $gapEntry->maxDuration,
                $missingDuration->copy(),
                $totalGapsDuration,
            );

            $gapEntry->duration->add($durationToIncrementOnDay);
            $missingDuration->deduct($durationToIncrementOnDay);

            $scheduledDayIndex--;
        }

        foreach ($this->gaps as $dateString => $gap) {
            $date = Carbon::make($dateString);

            $this->completeMissingHoursForDay(
                date: Carbon::make($date),
                missingDuration: $gap->duration,
            );
        }
    }

    /** @throws \Throwable  */
    #[ObjectShape([
        'duration' => TimeDuration::class,
        'maxDuration' => TimeDuration::class,
        'ordinaryGaps' => TimeSpanList::class,
    ])]
    private function getOrSetGapRecord(CarbonInterface $date): \stdClass
    {
        $key = $date->toDateString();

        if (!isset($this->gaps[$key])) {
            $entry = new \stdClass();
            $entry->duration = TimeDuration::make();
            $entry->maxDuration = TimeDuration::make();
            $entry->ordinaryGaps = TimeSpanList::empty();

            $this->gaps[$key] = $entry;
        }

        return $this->gaps[$key];
    }
    //region --\\   Data fetching | filter methods   //--

    /** @return Collection|TimeSheetHoliday[] */
    private static function getRelevantHolidays(TimeSheetPeriod $period): Collection|array
    {
        return TimeSheetHoliday::q(constraints: [
            ['ScheduledHoliday.userRoleSchedule_id', '=', $period->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule_id],
            ['ScheduledHoliday.date', '>=', $period->startDate->toDateString()],
            ['ScheduledHoliday.date', '<=', $period->endDate->toDateString()],
        ])->many();
    }

    /** @return Collection<LeaveRequest>|LeaveRequest[] */
    private static function getRelevantLeaveRequests(TimeSheetPeriod $period): Collection|array
    {
        return LeaveRequest::q(constraints: [
            ['IN', 'Workflow.status', [
                WorkflowStatusTypes\NewType::ID,
                WorkflowStatusTypes\SubmittedType::ID,
                WorkflowStatusTypes\PartiallyApprovedType::ID,
                WorkflowStatusTypes\ApprovedType::ID,
            ]],
            ['LeaveRequest.startDate', '<=', $period->endDate->toDateString()],
            ['LeaveRequest.endDate', '>=', $period->startDate->toDateString()],
            ['User.id', '=', $period->timeSheet->payRunItem->user_id],
        ])->many();
    }

    /** @param Collection|LeaveRequest[] $leaveRequests */
    private function getLeaveRequestDaysForDay(Collection|array $leaveRequests, CarbonInterface $date): Collection|array
    {
        return $leaveRequests->reduce(
            fn (Collection $days, LeaveRequest $leaveRequest) => $days->concat(
                $leaveRequest->days->filter(fn (LeaveRequestDay $day) => $day->date->isSameDay($date)),
            ),
            Collection::make(),
        );
    }

    /** @param Collection|TimeSheetHoliday[] $holidays */
    private function getHolidaysForDay(Collection|array $holidays, CarbonInterface $date): Collection|array
    {
        return $holidays->filter(
            fn (TimeSheetHoliday $timeSheetHoliday) => $timeSheetHoliday->timeSheetDay->date->isSameDay($date)
        );
    }

    /**
     * @param Collection|LeaveRequestDay[] $leaveRequestDaysFromAllRoles
     * @param Collection|TimeSheetHoliday[] $dayHolidays
     * @throws \Throwable
     */
    private function getAvailableOrdinaryDurationForDay(
        User $user,
        ScheduledPayRunPeriodDay $day,
        Collection|array $leaveRequestDaysFromAllRoles,
        Collection|array $dayHolidays,
        bool $doesWorkOnHolidays
    ): TimeDuration
    {
        // TODO: Do this properly when introduce part day holidays
        if (!$doesWorkOnHolidays && $dayHolidays->count() > 0) {
            return TimeDuration::zero();
        }

        $userMaxDuration = $user->settings->doesHaveMaximumHoursPerDay
            ? TimeDuration::parseFromHours($user->settings->maximumHoursPerDay ?? 24)
            : TimeDuration::oneDay();

        $scheduledDayMaxDuration = TimeDuration::min($day->getMaxDuration(), $userMaxDuration);

        foreach ($day->timeSheetDay->works as $timeSheetWork) {
            foreach ($timeSheetWork->items as $item) {
                if ($timeSheetWork->scheduledPayRunPeriodDay_id === $day->id) {
                    $scheduledDayMaxDuration->deduct($item->getOrdinaryDuration());
                }
            }

            $userMaxDuration->deduct($timeSheetWork->workDuration);
        }

        foreach ($leaveRequestDaysFromAllRoles as $leaveRequestDay) {
            if ($leaveRequestDay->leaveRequest->userRoleSchedule_id === $day->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule_id) {
                $scheduledDayMaxDuration->deduct($leaveRequestDay->duration);
            }

            $userMaxDuration->deduct($leaveRequestDay->duration);
        }

        return TimeDuration::min($scheduledDayMaxDuration, $userMaxDuration);
    }

    //endregion --\\   Data fetching | filter methods   //--

    //region --\\   Complete missing hours methods   //--

    /** @throws \Throwable */
    private function completeMissingHoursForDay(
        CarbonInterface $date,
        TimeDuration $missingDuration,
    ): void
    {
        $scheduleType = $this->period->scheduleType;

        if ($missingDuration->lte0()) {
            return ;
        }

        $leaveRequest = match ($scheduleType) {
            UserRoleScheduleType::WorkPattern => $this->placeWorkPatternLeaveRequest(
                date: $date,
                duration: $missingDuration->copy(),
            ),
            UserRoleScheduleType::DurationOnly => $this->placeDurationOnlyLeaveRequest(
                date: $date,
                duration: $missingDuration->copy(),
            ),
            default => throw new NotImplementedException("Not implemented for - " . $scheduleType->id())
        };

        $missingDuration->deduct($leaveRequest->duration);

        $this->completeMissingHoursForDay(date: $date, missingDuration: $missingDuration);
    }

    /**
     * @throws \Throwable
     */
    private function placeWorkPatternLeaveRequest(
        CarbonInterface $date,
        TimeDuration $duration,
    ): LeaveRequest
    {
        $dayData = $this->getOrSetGapRecord($date);

        if ($dayData->ordinaryGaps->count() < 1) {
            throw new InvalidArgumentException("Unable to find available schedule block for leave day - " . $date->toDateString());
        }

        $largestTimeBlock = $dayData->ordinaryGaps->pluckLongestSpan();
        $requestDuration = TimeDuration::min($largestTimeBlock->getDuration(), $duration->copy());
        $timeSpan = TimeSpan::createFromStartAndDuration($largestTimeBlock->getStart(), $requestDuration);

        $userRoleSchedule = $this->period->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule;

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $this->userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $date->copy(),
            endDate: $date->copy(),
            dailyStartTime: $timeSpan->getStart(),
            dailyEndTime: $timeSpan->getEnd(),
            dailyDuration: $requestDuration,
            actorUser: $this->actorUser,
        );

        if (!$preAvailability->canBeRequested) {
            $errorMessages = array_reduce(
                $preAvailability->errorMessages,
                fn ($ret, $errorMessage) => $ret . (strlen($ret) > 0 ? ', ' : '') . $errorMessage['message'],
                '',
            );

            throw new InvalidArgumentException("Unable to request leave request on " . $date->toDateString() . " at " . $timeSpan->getStart()  . ' - ' . $timeSpan->getEnd() . ". " . $errorMessages);
        }

        return PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $this->userLeaveBankType,
            preAvailability: $preAvailability,
            reason: $this->reason,
            actorUser: $this->actorUser,
            doesApprove: $this->actorUser->id !== $this->userLeaveBankType->bank->user_id,
            recordedVia: RecordedVia::AutoComplete,
            doesSendBroadcastEvents: false,
            doesCalculateRelatedRecords: false,
        );
    }

    /** @throws \Throwable */
    private function placeDurationOnlyLeaveRequest(
        CarbonInterface $date,
        TimeDuration $duration,
    ): LeaveRequest
    {
        $userRoleSchedule = $this->period->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule;

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleDurationOnly::make()->handle(
            userLeaveBankType: $this->userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $date->copy(),
            endDate: $date->copy(),
            dailyDuration: $duration->copy(),
            actorUser: $this->actorUser,
        );

        if (!$preAvailability->canBeRequested) {
            $errorMessages = array_reduce(
                $preAvailability->errorMessages,
                fn ($ret, $errorMessage) => $ret . (strlen($ret) > 0 ? ', ' : '') . $errorMessage['message'],
                '',
            );

            throw new InvalidArgumentException("Unable to request leave request on " . $date->toDateString() . ". " . $errorMessages);
        }

        return PlaceLeaveRequestFromPreAvailabilityDurationOnly::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $this->userLeaveBankType,
            preAvailability: $preAvailability,
            reason: $this->reason,
            actorUser: $this->actorUser,
            doesApprove: $this->actorUser->id !== $this->userLeaveBankType->bank->user_id,
            recordedVia: RecordedVia::AutoComplete,
            doesSendBroadcastEvents: false,
            doesCalculateRelatedRecords: false,
        );
    }

    //endregion --\\   Complete missing hours methods   //--
}
