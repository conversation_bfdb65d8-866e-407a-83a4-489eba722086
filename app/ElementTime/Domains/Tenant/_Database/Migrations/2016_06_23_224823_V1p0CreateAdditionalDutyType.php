<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('AdditionalDutyType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->string('externalId')->nullable();
            $table->string('name')->nullable();
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();

            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();
            $table->boolean('isEarntDuringLeave')->nullable();

            $table->float('awardRateValue')->nullable();
            $table->char('awardRateType', 1)->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('AdditionalDutyType_LeaveType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('additionalDutyType_id')->unsigned();
            $table->bigInteger('leaveType_id')->unsigned();

            $table->primary(['additionalDutyType_id', 'leaveType_id'], 'adtype_leavetype_primary');
            $table->foreign('additionalDutyType_id', 'adtype_leavetype_adtype_fn')->references('id')->on('AdditionalDutyType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('leaveType_id', 'adtype_leavetype_leavetype_fn')->references('id')->on('LeaveType')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('AdditionalDutyType');
        Schema::drop('AdditionalDutyType_LeaveType');
    }
};
