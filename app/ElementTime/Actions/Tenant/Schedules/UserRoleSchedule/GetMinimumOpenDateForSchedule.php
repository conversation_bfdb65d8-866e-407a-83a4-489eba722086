<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Users\BaseUsersAction;
use Element\ElementTime\Actions\Tenant\Users\UserRole\GetMinimumOpenDateForRole;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;

class GetMinimumOpenDateForSchedule extends BaseUsersAction
{
    /** @throws \Throwable */
    public function handle(UserRoleSchedule $userRoleSchedule, User|null $actor = null): CarbonInterface
    {
        $startDate = GetMinimumOpenDateForRole::make()->handle(
            userRole: $userRoleSchedule->userRole,
            actor: $actor,
        );

        return Carbon::parse(max($startDate, $userRoleSchedule->startDate));
    }
}
