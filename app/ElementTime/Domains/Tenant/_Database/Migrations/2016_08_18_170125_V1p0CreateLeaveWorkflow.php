<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('Leave', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userLeaveType_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->float('hours')->nullable();
            $table->text('reason')->nullable();

            $table->longText('approvalWorkflowMap')->nullable(); // From Settings->workflowLeaveApproval (duplicated to keep the current map)

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userLeaveType_id', 'leave_ult_fn')->references('id')->on('UserLeaveType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('LeaveApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Items relationships
            $table->bigInteger('leave_id')->unsigned();
            $table->bigInteger('manager_id')->unsigned()->nullable();

            $table->char('expectedUserSystemAccess', 1)->nullable();
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1); // The number of levels and the user type of each manager depends on Settings.workflowLeaveApproval map
            $table->text('notes')->nullable();

            $table->char('status', 1)->nullable(); // (W)aiting approval | (A)pproved | (D)isapproved

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('leave_id', 'leaveapproval_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('manager_id', 'leaveapproval_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('Leave');
        Schema::drop('LeaveApproval');
    }
};
