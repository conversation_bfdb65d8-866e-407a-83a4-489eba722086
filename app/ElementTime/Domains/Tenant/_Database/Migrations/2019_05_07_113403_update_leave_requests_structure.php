<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserLeaveBank

        Schema::create('UserLeaveBank', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // User identification
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'ulb_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            // Bank data
            $table->string('name');
            $table->text('description')->nullable();

            // Total balances
            $table->decimal('totalBalance', 16, 10)->default(0);
            $table->decimal('totalAccruedBalance', 16, 10)->default(0);
            $table->decimal('totalAvailableBalance', 16, 10)->default(0);
            $table->decimal('totalCommittedBalance', 16, 10)->default(0);
            $table->decimal('totalHistoricBalance', 16, 10)->default(0);
            $table->decimal('totalExpiredBalance', 16, 10)->default(0);
            $table->decimal('totalTakenBalance', 16, 10)->default(0);
            $table->decimal('totalPaidBalance', 16, 10)->default(0);

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserLeaveBank

        //region Table: UserLeaveBankType

        Schema::create('UserLeaveBankType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // Bank identification
            $table->unsignedBigInteger('userLeaveBank_id');
            $table->foreign('userLeaveBank_id', 'ulblt_ulb_fn')->references('id')->on('UserLeaveBank')->onUpdate('cascade')->onDelete('cascade');

            // Type identification (can be a LeaveType, RosteredTimeOffType, or null if it's Accrued hours)
            $table->string('type_type')->nullable();
            $table->unsignedBigInteger('type_id')->nullable();

            // Assignment data
            $table->text('comment')->nullable();

            // Custom project/activity code
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();

            // Flags
            $table->char('status', 1)->default('A');
            $table->boolean('accruesBalance')->default(true);
            $table->boolean('canTakeLeave')->default(true);
            $table->boolean('canScheduleLeave')->default(false);

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserLeaveBankType

        //region Table: LeaveRequest

        Schema::create('LeaveRequest', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // Leave bank identification
            $table->unsignedBigInteger('userLeaveBankType_id');
            $table->foreign('userLeaveBankType_id', 'lr_ulbt_fn')->references('id')->on('UserLeaveBankType')->onUpdate('cascade')->onDelete('restrict');

            // Shift identification
            $table->unsignedBigInteger('userShift_id')->nullable();
            $table->foreign('userShift_id', 'lr_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');

            // Actor User identification
            $table->unsignedBigInteger('actor_id')->nullable();
            $table->foreign('actor_id', 'lr_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            // Original request identification (if replaced)
            $table->unsignedBigInteger('originalReplaced_id')->nullable();
            $table->foreign('originalReplaced_id', 'lr_or_fn')->references('id')->on('LeaveRequest')->onUpdate('cascade')->onDelete('set null');

            // Original request identification (if split)
            $table->unsignedBigInteger('originalSplit_id')->nullable();
            $table->foreign('originalSplit_id', 'lr_os_fn')->references('id')->on('LeaveRequest')->onUpdate('cascade')->onDelete('set null');

            // Leave request date/time/hours
            $table->boolean('isFullDay')->default(false); // Flag if this leave request is a full-day request
            $table->dateTime('startDateTime'); // Start date/time for leave request (if full day, only the start date)
            $table->dateTime('endDateTime'); // End date/time for leave request (if full day, only the end date)
            $table->string('glideDailyStartTime')->nullable(); // Daily start time in case it's a glide shift type
            $table->string('glideDailyEndTime')->nullable(); // Daily end time in case it's a glide shift type
            $table->decimal('actualTotalHours', 16, 10)->default(0); // Hours based on scheduled hours
            $table->decimal('calculatedTotalHours', 16, 10)->default(0); // Hours calculated based on actual hours and FTE calculations (if applicable)
            $table->decimal('glideDailyHours', 16, 10)->default(0); // Daily hours in case it's a glide shift type

            // Leave request info
            $table->longText('reason')->nullable();

            // Leave config data
            $table->boolean('doesHaveFTECalculation')->default(false);
            $table->text('externalCalendarEmail')->nullable();
            $table->text('externalCalendarId')->nullable();
            $table->boolean('isScheduled')->default(false);

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: LeaveRequest

        //region Table: LeaveRequestDay

        Schema::create('LeaveRequestDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Leave request identification
            $table->unsignedBigInteger('leaveRequest_id');
            $table->foreign('leaveRequest_id', 'lrd_lr_fn')->references('id')->on('LeaveRequest')->onUpdate('cascade')->onDelete('cascade');

            // User identification
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'lrd_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            // Shift identification
            $table->unsignedBigInteger('userShift_id')->nullable();
            $table->foreign('userShift_id', 'lrd_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');

            // Shift-day identification
            $table->unsignedBigInteger('userShiftDay_id')->nullable();
            $table->foreign('userShiftDay_id', 'lrd_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');

            // Leave request date/time/hours
            $table->boolean('isFullDay')->default(false); // Flag if this leave request is a full-day request
            $table->date('date');
            $table->dateTime('startDateTime');
            $table->dateTime('endDateTime');
            $table->decimal('actualHours', 16, 10)->default(0); // Hours based on scheduled hours
            $table->decimal('calculatedHours', 16, 10)->default(0); // Hours calculated based on actual hours and FTE calculations (if applicable)

            // Leave config data
            $table->boolean('doesHaveFTECalculation')->default(false);
            $table->boolean('isScheduled')->default(false);

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: LeaveRequestDay

        //region Table: LeaveRequestDayBreak

        Schema::create('LeaveRequestDayBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Leave request-day identification
            $table->unsignedBigInteger('leaveRequestDay_id');
            $table->foreign('leaveRequestDay_id', 'lrdb_lrd_fn')->references('id')->on('LeaveRequestDay')->onUpdate('cascade')->onDelete('cascade');

            // Shift-day-break identification
            $table->unsignedBigInteger('userShiftDayBreak_id')->nullable();
            $table->foreign('userShiftDayBreak_id', 'lrdb_usdb_fn')->references('id')->on('UserShiftDayBreak')->onUpdate('cascade')->onDelete('restrict');

            // Leave request date/time/hours
            $table->dateTime('startDateTime');
            $table->dateTime('endDateTime');
            $table->decimal('hours', 16, 10)->default(0); // Hours based on scheduled break hours

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: LeaveRequestDayBreak

        //region Table: LeaveRequestAttachmentFile

        Schema::create('LeaveRequestAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('mediaFile_id');
            $table->foreign('mediaFile_id', 'lraf_mf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('leaveRequest_id');
            $table->foreign('leaveRequest_id', 'lraf_lr_fn')->references('id')->on('LeaveRequest')->onUpdate('cascade')->onDelete('cascade');

            // Timestamps
            $table->timestamps();
        });

        //endregion Table: LeaveRequestAttachmentFile

        //region Table: UserLeaveBankEntry

        Schema::create('UserLeaveBankEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // Bank identification
            $table->unsignedBigInteger('userLeaveBank_id');
            $table->foreign('userLeaveBank_id', 'ulbe_ulb_fn')->references('id')->on('UserLeaveBank')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('userLeaveBankType_id')->nullable();
            $table->foreign('userLeaveBankType_id', 'ulbe_ulbt_fn')->references('id')->on('UserLeaveBankType')->onUpdate('cascade')->onDelete('set null');

            // Entry values
            $table->decimal('entryBalance', 16, 10);
            $table->decimal('remainingBalance', 16, 10)->default(0);
            $table->decimal('currentHourlyRate', 16, 10)->nullable();

            // Availability data
            $table->boolean('isAvailable')->default(true);
            $table->date('availabilityDateTime')->nullable();
            $table->boolean('isExpired')->default(false);
            $table->dateTime('validityDateTime')->nullable();

            // Current situation
            $table->decimal('currentAccruedBalance', 16, 10)->nullable();
            $table->decimal('currentAvailableBalance', 16, 10)->nullable();
            $table->decimal('currentCommittedBalance', 16, 10)->nullable();
            $table->decimal('currentHistoricBalance', 16, 10)->nullable();

            // Previous Entry (when applicable)
            $table->unsignedBigInteger('previous_id')->nullable();
            $table->foreign('previous_id', 'ulbe_prev_fn')->references('id')->on('UserLeaveBankEntry')->onUpdate('cascade')->onDelete('set null');

            // Previous Entry situation
            $table->decimal('previousAccruedBalance', 16, 10)->nullable();
            $table->decimal('previousAvailableBalance', 16, 10)->nullable();
            $table->decimal('previousCommittedBalance', 16, 10)->nullable();
            $table->decimal('previousHistoricBalance', 16, 10)->nullable();

            // Entry data
            $table->string('entryType'); // Entry type class ID
            $table->dateTime('entryDateTime');
            $table->text('description')->nullable();
            $table->text('comments')->nullable();

            // Leave request that committed / used / uncommitted / unused this balance entry
            $table->unsignedBigInteger('leaveRequest_id')->nullable();
            $table->foreign('leaveRequest_id', 'ulbe_lr_fn')->references('id')->on('LeaveRequest')->onUpdate('cascade')->onDelete('set null');

            // Time-sheet that accrued this balance entry
            $table->unsignedBigInteger('timeSheet_id')->nullable();
            $table->foreign('timeSheet_id', 'ulbe_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('set null');

            // Time-sheet-day that accrued this balance entry
            $table->unsignedBigInteger('timeSheetDay_id')->nullable();
            $table->foreign('timeSheetDay_id', 'ulbe_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('set null');

            // Related entry (for undoing / deleting entries / returning / expiring / committing / using / making available, etc)
            $table->unsignedBigInteger('relEntry_id')->nullable();
            $table->foreign('relEntry_id', 'ulbe_rele_fn')->references('id')->on('UserLeaveBankEntry')->onUpdate('cascade')->onDelete('cascade');

            // Manager that manually did this entry
            $table->unsignedBigInteger('manager_id')->nullable();
            $table->foreign('manager_id', 'ulbe_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // Period that this balance was paid
            $table->unsignedBigInteger('payRunItem_id')->nullable();
            $table->foreign('payRunItem_id', 'ulbe_pri_fn')->references('id')->on('PayRunItem')->onUpdate('cascade')->onDelete('set null');

            // Attachment file
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'ulbe_attf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            // Flags
            $table->boolean('isDeleted')->default(false);

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserLeaveBankEntry

        //region Table: UserLeaveBankEntryRelated

        Schema::create('UserLeaveBankEntryRelated', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // Original entry
            $table->unsignedBigInteger('original_id');
            $table->foreign('original_id', 'ulber_o_fn')->references('id')->on('UserLeaveBankEntry')->onUpdate('cascade')->onDelete('cascade');

            // Changing entry
            $table->unsignedBigInteger('changing_id');
            $table->foreign('changing_id', 'ulber_c_fn')->references('id')->on('UserLeaveBankEntry')->onUpdate('cascade')->onDelete('cascade');

            // Balance changed
            $table->decimal('balance', 16, 10)->default(0);

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserLeaveBankEntryRelated

        //region Table: UserLeaveBankEntryNegative

        Schema::create('UserLeaveBankEntryNegative', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Record Identification
            $table->bigIncrements('id');

            // Committed entry
            $table->unsignedBigInteger('committedEntry_id');
            $table->foreign('committedEntry_id', 'ulben_ce_fn')->references('id')->on('UserLeaveBankEntry')->onUpdate('cascade')->onDelete('cascade');

            // Balances
            $table->decimal('balance', 16, 10)->default(0);
            $table->decimal('remainingBalance', 16, 10)->default(0);

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserLeaveBankEntryNegative

        //region Table: TimeSheetItemDayTime

        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->unsignedBigInteger('leaveRequestDay_id')->nullable();
            $table->foreign('leaveRequestDay_id', 'tsidt_lrd_fn')->references('id')->on('LeaveRequestDay')->onUpdate('cascade')->onDelete('cascade');
        });

        //endregion Table: TimeSheetItemDayTime
    }

    public function down(): void
    {
        //region Table: TimeSheetItemDayTime

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_lrd_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_lrd_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'leaveRequestDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('leaveRequestDay_id');
            });
        }

        //endregion Table: TimeSheetItemDayTime

        //region Table: UserLeaveBankEntryNegative

        Schema::dropIfExists('UserLeaveBankEntryNegative');

        //endregion Table: UserLeaveBankEntryNegative

        //region Table: UserLeaveBankEntryRelated

        Schema::dropIfExists('UserLeaveBankEntryRelated');

        //endregion Table: UserLeaveBankEntryRelated

        //region Table: UserLeaveBankEntry

        Schema::dropIfExists('UserLeaveBankEntry');

        //endregion Table: UserLeaveBankEntry

        //region Table: LeaveRequestAttachmentFile

        Schema::dropIfExists('LeaveRequestAttachmentFile');

        //endregion Table: LeaveRequestAttachmentFile

        //region Table: LeaveRequestDayBreak

        Schema::dropIfExists('LeaveRequestDayBreak');

        //endregion Table: LeaveRequestDayBreak

        //region Table: LeaveRequestDay

        Schema::dropIfExists('LeaveRequestDay');

        //endregion Table: LeaveRequestDay

        //region Table: LeaveRequest

        Schema::dropIfExists('LeaveRequest');

        //endregion Table: LeaveRequest

        //region Table: UserLeaveBankType

        Schema::dropIfExists('UserLeaveBankType');

        //endregion Table: UserLeaveBankType

        //region Table: UserLeaveBank

        Schema::dropIfExists('UserLeaveBank');

        //endregion Table: UserLeaveBank
    }
};
