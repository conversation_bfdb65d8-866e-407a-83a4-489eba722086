<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN canSubmitAnyTime TINYINT(1) NOT NULL DEFAULT 1 AFTER isDefault;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN allowsToDuplicateTimeSheets TINYINT(1) NOT NULL DEFAULT 0 AFTER canSubmitAnyTime;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN allowsCopyFromSchedule TINYINT(1) NOT NULL DEFAULT 0 AFTER allowsToDuplicateTimeSheets;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN allowsAutoCompleteFromSchedule TINYINT(1) NOT NULL DEFAULT 0 AFTER allowsCopyFromSchedule;');

        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN firstPayRunStartsOn DATE NOT NULL AFTER period;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN firstPayRunNumber INT NOT NULL DEFAULT 1 AFTER firstPayRunStartsOn;');

        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN dayPayRunCloses TINYINT NOT NULL DEFAULT 1 AFTER firstPayRunNumber;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN dueDayForTimeSheets TINYINT NOT NULL DEFAULT 0 AFTER dayPayRunCloses;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN dayPayRunIsProcessed TINYINT NOT NULL DEFAULT 1 AFTER dueDayForTimeSheets;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN timePayRunIsProcessed TIME NOT NULL AFTER dayPayRunIsProcessed;');
        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN dayPayRunIsPaid TINYINT NOT NULL DEFAULT 1 AFTER timePayRunIsProcessed;');

        DB::statement('ALTER TABLE PayRunType MODIFY COLUMN code INT NULL AFTER deleted_at;');

        Schema::table('PayRunType', function (Blueprint $table) {
            $table->tinyInteger('daysForAutoSubmission')->default(0)->after('dueDayForTimeSheets');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('PayRunType', 'daysForAutoSubmission')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('daysForAutoSubmission');
            });
        }
    }
};
