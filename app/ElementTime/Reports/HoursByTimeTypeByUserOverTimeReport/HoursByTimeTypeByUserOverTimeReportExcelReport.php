<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\HoursByTimeTypeByUserOverTimeReport;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Repositories\PayRunRepository;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShift;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftTimeType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;
use stdClass;

class HoursByTimeTypeByUserOverTimeReportExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'hours-by-time-type-by-user-over-time';
    const TYPE_SLUG = 'hours-by-time-type-by-user-over-time.xls';

    public $c = HoursByTimeTypeByUserOverTimeReportConfig::class;

    /** @var Collection|PayRun[]|null */
    public Collection|array|null $payRuns = null;

    public User|null $user = null;

    protected float $totalActual = 0.0;
    protected float $totalAdjusted = 0.0;

    protected ?array $roleReportUserRoleIds = null;
    protected ?array $directReportUserIds = null;

    protected function setData(array $info)
    {
        $this->_iSetData($info);

        $this->user = User::q(
            constraints: [
                ['User.id', '=', $info['idUser']],
            ],
        )->firstOrFail();
    }

    protected function collectReportData(): array
    {
        $userShiftTimeTypes = UserShiftTimeType::q(
            constraints: [
                ['UserShift.startDate', '<=', $this->getEndDate()],
                ['UserShift.user_id', '=', $this->user->id],
                [
                    [
                        ['IS NULL', 'UserShift.endDate', 'OR'],
                        ['UserShift.endDate', '=', '0000-00-00', 'OR'],
                        ['UserShift.endDate', '>=', $this->getStartDate(), 'OR'],
                    ],
                ],
            ],
            order: 'CAST(User.externalId AS SIGNED), TimeType.is_deletable, TimeType.isOrdinary DESC, TimeType.hourCode, TimeType.name',
            select: [
                'UserShiftTimeType.id',
                'UserShiftTimeType.userShift_id',
                'UserShiftTimeType.timeType_id',
            ],
        )->many();


        $userShiftTimeTypes->load([
            'timeType:id,name,hourCode,description,status',
            'userShift:id,startDate,endDate,userRole_id,shift_id,user_id',
            'userShift.userRole:id,role_id',
            'userShift.userRole.role:id,name',
            'userShift.shift:id,name',
            'userShift.user:id,externalId,nameFirst,nameLast,employeeType_id',
            'userShift.user.employeeType:id,name',
            'userShift.userRole.userRoleDepartments:id,userRole_id,department_id,startDate,endDate',
            'userShift.userRole.userRoleDepartments.department:id,name',
        ]);

        $this->payRuns = PayRunRepository::getMany([
            'constraints' => [
                ['PayRun.startDate', '>=', $this->getStartDate()],
                ['PayRun.endDate', '<=', $this->getEndDate()],
            ],
            'order' => 'PayRun.startDate',
            'returnType' => 'model',
        ]);

        $ret = [];

        foreach ($userShiftTimeTypes as $userShiftTimeType) {
            if (!$this->isUserShiftTimeTypeRelevant($userShiftTimeType)) {
                continue;
            }

            $timeType = $userShiftTimeType->timeType;
            $user = $userShiftTimeType->userShift->user;
            $userRole = $userShiftTimeType->userShift->userRole;

            $department = '';

            foreach ($userRole->userRoleDepartments as $userRoleDepartment) {
                if (!$userRoleDepartment->availability($this->payRunStart->startDate, $this->payRunEnd->endDate)->isCurrent(true)) {
                    continue;
                }

                if (strlen($department) > 0) {
                    $department .= ', ';
                }

                $department .= $userRoleDepartment->department->name;
            }

            $ret['_' . $userShiftTimeType->id] = [
                'timeType' => [
                    'id' => $timeType->id,
                    'name' => $timeType->name,
                    'hourCode' => $timeType->hourCode,
                    'description' => $timeType->description,
                    'status' => $timeType->statusClass::NAME,
                ],
                'user' => [
                    'id' => $user->id,
                    'externalId' => $userRole->repository->getExternalId(),
                    'lastName' => $user->nameLast,
                    'firstName' => $user->nameFirst,
                    'employeeType' => $user->employeeType->name,
                    'role' => $userRole->role->name,
                    'department' => $department,
                    'shift' => $userShiftTimeType->userShift->shift->name,
                ],
            ];
        }

        foreach ($this->payRuns as $payRun) {
            $payRun->load([
                'payRunItems' => fn($query) => $query->select()->where('user_id', '=', $this->user->id),
                'payRunItems:id,payRun_id,user_id',
                'payRunItems.timeSheet:id,payRunItem_id',
                'payRunItems.timeSheet.workflow:id,status',
                'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay:id,userShift_id',
                'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift:id,userRole_id',
                'payRunItems.timeSheet.timeSheetDayTimes:TimeSheetDayTime.id,userShiftDay_id,recordType,minutes,userShiftTimeType_id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks:id,timeSheetExcessTimeItemRule_id,actualMinutes,adjustedHours,timeSheetDayTime_id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule:id,timeSheetExcessTimeItem_id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.item:id,timeSheetExcessTime_id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.item.timeSheetExcessTime:id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.rule.item.timeSheetExcessTime.items:id,originalTimeSheetExcessTimeItem_id,timeSheetExcessTime_id',
                'payRunItems.timeSheet.timeSheetDayTimes.excessTimeWorks.splitItems:actualHours,adjustedHours,timeSheetExcessTimeItemRuleWork_id',
            ]);

            foreach ($userShiftTimeTypes as $userShiftTimeType) {
                if ($this->isUserShiftRelevantForPayRun($userShiftTimeType->userShift, $payRun)) {
                    $obj = new stdClass();
                    $obj->actual = 0;
                    $obj->adjusted = 0;
                    $ret['_' . $userShiftTimeType->id]['_' . $payRun->id] = $obj;
                }
            }

            foreach ($payRun->payRunItems as $payRunItem) {
                if (is_null($payRunItem->timeSheet) || $payRunItem->timeSheet->repository->isExcluded()) {
                    continue;
                }

                $doesPayRunItemBelongsToReportUser = false;
                if (!is_null($this->directReportUserIds)) {
                    $doesPayRunItemBelongsToReportUser = in_array($payRunItem->user->id, $this->directReportUserIds);
                    if (is_null($this->roleReportUserRoleIds) && !$doesPayRunItemBelongsToReportUser) {
                        continue;
                    }
                }

                foreach ($payRunItem->timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
                    if (!$timeSheetDayTime->recordTypeClass::IS_WORK) {
                        continue;
                    }

                    $doesTimeSheetDayTimeBelongsToRoleReport = false;
                    if (!is_null($this->roleReportUserRoleIds)) {
                        $userRole_id = $timeSheetDayTime->userShift->userRole_id;
                        $doesTimeSheetDayTimeBelongsToRoleReport = in_array($userRole_id, $this->roleReportUserRoleIds);
                    }

                    if (!is_null($this->directReportUserIds) && !is_null($this->roleReportUserRoleIds)) {
                        if (!$doesPayRunItemBelongsToReportUser && !$doesTimeSheetDayTimeBelongsToRoleReport) {
                            continue;
                        }
                    } elseif (!is_null($this->roleReportUserRoleIds) && !$doesTimeSheetDayTimeBelongsToRoleReport) {
                        continue;
                    }

                    $actual = $timeSheetDayTime->hours;
                    $adjusted = $timeSheetDayTime->hours;

                    foreach ($timeSheetDayTime->excessTimeWorks as $excessTimeWork) {
                        if ($excessTimeWork->rule->item->isActive) {
                            foreach ($excessTimeWork->splitItems as $splitItem) {
                                $adjusted -= $splitItem->actualHours;
                                $adjusted += $splitItem->adjustedHours;
                            }
                        }
                    }

                    if (!isset($ret['_' . $timeSheetDayTime->userShiftTimeType_id])) {
                        continue;
                    }

                    $ret['_' . $timeSheetDayTime->userShiftTimeType_id]['_' . $payRun->id]->actual += $actual;
                    $this->totalActual += $actual;
                    $ret['_' . $timeSheetDayTime->userShiftTimeType_id]['_' . $payRun->id]->adjusted += $adjusted;
                    $this->totalAdjusted += $adjusted;
                }

                unset($payRunItem->timeSheet->timeSheetDayTimes, $payRunItem->timeSheet);
            }

            unset($payRun->payRunItems);
        }

        return $ret;
    }

    protected function isUserShiftTimeTypeRelevant(UserShiftTimeType $userShiftTimeType): bool
    {
        foreach ($this->payRuns as $payRun) {
            if ($this->isUserShiftRelevantForPayRun($userShiftTimeType->userShift, $payRun)) {
                return true;
            }
        }

        return false;
    }

    protected function isUserShiftRelevantForPayRun(UserShift $userShift, PayRun $payRun): bool
    {
        return
            $userShift->startDate->lte($payRun->endDate)
            && (
                is_null($userShift->endDate)
                || $userShift->endDate->lt(Carbon::minValue())
                || $userShift->endDate->gte($payRun->startDate)
            );
    }

    /**
     * @throws InvalidArgumentException
     */
    protected function buildReportData(): void
    {
        $data = $this->collectReportData();

        $this->setSheetTitle($sheet, 'Hours by time type by user over time');
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function fetchSheetTotals(stdClass|null &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Date/time', 'text', 'center', 3),
            $this->createColumn('Span of report', 'text', 'center', 3),
            $this->createColumn('Total actual hours recorded', 'float', 'center', 2),
            $this->createColumn('Total hours adjusted', 'float', 'center', 2),
        ];

        if ($this->payRunStart->id == $this->payRunEnd->id) {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear;
        } else {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear . ' to Payrun #' . $this->payRunEnd->number . ' ' . $this->payRunEnd->fiscalYear;
        }

        $data->data = [[
            'config' => [],
            'data' => [
                $this->parseDate(Carbon::now()), // Date/time
                $span, // Span of report
                $this->parseFloat($this->totalActual), // Total hours paid recorded
                $this->parseFloat($this->totalAdjusted), // Total hours paid adjusted
            ],
        ]];

        $sheet->data[] = $data;
    }

    /**
     * @param Collection|stdClass[] $data
     */
    protected function fetchSheetData(stdClass &$sheet = null, $data = []): void
    {
        $this->setSheetDefaultSettings($sheet);

        $table = new stdClass();
        $table->type = 'data-table';
        $table->parentColumns = $this->getSheetDataParentHeader();
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($data);
        $sheet->data[] = $table;
    }

    protected function getSheetDataParentHeader(): array
    {
        $ret = [
            $this->createParentColumn(''),
            $this->createParentColumn('Staff', 7),
            $this->createParentColumn('Time type', 3),
        ];

        foreach ($this->payRuns as $payRun) {
            $ret[] = $this->createParentColumn('Payrun #' . $payRun->number . ' ' . $payRun->fiscalYear, 2);
        }

        return $ret;
    }

    protected function getSheetDataHeader(): array
    {
        $ret = [
            $this->createColumn('#', 'integer', 'center'),
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Last name'),
            $this->createColumn('First name'),
            $this->createColumn('Employee type'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
            $this->createColumn('Shift'),

            $this->createColumn('Hour code'),
            $this->createColumn('Name'),
            $this->createColumn('Current status'),
        ];

        foreach ($this->payRuns as $payRun) {
            if (!$payRun instanceof PayRun) {
                continue;
            }

            $ret[] = $this->createFloatColumn('Actual hours');
            $ret[] = $this->createFloatColumn('Adjusted hours');
        }

        return $ret;
    }

    /**
     * @param Collection|stdClass[] $data
     * @return array
     */
    protected function getSheetDataData($data = []): array
    {
        $tableData = [];

        $i = 1;
        foreach ($data as $datum) {
            $data = $this->getItemData($datum, $i);

            if (is_null($data)) {
                continue;
            }

            $rowConfig = [
                'color' => $i % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => $data,
            ];

            $i++;
        }

        return $tableData;
    }

    protected function getItemData(array $item, int $num): array|null
    {
        $ret = [
            $num, // #
            $item['user']['externalId'], // Ext ref ID
            $item['user']['lastName'], // Last name
            $item['user']['firstName'], // First name
            $item['user']['employeeType'], // Employee type
            $item['user']['role'], // Role
            $item['user']['department'], // Department
            $item['user']['shift'], // Shift
            $item['timeType']['hourCode'], // Hour code
            $item['timeType']['name'], // Name
            $item['timeType']['status'], // Current status
        ];

        $totalActual = 0;
        $totalAdjusted = 0;

        foreach ($this->payRuns as $payRun) {
            if (isset($item['_' . $payRun->id])) {
                $ret[] = $this->parseFloat($item['_' . $payRun->id]->actual);
                $ret[] = $this->parseFloat($item['_' . $payRun->id]->adjusted);

                $totalActual += $this->parseFloat($item['_' . $payRun->id]->actual);
                $totalAdjusted += $this->parseFloat($item['_' . $payRun->id]->adjusted);
            } else {
                $ret[] = 'N/A';
                $ret[] = 'N/A';
            }
        }

        if ($totalActual <= 0 && $totalAdjusted <= 0) {
            return null;
        }

        return $ret;
    }
}
