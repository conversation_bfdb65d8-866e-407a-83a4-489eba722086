<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Traits\Functionalities\Tenant\Workflows;

use Element\ElementTime\Domains\Tenant\Workflows\Contracts\ModelActionTypeModelRelContract;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeOutcomeType\ModelActionTypeOutcomeType;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeType\ModelActionTypeType;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelActionType;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelActionTypeModel;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelActionTypeOutcome;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;

trait ModelActionGeneralFunctionality
{
    public function w_mag_createLeaveModelAction(ModelActionTypeModelRelContract|null $typeRel, \Closure|null $afterAll = null): ModelActionType
    {
        $modelActionType = new ModelActionType;
        $modelActionType->name = 'Test';
        $modelActionType->description = 'It should require comments and attachment on submit';
        $modelActionType->type = ModelActionTypeType::Leave;
        $modelActionType->status = ActiveStatus::ID;
        $modelActionType->save();

        $modelActionTypeModel = new ModelActionTypeModel;
        $modelActionTypeModel->model_type = is_null($typeRel) ? null : $typeRel::class;
        $modelActionTypeModel->model_id = $typeRel?->id;
        $modelActionTypeModel->modelActionType_id = $modelActionType->id;
        $modelActionTypeModel->save();

        if (!is_null($afterAll)) {
            $afterAll($modelActionType);
        }

        return $modelActionType;
    }

    public function w_mag_createLeaveModelActionWithSubmitRequirements(ModelActionTypeModelRelContract|null $typeRel, \Closure|null $afterAll = null): ModelActionType
    {
        $modelActionType = $this->w_mag_createLeaveModelAction($typeRel, function (ModelActionType $newType) {
            $modelActionTypeOutcomeRequireAttachment = new ModelActionTypeOutcome;
            $modelActionTypeOutcomeRequireAttachment->type = ModelActionTypeOutcomeType::Leave_RequireAttachmentPriorToSubmit;
            $modelActionTypeOutcomeRequireAttachment->modelActionType_id = $newType->id;
            $modelActionTypeOutcomeRequireAttachment->options = '{}';
            $modelActionTypeOutcomeRequireAttachment->save();

            $modelActionTypeOutcomeRequireComments = new ModelActionTypeOutcome;
            $modelActionTypeOutcomeRequireComments->type = ModelActionTypeOutcomeType::Leave_RequireCommentPriorToSubmit;
            $modelActionTypeOutcomeRequireComments->modelActionType_id = $newType->id;
            $modelActionTypeOutcomeRequireComments->options = '{}';
            $modelActionTypeOutcomeRequireComments->save();
        });

        if (!is_null($afterAll)) {
            $afterAll($modelActionType);
        }

        return $modelActionType;
    }

    public function w_mag_createLeaveModelActionWithApproveRequirements(ModelActionTypeModelRelContract|null $typeRel, \Closure|null $afterAll = null): ModelActionType
    {
        $modelActionType = $this->w_mag_createLeaveModelAction($typeRel, function (ModelActionType $newType) {
            $modelActionTypeOutcomeRequireAttachment = new ModelActionTypeOutcome;
            $modelActionTypeOutcomeRequireAttachment->type = ModelActionTypeOutcomeType::Leave_RequireAttachmentPriorToApprove;
            $modelActionTypeOutcomeRequireAttachment->modelActionType_id = $newType->id;
            $modelActionTypeOutcomeRequireAttachment->options = '{}';
            $modelActionTypeOutcomeRequireAttachment->save();

            $modelActionTypeOutcomeRequireComments = new ModelActionTypeOutcome;
            $modelActionTypeOutcomeRequireComments->type = ModelActionTypeOutcomeType::Leave_RequireCommentPriorToApprove;
            $modelActionTypeOutcomeRequireComments->modelActionType_id = $newType->id;
            $modelActionTypeOutcomeRequireComments->options = '{}';
            $modelActionTypeOutcomeRequireComments->save();
        });

        if (!is_null($afterAll)) {
            $afterAll($modelActionType);
        }

        return $modelActionType;
    }
}
