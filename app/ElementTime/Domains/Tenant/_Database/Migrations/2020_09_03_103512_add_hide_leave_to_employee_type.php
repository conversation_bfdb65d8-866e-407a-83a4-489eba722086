<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        \Illuminate\Support\Facades\DB::statement('ALTER TABLE EmployeeType MODIFY COLUMN payRunType_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER externalId;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE EmployeeType MODIFY COLUMN rosteredTimeOffType_id BIGINT(20) UNSIGNED DEFAULT NULL AFTER allowsRosterDayOff;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE EmployeeType MODIFY COLUMN isFTE TINYINT(1) NOT NULL DEFAULT 1 AFTER workHours;');

        Schema::table('EmployeeType', function (Blueprint $table) {
            $table->boolean('doesHideLeave')->default(false)->after('isFTE');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('EmployeeType', 'doesHideLeave')) {
            Schema::table('EmployeeType', function (Blueprint $table) {
                $table->dropColumn('doesHideLeave');
            });
        }
    }
};
