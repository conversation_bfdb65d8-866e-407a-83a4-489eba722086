<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays;

use Carbon\CarbonInterface;
use Element\Core\Support\ProcessCache;
use Element\ElementTime\Actions\Tenant\Schedules\BaseSchedulesAction;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledDayStructContract;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDefaultData;
use Illuminate\Support\Collection;

abstract class BaseGetScheduledDays extends BaseSchedulesAction
{
    /**
     * @return Collection<StructScheduledPayRunPeriodDayDefaultData|ScheduledPayRunPeriodDay>|StructScheduledPayRunPeriodDayDefaultData[]|ScheduledPayRunPeriodDay[]
     * @throws \Throwable
     */
    public function handle(
        UserRoleSchedule $userRoleSchedule,
        CarbonInterface $startDate,
        CarbonInterface|null $endDate = null,
        bool $clearProcessCache = false,
    ): Collection|array
    {
        if (is_null($endDate)) {
            $endDate = $startDate->copy();
        }

        $processId = ProcessCache::ID(__METHOD__, $userRoleSchedule->id, $startDate, $endDate);

        if (!!$clearProcessCache) {
            ProcessCache::delete($processId);
        }

        return ProcessCache::getOrSet($processId, function () use ($userRoleSchedule, $startDate, $endDate) {
            $payRuns = PayRun::getManyByPeriod(start: $startDate, end: $endDate);

            $scheduledPayRuns = ScheduledPayRun::q()
                ->constraints([
                    ['ScheduledPayRun.userRoleSchedule_id', '=', $userRoleSchedule->id],
                    ['ScheduledPayRun.startDate', '<=', $payRuns->last()->endDate->toDateString()],
                    ['ScheduledPayRun.endDate', '>=', $payRuns->first()->startDate->toDateString()],
                ])
                ->many();

            $ret = Collection::make();

            foreach ($payRuns as $payRun) {
                $this->buildScheduledDaysByPayRun($ret, $userRoleSchedule, $scheduledPayRuns, $payRun);
            }

            return $ret;
        });
    }

    abstract protected function makeDayActual(
        UserRoleSchedule $userRoleSchedule,
        ScheduledPayRunPeriodDay $dayData,
        ScheduledPayRunPeriod $scheduledPayRunPeriod,
    ): ScheduledDayStructContract;

    abstract protected function makeDayDefault(
        UserRoleSchedule $userRoleSchedule,
        StructScheduledPayRunPeriodDayDefaultData $dayData,
        StructScheduledPayRunPeriodDefaultData $specificPeriod,
    ): ScheduledDayStructContract;

    //--------------------------------------------------------------------

    /**
     * @param Collection|ScheduledPayRun[] $scheduledPayRuns
     * @throws \Throwable
     */
    protected function buildScheduledDaysByPayRun(
        Collection $scheduledPayRunPeriodDays,
        UserRoleSchedule $userRoleSchedule,
        Collection|array $scheduledPayRuns,
        PayRun $payRun,
    ): void
    {
        $scheduledPayRun = $scheduledPayRuns->where(fn (ScheduledPayRun $check) => $check->startDate->eq($payRun->startDate))->first();

        if (is_null($scheduledPayRun)) {
            $scheduledPayRunDefaultData = $userRoleSchedule->specificAssignment->getScheduledPayRunDefaultData($payRun);
            $scheduledPayRunPeriodsDefaultData = $userRoleSchedule->specificAssignment->getScheduledPayRunPeriodDefaultData($scheduledPayRunDefaultData);

            foreach ($scheduledPayRunPeriodsDefaultData as $scheduledPayRunPeriodDefaultData) {
                $scheduledPayRunPeriodDaysDefaultData = $userRoleSchedule->specificAssignment->getScheduledPayRunPeriodDayDefaultData($scheduledPayRunPeriodDefaultData);
                foreach ($scheduledPayRunPeriodDaysDefaultData as $scheduledPayRunPeriodDayDefaultData) {
                    $scheduledPayRunPeriodDays->push($this->makeDayDefault($userRoleSchedule, $scheduledPayRunPeriodDayDefaultData, $scheduledPayRunPeriodDefaultData));
                }
            }
        } else {
            foreach ($scheduledPayRun->periods as $scheduledPayRunPeriod) {
                foreach ($scheduledPayRunPeriod->days as $scheduledPayRunPeriodDay) {
                    $scheduledPayRunPeriodDays->push($this->makeDayActual($userRoleSchedule, $scheduledPayRunPeriodDay, $scheduledPayRunPeriod));
                }
            }
        }
    }
}
