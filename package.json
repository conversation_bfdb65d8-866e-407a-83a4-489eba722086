{"name": "elementtime", "description": "elementTIME", "private": true, "authors": ["Adroit Creations <<EMAIL>>"], "dependencies": {"dotenv": "^4.0.0", "express": "4.16.1", "fs": "0.0.1-security", "gulp-clean-css": "3.9.0", "gulp-concat": "2.6.1", "gulp-concat-sourcemap": "1.3.1", "gulp-filter": "5.0.1", "gulp-if": "2.0.2", "gulp-jshint": "2.0.4", "gulp-ng-annotate": "2.0.0", "gulp-notify": "3.0.0", "gulp-sass": "5.1.0", "gulp-uglify": "3.0.0", "ioredis": "3.1.4", "jshint-stylish": "2", "laravel-elixir": "^6.0.0-18", "laravel-elixir-livereload": "1.1.6", "sass": "^1.87.0", "socket.io": "2.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "brfs": "^2.0.2", "browserify": "^16.5.2", "browserify-shim": "^3.8.16", "css-flip": "0.5.0", "del": "3.0.0", "gulp": "3.9.1", "gulp-angular-htmlify": "2.3.0", "gulp-angular-templatecache": "^2.2.7", "gulp-babel": "^8.0.0", "gulp-changed": "3.1.0", "gulp-compass": "2.1.0", "gulp-css-flip": "0.4.0", "gulp-expect-file": "^1.0.2", "gulp-html-prettify": "0.0.1", "gulp-ignore": "^2.0.2", "gulp-inject": "4.3.0", "gulp-jsvalidate": "3.0.0", "gulp-less": "^4.0.1", "gulp-load-plugins": "1.5.0", "gulp-pug": "3.3.0", "gulp-rename": "1.2.2", "gulp-sourcemaps": "^2.6.4", "gulp-strip-comments": "^2.6.0", "gulp-sync": "0.1.4", "gulp-util": "3.0.8", "html": "^0.0.7", "jasmine-core": "^2.4.1", "karma": "^4.4.1", "karma-browserify": "^6.1.0", "karma-chrome-launcher": "^1.0.1", "karma-jasmine": "^1.0.2", "karma-ng-html2js-preprocessor": "^1.0.0", "karma-sourcemap-loader": "^0.3.8", "karma-spec-reporter": "0.0.26", "less": "^2.7.3", "marked": "^0.5.1", "sass": "^1.87.0", "through2": "2.0.3", "watchify": "^3.11.1", "yargs": "9.0.1"}, "engines": {"node": "8.12.0", "npm": "6.4.1"}, "scripts": {"prestart": "bower install", "start": "npm install", "poststart": "gulp", "test": "./node_modules/karma/bin/karma start"}, "browserify-shim": {}}