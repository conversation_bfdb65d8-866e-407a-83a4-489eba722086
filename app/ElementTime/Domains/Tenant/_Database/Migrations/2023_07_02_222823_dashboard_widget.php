<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\General\Enums\DashboardWidgetType\DashboardWidgetType;
use Element\ElementTime\Support\Domains\Type\StatusTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('DashboardWidget', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('type')->default(DashboardWidgetType::getDefaultCase()->id());
            $table->string('title');
            $table->text('content')->nullable();
            $table->string('buttonLabel')->nullable();
            $table->text('url')->nullable();
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->char('status', 1)->default(StatusTypes\ActiveStatus::ID);
            $table->index(['startDate', 'endDate', 'status'], 'period_ix');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('DashboardWidget');
    }
};
