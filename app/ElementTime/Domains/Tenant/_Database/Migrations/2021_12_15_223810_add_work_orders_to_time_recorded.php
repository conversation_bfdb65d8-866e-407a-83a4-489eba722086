<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeRecordingMethods\ProjectsOnlyMethod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table TimeSheetDayTime

        Schema::table('TimeSheetDayTime', function (Blueprint $table) {
            $table->string('timeRecordingMethod')->default(ProjectsOnlyMethod::ID)->after('endDateTime');
        });

        //endregion Table TimeSheetDayTime

        //region Table TimeSheetDayTimeWork

        Schema::create('TimeSheetDayTimeWork', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetDayTime_id');
            $table->foreign('timeSheetDayTime_id', 'tsdtw_tsdt_fn')
                ->references('id')->on('TimeSheetDayTime')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->unsignedBigInteger('workOrderType_id')->nullable();
            $table->foreign('workOrderType_id', 'tsdtw_wot_fn')
                ->references('id')->on('WorkOrderType')
                ->onUpdate('cascade')
                ->onDelete('restrict');
            $table->unsignedBigInteger('activityType_id')->nullable();
            $table->foreign('activityType_id', 'tsdtw_at_fn')
                ->references('id')->on('ActivityType')
                ->onUpdate('cascade')
                ->onDelete('restrict');

            $table->integer('minutes');

            $table->string('taskName')->nullable();
            $table->string('taskCode')->nullable();
            $table->text('description')->nullable();

            $table->timestamps();
        });

        //endregion Table TimeSheetDayTimeWork
    }

    public function down(): void
    {
        //region Table TimeSheetDayTimeWork

        Schema::dropIfExists('TimeSheetDayTimeWork');

        //endregion Table TimeSheetDayTimeWork

        //region Table TimeSheetDayTime

        if (Schema::hasColumn('TimeSheetDayTime', 'timeRecordingMethod')) {
            Schema::table('TimeSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('timeRecordingMethod');
            });
        }

        //endregion Table TimeSheetDayTime
    }
};
