<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet;

use Carbon\CarbonInterface;
use Element\Core\Support\ProcessCache;
use Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRun\CreateScheduleForUserOnPayRun;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem;
use Element\ElementTime\Domains\Tenant\Schedules\Models\CalendarDate;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ExcludedType;
use Element\ElementTime\Support\Database\EDB;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

class GetTimeSheetForUser extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        User $user,

        PayRun|CarbonInterface|null $target = null,
        array $relations = [],
        bool $checkSchedules = false,
        bool $onlyExisting = false,
        bool $recalculateExisting = false,
        bool $doesCreateFuture = false,
        bool $asyncCalculations = false,
        bool $refreshCache = false,
    ): TimeSheet|null
    {
        if (!$target instanceof PayRun) {
            $target = TenantSystemSettings::getPayRunByDate($target);
        }

        if ($target->exists() && $checkSchedules) {
            foreach ($user->getActiveUserRoles($target->startDate) as $userRole) {
                $schedule = $userRole->getActiveSchedule($target->startDate);

                if (is_null($schedule)) {
                    continue;
                }

                CreateScheduleForUserOnPayRun::make()->handle($schedule, $target);
            }

            $refreshCache = true;
        }

        $processId = ProcessCache::ID(__METHOD__, $user, $target, $relations);

        if ($refreshCache) {
            ProcessCache::delete($processId);
        }

        return ProcessCache::getOrSet($processId, function () use ($user, $target, $relations, $onlyExisting, $recalculateExisting, $doesCreateFuture, $asyncCalculations) {
            $timeSheet = $this->getExisting($user, $target);

            if (!is_null($timeSheet)) {
                if ($recalculateExisting) {
                    if ($asyncCalculations) {
                        CalculateTimeSheet::dispatch(
                            timeSheet: $timeSheet,
                            allCalculations: true,
                        )->afterResponse()->delay(1);
                    } else {
                        CalculateTimeSheet::make()->handle(
                            timeSheet: $timeSheet,
                            allCalculations: true,
                        );
                    }
                }

                $timeSheet->loadMissing($relations);

                return $timeSheet;
            }

            if ($onlyExisting) {
                return null;
            }

            $payRunItem = PayRunItem::getFromUserByDate($user, $target->startDate, [], $doesCreateFuture);

            if (is_null($payRunItem) || is_null($payRunItem->id) || !$payRunItem->payRun->status->isOpen()) {
                return null;
            }

            return EDB::tenantConn()->transaction(function () use ($user, $target, $payRunItem, $asyncCalculations) {
                $timeSheet = new TimeSheet();
                $timeSheet->payRunItem_id = $payRunItem->id;
                $timeSheet->clearSaveOrFail(); // TODO: Update to `saveOrFail()` after model is updated

                $timeSheet->repository->checkWorkflow($user);
                $this->createDays($target, $timeSheet);
                $timeSheet->setScheduleInfo(isNew: true);

                if ($asyncCalculations) {
                    CalculateTimeSheet::dispatch(
                        timeSheet: $timeSheet,
                        allCalculations: true,
                    )->afterResponse()->delay(1);
                } else {
                    CalculateTimeSheet::make()->handle(
                        timeSheet: $timeSheet,
                        allCalculations: true,
                    );
                }

                // TODO: In this case - what broadcast events need to be triggered? (user's for profile, ...)

                return $timeSheet;
            });
        });
    }

    /** @throws \Throwable */
    private function getExisting(User $user, PayRun $payRun): TimeSheet|null
    {
        $timeSheet = TimeSheet::q()
            ->constraints([
                ['PayRunItem.user_id', '=', $user->id],
                ['PayRunItem.payRun_id', '=', $payRun->id],
            ])
            ->first();

        if (is_null($timeSheet)) {
            return null;
        }

        if (!$timeSheet->payRun->status->isOpen() && !$timeSheet->repository->isApproved() && !$timeSheet->repository->isExcluded()) {
            $timeSheet->workflow->status = ExcludedType::ID;
            $timeSheet->workflow->clearSave();
            $timeSheet->load('workflow');
        }

        return $timeSheet;
    }

    /** @throws \Throwable */
    private function createDays(PayRun $payRun, TimeSheet $timeSheet): void
    {
        $hasChanges = false;

        $startDate = $payRun->startDate->copy();
        $endDate = $payRun->endDate->copy();
        $date = $startDate->copy();

        while ($date->lte($endDate)) {
            $timeSheetDay = new TimeSheetDay;
            $timeSheetDay->timeSheet_id = $timeSheet->id;
            $timeSheetDay->date = $date->toDateString();
            $timeSheetDay->calendarDate_id = CalendarDate::getByDate($date)->id;

            $timeSheetDay->clearSaveOrFail(); // TODO: Change to `saveOrFail` after model is updated

            $date->addDays(1);
        }

        $timeSheet->load('timeSheetDays');
    }
}
