<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->dropForeign('usershiftday_us_fn');
            $table->dropForeign('usd_leave_fn');
            $table->dropForeign('usershiftday_rdo_fn');
            $table->dropPrimary();
        });

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreign('userShift_id', 'usershiftday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leave_id', 'usd_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('rosteredTimeOffType_id', 'usershiftday_rdo_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->bigInteger('userShiftDay_id')->unsigned();
        });

        DB::table('LeaveApproval')->delete();
        DB::table('LeaveDay')->delete();
        DB::table('Leave')->delete();

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->foreign('userShiftDay_id', 'leaveday_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->dropForeign('leaveday_leave_fn');
            $table->dropForeign('leaveday_us_fn');
            $table->dropPrimary();
        });

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->foreign('leave_id', 'leaveday_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'leaveday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->boolean('hasTimer')->default(false);
        });

        Schema::create('TimeSheetDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('publicHoliday_id')->unsigned()->nullable();
            $table->bigInteger('ELT_PublicHoliday_id')->unsigned()->nullable();

            $table->date('date');
            $table->decimal('hoursWorked', 16, 10)->nullable();
            $table->decimal('hoursExpected', 16, 10)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheet_id', 'tsd_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetItemDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('timeSheetItem_id')->unsigned();
            $table->bigInteger('timeSheetDay_id')->unsigned();

            $table->date('date')->nullable();
            $table->decimal('hours', 16, 10)->nullable();
            $table->string('notes')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetItem_id', 'tsid_tsi_fn')->references('id')->on('TimeSheetItem')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('timeSheetDay_id', 'tsid_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('timeSheetItemDay_id')->unsigned();
            $table->bigInteger('userShiftDay_id')->unsigned()->nullable();

            $table->decimal('hours', 16, 10)->nullable();

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetItemDay_id', 'tsidt_tsid_fn')->references('id')->on('TimeSheetItemDay')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('userShiftDay_id', 'tsidt_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('set null');
        });

        $entries = DB::table('UserLeaveTypeEntry')->get();
        foreach ($entries as $entry) {
            DB::table('UserLeaveTypeEntry')->where('id', $entry->id)->update(['previous_id' => null]);
        }
        DB::table('UserLeaveTypeEntry')->delete();
        DB::table('UserLeaveType')->delete();
        DB::table('TimeSheetItem')->delete();
        DB::table('TimeSheetApproval')->delete();
        DB::table('TimeSheet')->delete();
    }

    public function down(): void
    {
        Schema::dropIfExists('TimeSheetItemDayTime');
        Schema::dropIfExists('TimeSheetItemDay');
        Schema::dropIfExists('TimeSheetDay');

        if (Schema::hasColumn('TimeSheet', 'hasTimer')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hasTimer');
            });
        }

        if (Schema::hasColumn('LeaveDay', 'id')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropForeign('leaveday_leave_fn');
                $table->dropForeign('leaveday_us_fn');
                $table->dropColumn('id');
            });

            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->primary(['leave_id', 'date'], 'leaveday_unique');
                $table->foreign('leave_id', 'leaveday_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
                $table->foreign('userShift_id', 'leaveday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            });
        }

        $leaveDayForeign = DB::select('SELECT count(*) as n FROM information_schema.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = "' . Schema::getConnection()->getDatabaseName() . '" AND TABLE_NAME = "LeaveDay" AND CONSTRAINT_NAME = "leaveday_usd_fn"');
        if ($leaveDayForeign[0]->n > 0) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropForeign('leaveday_usd_fn');
            });
        }

        if (Schema::hasColumn('LeaveDay', 'userShiftDay_id')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('userShiftDay_id');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'id')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropForeign('usershiftday_us_fn');
                $table->dropForeign('usd_leave_fn');
                $table->dropForeign('usershiftday_rdo_fn');
                $table->dropColumn('id');
            });

            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->primary(['userShift_id', 'date']);
                $table->foreign('userShift_id', 'usershiftday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
                $table->foreign('leave_id', 'usd_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
                $table->foreign('rosteredTimeOffType_id', 'usershiftday_rdo_fn')->references('id')->on('RosteredTimeOffType')->onUpdate('cascade')->onDelete('restrict');
            });
        }
    }
};
