<?php

namespace Element\ElementTime\Domains\Tenant\_System\Enums\KioskDeviceRequestStatus;

use <PERSON>cello\SweetEnum\SweetCase;
use Leocello\SweetEnum\SweetEnum;
use Leocello\SweetEnum\SweetEnumContract;

/**
 * @method bool isPending()
 */
enum KioskDeviceRequestStatus : string implements SweetEnumContract
{
    use SweetEnum;

    const DEFAULT = self::Pending;

    #[SweetCase(
        color: 'warning',
        isPending: true,
    )]
    case Pending = 'pending';

    #[SweetCase(
        color: 'success',
        isPending: false,
    )]
    case Approved = 'approved';

    #[SweetCase(
        color: 'danger',
        isPending: false,
    )]
    case Declined = 'declined';

    #[SweetCase(
        color: 'gray',
        isPending: false,
    )]
    case Cancelled = 'cancelled';

    public function canChangeTo(KioskDeviceRequestStatus $newStatus): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        return match($newStatus) {
            self::Pending => false,
            default => true,
        };
    }
}
