div(ng-if='!!$availabilityBoxForm.calculated.warningMessage && $availabilityBoxForm.calculated.warningMessage.length > 0')
  .row
    .col-xs-12.col-md-8.col-md-offset-2.mt.mb
      .list-group.mb0
        .list-group-item.list-group-item-warning
          .text-center(ng-bind-html='$availabilityBoxForm.calculated.warningMessage')

.row
  .col-xs-12.col-md-8.col-md-offset-2.mt.mb
    .p.rounded(class='{{$availabilityBoxForm.calculated.afterAvailableBalance < 0 ? "bg-warning" : "bg-info"}}')
      div(ng-if='$availabilityBoxForm.selectedShift.type.id != "G" && !$availabilityBoxForm.data.isFullDay && !!$availabilityBoxForm.data.isApplyingByDuration')
        .row
          .col-xs-4
            .h3.mt0.sub.mb0 Start
          .col-xs-8.text-right
            .h4.mt0.mb0 {{$availabilityBoxForm.data.startDate | _Elt$DateFormat}} at {{$availabilityBoxForm.data.startTime | _Elt$TimeFormat : false}}
        .row
          .col-xs-12
            hr.mt-sm.mb-sm
        .row
          .col-xs-4
            .h3.mt0.sub.mb0 End
          .col-xs-8.text-right
            .h4.mt0.mb0 {{$availabilityBoxForm.data.endDate | _Elt$DateFormat}} at {{$availabilityBoxForm.data.endTime | _Elt$TimeFormat : false}}
        .row
          .col-xs-12
            hr.mt-sm.mb-sm
      .row
        .col-xs-6
          .h3.mt0.sub.mb0 Hours to take in this request
        .col-xs-6.text-right
          .h1.mt0.mb0 {{$availabilityBoxForm.calculated.leaveHours | number : 2}}
          span(ng-if='($availabilityBoxForm.calculated.leaveHours !== $availabilityBoxForm.calculated.hours)')
            .h5.sub.mt5.mb0 * ({{$availabilityBoxForm.calculated.hours | number : 2}} hours FTE adjusted)
      .row(ng-if='$availabilityBoxForm.calculated.afterAvailableBalance >= 0 || !!$availabilityBoxForm.calculated.doesShowNegativeBalance')
        .col-xs-12
          hr.mt-sm.mb-sm
        .col-xs-6
          .h3.mt0.sub.mb0 Estimated remaining balance after this leave is taken
        .col-xs-6.text-right
          .h1.mt0.mb0 {{($availabilityBoxForm.calculated.afterAvailableBalance | number : 2) + ($availabilityBoxForm.calculated.afterAvailableBalance < 0 || !!$availabilityBoxForm.calculated.doesUseProRata ? '*' : '')}}
      .row(ng-if='($availabilityBoxForm.calculated.afterAvailableBalance < 0 && !!$availabilityBoxForm.calculated.doesShowNegativeBalance ) || !!$availabilityBoxForm.calculated.doesUseProRata')
        .col-xs-12
          hr.mt-sm.mb-sm
        .col-xs-12.text-right(ng-if='!!$availabilityBoxForm.calculated.doesUseProRata')
          h6.h6.m0 * This request is using hours from your pro-rata balance
        .col-xs-12.text-right(ng-if='$availabilityBoxForm.calculated.afterAvailableBalance < 0 && !!$availabilityBoxForm.calculated.doesShowNegativeBalance')
          h6.h6.m0 * This leave balance will go into negative
      .row(ng-if='!!$ctrl.form.timeFrameDateRecordedData.recordedTimes && $ctrl.form.timeFrameDateRecordedData.recordedTimes.length > 0')
        .col-xs-12
          hr.mt-sm.mb-sm
        .col-xs-9
          .h3.mt-xs.sub.mb0
            | This leave request may have clashes with existing time
        .col-xs-3
          a.btn.btn-default.btn-xs.pull-right.mt-xs.mb0.pt0.pb0.pl.pr.text-nowrap(type='button' href='javascript:' ng-click='$ctrl.showModalTimeFrame($ctrl.form)') See more
