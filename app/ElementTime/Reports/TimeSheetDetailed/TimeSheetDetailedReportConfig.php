<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\TimeSheetDetailed;

use Element\ElementTime\Reports\ReportCategoryType;
use Element\ElementTime\Support\Reports\TenantReportConfig;

class TimeSheetDetailedReportConfig extends TenantReportConfig
{
    const IS_LISTED = false;
    const SLUG = 'time-sheet-detailed';
    const CATEGORY = ReportCategoryType::General;
    const NAME = 'Detailed timesheet';
    const ROUTE = 'time-sheet-detailed.{{type}}';
    const FILE_NAME_TEMPLATE = '{{tenant.slug}}__{{slug}}-{{timeSheet.id}}-{{timeSheet.status}}-{{genUser.id}}.{{fileType}}';
    const DOWNLOAD_FILE_NAME_TEMPLATE = '{{name}} - {{user.name}} - {{payRunType.name}} {{payRun.number}} - {{tenant.name}}.{{fileType}}';

    const TYPES = [
        [
            'class' => TimeSheetDetailedPdfReport::class,
            'options' => [
                'order' => 1,
            ],
        ],
        [
            'class' => TimeSheetDetailedExcelReport::class,
            'options' => [
                'order' => 2,
            ],
        ],
    ];

    const FILTERS = [];

    const PARAMETERS = [
        'id' => '',
    ];
}
