<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->decimal('capAmountOfLeaveAlertPercentage', 16, 10)->default(100)->after('capAmountOfLeaveTimeValue');
        });

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->decimal('capAmountOfLeaveAlertPercentage', 16, 10)->default(100)->after('capAmountOfLeaveTimeValue');
        });

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesHaveMaximumAllowedExcessTimeBalance')->default(true)->after('excessTimeBalanceRetainedAfterExpiry');
            $table->decimal('maximumAllowedExcessTimeBalanceAlertPercentage', 16, 10)->default(75)->after('maximumAllowedExcessTimeBalanceAction');
        });

        Schema::table('UserLeaveBank', function (Blueprint $table) {
            $table->boolean('doesUseDefaultCapSettings')->default(true)->after('description');
            $table->unsignedBigInteger('defaultCapUserLeaveBankType_id')->nullable()->after('doesUseDefaultCapSettings');
            $table->foreign('defaultCapUserLeaveBankType_id', 'usb_dculbt_fn')
                ->references('id')->on('UserLeaveBankType')
                ->onUpdate('cascade')->onDelete('set null');
            $table->boolean('doesHaveCap')->default(false)->after('defaultCapUserLeaveBankType_id');
            $table->decimal('capAmount', 16, 10)->default(0)->after('doesHaveCap');
            $table->char('capReachedAction')->default('N')->after('capAmount');
            $table->decimal('capAlertPercentage', 16, 10)->default(100)->after('capReachedAction');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserLeaveBank', 'doesUseDefaultCapSettings')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('doesUseDefaultCapSettings');
            });
        }

        if (Schema::hasColumn('UserLeaveBank', 'defaultCapUserLeaveBankType_id')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('defaultCapUserLeaveBankType_id');
            });
        }

        if (Schema::hasColumn('UserLeaveBank', 'doesHaveCap')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('doesHaveCap');
            });
        }

        if (Schema::hasColumn('UserLeaveBank', 'capAmount')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('capAmount');
            });
        }

        if (Schema::hasColumn('UserLeaveBank', 'capReachedAction')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('capReachedAction');
            });
        }

        if (Schema::hasColumn('UserLeaveBank', 'capAlertPercentage')) {
            Schema::table('UserLeaveBank', function (Blueprint $table) {
                $table->dropColumn('capAlertPercentage');
            });
        }

        if (Schema::hasColumn('Settings', 'maximumAllowedExcessTimeBalanceAlertPercentage')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('maximumAllowedExcessTimeBalanceAlertPercentage');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'capAmountOfLeaveAlertPercentage')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('capAmountOfLeaveAlertPercentage');
            });
        }

        if (Schema::hasColumn('LeaveType', 'capAmountOfLeaveAlertPercentage')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('capAmountOfLeaveAlertPercentage');
            });
        }
    }
};
