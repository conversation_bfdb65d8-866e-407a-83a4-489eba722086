<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\Workflow;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\LeaveRequestRepository;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Repositories\PayRunRepository;
use Element\ElementTime\Domains\Tenant\Settings\Models\LeaveType;
use Element\ElementTime\Domains\Tenant\Settings\Models\RosteredTimeOffType;
use Element\ElementTime\Domains\Tenant\Settings\Repositories\LeaveTypeRepository;
use Element\ElementTime\Domains\Tenant\Settings\Repositories\RosteredTimeOffTypeRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRepository;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Models\Workflow;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowStepItem;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class WorkflowExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
    ];

    const REPORT_SLUG = 'workflow';
    const TYPE_SLUG = 'workflow.xls';

    public $c = WorkflowReportConfig::class;

    /**
     * @param array $info
     */
    protected function setData(array $info)
    {
        $this->config->title = $this->c::NAME;
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        $this->reportValidity = Carbon::now()->subSeconds(1);

        return $this;
    }

    /**
     * @throws InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     * @throws \Throwable
     */
    protected function buildReportData()
    {
        $now = Carbon::now();
        $today = $now->copy()->startOfDay();

        /** @var User[]|Collection $users */
        $users = UserRepository::getMany([
            'constraints' => [
                ['User.id', '<>', 1],
                [
                    [
                        ['IS NULL', 'User.startDate', 'OR'],
                        ['User.startDate', '=', '0000-00-00', 'OR'],
                        ['User.startDate', '<=', $now->toDateString(), 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'User.endDate', 'OR'],
                        ['User.endDate', '=', '0000-00-00', 'OR'],
                        ['User.endDate', '>=', $now->toDateString(), 'OR'],
                    ],
                ],
            ],
            'scopes' => [
                'withSystemAccess',
            ],
            'relations' => [
                'userManagers.manager',
                'userShifts.shift.manager',
                'userShifts.userRole.role',
                'userDepartment.department',
                'userLeaveBankTypes',
            ],
            'order' => 'CONCAT(User.nameFirst, IF(User.nameMiddle IS NOT NULL, User.nameMiddle, ""), User.nameLast)',
            'returnType' => 'model',
        ]);

        /** @var PayRun $payRun */
        $payRun = PayRunRepository::getOne([
            'constraints' => [
                ['PayRun.startDate', '<=', $now->toDateString()],
                ['PayRun.endDate', '>=', $now->toDateString()],
            ],
            'returnType' => 'model',
            'relations' => [
                'payRunItems.timeSheet.workflow.steps.parentItems.children',
            ],
        ]);

        /** @var LeaveType[] $leaveTypes */
        $leaveTypes = LeaveTypeRepository::getMany([
            'constraints' => [
                ['LeaveType.status', '=', 'A'],
            ],
            'order' => 'LeaveType.name',
            'returnType' => 'model',
        ]);

        /** @var RosteredTimeOffType[] $rdoTypes */
        $rdoTypes = RosteredTimeOffTypeRepository::getMany([
            'constraints' => [
                ['RosteredTimeOffType.status', '=', 'A'],
            ],
            'order' => 'RosteredTimeOffType.name',
            'returnType' => 'model',
        ]);

        $leaveTypesCount = 1;

        foreach ($leaveTypes as $leaveType) {
            if ($leaveType->countAssignments > 0) {
                $leaveTypesCount++;
            }
        }

        foreach ($rdoTypes as $rdoType) {
            if ($rdoType->countAssignments > 0) {
                $leaveTypesCount++;
            }
        }

        $activeUsers = 0;
        $inactiveUsers = 0;
        $ordinaryUsers = 0;
        $managerUsers = 0;
        $payrollUsers = 0;
        foreach ($users as $user) {
            if ($user->status == 'A') {
                $activeUsers++;
            } else {
                $inactiveUsers++;
            }

            if ($user->access->isPayrollOfficer()) {
                $payrollUsers++;
            } elseif ($user->access->isStaffManager()) {
                $managerUsers++;
            } else {
                $ordinaryUsers++;
            }
        }

        $headerColumns = json_decode(json_encode([
            [
                'label' => 'Organisation',
                'type' => 'text',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 2,
            ],
            [
                'label' => 'Date',
                'type' => 'date',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Pay-run number',
                'type' => 'text',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Pay-run period',
                'type' => 'text',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Number of active users',
                'type' => 'integer',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Number of inactive users',
                'type' => 'integer',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Number of ordinary users',
                'type' => 'integer',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Number of managers',
                'type' => 'integer',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
            [
                'label' => 'Number of payroll officers',
                'type' => 'integer',
                'align' => 'center',
                'verticalAlign' => 'center',
                'span' => 1,
            ],
        ]));

        $headerData = [[
            'config' => [],
            'data' => [
                $this->tenant->name, // Organisation
                $now, // Date
                !is_null($payRun) ? $payRun->fiscalYear . ' - #' . $payRun->number : '-', // Pay-run number
                !is_null($payRun) ? $payRun->startDate->format($this->dateFormat) . ' - ' . $payRun->endDate->format($this->dateFormat) : '-', // Pay-run period
                $activeUsers, // Number of active users
                $inactiveUsers, // Number of inactive users
                $ordinaryUsers, // Number of ordinary users
                $managerUsers, // Number of managers
                $payrollUsers, // Number of payroll officers
            ],
        ]];

        $listParentColumns = json_decode(json_encode([
            [
                'label' => '',
                'span' => 1,
            ],
            [
                'label' => 'Staff',
                'span' => 9,
            ],
            [
                'label' => 'Expected timesheet workflow',
                'span' => 1,
            ],
            [
                'label' => 'Expected leave workflow',
                'span' => $leaveTypesCount,
            ],
        ]));

        $accruedName = strlen(TenantSystemSettings::getValue('settings.customExcessTimeLeaveName')) > 0
            ? TenantSystemSettings::getValue('settings.customExcessTimeLeaveName')
            : (
                strlen(TenantSystemSettings::getValue('settings.customExcessTimeBalanceName')) > 0
                    ? TenantSystemSettings::getValue('settings.customExcessTimeBalanceName')
                    : 'Accrued hours'
            );

        $lColumns = [
            [
                'label' => '#',
                'type' => 'integer',
                'align' => 'center',
            ],
            [
                'label' => 'Ext ref ID',
                'type' => 'text',
            ],
            [
                'label' => 'Name',
                'type' => 'text',
            ],
            [
                'label' => 'Status',
                'type' => 'text',
            ],
            [
                'label' => 'Role',
                'type' => 'text',
            ],
            [
                'label' => 'Direct Manager',
                'type' => 'text',
            ],
            [
                'label' => 'Department',
                'type' => 'text',
            ],
            [
                'label' => 'Department manager',
                'type' => 'text',
            ],
            [
                'label' => 'Shift',
                'type' => 'text',
            ],
            [
                'label' => 'Shift manager',
                'type' => 'text',
            ],
            [
                'label' => '',
                'type' => 'text',
            ],
            [
                'label' => $accruedName,
                'type' => 'text',
            ],
        ];

        foreach ($leaveTypes as $leaveType) {
            $lColumns[] = [
                'label' => $leaveType->name,
                'type' => 'text',
            ];
        }

        foreach ($rdoTypes as $rdoType) {
            $lColumns[] = [
                'label' => $rdoType->name,
                'type' => 'text',
            ];
        }

        $listColumns = json_decode(json_encode($lColumns));

        $listData = [];

        $i = 1;
        foreach ($users as $user) {
            foreach ($user->userShifts as $userShift) {
                if (
                    (
                        is_null($userShift->startDate)
                        || $userShift->startDate->lte($today)
                    )
                    && (
                        is_null($userShift->endDate)
                        || $userShift->endDate->gte($today)
                    )
                ) {
                    $department = '';
                    $departmentManager = '';
                    $departmentManagers = [];

                    foreach ($user->userDepartments as $userDepartment) {
                        if ($userDepartment->isActiveOnTime($today)) {
                            if (strlen($department) > 0) {
                                $department .= ', ';
                            }

                            $department .= $userDepartment->department->name;

                            $ms = $userDepartment->department->repository->getAllManagers(false);
                            foreach ($ms as $m) {
                                if (!in_array($m->fullName, $departmentManagers)) {
                                    $departmentManagers[] = $m->fullName;
                                }
                            }
                        }
                    }

                    $departmentManagers = array_values(Arr::sort($departmentManagers));

                    foreach ($departmentManagers as $dm) {
                        if (strlen($departmentManager) > 0) {
                            $departmentManager .= ', ';
                        }

                        $departmentManager .= $dm;
                    }


                    $timeSheetExpectedWorkflow = 'Not currently known';

                    foreach ($payRun->payRunItems as $payRunItem) {
                        if ($payRunItem->user_id == $user->id && $payRunItem->timeSheet) {
                            $timeSheetExpectedWorkflow = $this->getExpectedWorkflow($payRunItem->timeSheet->workflow);

                            break;
                        }
                    }

                    $data = [
                        $i,
                        $userShift->userRole->repository->getExternalId(), // Ext ref ID
                        $user->fullName, // Name
                        $user->getStatusObject()['name'], // Status
                        $userShift->userRole->role->name, // Role
                        !is_null($user->currentManager) ? $user->currentManager->fullName : '', // Direct Manager
                        $department, // Department
                        $departmentManager, // Department manager
                        $userShift->shift->name, // Shift
                        $userShift->shift->manager->fullName, // Shift manager
                        $timeSheetExpectedWorkflow, // Expected timesheet workflow
                    ];

                    // Accrued hours
                    $data[] = $this->getExpectedWorkflowForBankType($user->userLeaveBankTypes->where('type_type', '=', null)->first());

                    // Leave types
                    foreach ($leaveTypes as $leaveType) {
                        $data[] = $this->getExpectedWorkflowForBankType($user->userLeaveBankTypes->where('type_type', '=', LeaveType::class)->where('type_id', '=', $leaveType->id)->first());
                    }

                    // RDO types
                    foreach ($rdoTypes as $rdoType) {
                        $data[] = $this->getExpectedWorkflowForBankType($user->userLeaveBankTypes->where('type_type', '=', RosteredTimeOffType::class)->where('type_id', '=', $rdoType->id)->first());
                    }

                    $listData[] = [
                        'config' => [],
                        'data' => $data,
                    ];
                    $i++;
                }
            }
        }

        $this->sheets = [];
        $sheet = new \stdClass();
        $sheet->label = $this->c::NAME;
        $sheet->autoSize = true;
        $sheet->data = [];

        $data = new \stdClass();
        $data->type = 'sheet-title';
        $data->title = $this->c::NAME;
        $sheet->data[] = $data;

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = $headerColumns;
        $data->data = $headerData;
        $sheet->data[] = $data;

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->parentColumns = $listParentColumns;
        $data->columns = $listColumns;
        $data->data = $listData;
        $sheet->data[] = $data;

        $this->sheets[] = $sheet;
    }

    /**
     * @param Workflow $workflow
     *
     * @return string
     * @throws \Throwable
     */
    protected function getExpectedWorkflow(Workflow $workflow)
    {
        $ret = '';
        $users = [];

        foreach ($workflow->steps as $step) {
            $retForStep = '';
            foreach ($step->parentItems as $item) {
                $this->getItem($item, $retForStep, $users);
            }

            if (strlen($ret) > 0) {
                $ret .= ' - then ';
            }

            $ret .= $retForStep;
        }

        if (strlen($ret) == 0) {
            return 'Automatically approved';
        }

        return $ret;
    }

    /**
     * @param WorkflowStepItem $item
     * @param string $ret
     * @param array $users
     *
     * @return string
     * @throws \Throwable
     */
    protected function getItem(WorkflowStepItem $item, &$ret, &$users)
    {
        foreach ($item->repository->getMatchingUsers() as $matchingUser) {
            if (!$matchingUser->access->isSuperUser() && !in_array($matchingUser->id, $users)) {
                $users[] = $matchingUser->id;

                if (strlen($ret) > 0) {
                    $ret .= ', ';
                }
                $ret .= $matchingUser->fullName;
            }
        }

        foreach ($item->children as $child) {
            $this->getItem($child, $ret, $users);
        }

        return $ret;
    }

    /**
     * @param UserLeaveBankType|null $bankType
     *
     * @return string
     * @throws InvalidArgumentException
     * @throws \Throwable
     */
    protected function getExpectedWorkflowForBankType(UserLeaveBankType $bankType = null)
    {
        if (is_null($bankType)) {
            return '-';
        }

        /** @var LeaveRequest $lastRequest */
        $lastRequest = LeaveRequestRepository::getOne([
            'constraints' => [
                ['LeaveRequest.userLeaveBankType_id', '=', $bankType->id],
            ],
            'relations' => [
                'workflow.steps.parentItems.children',
            ],
            'order' => 'LeaveRequest.created_at DESC, LeaveRequest.startDate DESC',
            'returnType' => 'model',
        ]);

        if (is_null($lastRequest)) {
            return 'Not currently known';
        }

        return $this->getExpectedWorkflow($lastRequest->workflow);
    }
}
