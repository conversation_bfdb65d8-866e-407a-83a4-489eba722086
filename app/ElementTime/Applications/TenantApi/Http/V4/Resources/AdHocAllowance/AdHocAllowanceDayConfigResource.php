<?php

/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance;

use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleAllowanceType;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'AdHocAllowanceDayConfigResource',
    title: 'Ad hoc allowance - day config data',
    required: ['id', 'isEligibleForAllowances', 'allowance_types'],
    properties: [
        new OA\Property(property: 'id', description: 'The ID of timesheet day record', type: 'integer', example: 42),
        new OA\Property(property: 'isEligibleForAllowances', type: 'bool', example: true),
        new OA\Property(property: 'allowance_types', type: 'array', items: new OA\Items(ref: '#/components/schemas/AdHocAllowanceTypeConfigResource')),
    ],
)]
class AdHocAllowanceDayConfigResource extends Resource
{
    public static $relations = [
        'timeSheet.payRunItem.user.userAllowanceTypes.allowanceType',
    ];

    public function toArray(Request $request): array
    {
        /** @var TimeSheetDay $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'isEligibleForAllowances' => $r->timeSheet->user->isEligibleForAllowances,
            'allowance_types' => $this->fetchAllowanceTypes($r),
        ];
    }

    /** @throws \Throwable */
    protected function fetchAllowanceTypes(TimeSheetDay $r): \Illuminate\Http\Resources\Json\AnonymousResourceCollection|array
    {
        $user = $r->timeSheet->user;
        $userRole = $user->getMasterUserRole($r->date);

        if (!$user->isEligibleForAllowances) {
            return [];
        }

        $allowanceTypes = $userRole->getActiveUserRoleAllowanceTypes($r->date)
            ->where(fn(UserRoleAllowanceType $userRoleAllowanceType) => $userRoleAllowanceType->allowanceType->costingType->canCostToMaster())
            ->sortBy('allowanceType.name')
            ->map(fn(UserRoleAllowanceType $userRoleAllowanceType) => $userRoleAllowanceType->allowanceType);

        return AdHocAllowanceTypeConfigResource::collection($allowanceTypes);
    }
}
