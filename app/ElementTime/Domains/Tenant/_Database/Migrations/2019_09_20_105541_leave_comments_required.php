<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: LeaveType

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('commentRequired')->default(true)->after('attachmentRequiredDurationType');
            $table->boolean('commentRequiredHasDuration')->default(false)->after('commentRequired');
            $table->decimal('commentRequiredDurationValue', 16, 10)->nullable()->default(0)->after('commentRequiredHasDuration');
            $table->char('commentRequiredDurationType', 1)->nullable()->default('H')->after('commentRequiredDurationValue'); // H => Hours | D => Days
        });

        //endregion Table: LeaveType

        //region Table: RosteredTimeOffType

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->boolean('commentRequired')->default(true)->after('mustBeApproved');
            $table->boolean('commentRequiredHasDuration')->default(false)->after('commentRequired');
            $table->decimal('commentRequiredDurationValue', 16, 10)->nullable()->default(0)->after('commentRequiredHasDuration');
            $table->char('commentRequiredDurationType', 1)->nullable()->default('H')->after('commentRequiredDurationValue'); // H => Hours | D => Days
        });

        //endregion Table: RosteredTimeOffType

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('excessTimeLeaveCommentRequired')->default(true)->after('customExcessTimeLeaveName');
            $table->boolean('excessTimeLeaveCommentRequiredHasDuration')->default(false)->after('excessTimeLeaveCommentRequired');
            $table->decimal('excessTimeLeaveCommentRequiredDurationValue', 16, 10)->nullable()->default(0)->after('excessTimeLeaveCommentRequiredHasDuration');
            $table->char('excessTimeLeaveCommentRequiredDurationType', 1)->nullable()->default('H')->after('excessTimeLeaveCommentRequiredDurationValue'); // H => Hours | D => Days
        });

        //endregion Table: Settings
    }

    public function down(): void
    {
        //region Table: Settings

        if (Schema::hasColumn('Settings', 'excessTimeLeaveCommentRequiredDurationType')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveCommentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeLeaveCommentRequiredDurationValue')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveCommentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeLeaveCommentRequiredHasDuration')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveCommentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeLeaveCommentRequired')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveCommentRequired');
            });
        }

        //endregion Table: Settings

        //region Table: RosteredTimeOffType

        if (Schema::hasColumn('RosteredTimeOffType', 'commentRequiredDurationType')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'commentRequiredDurationValue')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'commentRequiredHasDuration')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('RosteredTimeOffType', 'commentRequired')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('commentRequired');
            });
        }

        //endregion Table: RosteredTimeOffType

        //region Table: LeaveType

        if (Schema::hasColumn('LeaveType', 'commentRequiredDurationType')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('LeaveType', 'commentRequiredDurationValue')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('LeaveType', 'commentRequiredHasDuration')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('commentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('LeaveType', 'commentRequired')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('commentRequired');
            });
        }

        //endregion Table: LeaveType
    }
};
