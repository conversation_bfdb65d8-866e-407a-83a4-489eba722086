<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\LeaveDetails;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Settings\Models\LeaveType;
use Element\ElementTime\Domains\Tenant\Settings\Models\RosteredTimeOffType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class LeaveDetailsExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
        UserSystemAccessFlags\StaffManagerFlag::class,
    ];

    const IS_LISTED = false;
    const REPORT_SLUG = 'leave-details';
    const TYPE_SLUG = 'leave-details.xls';

    /** @var string|LeaveDetailsReportConfig */
    public $c = LeaveDetailsReportConfig::class;

    /**
     * @inheritDoc
     */
    protected function setData(array $info)
    {
        // TODO: Implement setData() method.
    }

    /**
     * @throws InvalidArgumentException
     */
    protected function buildReportData(): void
    {
        $this->setSheetTitle($sheet, $this->c::NAME);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->parentColumns = [];
        $data->columns = [];
        $data->data = [];

        $reportUsers = static::getReportUsers($this->genUser);
        $leaveTypes = static::getLeaveTypes(LeaveType::class);
        $rdoTypes = static::getLeaveTypes(RosteredTimeOffType::class);
        $excessTimeLeaveName = TenantSystemSettings::o()->customExcessTimeLeaveName;
        $fiscalYearStart = $this->tenant->fiscalYearStart;
        $year = TenantSystemSettings::getValue('general.currentYear');

        $fiscalYearStartDate = Carbon::parse($fiscalYearStart)->year($year);
        if ($fiscalYearStartDate->isFuture()) {
            $fiscalYearStartDate->subYears(1);
        }

        $data->parentColumns[] = $this->createParentColumn('Staff', 2);
        $data->columns[] = $this->createColumn('Name');
        $data->columns[] = $this->createColumn('Role');

        $data->parentColumns[] = $this->createParentColumn($excessTimeLeaveName, 5);
        $data->columns = array_merge($data->columns, $this->buildLeaveTypeColumns(true));

        foreach ($leaveTypes as $leaveType) {
            $data->parentColumns[] = $this->createParentColumn($leaveType->name, 6);
            $data->columns = array_merge($data->columns, $this->buildLeaveTypeColumns());
        }

        foreach ($rdoTypes as $rdoType) {
            $data->parentColumns[] = $this->createParentColumn($rdoType->name, 6);
            $data->columns = array_merge($data->columns, $this->buildLeaveTypeColumns());
        }

        foreach ($reportUsers as $reportUser) {
            $userLeaveBankTypes = $reportUser->userLeaveBankTypes;
            $rowData = [
                $reportUser->fullName,
                $reportUser->masterRole,
            ];

            $accruedType = $userLeaveBankTypes->where('type_type', '=', null)->first();
            $accruedTypeData = $this->getUserLeaveBankData($accruedType, true, $fiscalYearStartDate);
            $rowData = array_merge($rowData, $accruedTypeData);

            foreach ($leaveTypes as $leaveType) {
                $userLeaveBankType = $userLeaveBankTypes
                    ->where('type_type', '=', LeaveType::class)
                    ->where('type_id', '=', $leaveType->id)
                    ->first();
                $leaveTypeData = $this->getUserLeaveBankData($userLeaveBankType, false, $fiscalYearStartDate);
                $rowData = array_merge($rowData, $leaveTypeData);
            }

            foreach ($rdoTypes as $rdoType) {
                $userLeaveBankType = $userLeaveBankTypes
                    ->where('type_type', '=', RosteredTimeOffType::class)
                    ->where('type_id', '=', $rdoType->id)
                    ->first();
                $rdoTypeData = $this->getUserLeaveBankData($userLeaveBankType, false, $fiscalYearStartDate);
                $rowData = array_merge($rowData, $rdoTypeData);
            }

            $data->data[] = [
                'config' => [],
                'data' => $rowData,
            ];
        }

        $sheet->data[] = $data;
        $this->sheets = [
            $sheet,
        ];
    }

    private function buildLeaveTypeColumns(bool $isExcessTimeLeave = false): array
    {
        $leaveTypeColumns = [];
        $leaveTypeColumns[] = $this->createColumn('Available');
        $leaveTypeColumns[] = $this->createColumn('Committed');

        if (!$isExcessTimeLeave) {
            $leaveTypeColumns[] = $this->createColumn('Accrued');
        }

        $leaveTypeColumns[] = $this->createColumn('Used this year');
        $leaveTypeColumns[] = $this->createColumn('Instances');
        $leaveTypeColumns[] = $this->createColumn('Attachments');

        return $leaveTypeColumns;
    }

    private function getUserLeaveBankData(UserLeaveBankType|null $userLeaveBankType, bool $isExcessTimeAsLeave, Carbon $startDate): array
    {
        $data = [];
        if (is_null($userLeaveBankType) || !$userLeaveBankType->isActiveOnTime()) {
            $data[] = null;
            $data[] = null;
            if (!$isExcessTimeAsLeave) {
                $data[] = null;
            }
            $data[] = null;
            $data[] = null;
            $data[] = null;
        } else {
            $hours = 0;
            $instances = 0;
            $attachments = 0;
            $today = Carbon::today();

            foreach ($userLeaveBankType->leaveRequests as $leaveRequest) {
                if (!$leaveRequest->repository->isApproved()) {
                    continue;
                }

                if ($leaveRequest->endDateTime->gte($startDate) && $leaveRequest->endDateTime->lte($today)) {
                    $hours += $leaveRequest->durationAdjusted->getTotalHours();
                    $instances++;
                    if ($leaveRequest->attachmentFiles->count() > 0) {
                        $attachments++;
                    }
                }
            }

            $data[] = $this->parseFloat($userLeaveBankType->bank->totalAvailableBalance, 2);
            $data[] = $this->parseFloat($userLeaveBankType->bank->totalCommittedBalance, 2);

            if (!$isExcessTimeAsLeave) {
                $data[] = $this->parseFloat($userLeaveBankType->bank->totalAccruedBalance, 2);
            }

            $data[] = $this->parseFloat($hours, 2);
            $data[] = $this->parseFloat($instances, 2);
            $data[] = $this->parseFloat($attachments, 2);
        }

        return $data;
    }

    /**
     * @param string|LeaveType|RosteredTimeOffType $type
     *
     * @return LeaveType[]|RosteredTimeOffType[]|Collection
     */
    public static function getLeaveTypes($type): Collection|array
    {
        $tableName = $type::getTableName();
        $constraints = [
            [$tableName . '.status', '=', ActiveStatus::ID],
            [$tableName . '.doesShowBalanceToManager', '=', true],
        ];
        $order = $tableName . '.name';

        return $type::q(
            constraints: $constraints,
            order: $order
        )->many();
    }

    /**
     * @return User[]|Collection
     *
     * @throws InvalidArgumentException
     */
    private static function getReportUsers(User $user): Collection|array
    {
        $constraints = [
            ['User.id', '<>', 1],
            ['EmployeeType.doesHideLeave', '=', false],
            [
                [
                    ['IS NULL', 'User.endDate', 'OR'],
                    ['User.endDate', '=', '0000-00-00', 'OR'],
                    ['User.endDate', '>=', Carbon::today(), 'OR'],
                ],
            ],
        ];
        $joins = [
            ['EmployeeType', 'User.employeeType_id', '=', 'EmployeeType.id'],
        ];
        $relations = [
            'employeeType',
            'userManagers.manager',
            'userRoles.userRoleMasters',
            'userLeaveBankTypes.leaveRequests.attachmentFiles',
            'userLeaveBankTypes.leaveRequests.workflow',
            'userLeaveBankTypes.bank',
        ];

        if ($user->access->isPayrollOfficer(true) || $user->access->isBusinessIntelligence(true)) {
            return User::q(
                constraints: $constraints,
                relations: $relations,
                joins: $joins
            )->many();
        } else {
            return $user->repository->getReportsByPeriod(null, null, [
                'constraints' => $constraints,
                'relations' => $relations,
                'joins' => $joins,
                'doesIncludeIndirectReports' => TenantSystemSettings::o()->displayIndirectReportsToManager && $user->settings->showIndirectReportsLeaveBalances,
                'doesIncludeHigherDuties' => true,
            ]);
        }
    }
}
