<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SplitLeaveRequest;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Actions\Tenant\Workflow\CloneWorkflowRecord;
use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Database\EDB;
use Element\ElementTime\Support\Enums\RecordedVia;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Database\Eloquent\Collection;

abstract class BaseSplitLeaveRequest extends BaseLeavesAction
{
    protected LeaveRequest $originalLeaveRequest;

    /**@throws \Throwable */
    public function handle(
        LeaveRequest $leaveRequest,
        User $actor = null,
        string $notes = '',
        Carbon $dateTime = null,
        MediaFile $attachmentFile = null,
        RecordedVia|null $recordedVia = null,
        $runsStatusEvent = true,
    ): void
    {
        if (!$leaveRequest->isAcrossMultiplePayRuns()) {
            throw new \BadMethodCallException('Splitting leave is not available for single pay-run requests');
        }

        EDB::tenantConn()->transaction(function () use ($leaveRequest, $actor, $notes, $dateTime, $attachmentFile, $recordedVia, $runsStatusEvent) {
            $endDate = $leaveRequest->endDate->copy();
            $payRun = TenantSystemSettings::getPayRunByDate($leaveRequest->startDate);
            $this->originalLeaveRequest = $leaveRequest;

            while ($payRun->startDate->copy()->lte($endDate)) {
                $scheduledPayRun = $leaveRequest->userRoleSchedule->scheduledPayRuns->firstWhere(fn (ScheduledPayRun $scheduledPayRun) => $scheduledPayRun->payRun->id = $payRun->id);
                $newSplitPart = $this->makeSplitPartLeaveRequestForPayRun($payRun, $scheduledPayRun, $recordedVia);

                CloneWorkflowRecord::make()->handle($newSplitPart, $leaveRequest);

                $newSplitPart->repository->approve($actor, $notes, $dateTime, $attachmentFile, $runsStatusEvent);

                $newSplitPart->buildModelTasks();

                $payRun = TenantSystemSettings::getPayRunByDate($payRun->endDate->copy()->addDay());
            }
        });
    }

    /// ------------------------

    /** @throws \Throwable */
    protected function makeSplitPartLeaveRequestForPayRun(PayRun $payRun, ScheduledPayRun|null $scheduledPayRun, RecordedVia|null $recordedVia): LeaveRequest
    {
        $splitPartLeaveRequest = new LeaveRequest;
        $splitPartLeaveRequest->scheduleType = $this->originalLeaveRequest->scheduleType;

        $splitPartLeaveRequest->originalSplit_id = $this->originalLeaveRequest->id;
        $splitPartLeaveRequest->recordedVia = $recordedVia ?? $this->originalLeaveRequest->recordedVia;
        $splitPartLeaveRequest->actor_id = $this->originalLeaveRequest->actor_id;
        $splitPartLeaveRequest->userRoleSchedule_id = $this->originalLeaveRequest->userRoleSchedule_id;
        $splitPartLeaveRequest->userLeaveBankType_id = $this->originalLeaveRequest->userLeaveBankType_id;
        $splitPartLeaveRequest->isFullDay = $this->originalLeaveRequest->isFullDay;
        $splitPartLeaveRequest->reason = $this->originalLeaveRequest->reason;
        $splitPartLeaveRequest->doesHaveFTECalculation = $this->originalLeaveRequest->doesHaveFTECalculation;
        $splitPartLeaveRequest->duration = TimeDuration::zero();
        $splitPartLeaveRequest->durationAdjusted = TimeDuration::zero();
        $splitPartLeaveRequest->scheduledPayRun_id = $scheduledPayRun?->id;

        $leaveRequestDays = $this->makeSplitPartLeaveRequestDays($payRun, $splitPartLeaveRequest->duration, $splitPartLeaveRequest->durationAdjusted, $scheduledPayRun);
        $splitPartLeaveRequest->startDate = $leaveRequestDays->first()->date->copy();
        $splitPartLeaveRequest->endDate = $leaveRequestDays->last()->date->copy();
        $splitPartLeaveRequest->clearSaveOrFail();

        $splitPartLeaveRequest->days()->saveMany($leaveRequestDays);

        $splitPartLeaveRequest->repository->syncAttachments($this->originalLeaveRequest->attachmentFiles);
        $splitPartLeaveRequest->clearSaveOrFail();

        return $splitPartLeaveRequest;
    }

    protected function makeSplitPartLeaveRequestDays(PayRun $payRun, TimeDuration $duration, TimeDuration $durationAdjusted, ScheduledPayRun|null $scheduledPayRun): Collection
    {
        $payRunPeriod = CarbonPeriod::create($payRun->startDate, $payRun->endDate);
        $ret = Collection::make([]);

        foreach ($this->originalLeaveRequest->days as $day) {
            $scheduledPayRunPeriod = !is_null($scheduledPayRun)
                ? $scheduledPayRun->periods->firstWhere(fn (ScheduledPayRunPeriod $period) => $period->availability($day->date)->isCurrent())
                : null;

            if (!$payRunPeriod->contains($day->date)) {
                continue;
            }

            $leaveRequestDay = $this->makeSplitPartLeaveRequestDay($day, $scheduledPayRunPeriod);

            $duration->add($leaveRequestDay->duration->copy());
            $durationAdjusted->add($leaveRequestDay->durationAdjusted->copy());

            $ret->push($leaveRequestDay);
        }

        return $ret;
    }

    protected function makeSplitPartLeaveRequestDay(LeaveRequestDay $leaveRequestDay, ScheduledPayRunPeriod|null $scheduledPayRunPeriod): LeaveRequestDay
    {
        $day = new LeaveRequestDay;

        $day->user_id = $leaveRequestDay->user_id;
        $day->date = $leaveRequestDay->date->copy();
        $day->duration = $leaveRequestDay->duration->copy();
        $day->durationAdjusted = $leaveRequestDay->durationAdjusted->copy();
        $day->timeSheetPeriod_id = $scheduledPayRunPeriod?->timeSheetPeriod?->id;

        $scheduledPayRunPeriodDay = !is_null($scheduledPayRunPeriod)
            ? $scheduledPayRunPeriod->days->firstWhere(fn (ScheduledPayRunPeriodDay $periodDay) => $periodDay->date->isSameDay($day->date))
            : null;

        if (is_null($scheduledPayRunPeriodDay)) {
            return $day;
        }

        $day->timeSheetDay_id = $scheduledPayRunPeriodDay->timeSheetDay_id;
        $day->scheduledPayRunPeriodDay_id = $scheduledPayRunPeriodDay->id;

        return $day;
    }
}
