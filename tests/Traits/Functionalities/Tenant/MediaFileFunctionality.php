<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Traits\Functionalities\Tenant;

use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Faker\Provider\Lorem;
use Illuminate\Database\Eloquent\Collection;

trait MediaFileFunctionality
{
    public function g_mf_createRandomMediaFile(User $user): MediaFile
    {
        $filename = Lorem::word() . '.pdf';

        $mediaFile = new MediaFile();
        $mediaFile->slug = $filename;
        $mediaFile->user_id = $user->id;
        $mediaFile->bucket = $mediaFile->defaultBucket;
        $mediaFile->path = '';
        $mediaFile->mimeType = 'application/pdf';
        $mediaFile->name = $filename;
        $mediaFile->isPrivate = false;
        $mediaFile->clearSave();

        return $mediaFile;
    }

    public function g_mf_createRandomMediaFiles(User $user, int $count = 1): Collection
    {
        $ret = Collection::make([]);

        while ($ret->count() < $count) {
            $ret->push($this->g_mf_createRandomMediaFile($user));
        }

        return $ret;
    }
}
