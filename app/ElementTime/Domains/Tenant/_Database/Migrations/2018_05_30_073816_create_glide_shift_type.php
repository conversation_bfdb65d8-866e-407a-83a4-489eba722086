<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table Shift

        Schema::table('Shift', function (Blueprint $table) {
            $table->char('glideDurationType', 1)->default('D');

            $table->unsignedBigInteger('glideAlignPayRunType_id')->nullable();
            $table->foreign('glideAlignPayRunType_id', 'shift_gaprt_fn')->references('id')->on('PayRunType')->onUpdate('cascade')->onDelete('restrict');

            $table->decimal('glideDuration', 16, 10)->nullable()->default(7.6);
            $table->longText('glideTime')->nullable();
            $table->boolean('glideRequireTimeBlock')->nullable()->default(false);
            $table->longText('glideDays')->nullable();
            $table->char('glidePublicHolidaysRateType', 1)->nullable()->default('A');
        });

        //endregion

        //region Table UserShiftDay

        $ids = DB::select('select GROUP_CONCAT(id) as ids from UserShiftDay group by userShift_id, calendarDate_id having count(*) > 1;');
        foreach ($ids as $id) {
            DB::delete('DELETE FROM UserShiftDay WHERE id IN(' . $id->ids . ') AND hours IS NULL');
        }

        $ids = DB::select('select GROUP_CONCAT(id) as ids from UserShiftDay group by userShift_id, calendarDate_id having count(*) > 1;');
        foreach ($ids as $id) {
            $r = explode(',', $id->ids);
            $c = count($r);
            foreach ($r as $i) {
                try {
                    DB::delete('DELETE FROM UserShiftDay WHERE id = ' . $i);
                    $c--;
                    if ($c == 1) {
                        break;
                    }
                } catch (\Throwable) {
                    $c--;
                    continue;
                }
            }
        }

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->char('shiftType', 1)->nullable();

            $table->char('glideDurationType', 1)->nullable();
            $table->date('glidePeriodStartDate')->nullable();
            $table->date('glidePeriodEndDate')->nullable();
            $table->decimal('glidePeriodHours', 16, 10)->nullable()->default(0);
            $table->boolean('glideDayIsOn')->nullable()->default(true);
            $table->decimal('glideDayMinHours', 16, 10)->nullable()->default(0);
            $table->decimal('glideDayMaxHours', 16, 10)->nullable()->default(0);
            $table->decimal('glideDayHoursAverage', 16, 10)->nullable()->default(0);

            $table->unique(['userShift_id', 'calendarDate_id'], 'usd_us_cd_uq');
        });

        //endregion

        //region Tables Leave / Toil / RosteredTimeOff

        Schema::table('Leave', function (Blueprint $table) {
            $table->string('glideDailyStartTime')->nullable();
            $table->string('glideDailyEndTime')->nullable();
            $table->decimal('glideDailyHours', 16, 10)->nullable();
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->string('glideDailyStartTime')->nullable();
            $table->string('glideDailyEndTime')->nullable();
            $table->decimal('glideDailyHours', 16, 10)->nullable();
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->string('glideDailyStartTime')->nullable();
            $table->string('glideDailyEndTime')->nullable();
            $table->decimal('glideDailyHours', 16, 10)->nullable();
        });

        //endregion
    }

    public function down(): void
    {
        //region Tables Leave / Toil / RosteredTimeOff

        if (Schema::hasColumn('RosteredTimeOff', 'glideDailyHours')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('glideDailyHours');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'glideDailyEndTime')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('glideDailyEndTime');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'glideDailyStartTime')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('glideDailyStartTime');
            });
        }

        if (Schema::hasColumn('Toil', 'glideDailyHours')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('glideDailyHours');
            });
        }
        if (Schema::hasColumn('Toil', 'glideDailyEndTime')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('glideDailyEndTime');
            });
        }
        if (Schema::hasColumn('Toil', 'glideDailyStartTime')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('glideDailyStartTime');
            });
        }

        if (Schema::hasColumn('Leave', 'glideDailyHours')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('glideDailyHours');
            });
        }
        if (Schema::hasColumn('Leave', 'glideDailyEndTime')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('glideDailyEndTime');
            });
        }
        if (Schema::hasColumn('Leave', 'glideDailyStartTime')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('glideDailyStartTime');
            });
        }

        //endregion

        //region Table UserShiftDay
        if (DB::select(DB::raw('SHOW KEYS FROM UserShiftDay WHERE Key_name=\'usd_us_cd_uq\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropUnique('usd_us_cd_uq');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glideDayHoursAverage')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDayHoursAverage');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glideDayMaxHours')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMaxHours');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glideDayMinHours')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDayMinHours');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glideDayIsOn')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDayIsOn');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glidePeriodHours')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHours');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glidePeriodEndDate')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodEndDate');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glidePeriodStartDate')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodStartDate');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'glideDurationType')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glideDurationType');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'shiftType')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('shiftType');
            });
        }

        //endregion

        //region Table Shift

        if (Schema::hasColumn('Shift', 'glidePublicHolidaysRateType')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glidePublicHolidaysRateType');
            });
        }
        if (Schema::hasColumn('Shift', 'glideDays')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideDays');
            });
        }
        if (Schema::hasColumn('Shift', 'glideRequireTimeBlock')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideRequireTimeBlock');
            });
        }
        if (Schema::hasColumn('Shift', 'glideTime')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideTime');
            });
        }
        if (Schema::hasColumn('Shift', 'glideDuration')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideDuration');
            });
        }
        try {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropForeign('shift_gaprt_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('Shift', 'glideAlignPayRunType_id')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideAlignPayRunType_id');
            });
        }
        if (Schema::hasColumn('Shift', 'glideDurationType')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('glideDurationType');
            });
        }

        //endregion
    }
};
