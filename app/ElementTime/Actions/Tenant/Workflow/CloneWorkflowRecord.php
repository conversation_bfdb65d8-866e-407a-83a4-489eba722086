<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Workflow;

use Element\ElementTime\Domains\Tenant\Workflows\Contracts\WorkflowRelModelContract;
use Element\ElementTime\Domains\Tenant\Workflows\Models\Workflow;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowApproval;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowStep;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowStepItem;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowTimeline;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes as StatusTypes;

class CloneWorkflowRecord extends BaseWorkflowAction
{
    /** @throws \Throwable */
    public function handle(WorkflowRelModelContract $newRecord, WorkflowRelModelContract $originalRecord): void
    {
        $approvals_ids = [];

        $workflow = new Workflow;
        $workflow->rel_type = get_class($newRecord);
        $workflow->rel_id = $newRecord->id;
        $workflow->otherRel_type = get_class($originalRecord);
        $workflow->otherRel_id = $originalRecord->id;

        $workflow->assigningUser_id = $originalRecord->workflow->assigningUser_id;
        $workflow->ownerUser_id = $originalRecord->workflow->ownerUser_id;
        $workflow->type = $originalRecord->workflow->type;
        $workflow->status = StatusTypes\SubmittedType::ID;

        $workflow->clearSaveOrFail();

        foreach ($originalRecord->workflow->approvals as $oldApproval) {
            $newApproval = new WorkflowApproval;
            $newApproval->workflow_id = $workflow->id;
            $newApproval->workflowStepItem_id = $oldApproval->workflowStepItem_id;
            $newApproval->actorUser_id = $oldApproval->actorUser_id;
            $newApproval->type = $oldApproval->type;
            $newApproval->dateTime = $oldApproval->dateTime;
            $newApproval->notes = $oldApproval->notes;
            $newApproval->attachmentFile_id = $oldApproval->attachmentFile_id;

            $newApproval->clearSaveOrFail();

            $approvals_ids['_' . $oldApproval->id . '_'] = $newApproval->id;
        }

        foreach ($originalRecord->workflow->timelineEntries as $oldEntry) {
            if ($oldEntry->status != StatusTypes\SplitType::ID) {
                $newEntry = new WorkflowTimeline;
                $newEntry->workflow_id = $workflow->id;
                $newEntry->actorUser_id = $oldEntry->actorUser_id;
                $newEntry->approval_id =
                    isset($approvals_ids['_' . $oldEntry->approval_id . '_'])
                    && !is_null(isset($approvals_ids['_' . $oldEntry->approval_id . '_']))
                        ? $approvals_ids['_' . $oldEntry->approval_id . '_']
                        : null;
                $newEntry->dateTime = $oldEntry->dateTime;
                $newEntry->reason = $oldEntry->notes;
                $newEntry->comment = $oldEntry->comment;
                $newEntry->type = $oldEntry->type;
                $newEntry->status = $oldEntry->status;
                $newEntry->oldStatus = $oldEntry->oldStatus;

                $newEntry->clearSaveOrFail();
            }
        }

        foreach ($originalRecord->workflow->steps as $oldStep) {
            $newStep = new WorkflowStep;
            $newStep->workflow_id = $workflow->id;
            $newStep->assigningUser_id = $oldStep->assigningUser_id;
            $newStep->name = $oldStep->name;
            $newStep->number = $oldStep->number;
            $newStep->status = $oldStep->status;
            $newStep->clearSaveOrFail();

            foreach ($oldStep->parentItems as $oldStepParentItem) {
                $this->copyStepItem($oldStepParentItem, $newStep);
            }
        }
    }

    /** @throws \Throwable */
    protected function copyStepItem(WorkflowStepItem $oldStepItem, WorkflowStep $newStep, WorkflowStepItem|null $parentStepItem = null): void
    {
        $newStepItem = new WorkflowStepItem;
        $newStepItem->workflowStep_id = $newStep->id;
        $newStepItem->assigningUser_id = $oldStepItem->assigningUser_id;
        $newStepItem->parentWorkflowStepItem_id = !is_null($parentStepItem) ? $parentStepItem->id : null;
        $newStepItem->type = $oldStepItem->type;
        $newStepItem->details = $oldStepItem->details;
        $newStepItem->order = $oldStepItem->order;
        $newStepItem->isDone = $oldStepItem->isDone;
        $newStepItem->areNotificationsDisabled = $oldStepItem->areNotificationsDisabled;
        $newStepItem->canOverridePreviousSteps = $oldStepItem->canOverridePreviousSteps;

        $newStepItem->clearSaveOrFail();

        foreach ($oldStepItem->children as $child) {
            $this->copyStepItem($child, $newStep, $newStepItem);
        }
    }
}
