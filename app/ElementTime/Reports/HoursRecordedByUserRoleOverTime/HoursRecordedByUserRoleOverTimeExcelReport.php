<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\HoursRecordedByUserRoleOverTime;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Users\Models\UserPayType;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class HoursRecordedByUserRoleOverTimeExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
    ];

    const TIME = '1:30 min';
    const REPORT_SLUG = 'hours-recorded-by-user-role-over-time';
    const TYPE_SLUG = 'hours-recorded-by-user-role-over-time.xls';

    /** @var string|HoursRecordedByUserRoleOverTimeReportConfig */
    public $c = HoursRecordedByUserRoleOverTimeReportConfig::class;

    /** @var Collection|PayRun[]|null */
    public Collection|array|null $payRuns = null;

    // region --\\   Overwritten methods   //--

    protected function setData(array $info)
    {
        $this->_iSetData($info);
    }

    protected function collectReportData(): array|Collection
    {
        $ret = [];

        $this->payRuns = PayRun::q(
            constraints: [
                ['PayRun.startDate', '>=', $this->payRunStart->startDate],
                ['PayRun.endDate', '<=', $this->payRunEnd->endDate],
            ],
            relations: [
                'payRunItems:id,payRun_id,user_id',
                'payRunItems.timeSheet:id,payRunItem_id',
                'payRunItems.timeSheet.workflow',
//                'payRunItems.user.userShifts.userRole.userPayTypes.payType',
//                'payRunItems.user.userShifts.leaveRequestDays.leaveRequest.workflow',
//                'payRunItems.timeSheet.timeSheetDays.excessTime.items',
//                'payRunItems.timeSheet.timeSheetDays.excessTime.userShiftDay.userShift.userRole.userPayTypes.payType',
//                'payRunItems.timeSheet.timeSheetDayTimes.excessTimes.items',
//                'payRunItems.timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.userPayTypes.payType',
//                'payRunItems.timeSheet.timeSheetDayTimes.timeSheetDay',
            ],
            order: 'PayRun.startDate',
        )->many();

        $userRoles = UserRole::q(
            constraints: [
                ['UserRole.startDate', '<=', $this->payRunEnd->endDate],
                [
                    [
                        ['IS NULL', 'UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
            ],
            relations: [
                'user.userManagers.manager',
                'user.employeeType',
                'role',
                'userRoleManagers.manager',
                'userRoleDepartments.department',
                'userPayTypes.payType.payBand',
            ],
            order: 'CAST(User.externalId AS SIGNED), UserRole.startDate',
        )->many();

        foreach ($userRoles as $userRole) {
            if (!$this->isUserRoleRelevant($userRole)) {
                continue;
            }

            try {
                $userRoleManager = $userRole->repository->getManager($this->payRunEnd->endDate);
            } catch (InvalidArgumentException $e) {
                report($e);
                $userRoleManager = null;
            }

            $department = '';
            foreach ($userRole->userRoleDepartments as $userRoleDepartment) {
                if (!$userRoleDepartment->availability($this->payRunStart->startDate, $this->payRunEnd->endDate)->isCurrent(true)) {
                    continue;
                }

                if (strlen($department) > 0) {
                    $department .= ', ';
                }

                $department .= $userRoleDepartment->department->name;
            }

            foreach ($userRole->userPayTypes as $userPayType) {
                if ($userPayType->isActiveAnyTimeOnPeriod($this->payRunStart->startDate, $this->payRunEnd->endDate)) {
                    $rowId = static::buildRowId($userRole, $userPayType);

                    $obj = new \stdClass();
                    $obj->totalHoursWorked = 0;
                    $obj->totalHoursLeave = 0;
                    $obj->totalHoursPublicHoliday = 0;
                    $obj->totalHoursPaidExcessTime = 0;
                    $obj->totalHoursAccruedExcessTime = 0;
                    $obj->totalHoursAdditionalExcessTime = 0;
                    $obj->totalHoursUnpaidExcessTime = 0;
                    $obj->totalHours = 0;

                    $obj->actual = 0;
                    $obj->adjusted = 0;

                    $ret[$rowId] = [
                        'user' => [
                            'id' => $userRole->user->id,
                            'externalId' => $userRole->repository->getExternalId(),
                            'fullName' => $userRole->user->fullName,
                            'employeeType' => $userRole->user->employeeType->name,
                            'role' => $userRole->role->name,
                            'userRole_status' => $userRole->isActiveOnTime($this->payRunEnd->endDate) ? 'Active' : 'Inactive',
                            'roleManagerName' => !is_null($userRoleManager)
                                ? $userRoleManager->fullName
                                : '',
                            'department' => $department,
                            'payBand_name' => !is_null($userPayType->payType) ? $userPayType->payType->payBand->name : 'Custom',
                            'payType_externalId' => $userPayType->externalId,
                            'payType_hourlyAmount' => $userPayType->getHourlyAmount(),
                        ],
                        'hours' => $obj,
                    ];
                }
            }

            unset($userRole->user, $userRole->role, $userRole->userRoleMasters, $userRole->userRoleDepartments, $userRole->userPayTypes);
        }

        foreach ($this->payRuns as $payRun) {
            foreach ($payRun->payRunItems as $payRunItem) {
                if (is_null($payRunItem->timeSheet) || $payRunItem->timeSheet->repository->isExcluded()) {
                    continue;
                }

                $payRunItem->loadMissing([
                    'user.userShifts.userRole.userPayTypes.payType',
                    'user.userShifts.leaveRequestDays.leaveRequest.workflow',
                    'timeSheet.timeSheetDays.excessTime.items',
                    'timeSheet.timeSheetDays.excessTime.userShiftDay.userShift.userRole.userPayTypes.payType',
                    'timeSheet.timeSheetDayTimes.excessTimes.items',
                    'timeSheet.timeSheetDayTimes.userShiftDay.userShift.userRole.userPayTypes.payType',
                    'timeSheet.timeSheetDayTimes.timeSheetDay',
                ]);

                foreach ($payRunItem->user->userShifts as $userShift) {
                    foreach ($userShift->leaveRequestDays as $leaveRequestDay) {
                        if ($leaveRequestDay->date->isBetween($payRun->startDate, $payRun->endDate) && $leaveRequestDay->leaveRequest->repository->isApproved()) {
                            $userPayType = static::getUserPayType($userShift->userRole, $leaveRequestDay->date);
                            if (is_null($userPayType)) {
                                continue;
                            }

                            $rowId = static::buildRowId($userShift->userRole, $userPayType);

                            if (isset($ret[$rowId]['hours'])) {
                                $ret[$rowId]['hours']->totalHoursLeave += $leaveRequestDay->duration->getTotalHours();
                                $ret[$rowId]['hours']->totalHours += $leaveRequestDay->duration->getTotalHours();
                            }
                        }
                    }
                }

                foreach ($payRunItem->timeSheet->timeSheetDays as $timeSheetDay) {
                    if ($timeSheetDay->isFirstGlideDay && !is_null($timeSheetDay->excessTime)) {
                        $userPayType = static::getUserPayType($timeSheetDay->excessTime->userShiftDay->userShift->userRole, $timeSheetDay->date);
                        if (is_null($userPayType)) {
                            continue;
                        }
                        $rowId = static::buildRowId($timeSheetDay->excessTime->userShiftDay->userShift->userRole, $userPayType);

                        $ret[$rowId]['hours']->totalHoursPaidExcessTime += $timeSheetDay->excessTime->activeItem->totalHoursPaid;
                        $ret[$rowId]['hours']->totalHoursAccruedExcessTime += $timeSheetDay->excessTime->activeItem->totalHoursAccrued;
                        $ret[$rowId]['hours']->totalHoursAdditionalExcessTime += $timeSheetDay->excessTime->activeItem->totalHoursAdditional;
                        $ret[$rowId]['hours']->totalHoursUnpaidExcessTime += $timeSheetDay->excessTime->activeItem->totalHoursUnpaid;
                    }
                }

                foreach ($payRunItem->timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
                    $userPayType = static::getUserPayType($timeSheetDayTime->userShift->userRole, $timeSheetDayTime->timeSheetDay->date);
                    if (is_null($userPayType)) {
                        continue;
                    }

                    $rowId = static::buildRowId($timeSheetDayTime->userShift->userRole, $userPayType);

                    if (!isset($ret[$rowId])) {
                        continue;
                    }

                    if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                        $ret[$rowId]['hours']->totalHoursWorked += $timeSheetDayTime->hours;
                    } elseif ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                        $ret[$rowId]['hours']->totalHoursPublicHoliday += $timeSheetDayTime->hours;
                    }
                    $ret[$rowId]['hours']->totalHours += $timeSheetDayTime->hours;

                    foreach ($timeSheetDayTime->excessTimes as $excessTime) {
                        $ret[$rowId]['hours']->totalHoursPaidExcessTime += $excessTime->activeItem->totalHoursPaid;
                        $ret[$rowId]['hours']->totalHoursAccruedExcessTime += $excessTime->activeItem->totalHoursAccrued;
                        $ret[$rowId]['hours']->totalHoursAdditionalExcessTime += $excessTime->activeItem->totalHoursAdditional;
                        $ret[$rowId]['hours']->totalHoursUnpaidExcessTime += $excessTime->activeItem->totalHoursUnpaid;
                    }
                }

                unset($payRunItem->timeSheet, $payRunItem->user);
            }

            unset($payRun->payRunItems);
        }

        return $ret;
    }

    private static function getUserPayType(UserRole $userRole, Carbon $date): UserPayType|null
    {
        return $userRole->repository->getUserPayType($date);
    }

    private static function buildRowId(UserRole $userRole, UserPayType $userPayType): string
    {
        if (!is_null($userPayType->payType)) {
            return '_' . $userRole->id . '_' . $userPayType->payType_id;
        }

        return '_' . $userRole->id . '_' . $userPayType->getHourlyAmount();
    }

    protected function isUserRoleRelevant(UserRole $userRole): bool
    {
        return
            $userRole->startDate->lte($this->payRunEnd->endDate)
            && (
                is_null($userRole->endDate)
                || $userRole->endDate->lt(Carbon::minValue())
                || $userRole->endDate->gte($this->payRunStart->startDate)
            );
    }

    protected function buildReportData(): void
    {
        $data = $this->collectReportData();
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetTotals($sheet, $data);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function fetchSheetTotals(\stdClass|null &$sheet, array $data): void
    {
        $this->setSheetDefaultSettings($sheet);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->columns = [
            $this->createColumn('Date/time', 'text', 'center', 3),
            $this->createColumn('Span of report', 'text', 'center', 3),
            $this->createColumn('Number of staff', 'integer', 'center', 3),
        ];
        $staffIds = [];

        if ($this->payRunStart->id == $this->payRunEnd->id) {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear;
        } else {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear . ' to Payrun #' . $this->payRunEnd->number . ' ' . $this->payRunEnd->fiscalYear;
        }

        foreach ($data as $datum) {
            if (!in_array($datum['user']['id'], $staffIds)) {
                $staffIds[] = $datum['user']['id'];
            }
        }

        $table->data = [[
            'config' => [],
            'data' => [
                $this->parseDate(Carbon::now()),
                $span,
                count($staffIds),
            ],
        ]];

        $sheet->data[] = $table;
    }


    protected function fetchSheetData(\stdClass &$sheet = null, $data = []): void
    {
        $this->setSheetDefaultSettings($sheet);
        $rows = $this->loadData($data);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($rows);
        $sheet->data[] = $table;
    }

    public function loadData(array $data = []): array
    {
        $rows = [];

        foreach ($data as $datum) {
            $rows[] = [
                $datum['user']['externalId'],
                $datum['user']['fullName'],
                $datum['user']['employeeType'],
                $datum['user']['role'],
                $datum['user']['userRole_status'],
                $datum['user']['roleManagerName'],
                $datum['user']['department'],
                $datum['user']['payBand_name'],
                $datum['user']['payType_externalId'],
                $datum['user']['payType_hourlyAmount'],

                etime_round($datum['hours']->totalHoursWorked,2),
                etime_round($datum['hours']->totalHoursLeave),
                etime_round($datum['hours']->totalHoursPublicHoliday),
                etime_round($datum['hours']->totalHoursPaidExcessTime),
                etime_round($datum['hours']->totalHoursAccruedExcessTime),
                etime_round($datum['hours']->totalHoursAdditionalExcessTime),
                etime_round($datum['hours']->totalHoursUnpaidExcessTime),
                etime_round($datum['hours']->totalHours),
            ];
        }

        return $rows;
    }

    protected function getSheetDataHeader(): array
    {
        $ret = [
            $this->createColumn('#', 'integer', 'center'),
            $this->createColumn('Ext ref ID'),
            $this->createColumn('User name'),
            $this->createColumn('Employee type'),
            $this->createColumn('Role name'),
            $this->createColumn('Role status'),
            $this->createColumn('Role manager'),
            $this->createColumn('Department'),
            $this->createColumn('Pay-rate name'),
            $this->createColumn('Pay-rate code'),
            $this->createFloatColumn('Pay-rate hourly amount'),
            $this->createFloatColumn('Total hours worked'),
            $this->createFloatColumn('Total hours leave'),
            $this->createFloatColumn('Total hours public holiday'),
            $this->createFloatColumn('Total hours overtime paid'),
            $this->createFloatColumn('Total hours overtime accrued'),
            $this->createFloatColumn('Total hours overtime additional hours'),
            $this->createFloatColumn('Total hours overtime unpaid'),
            $this->createFloatColumn('Total hours'),
        ];

        return $ret;
    }

    protected function getSheetDataData(array $rows = []): array
    {
        $i = 0;
        $tableData = [];

        foreach ($rows as $row) {
            $rowConfig = [
                'color' => $i % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => array_merge([$i + 1], $row),
            ];

            $i++;
        }

        return $tableData;
    }
}
