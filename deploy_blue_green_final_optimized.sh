#!/bin/bash
#
#       _                           _  _____ ___ __  __ _____
#   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
#  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
# |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
#  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
#
# @link https://www.elementtime.com
# @copyright 2024 Adroit Creations
# Final Optimized Blue-Green ASG Deployment Script
#

set -euo pipefail

# Logging functions
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $*" >&2
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $*" >&2
}

log_debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] DEBUG: $*" >&2
    fi
}

function usage() {
    cat <<EOF
Usage:
    -h|--help
    -e|--env                    [ENV: test | stage | prod]
    -t|--type                   [TYPE: skt | normal | cmd]
    -n|--next                   [NEXT COLOR: b | g]
    -s|--env-version            [ENV_VERSION: default: 14si]

Examples:
    ./deploy_blue_green_final_optimized.sh -e test -t skt
    ./deploy_blue_green_final_optimized.sh -e prod -t normal
    ./deploy_blue_green_final_optimized.sh -e prod -t cmd
    ./deploy_blue_green_final_optimized.sh -e test -t skt -n b
    ./deploy_blue_green_final_optimized.sh -e test -t skt -n b -s 14si
EOF
}

# Parse command line arguments
parse_arguments() {
    if [[ $# -lt 4 ]]; then
        usage
        exit 1
    fi

    local POSITIONAL=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -e|--env)
                env="$2"
                shift 2
                ;;
            -t|--type)
                type="$2"
                shift 2
                ;;
            -n|--next)
                next="$2"
                shift 2
                ;;
            -s|--env-version)
                env_version="$2"
                shift 2
                ;;
            *)
                POSITIONAL+=("$1")
                shift
                ;;
        esac
    done

    set -- "${POSITIONAL[@]}"

    # Validate required parameters
    if [[ -z "${env:-}" ]]; then
        log_error "Environment parameter (-e|--env) is required"
        usage
        exit 1
    fi

    if [[ -z "${type:-}" ]]; then
        log_error "Type parameter (-t|--type) is required"
        usage
        exit 1
    fi

    if [[ ! "$env" =~ ^(test|stage|prod)$ ]]; then
        log_error "Invalid environment: $env. Must be test, stage, or prod"
        exit 1
    fi

    if [[ ! "$type" =~ ^(skt|normal|cmd)$ ]]; then
        log_error "Invalid type: $type. Must be skt, normal, or cmd"
        exit 1
    fi
}

# Initialize variables
initialize_variables() {
    readonly TARGET="${env}"
    readonly ENV_VERSION="${env_version:-14si}"
    readonly TYPE="$type"

    log_info "ASG deployment configuration:"
    log_info "  Environment: $TARGET"
    log_info "  Type: $TYPE"
    log_info "  Version: $ENV_VERSION"
    
    # ASG count configuration
    readonly ASG_COUNT_SOCKET=1
    readonly ASG_COUNT_SOCKET_MIN=1
    readonly ASG_COUNT_SOCKET_MAX=1

    readonly ASG_COUNT_CMD=0
    readonly ASG_COUNT_CMD_MIN=0
    readonly ASG_COUNT_CMD_MAX=1

    readonly ASG_COUNT_TEST=1

    readonly ASG_COUNT_STAGE=1
    readonly ASG_COUNT_STAGE_MIN_DAY=1
    readonly ASG_COUNT_STAGE_MIN_NIGHT=1
    readonly ASG_COUNT_STAGE_MAX_DAY=2
    readonly ASG_COUNT_STAGE_MAX_NIGHT=1

    readonly ASG_COUNT_PROD=2
    readonly ASG_COUNT_PROD_MIN_DAY=2
    readonly ASG_COUNT_PROD_MIN_NIGHT=2
    readonly ASG_COUNT_PROD_MAX_DAY=6
    readonly ASG_COUNT_PROD_MAX_NIGHT=2
}

# Generate ASG names based on type
generate_asg_names() {
    if [[ "$TYPE" == "skt" || "$TYPE" == "cmd" ]]; then
        readonly asg_b="asg-etime-${TARGET}-${TYPE}-b-${ENV_VERSION}"
        readonly asg_g="asg-etime-${TARGET}-${TYPE}-g-${ENV_VERSION}"
    elif [[ "$TYPE" == "normal" ]]; then
        readonly asg_b="asg-etime-${TARGET}-b-${ENV_VERSION}"
        readonly asg_g="asg-etime-${TARGET}-g-${ENV_VERSION}"
    else
        log_error "Invalid type: $TYPE"
        exit 1
    fi
}

# Get ASG count with error handling
get_asg_count() {
    local asg_name=$1
    
    local asg_count
    if ! asg_count=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-name "$asg_name" \
        --query "AutoScalingGroups[0].DesiredCapacity" \
        --output text 2>/dev/null); then
        log_debug "Failed to get ASG count for: $asg_name"
        echo "0"
        return 1
    fi
    
    # Validate count is a number
    if ! [[ $asg_count =~ ^[0-9]+$ ]]; then
        asg_count=0
    fi
    
    echo "$asg_count"
}

# Set ASG count with error handling
set_asg_count() {
    local asg=$1
    local min=$2
    local max=$3
    local desired=$4
    
    log_info "Updating ASG $asg: min=$min, max=$max, desired=$desired"
    
    if ! aws autoscaling update-auto-scaling-group \
        --auto-scaling-group-name "$asg" \
        --min-size "$min" \
        --max-size "$max" \
        --desired-capacity "$desired" 2>/dev/null; then
        log_error "Failed to update ASG: $asg"
        return 1
    fi
    
    log_info "Successfully updated ASG: $asg"
}

# Get empty ASG color (for determining next deployment target)
get_asg_empty_color() {
    log_debug "Checking ASG counts for $asg_b and $asg_g"
    
    local asg_empty_color
    if ! asg_empty_color=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "$asg_b" "$asg_g" \
        --query 'AutoScalingGroups[?DesiredCapacity==`0`].AutoScalingGroupName' \
        --output text 2>/dev/null | head -1 | awk -F'-' '{print $(NF-1)}'); then
        log_error "Failed to check ASG counts"
        return 1
    fi
    
    # If both ASGs are running (not empty), deployment is already in progress
    if [[ -z "$asg_empty_color" ]]; then
        log_error "All ASGs are not empty - deployment is running. Exit..."
        exit 1
    fi
    
    echo "$asg_empty_color"
}

# Scale up ASG based on type and environment
asg_scale_up() {
    local target=$1
    local type=$2
    local asg=$3
    local current_asg_name="${4:-}"  # Optional parameter for normal type

    local min max desired

    case "$type" in
        skt)
            min=$ASG_COUNT_SOCKET_MIN
            max=$ASG_COUNT_SOCKET_MAX
            desired=$ASG_COUNT_SOCKET
            ;;
        cmd)
            min=$ASG_COUNT_CMD_MIN
            max=$ASG_COUNT_CMD_MAX
            desired=$ASG_COUNT_CMD
            ;;
        normal)
            case "$target" in
                test)
                    min=$ASG_COUNT_TEST
                    max=$ASG_COUNT_TEST
                    desired=$ASG_COUNT_TEST
                    ;;
                stage)
                    min=$ASG_COUNT_STAGE_MIN_DAY
                    max=$ASG_COUNT_STAGE_MAX_DAY
                    desired=$ASG_COUNT_STAGE
                    ;;
                prod)
                    min=$ASG_COUNT_PROD_MIN_DAY
                    max=$ASG_COUNT_PROD_MAX_DAY
                    desired=$ASG_COUNT_PROD
                    ;;
                *)
                    log_error "Invalid target: $target"
                    exit 1
                    ;;
            esac

            # For normal type, respect current ASG count before switch
            if [[ -n "$current_asg_name" ]]; then
                log_info "ASG count init for [$asg]: $desired"
                local current_asg_count
                if current_asg_count=$(get_asg_count "$current_asg_name"); then
                    if [[ $current_asg_count -ne 0 && $current_asg_count -ne $desired ]]; then
                        desired=$current_asg_count
                        log_info "Respecting current ASG count: $desired"
                    fi
                fi
            fi
            ;;
        *)
            log_error "Invalid type: $type"
            exit 1
            ;;
    esac

    set_asg_count "$asg" "$min" "$max" "$desired"
}

# Main function
main() {
    # Parse arguments and validate
    parse_arguments "$@"
    
    # Initialize
    initialize_variables
    generate_asg_names
    
    log_info "Starting ASG deployment for $TYPE"
    
    # Determine next color
    local asg_next_color
    if [[ -n "${next:-}" ]]; then
        log_info "Using specified next color: $next"
        asg_next_color="$next"
    else
        log_info "Determining next color automatically"
        if ! asg_next_color=$(get_asg_empty_color); then
            exit 1
        fi
    fi
    
    # Determine current color
    local asg_current_color
    if [[ "$asg_next_color" == "b" ]]; then
        asg_current_color="g"
    elif [[ "$asg_next_color" == "g" ]]; then
        asg_current_color="b"
    else
        log_error "Unable to determine next color for ASG deployment"
        exit 1
    fi

    # Generate ASG names for current and next
    local asg_next_name asg_current_name
    if [[ "$TYPE" == "skt" || "$TYPE" == "cmd" ]]; then
        asg_next_name="asg-etime-${TARGET}-${TYPE}-${asg_next_color}-${ENV_VERSION}"
        asg_current_name="asg-etime-${TARGET}-${TYPE}-${asg_current_color}-${ENV_VERSION}"
    elif [[ "$TYPE" == "normal" ]]; then
        asg_next_name="asg-etime-${TARGET}-${asg_next_color}-${ENV_VERSION}"
        asg_current_name="asg-etime-${TARGET}-${asg_current_color}-${ENV_VERSION}"
    fi

    log_info "Next ASG name: $asg_next_name"
    log_info "Current ASG name: $asg_current_name"

    # Scale up the next ASG (pass current ASG name for normal type logic)
    asg_scale_up "$TARGET" "$TYPE" "$asg_next_name" "$asg_current_name"
    
    log_info "ASG deployment completed for $TYPE"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
