<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('EdrmsIntegratedDocumentType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->text('class');

            // Send data
            $table->text('documentType')->nullable();
            $table->text('containerId')->nullable();

            // Internal data
            $table->string('name');
            $table->boolean('isActive')->default(false);

            $table->timestamps();
        });

        Schema::create('EdrmsIntegratedFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Send data
            $table->text('iBucket');
            $table->text('iFilePath');
            $table->longText('iCustom');
            $table->text('iDocumentTitle');
            $table->text('iDocumentContainer');
            $table->longText('iDocumentExt');
            $table->text('iDocumentAuthor');
            $table->longText('iDocumentActions');
            $table->longText('iDocumentCustom');
            $table->text('iExternalId')->nullable();
            $table->longText('iResponseData')->nullable();

            // Dates
            $table->dateTime('createdDateTime');
            $table->dateTime('scheduledDateTime')->nullable();
            $table->dateTime('queuedDateTime')->nullable();
            $table->dateTime('finishedDateTime')->nullable();

            // Internal data
            $table->unsignedBigInteger('documentType_id');
            $table->foreign('documentType_id', 'edrmsif_edrmsdt_fn')->references('id')->on('EdrmsIntegratedDocumentType')->onUpdate('cascade')->onDelete('restrict');
            $table->text('integrable_type')->nullable();
            $table->unsignedBigInteger('integrable_id')->nullable();

            $table->unique(['documentType_id', 'integrable_id'], 'edrmsif_file_uq');

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::table('PayRun', function (Blueprint $table) {
            $table->boolean('hasEdrmsIntegrationDone')->default(true);
            $table->dateTime('dateTimeEdrmsIntegrationDone')->nullable();
            $table->boolean('isDoingEdrmsIntegration')->default(false);
        });

        Schema::table('PayRunItem', function (Blueprint $table) {
            $table->boolean('hasEdrmsIntegrationDone')->default(false);
            $table->dateTime('dateTimeEdrmsIntegrationDone')->nullable();
            $table->boolean('isDoingEdrmsIntegration')->default(false);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('PayRunItem', 'hasEdrmsIntegrationDone')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDone');
            });
        }
        if (Schema::hasColumn('PayRunItem', 'dateTimeEdrmsIntegrationDone')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDone');
            });
        }
        if (Schema::hasColumn('PayRunItem', 'isDoingEdrmsIntegration')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegration');
            });
        }

        if (Schema::hasColumn('PayRun', 'hasEdrmsIntegrationDone')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDone');
            });
        }
        if (Schema::hasColumn('PayRun', 'dateTimeEdrmsIntegrationDone')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDone');
            });
        }
        if (Schema::hasColumn('PayRun', 'isDoingEdrmsIntegration')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegration');
            });
        }

        Schema::dropIfExists('EdrmsIntegratedFile');
        Schema::dropIfExists('EdrmsIntegratedDocumentType');
    }
};
