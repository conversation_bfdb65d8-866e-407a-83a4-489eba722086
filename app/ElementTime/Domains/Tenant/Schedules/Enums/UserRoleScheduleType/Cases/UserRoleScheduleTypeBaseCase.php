<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\Cases;

use Element\Core\Exceptions\InvalidStatusException;
use Element\Core\Exceptions\NotImplementedException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Domains\Tenant\_System\Support\EavSetType\EavSetType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRun\ScheduledPayRunSpecificDetailedResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRun\ScheduledPayRunSpecificListingResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRun\ScheduledPayRunSpecificModelContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriod\ScheduledPayRunPeriodSpecificDetailedResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriod\ScheduledPayRunPeriodSpecificListingResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriod\ScheduledPayRunPeriodSpecificModelContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDaySpecificDetailedResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDaySpecificListingResourceContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDaySpecificModelContract;
use Element\ElementTime\Domains\Tenant\Schedules\Contracts\UserRoleScheduleTypeModelContract;
use Element\ElementTime\Domains\Tenant\Schedules\EavSets\UserRoleSchedule\ExcessTimeAsOrdinary\UserRoleScheduleExcessTimeAsOrdinaryEavSet;
use Element\ElementTime\Domains\Tenant\Schedules\EavSets\UserRoleSchedule\HolidayCalculations\UserRoleScheduleHolidayCalculationsEavSet;
use Element\ElementTime\Domains\Tenant\Schedules\EavSets\UserRoleSchedule\LeaveCalculations\UserRoleScheduleLeaveCalculationsEavSet;
use Element\ElementTime\Domains\Tenant\Schedules\EavSets\UserRoleSchedule\RdoCalculations\UserRoleScheduleRdoCalculationsEavSet;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRun\ScheduledPayRunDetailedResource;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRun\ScheduledPayRunListingResource;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRunPeriod\ScheduledPayRunPeriodDetailedResource;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRunPeriod\ScheduledPayRunPeriodListingResource;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayDetailedResource;
use Element\ElementTime\Domains\Tenant\Schedules\Resources\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayListingResource;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructMissingMappingItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructRecordedMappingItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructScheduledDayMappingItem;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructScheduledPeriodMappingItem;
use Element\ElementTime\Support\Domains\Resource;
use Element\ElementTime\Support\Enums\RecordedVia;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Leocello\SweetEnum\SweetCaseClass;

class UserRoleScheduleTypeBaseCase extends SweetCaseClass
{
    /** @throws \Throwable */
    public function getSpecificAssignment(UserRoleSchedule $userRoleSchedule): UserRoleScheduleTypeModelContract
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    //region --\\   EAV options   //--

    public function hasExcessTimeAsOrdinaryEavSet(): bool
    {
        return false;
    }

    public function getExcessTimeAsOrdinaryEavSetOptions(): array|null
    {
        return null;
    }

    public function hasHolidayCalculationsEavSet(): bool
    {
        return false;
    }

    public function getHolidayCalculationsEavSetOptions(): array|null
    {
        return null;
    }

    public function hasLeaveCalculationsEavSet(): bool
    {
        return false;
    }

    public function getLeaveCalculationsEavSetOptions(): array|null
    {
        return null;
    }

    public function hasRdoCalculationsEavSet(): bool
    {
        return false;
    }

    public function getRdoCalculationsEavSetOptions(): array|null
    {
        return null;
    }

    //endregion --\\   EAV options   //--

    //region --\\   Calculation methods   //--

    /** @throws \Throwable */
    public function getMinDurationOnScheduledPeriod(ScheduledPayRunPeriod $scheduledPeriod): TimeDuration
    {
        return TimeDuration::zero();
    }

    /** @throws \Throwable */
    public function getMaxDurationOnScheduledPeriod(ScheduledPayRunPeriod $scheduledPeriod): TimeDuration
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    /** @throws \Throwable */
    public function getMinDurationOnScheduledDay(ScheduledPayRunPeriodDay $scheduledDay): TimeDuration
    {
        return TimeDuration::zero();
    }

    /** @throws \Throwable */
    public function getMaxDurationOnScheduledDay(ScheduledPayRunPeriodDay $scheduledDay): TimeDuration
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    /** @throws \Throwable */
    public function addTimeSheetDayMappingForScheduledDay(Collection|array $map, ScheduledPayRunPeriodDay $scheduledDay): void
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    /** @throws \Throwable */
    public function getPeriodMappingForScheduledPeriod(ScheduledPayRunPeriod $scheduledPeriod): StructScheduledPeriodMappingItem
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    /** @throws \Throwable */
    public function getOrdinaryMappingItemFromScheduledAndRecorded(StructScheduledDayMappingItem $scheduledMappingItem, StructRecordedMappingItem $recordedMappingItem, TimeDuration|null $maxDurationAllowed): StructRecordedMappingItem|null
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    /** @throws \Throwable */
    public function getMissingMappingItemFromScheduledAndRecorded(StructScheduledDayMappingItem $scheduledMappingItem, Collection|array $recordedMapping): StructMissingMappingItem|null
    {
        return null;
    }

    /** @throws \Throwable */
    public function getExtraMappingItemFromScheduledAndRecorded(Collection|array $scheduledMapping, StructRecordedMappingItem $recordedMappingItem, TimeDuration|null $maxDurationAllowed): Collection|array
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    public function getLabel(UserRoleSchedule $userRoleSchedule): string
    {
        return '';
    }

    //endregion --\\   Calculation methods   //--

    //region --\\   Leave Calculation methods   //--

    /** @throws \Throwable */
    public function splitLeaveRequest(LeaveRequest $leaveRequest, RecordedVia $recordedVia): void
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    public function getLeavePeriodDescription(LeaveRequest $leaveRequest, $withTime = false, bool $isHtml = false): string
    {
        $dateFormat = TenantSystemSettings::o()->dateFormat;

        if ($isHtml) {
            return $leaveRequest->endDate->isSameDay($leaveRequest->startDate)
                ? '<strong>' . $leaveRequest->startDate->format($dateFormat) . '</strong>'
                : '<strong>' . $leaveRequest->startDate->format($dateFormat) . '</strong> to <strong>' . $leaveRequest->endDate->format($dateFormat) . '</strong>';
        }

        return $leaveRequest->endDate->isSameDay($leaveRequest->startDate)
            ? $leaveRequest->startDate->format($dateFormat)
            : $leaveRequest->startDate->format($dateFormat) . ' to ' . $leaveRequest->endDate->format($dateFormat);
    }

    /** @throws \Throwable */
    public function getLeaveDayDescription(LeaveRequestDay $leaveRequestDay, $withTime = false, bool $isHtml = false): string
    {
        throw new NotImplementedException(__FUNCTION__ . ' on ' . static::class);
    }

    //endregion --\\   Leave Calculation methods   //--

    //region --\\   Related models   //--

    public function scheduledPayRunModel(): string|ScheduledPayRunSpecificModelContract
    {
        return ScheduledPayRun::class . $this->case->name();
    }

    public function scheduledPayRunPeriodModel(): string|ScheduledPayRunPeriodSpecificModelContract
    {
        return ScheduledPayRunPeriod::class . $this->case->name();
    }

    public function scheduledPayRunPeriodDayModel(): string|ScheduledPayRunPeriodDaySpecificModelContract
    {
        return ScheduledPayRunPeriodDay::class . $this->case->name();
    }

    //endregion --\\   Related models   //--

    //region --\\   Resource classes   //--

    public function getSpecificResourceForCommon(Resource|string $resource): Resource|string
    {
        $classNamespace = str_replace('/', '\\', dirname(str_replace('\\', '/', $resource)));
        $classArray = explode('\\', $resource);

        $classNamespace .= '\\' . $classArray[count($classArray) - 2] . $this->case->name();
        $className = $classArray[count($classArray) - 1];

        return $classNamespace . '\\' . $className;
    }

    public function payRunListingResource(): string|ScheduledPayRunSpecificListingResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunListingResource::class);
    }

    public function payRunPeriodListingResource(): string|ScheduledPayRunPeriodSpecificListingResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunPeriodListingResource::class);
    }

    public function payRunPeriodDayListingResource(): string|ScheduledPayRunPeriodDaySpecificListingResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunPeriodDayListingResource::class);
    }

    public function payRunDetailedResource(): string|ScheduledPayRunSpecificDetailedResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunDetailedResource::class);
    }

    public function payRunPeriodDetailedResource(): string|ScheduledPayRunPeriodSpecificDetailedResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunPeriodDetailedResource::class);
    }

    public function payRunPeriodDayDetailedResource(): string|ScheduledPayRunPeriodDaySpecificDetailedResourceContract
    {
        return $this->getSpecificResourceForCommon(ScheduledPayRunPeriodDayDetailedResource::class);
    }

    //endregion --\\   Resource classes   //--

    //region --\\   General schedule assignment methods   //--

    /** @throws \Throwable */
    public function createFromData(UserRoleSchedule $userRoleSchedule, array|\stdClass|null $data = null): UserRoleScheduleTypeModelContract|null
    {
        if (is_array($data)) {
            $data = (object) $data;
        }

        return $this->case->modelClass()::createFromData($userRoleSchedule, $data);
    }

    /**
     * @throws UnauthorizedActionException
     * @throws InvalidStatusException
     * @throws \Throwable
     */
    public function onAfterSaveSchedule(UserRoleSchedule $userRoleSchedule): void
    {
        if ($this->hasExcessTimeAsOrdinaryEavSet()) {
            $userRoleSchedule->excessTimeAsOrdinary->refreshModel($userRoleSchedule)->save();
        }

        if ($this->hasHolidayCalculationsEavSet()) {
            $userRoleSchedule->holidayCalculations->refreshModel($userRoleSchedule)->save();
        }

        if ($this->hasLeaveCalculationsEavSet()) {
            $userRoleSchedule->leaveCalculations->refreshModel($userRoleSchedule)->save();
        }

        if ($this->hasRdoCalculationsEavSet()) {
            $userRoleSchedule->rdoCalculations->refreshModel($userRoleSchedule)->save();
        }
    }

    //endregion --\\   General schedule assignment methods   //--

    // --------------------------------------------------

    //region --\\   EAV final methods   //--

    final public function eavSetsConfig(bool $asArray = false): array|\stdClass
    {
        $ret = [
            'excessTimeAsOrdinary' => $this->eavSetsConfigExcessTimeAsOrdinary(),
            'holidayCalculations' => $this->eavSetsConfigHolidayCalculations(),
            'leaveCalculations' => $this->eavSetsConfigLeaveCalculations(),
            'rdoCalculations' => $this->eavSetsConfigRdoCalculations(),
        ];

        if ($asArray) {
            return $ret;
        }

        return json_decode(json_encode($ret));
    }

    final public function eavSetsConfigExcessTimeAsOrdinary(): array|null
    {
        return self::getEavConfig(
            UserRoleScheduleExcessTimeAsOrdinaryEavSet::class,
            $this->hasExcessTimeAsOrdinaryEavSet(),
            $this->getExcessTimeAsOrdinaryEavSetOptions(),
        );
    }

    final public function eavSetsConfigHolidayCalculations(): array|null
    {
        return self::getEavConfig(
            UserRoleScheduleHolidayCalculationsEavSet::class,
            $this->hasHolidayCalculationsEavSet(),
            $this->getHolidayCalculationsEavSetOptions(),
        );
    }

    final public function eavSetsConfigLeaveCalculations(): array|null
    {
        return self::getEavConfig(
            UserRoleScheduleLeaveCalculationsEavSet::class,
            $this->hasLeaveCalculationsEavSet(),
            $this->getLeaveCalculationsEavSetOptions(),
        );
    }

    final public function eavSetsConfigRdoCalculations(): array|null
    {
        return self::getEavConfig(
            UserRoleScheduleRdoCalculationsEavSet::class,
            $this->hasRdoCalculationsEavSet(),
            $this->getRdoCalculationsEavSetOptions(),
        );
    }

    final public function addSpecificEavSets(UserRoleSchedule $userRoleSchedule, array|\stdClass|null $data = null): void
    {
        if ($this->hasExcessTimeAsOrdinaryEavSet()) {
            $userRoleSchedule->excessTimeAsOrdinary->setValues($data->excessTimeAsOrdinary ?? []);
        }

        if ($this->hasHolidayCalculationsEavSet()) {
            $userRoleSchedule->holidayCalculations->setValues($data->holidayCalculations ?? []);
        }

        if ($this->hasLeaveCalculationsEavSet()) {
            $userRoleSchedule->leaveCalculations->setValues($data->leaveCalculations ?? []);
        }

        if ($this->hasRdoCalculationsEavSet()) {
            $userRoleSchedule->rdoCalculations->setValues($data->rdoCalculations ?? []);
        }
    }

    final protected static function getEavConfig(EavSetType|string $eavSetType, bool $hasEavSet, array|null $options): array|null
    {
        if (!$hasEavSet) {
            return null;
        }

        $config = $eavSetType::getConfig();

        if (!is_null($options)) {
            foreach ($options as $k => $v) {
                if (isset($config[$k])) {
                    if (isset($v['required'])) {
                        $config[$k]['required'] = $v['required'];
                    }

                    if (isset($v['default'])) {
                        if ($v['default'] instanceof Arrayable) {
                            $config[$k]['default'] = $v['default']->toArray();
                        } else {
                            $config[$k]['default'] = $v['default'];
                        }
                    }

                    if (isset($v['typeOptions']) && isset($config[$k]['typeOptions'])) {
                        $config[$k]['typeOptions'] = [];
                        foreach ($v['typeOptions'] as $typeOption) {
                            $config[$k]['typeOptions'][] = $typeOption->toArray();
                        }
                    }
                }
            }
        }

        return $config;
    }

    //endregion --\\   EAV final methods   //--
}
