<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\DailyWorkCostingReport;

use Carbon\Carbon;
use Element\Core\Support\Helpers\Str;
use Element\Core\Types\Minute;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantSheetDayTime;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class DailyWorkCostingExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
        UserSystemAccessFlags\DepartmentManagerFlag::class,
    ];

    const TIME = '~30sec';
    const REPORT_SLUG = 'daily-work-costing-report';
    const TYPE_SLUG = 'daily-work-costing.xls';
    const DOES_CACHE_REPORT = false;

    public $c = DailyWorkCostingReportConfig::class;
    public bool $doesIncludeSubDepartments = false;

    private Department|null $department = null;
    private Carbon|null $date = null;

    protected function setData(array $info): void
    {
        $this->department = Department::q(
            constraints: [
                ['Department.id', '=', $info['idDepartment']],
            ],
        )->first();

        if (isset($info['doesIncludeSubDepartments'])) {
            $this->doesIncludeSubDepartments = boolval($info['doesIncludeSubDepartments']);
        }

        $this->date = Carbon::parse($info['date']);
    }

    public function getReplacedString(string $string): string
    {
        $string = parent::getReplacedString($string);

        if (isset($this->department) && $this->department instanceof Department) {
            $string = Str::replace('{{department.id}}', $this->department->id . ($this->doesIncludeSubDepartments ? '-s' : ''), $string);
            $string = Str::replace('{{department.name}}', $this->department->name . ($this->doesIncludeSubDepartments ? ' and sub departments' : ''), $string);
        } else {
            $string = Str::replace('{{department.id}}', '0', $string);
            $string = Str::replace('{{department.name}}', 'All departments', $string);
        }

        if (isset($this->date) && $this->date instanceof Carbon) {
            $string = Str::replace('{{date}}', $this->date->toDateString(), $string);
        }

        return $string;
    }

    /**
     * @return Collection|UserShiftDay[]
     */
    private function collectReportData(): Collection|array
    {
        $constraints = [
            ['UserShiftDay.date', '=', $this->date->toDateString()],
            [
                [
                    ['IS NULL', 'UserShift.startDate', 'OR'],
                    ['UserShift.startDate', '=', '0000-00-00', 'OR'],
                    ['UserShift.startDate', '<=', $this->date, 'OR'],
                ]
            ],
            [
                [
                    ['IS NULL', 'UserShift.endDate', 'OR'],
                    ['UserShift.endDate', '=', '0000-00-00', 'OR'],
                    ['UserShift.endDate', '>=', $this->date, 'OR'],
                ]
            ],
            [
                [
                    ['IS NULL', 'UserRole.startDate', 'OR'],
                    ['UserRole.startDate', '=', '0000-00-00', 'OR'],
                    ['UserRole.startDate', '<=', $this->date, 'OR'],
                ]
            ],
            [
                [
                    ['IS NULL', 'UserRole.endDate', 'OR'],
                    ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                    ['UserRole.endDate', '>=', $this->date, 'OR'],
                ]
            ],
        ];

        if (!is_null($this->department)) {
            if ($this->doesIncludeSubDepartments) {
                $allStaffs = $this->department->repository->getAllStaff(true, false, $this->date);
                $user_ids = [];

                foreach ($allStaffs as $staff) {
                    $user_ids[] = $staff->id;
                }

                $constraints[] = ['IN', 'UserDepartment.user_id', $user_ids];
            } else {
                $constraints[] = ['UserDepartment.department_id', '=', $this->department->id];
            }
        }

        return UserShiftDay::q(
            constraints: $constraints,
            relations: [
                'scheduledTimes',
                'expectedTimes',
                'userShift.shift',
                'userShift.userRole.role:id,name',
                'userShift.userRole.userRoleProjects.project:id,externalId,name,projectCode,activityCode',
                'userShift.userRole.userRoleProjects.projectActivity.project:id,externalId,name,projectCode,activityCode',
                'userShift.userRole.userRoleProjects.project:id,externalId,name,projectCode,activityCode',
                'userShift.user.userHigherDuties.workflow:id,rel_id,rel_type,status',
                'userShift.user.userHigherDuties.dutyType',
                'userShift.user.userHigherDuties.onBehalfUserRole.user',
                'userShift.user.userHigherDuties.onBehalfUserRole.role:id,name',
                'userShift.user.userHigherDuties.userHigherDutyUserRoles',
                'userShift.user.userHigherDuties.role:id,name',
                'approvedLeaveRequestDays:id,userShiftDay_id,minutes,leaveRequest_id',
                'approvedLeaveRequestDays.leaveRequest.userLeaveBankType.type',
                'approvedLeaveRequestDays.leaveRequest.userLeaveBankType.bank.user.userRoles.userRoleMasters',
                'approvedLeaveRequestDays.leaveRequest.userLeaveBankType.bank.user.userRoles.userRoleProjects.project:id,externalId,name,projectCode,activityCode',
                'timeSheetDayTimes.type.projectActivity.project:id,externalId,name,projectCode,activityCode',
                'timeSheetDayTimes.type.project:id,externalId,name,projectCode,activityCode',
                'timeSheetDayTimes.plantTimes.plantSheet.plantItem',
                'timeSheetDayTimes.works.plantTimes.plantSheet.plantItem',
                'timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.workOrderType',
                'timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType:id,costCode,activityCode,externalId',
                'timeSheetDayTimes.works.workOrderType.workOrderTypeActivityTypes.activityType:id,name,costCode,activityCode,externalId',
                'timeSheetDayTimes.works.activityType:id,costCode,activityCode,externalId',
            ],
            joins: [
                ['UserRole', 'UserShift.userRole_id', '=', 'UserRole.id'],
                ['UserDepartment', 'UserDepartment.userRole_id', '=', 'UserRole.id'],
            ],
            order: [
                'UserRole.user_id ASC',
            ],
        )->many();
    }

    protected function buildReportData(): void
    {
        $userShiftDays = $this->collectReportData();

        $this->setSheetTitle($sheet, 'Daily work costing report');
        $this->setSheetHeading($sheet);
        $this->setSheetData($sheet, $userShiftDays);

        $this->sheets = [
            $sheet,
        ];
    }

    private static function createRowStructure(array $config, array $data): array
    {
        return [
            'config' => $config,
            'data' => $data,
        ];
    }

    private function setSheetHeading(\stdClass &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass;
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Date', 'text', 'center', 3),
            $this->createColumn('Department', 'text', 'center', 3),
            $this->createColumn('Time of report', 'text', 'center', 3),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                $this->date->format($this->dateFormat),
                !is_null($this->department)
                    ? $this->department->name
                    : 'All departments',
                Carbon::now()->format($this->dateFormat),
            ],
        ]];

        $sheet->data[] = $data;
    }

    /**
     * @param Collection|UserShiftDay[] $userShiftDays
     */
    private function setSheetData(\stdClass $sheet, Collection|array $userShiftDays): void
    {
        foreach ($userShiftDays as $rowIndex => $userShiftDay) {
            $separator = new \stdClass;
            $separator->type = 'separator';
            $separator->height = 40;
            $sheet->data[] = $separator;

            $headerRowConfig = [
                'customBorder' => Border::BORDER_NONE,
                'font-weight' => 'bold',
                'color' => $rowIndex % 2 === 0
                    ? 'c6e0b4'
                    : 'f8cbad',
            ];
            $this->getDataPrimaryHeader($sheet, $userShiftDay, $headerRowConfig);
            $this->getDataSecondaryHeader($sheet, $userShiftDay, $headerRowConfig);
            $this->getDataDetails($sheet, $userShiftDay);
        }
    }
    private function getDataPrimaryHeader(\stdClass $sheet, UserShiftDay $userShiftDay, array $headerColumnOptions): void
    {
        $primaryHeader = new \stdClass;
        $primaryHeader->type = 'data-table';
        $primaryHeader->noSeparator = true;
        $primaryHeader->config = new \stdClass;
        $primaryHeader->config->noLabel = true;

        $scheduledHours = 'Not scheduled';
        $scheduledTime = 'Not scheduled';

        if ($userShiftDay->isOn && isset($userShiftDay->scheduledTimes[0])) {
            if ($userShiftDay->userShift->shift->type == 'G' && !$userShiftDay->userShift->shift->glideRequireTimeBlock) {
                $scheduledHours = $userShiftDay->hours;
                $scheduledTime = ' - ';
            } elseif ($userShiftDay->userShift->shift->type == 'G') {
                $scheduledHours = $userShiftDay->userShift->shift->glideDurationType == 'D' ? $userShiftDay->hours : 'No expected hours';
                $scheduledTime = 'No expected time';

                $expectedTime = $userShiftDay->hasExpectedSchedule && isset($userShiftDay->expectedTimes[0]) ? $userShiftDay->expectedTimes[0] : null;

                if (!is_null($expectedTime)) {
                    try {
                        $scheduledHours = $expectedTime->minutes->toHours();
                    } catch (\Throwable) {
                        $scheduledHours = Minute::make($expectedTime->startDateTime->diffInMinutes($expectedTime->endDateTime), 'minutes')->toHours();

                        $scheduledTimeBlock = $userShiftDay->scheduledTimes[0];
                        if (!is_null($scheduledTimeBlock)) {
                            foreach ($scheduledTimeBlock->breaks as $break) {
                                $scheduledHours -= $break->minutes->toHours();
                            }
                        }
                    }
                    $scheduledTime = $expectedTime->startDateTime->format($this->timeFormat) . ' - ' . $expectedTime->endDateTime->format($this->timeFormat);
                }
            } else {
                $scheduledTimeBlock = $userShiftDay->scheduledTimes[0];

                if (!is_null($scheduledTimeBlock)) {
                    $scheduledHours = $scheduledTimeBlock->minutes->toHours();
                    $scheduledTime = $scheduledTimeBlock->startDateTime->format($this->timeFormat) . ' - ' . $scheduledTimeBlock->endDateTime->format($this->timeFormat);
                }
            }
        }

        $primaryHeader->columns = [
            $this->createcolumn('', 'text', 'right'),
            $this->createcolumn('', 'text', 'left'),
            $this->createcolumn('', 'text', 'left'),
            $this->createcolumn('', 'text'),
            $this->createcolumn('', 'text'),
            $this->createcolumn('', 'text'),
            $this->createcolumn('', 'text', 'center'),
            $this->createcolumn('', 'text', 'center'),
            $this->createcolumn('', 'text', 'center'),
            $this->createcolumn('', 'text', 'center'),
            $this->createcolumn('', 'text', 'center'),
        ];

        $primaryHeader->data = [[
            'config' => $headerColumnOptions,
            'data' => [
                $userShiftDay->userShift->userRole->repository->getExternalId(),
                $userShiftDay->userShift->user->fullName,
                $userShiftDay->userShift->userRole->role->name,
                '',
                '',
                '',
                '',
                'Scheduled hours:',
                $scheduledHours,
                'Start and end time',
                $scheduledTime,
            ]
        ]];

        $sheet->data[] = $primaryHeader;
    }

    private function getDataSecondaryHeader(\stdClass $sheet, UserShiftDay $userShiftDay, array $headerColumnOptions): void
    {
        $secondaryHeader = new \stdClass;
        $secondaryHeader->type = 'data-table';
        $secondaryHeader->noSeparator = true;
        $secondaryHeader->config = new \stdClass;
        $secondaryHeader->config->noLabel = true;
        $lines = 1;

        $dutyLabel = '';
        $glideRequireTimeBlock = $userShiftDay->userShift->shift->glideRequireTimeBlock;
        if ($userShiftDay->isOnHigherDuty) {
            $dutyLabel = $userShiftDay->higherDuty->dutyTypeName . ': ';
            try {
                $dutyLabel .= !is_null($userShiftDay->higherDuty->onBehalfUserRole)
                    ? 'Acting on behalf of '. $userShiftDay->higherDuty->onBehalfUserRole->user->fullName . ' (' . $userShiftDay->higherDuty->onBehalfUserRole->role->name . ')'
                    : 'Acting as '. $userShiftDay->higherDuty->role->name;
            } catch (\Throwable) {
            }
        }

        $actualHours = 0;
        $recordedTimes = '';

        $index = 0;

        foreach ($userShiftDay->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                continue;
            }

            $actualHours += $timeSheetDayTime->hours;

            if ($index > 0) {
                $recordedTimes .= $this->br;
                $lines++;
            }

            if ($glideRequireTimeBlock) {
                $recordedTimes .= $timeSheetDayTime->startDateTime->format($this->timeFormat) . ' - ' . $timeSheetDayTime->endDateTime->format($this->timeFormat);
            }

            $index++;
        }

        if (strlen($recordedTimes) < 1) {
            $recordedTimes = ' - ';
        }

        $actualHoursLabel = $actualHours > 0
            ? $actualHours
            : 'No hours';
        $recordedTimesLabel = strlen($recordedTimes) > 0
            ? $recordedTimes
            : 'No hours';

        $secondaryHeader->columns = [
            $this->createColumn('', 'text', 'right'),
            $this->createColumn('', 'text', 'left'),
            $this->createColumn('', 'text', 'left'),
            $this->createColumn('', 'text'),
            $this->createColumn('', 'text'),
            $this->createColumn('', 'text'),
            $this->createColumn('', 'text'),
            $this->createColumn('', 'text', 'center'),
            $this->createColumn('', 'text', 'center'),
            $this->createColumn('', 'text', 'center'),
            $this->createColumn('', 'text', 'center'),
        ];

        $headerColumnOptions['height'] = ($lines * 13) + 10;

        $secondaryHeader->data = [[
            'config' => $headerColumnOptions,
            'data' => [
                'Date:',
                $this->date->format($this->dateFormat),
                $dutyLabel,
                '',
                '',
                '',
                '',
                'Actual hours:',
                $actualHoursLabel,
                'Start and end time',
                $recordedTimesLabel,
            ],
        ]];

        $sheet->data[] = $secondaryHeader;
    }

    private function getDataDetails(\stdClass $sheet, UserShiftDay $userShiftDay): void
    {
        $total = 0;
        $table = new \stdClass;
        $table->type = 'data-table';
        $table->noSeparator = true;
        $table->columns = [
            $this->createColumn(label: 'Worked hours'),
            $this->createColumn(label: 'Cost code'),
            $this->createColumn(label: 'Costing detail'),
            $this->createColumn(label: 'Time worked comments'),
            $this->createColumn(label: 'Plant code'),
            $this->createColumn(label: 'Plant details'),
            $this->createColumn(label: 'Plant comments'),
            $this->createColumn(label: 'Plant hours'),
            $this->createColumn(label: 'Plant mileage'),
            $this->createColumn(label: 'Plant cartage'),
            $this->createColumn(label: 'Plant odometer'),
        ];

        $data = [];
        $groupedWorks = [];

        foreach ($userShiftDay->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                continue;
            }

            if ($timeSheetDayTime->finalCalculatedHoursMinusWorks > 0) {
                $key = 'project_' . $timeSheetDayTime->formattedCode;
                if (!isset($groupedWorks[$key])) {
                    $groupedWorks[$key] = [
                        'hours' => 0,
                        'code' => $timeSheetDayTime->formattedCode,
                        'itemName' => $timeSheetDayTime->itemName,
                        'comments' => [],
                        'plantTimes' => collect([]),
                    ];
                }
                $groupedWorks[$key]['hours'] += $timeSheetDayTime->finalCalculatedHoursMinusWorks;
                $groupedWorks[$key]['plantTimes'] = $groupedWorks[$key]['plantTimes']->concat($timeSheetDayTime->plantTimes->whereNull('timeSheetDayTimeWork_id'));
                if (!is_null($timeSheetDayTime->notes) && strlen($timeSheetDayTime->notes) > 0) {
                    $groupedWorks[$key]['comments'][] = $timeSheetDayTime->notes;
                }
            }

            foreach ($timeSheetDayTime->works as $work) {
                $key = 'work_' . $work->formattedCode;
                if (!isset($groupedWorks[$key])) {
                    $groupedWorks[$key] = [
                        'hours' => 0,
                        'code' => $work->formattedCode,
                        'itemName' => $work->itemName,
                        'comments' => [],
                        'plantTimes' => collect([]),
                    ];
                }
                $groupedWorks[$key]['hours'] += $work->hours;
                $groupedWorks[$key]['plantTimes'] = $groupedWorks[$key]['plantTimes']->concat($work->plantTimes);
                if (!is_null($work->description) && strlen($work->description) > 0) {
                    $groupedWorks[$key]['comments'][] = $work->description;
                }
            }
        }

        foreach ($groupedWorks as $item) {
            $workData = [
                $item['hours'],
                $item['code'],
                $item['itemName'],
                implode($this->br, $item['comments']),
            ];

            if (count($item['plantTimes']) < 1) {
                $data[] = static::createRowStructure([], array_merge($workData, ['', '', '', '', '', '', '']));
            }

            foreach ($item['plantTimes'] as $plantTime) {
                $data[] = static::createRowStructure([], array_merge($workData, static::getDataPlantCostingDetails($plantTime)));
                $workData = ['', '', '', ''];
            }

            $total += $item['hours'];
        }

        foreach ($userShiftDay->approvedLeaveRequestDays as $approvedLeaveRequestDay) {
            $data = array_merge($data, static::getDataLeaveDetails($approvedLeaveRequestDay, $total));
        }

        $table->data = $data;

        $footerTable = new \stdClass;
        $footerTable->type = 'data-table';
        $footerTable->config = new \stdClass;
        $footerTable->noSeparator = true;
        $footerTable->columns = [];
        $footerTableRowConfig = [
            'config' => [
                'font-weight' => 'bold',
            ],
        ];

        if (count($data) < 1) {
            $footerTable->config->noLabel = true;
            $footerTable->columns[] = $this->createColumn('Total', 'text', 'left', count($table->columns));
            $footerTable->data = [
                static::createRowStructure($footerTableRowConfig, ['No hours recorded']),
            ];
        } else {
            $footerTable->columns[] = $this->createColumn('Total', 'float', 'left', count($table->columns), [
                'labelHorizontalAlignment' => Alignment::HORIZONTAL_LEFT,
            ]);
            $footerTable->data = [
                static::createRowStructure($footerTableRowConfig, [$total]),
            ];
        }


        $sheet->data[] = $table;
        $sheet->data[] = $footerTable;
    }

    private static function getDataPlantCostingDetails(PlantSheetDayTime $plantSheetDayTime): array
    {
        return [
            $plantSheetDayTime->formattedCode,
            $plantSheetDayTime->plantItem->name,
            $plantSheetDayTime->description,
            $plantSheetDayTime->hours,
            $plantSheetDayTime->mileage,
            $plantSheetDayTime->cartage,
            !is_null($plantSheetDayTime->mileageStart) && !is_null($plantSheetDayTime->mileageEnd)
                ? $plantSheetDayTime->mileageStart . '-' . $plantSheetDayTime->mileageEnd
                : ''
        ];
    }

    private static function getDataLeaveDetails(LeaveRequestDay $leaveRequestDay, int &$total): array
    {
        $total += $leaveRequestDay->duration->getTotalHours();

        $data = [
            $leaveRequestDay->duration->getTotalHours(),
            $leaveRequestDay->leaveRequest->formattedCode,
            $leaveRequestDay->leaveRequest->typeName,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        ];

        return [static::createRowStructure([], $data)];
    }
}
