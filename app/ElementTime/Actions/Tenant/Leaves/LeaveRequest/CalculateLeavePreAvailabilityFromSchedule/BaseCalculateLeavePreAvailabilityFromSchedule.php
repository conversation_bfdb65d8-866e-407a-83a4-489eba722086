<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\CarbonInterface;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeaveRequestPreAvailabilityErrorType\LeaveRequestPreAvailabilityErrorType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Support\Collection;

abstract class BaseCalculateLeavePreAvailabilityFromSchedule extends BaseLeavesAction
{
    /** @throws \InvalidArgumentException */
    protected function validateUserLeaveBankType(UserLeaveBankType $userLeaveBankType): bool
    {
        if ($userLeaveBankType->status != ActiveStatus::ID || !$userLeaveBankType->canTakeLeave) {
            throw new \InvalidArgumentException('Leave assignment cannot be used to take leave');
        }

        return true;
    }

    /** @throws \InvalidArgumentException */
    protected function checkUserRoleScheduleAvailability(UserRoleSchedule $userRoleSchedule, CarbonInterface $startDate, CarbonInterface|null $endDate = null): bool
    {
        if (is_null($endDate)) {
            $endDate = $startDate->copy();
        }

        if ($userRoleSchedule->startDate->gt($startDate)) {
            throw new \InvalidArgumentException('Leave is starting before the start of schedule assignment');
        }

        if (!is_null($userRoleSchedule->endDate) && $userRoleSchedule->endDate->lt($endDate)) {
            throw new \InvalidArgumentException('Leave is ending after the end of schedule assignment');
        }

        return true;
    }

    /** @return Collection<Holiday|PublicHoliday>|Holiday[]|PublicHoliday[] */
    protected function getHolidays(UserRoleSchedule $userRoleSchedule, UserLeaveBankType $userLeaveBankType, CarbonInterface $startDate, CarbonInterface $endDate): Collection|array
    {
        $holidays = Collection::make([]);

        if (!$userRoleSchedule->holidayCalculations->doesWorkOnHolidays && !$userLeaveBankType->doesIgnorePublicHolidays) {
            $holidays = $holidays
                ->concat(PublicHoliday::getByPeriod($startDate, $endDate))
                ->concat(Holiday::getByPeriod($startDate, $endDate, $userRoleSchedule));
        }

        return $holidays;
    }

    protected function calculateBalances(UserLeaveBankType $userLeaveBankType, LeaveRequest|null $replaced, StructLeaveRequestPreAvailability $ret): void
    {
        $ret->doesUseProRata = false;

        $availableDuration = TimeDuration::parseFromHours($userLeaveBankType->bank->repository->getAvailableBalance($ret->endDate, true));

        if ($userLeaveBankType->doesAllowStaffToUseProRata) {
            $proRataDuration = TimeDuration::parseFromHours($userLeaveBankType->bank->repository->getAccruedBalance($ret->endDate));
            $ret->doesUseProRata = $ret->durationAdjusted->gt($availableDuration) && $proRataDuration->gt(TimeDuration::zero());
            $availableDuration->add($proRataDuration);

            $ret->addWarningMessage('This request is using hours from the pro-rata balance');
        }

        if (!is_null($replaced) && $replaced->endDate->copy()->lte($ret->endDate)) {
            $availableDuration->add($replaced->durationAdjusted);
        }

        $ret->beforeAvailableDuration = $availableDuration;
        $ret->afterAvailableDuration = $availableDuration->copy()->deduct($ret->durationAdjusted);
    }

    protected function calculateNegativeBalancesAvailability(UserLeaveBankType $userLeaveBankType, User $actorUser, StructLeaveRequestPreAvailability $ret): void
    {

        if ($ret->afterAvailableDuration->gte(TimeDuration::zero())) {
            return;
        }

        $ret->doesShowNegativeBalance = $userLeaveBankType->doesShowNegativeBalance;

        $isOwner = $userLeaveBankType->bank->user_id == $actorUser->id;
        $availableWithMaxNegative = $userLeaveBankType->maxNegativeBalanceAllowed->copy()->add($ret->afterAvailableDuration);

        if ($availableWithMaxNegative->gte(TimeDuration::zero())) {
            if ($userLeaveBankType->doesShowNegativeBalance) {
                $ret->addWarningMessage('This will make the balance go into negative');
            }

            return;
        }

        if (!$isOwner && $actorUser->access->isPayrollOfficer(true)) {
            $ret->addWarningMessage('Caution: This will make the balance go into negative over the maximum allowed');

            return;
        }

        $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::OverMaximumAllowed, 'The leave request is over the maximum hours available at the moment');
        $ret->canBeRequested = false;
    }
}
