#!/bin/bash
#
#       _                           _  _____ ___ __  __ _____
#   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
#  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
# |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
#  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
#
# @link https://www.elementtime.com
# @copyright 2024 Adroit Creations
# Optimized Blue-Green Deployment Script
#

set -euo pipefail

# Global variables for caching and performance
declare -A LB_ARN_CACHE
declare -A TG_ARN_CACHE
declare -A HEALTH_CHECK_CACHE

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly DEPLOY_BG_SCRIPT="${SCRIPT_DIR}/deploy_blue_green_optimized.sh"
readonly MAX_PARALLEL_JOBS=10
readonly HEALTH_CHECK_INTERVAL=30  # Reduced from 60 seconds
readonly HEALTH_CHECK_TIMEOUT=5400  # 90 minutes in seconds

# Logging functions
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $*" >&2
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $*" >&2
}

log_debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] DEBUG: $*" >&2
    fi
}

# Performance monitoring
start_timer() {
    echo "$(date +%s)"
}

end_timer() {
    local start_time=$1
    local end_time=$(date +%s)
    echo $((end_time - start_time))
}

function usage() {
    cat <<EOF
Usage:
    -h|--help
    -e|--env                    [ENV: test | stage | prod]
    -s|--sleep                  [SLEEP: sleep seconds before stop old EC2, default: 0s]
    -v|--env-version            [ENV_VERSION: default: 14si]

Examples:
    ./deploy_optimized.sh -e test -s 5
    ./deploy_optimized.sh -e stage -v 14si
    ./deploy_optimized.sh -e prod
EOF
}

# Validate required tools
validate_dependencies() {
    local missing_tools=()
    
    for tool in aws jq; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install missing tools and try again"
        exit 1
    fi
}

# Validate AWS credentials
validate_aws_credentials() {
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured or invalid"
        log_error "Please configure AWS credentials and try again"
        exit 1
    fi
}

# Validate environment parameter
validate_environment() {
    local env=$1
    if [[ ! "$env" =~ ^(test|stage|prod)$ ]]; then
        log_error "Invalid environment: $env. Must be test, stage, or prod"
        usage
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    if [[ $# -lt 2 ]]; then
        usage
        exit 1
    fi

    local POSITIONAL=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -e|--env)
                env="$2"
                shift 2
                ;;
            -s|--sleep)
                sleep="$2"
                shift 2
                ;;
            -v|--env-version)
                env_version="$2"
                shift 2
                ;;
            *)
                POSITIONAL+=("$1")
                shift
                ;;
        esac
    done

    set -- "${POSITIONAL[@]}"

    # Validate required parameters
    if [[ -z "${env:-}" ]]; then
        log_error "Environment parameter (-e|--env) is required"
        usage
        exit 1
    fi

    validate_environment "$env"
}

# Initialize global variables
initialize_variables() {
    readonly TARGET="${env}"
    readonly SLEEP="${sleep:-0}"
    readonly ENV_VERSION="${env_version:-14si}"
    readonly SLEEP_BEFORE_SCALE_DOWN="$SLEEP"

    log_info "Deployment configuration:"
    log_info "  Environment: $TARGET"
    log_info "  Version: $ENV_VERSION"
    log_info "  Sleep before scale down: ${SLEEP_BEFORE_SCALE_DOWN}s"
}

# Generate resource names
generate_resource_names() {
    # Socket resources
    readonly asg_skt_b="asg-etime-${TARGET}-skt-b-${ENV_VERSION}"
    readonly asg_skt_g="asg-etime-${TARGET}-skt-g-${ENV_VERSION}"
    readonly elb_skt_b="etime-alb-${TARGET}-skt-b-${ENV_VERSION}"
    readonly elb_skt_g="etime-alb-${TARGET}-skt-g-${ENV_VERSION}"

    # Command resources
    readonly asg_cmd_b="asg-etime-${TARGET}-cmd-b-${ENV_VERSION}"
    readonly asg_cmd_g="asg-etime-${TARGET}-cmd-g-${ENV_VERSION}"
    readonly elb_cmd_b="etime-alb-${TARGET}-cmd-b-${ENV_VERSION}"
    readonly elb_cmd_g="etime-alb-${TARGET}-cmd-g-${ENV_VERSION}"

    # Normal resources
    readonly asg_normal_b="asg-etime-${TARGET}-b-${ENV_VERSION}"
    readonly asg_normal_g="asg-etime-${TARGET}-g-${ENV_VERSION}"
    readonly elb_normal_b="etime-alb-${TARGET}-b-${ENV_VERSION}"
    readonly elb_normal_g="etime-alb-${TARGET}-g-${ENV_VERSION}"
}

# Configuration constants
set_configuration() {
    # ASG counts
    readonly ASG_COUNT_SOCKET=1
    readonly ASG_COUNT_SOCKET_MIN=1
    readonly ASG_COUNT_SOCKET_MAX=1

    readonly ASG_COUNT_CMD=0
    readonly ASG_COUNT_CMD_MIN=0
    readonly ASG_COUNT_CMD_MAX=1

    readonly ASG_COUNT_TEST=1

    readonly ASG_COUNT_STAGE=1
    readonly ASG_COUNT_STAGE_MIN_DAY=1
    readonly ASG_COUNT_STAGE_MIN_NIGHT=1
    readonly ASG_COUNT_STAGE_MAX_DAY=2
    readonly ASG_COUNT_STAGE_MAX_NIGHT=1

    readonly ASG_COUNT_PROD=2
    readonly ASG_COUNT_PROD_MIN_DAY=2
    readonly ASG_COUNT_PROD_MIN_NIGHT=2
    readonly ASG_COUNT_PROD_MAX_DAY=6
    readonly ASG_COUNT_PROD_MAX_NIGHT=2

    readonly MIN_EC2_HEALTHY_COUNT=1
}

# Optimized AWS functions with caching and error handling
get_lb_arn_by_name() {
    local lb_name=$1
    local cache_key="lb_arn_${lb_name}"
    
    if [[ -n "${LB_ARN_CACHE[$cache_key]:-}" ]]; then
        echo "${LB_ARN_CACHE[$cache_key]}"
        return 0
    fi
    
    local lb_arn
    if ! lb_arn=$(aws elbv2 describe-load-balancers --names "$lb_name" \
        --query 'LoadBalancers[0].LoadBalancerArn' --output text 2>/dev/null); then
        log_error "Failed to get load balancer ARN for: $lb_name"
        return 1
    fi
    
    if [[ "$lb_arn" == "None" || -z "$lb_arn" ]]; then
        log_error "Load balancer not found: $lb_name"
        return 1
    fi
    
    LB_ARN_CACHE[$cache_key]="$lb_arn"
    echo "$lb_arn"
}

# Optimized function to get current color from traffic weight
get_current_color_from_traffic_weight() {
    local target=$1
    local type=$2
    local env_version=$3
    
    local lb_name
    if [[ "$type" == "skt" || "$type" == "cmd" ]]; then
        lb_name="alb-etime-${target}-${type}-${env_version}"
    elif [[ "$type" == "normal" ]]; then
        lb_name="alb-etime-${target}-${env_version}"
    else
        log_error "Invalid type: $type"
        return 1
    fi
    
    local lb_arn
    if ! lb_arn=$(get_lb_arn_by_name "$lb_name"); then
        return 1
    fi
    
    local current_color
    if ! current_color=$(aws elbv2 describe-listeners --load-balancer-arn "$lb_arn" \
        --query 'Listeners[?Port==`443`].DefaultActions[0].ForwardConfig.TargetGroups[?Weight==`100`].TargetGroupArn' \
        --output text 2>/dev/null | awk -F'/' '{print $(NF-1)}' | awk -F'-' '{print $(NF-1)}'); then
        log_error "Failed to get current color for $type"
        return 1
    fi
    
    echo "$current_color"
}

# Optimized function to get target group ARN with caching
get_tg_arn_by_pattern() {
    local pattern=$1
    local cache_key="tg_arn_${pattern}"
    
    if [[ -n "${TG_ARN_CACHE[$cache_key]:-}" ]]; then
        echo "${TG_ARN_CACHE[$cache_key]}"
        return 0
    fi
    
    local tg_arn
    if ! tg_arn=$(aws elbv2 describe-target-groups \
        --query "TargetGroups[?contains(TargetGroupArn, '$pattern')].TargetGroupArn" \
        --output text 2>/dev/null | head -1); then
        log_error "Failed to get target group ARN for pattern: $pattern"
        return 1
    fi
    
    if [[ -z "$tg_arn" || "$tg_arn" == "None" ]]; then
        log_error "Target group not found for pattern: $pattern"
        return 1
    fi
    
    TG_ARN_CACHE[$cache_key]="$tg_arn"
    echo "$tg_arn"
}

# Optimized health check with exponential backoff
check_target_group_health() {
    local type=$1
    local tg_arn=$2
    local start_time=$(start_timer)
    local check_interval=$HEALTH_CHECK_INTERVAL
    local max_interval=300  # 5 minutes max

    log_info "Starting health check for $type target group"

    while [[ $(($(date +%s) - start_time)) -lt $HEALTH_CHECK_TIMEOUT ]]; do
        local healthy_count
        if healthy_count=$(aws elbv2 describe-target-health --target-group-arn "$tg_arn" \
            --query 'TargetHealthDescriptions[?TargetHealth.State==`healthy`]' \
            --output json 2>/dev/null | jq length); then

            log_debug "Health check for $type: $healthy_count healthy instances"

            if [[ $healthy_count -ge $MIN_EC2_HEALTHY_COUNT ]]; then
                local elapsed=$(end_timer $start_time)
                log_info "Health check passed for $type after ${elapsed}s"
                echo "healthy"
                return 0
            fi
        else
            log_error "Failed to check health for $type target group"
        fi

        log_debug "Waiting ${check_interval}s before next health check for $type"
        sleep $check_interval

        # Exponential backoff with max limit
        check_interval=$((check_interval < max_interval ? check_interval * 2 : max_interval))
    done

    log_error "Health check timeout for $type after $HEALTH_CHECK_TIMEOUT seconds"
    echo "unhealthy"
    return 1
}

# Parallel health checks for better performance
check_all_health_status() {
    local types=("$@")
    local pids=()
    local results=()

    log_info "Starting parallel health checks for: ${types[*]}"

    # Start health checks in parallel
    for type in "${types[@]}"; do
        local tg_arn
        case $type in
            skt) tg_arn=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-skt-${next_color}-${ENV_VERSION}") ;;
            normal) tg_arn=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-${next_color}-${ENV_VERSION}") ;;
            cmd) tg_arn=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-cmd-${next_color}-${ENV_VERSION}") ;;
            *) log_error "Invalid type: $type"; return 1 ;;
        esac

        if [[ -z "$tg_arn" ]]; then
            log_error "Failed to get target group ARN for $type"
            return 1
        fi

        # Run health check in background
        (check_target_group_health "$type" "$tg_arn") &
        pids+=($!)
    done

    # Wait for all health checks to complete
    local all_healthy=true
    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        local type=${types[$i]}

        if wait $pid; then
            log_info "Health check completed successfully for $type"
        else
            log_error "Health check failed for $type"
            all_healthy=false
        fi
    done

    if $all_healthy; then
        echo "healthy"
    else
        echo "unhealthy"
    fi
}

# Optimized ASG operations
set_asg_count() {
    local asg=$1
    local min=$2
    local max=$3
    local desired=$4

    log_info "Updating ASG $asg: min=$min, max=$max, desired=$desired"

    if ! aws autoscaling update-auto-scaling-group \
        --auto-scaling-group-name "$asg" \
        --min-size "$min" \
        --max-size "$max" \
        --desired-capacity "$desired" 2>/dev/null; then
        log_error "Failed to update ASG: $asg"
        return 1
    fi

    log_info "Successfully updated ASG: $asg"
}

get_ec2_count() {
    local asg_name="$1"

    local count
    if ! count=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "$asg_name" \
        --query "AutoScalingGroups[0].DesiredCapacity" \
        --output text 2>/dev/null); then
        count=0
    fi

    # Validate count is a number
    if ! [[ $count =~ ^[0-9]+$ ]]; then
        count=0
    fi

    echo "$count"
}

# Check if either ASG is empty (for fresh deployment detection)
status_check_both_asg_counts() {
    local asg_name_a="$1"
    local asg_name_b="$2"

    local count_a count_b
    count_a=$(get_ec2_count "$asg_name_a")
    count_b=$(get_ec2_count "$asg_name_b")

    if [[ $count_a -eq 0 || $count_b -eq 0 ]]; then
        echo "True"
    else
        echo "False"
    fi
}

# Optimized listener modification with better error handling
modify_listener_traffic() {
    local listener_arn=$1
    local current_tg_arn=$2
    local next_tg_arn=$3
    local service_name=$4

    log_info "Switching traffic for $service_name"

    local action_json
    action_json=$(cat <<EOF
[{
  "Type": "forward",
  "Order": 1,
  "ForwardConfig": {
    "TargetGroups": [
      {
        "TargetGroupArn": "$current_tg_arn",
        "Weight": 0
      },
      {
        "TargetGroupArn": "$next_tg_arn",
        "Weight": 100
      }
    ]
  }
}]
EOF
)

    if ! aws elbv2 modify-listener \
        --listener-arn "$listener_arn" \
        --default-actions "$action_json" >/dev/null 2>&1; then
        log_error "Failed to switch traffic for $service_name"
        return 1
    fi

    log_info "Successfully switched traffic for $service_name"
}

# Get listener ARN with caching
get_lb_listener_443_arn() {
    local target=$1
    local type=$2
    local env_version=$3

    local cache_key="listener_${target}_${type}_${env_version}"
    if [[ -n "${LB_ARN_CACHE[$cache_key]:-}" ]]; then
        echo "${LB_ARN_CACHE[$cache_key]}"
        return 0
    fi

    local lb_name
    if [[ "$type" == "skt" || "$type" == "cmd" ]]; then
        lb_name="alb-etime-${target}-${type}-${env_version}"
    elif [[ "$type" == "normal" ]]; then
        lb_name="alb-etime-${target}-${env_version}"
    else
        log_error "Invalid type: $type"
        return 1
    fi

    local lb_arn listener_arn
    if ! lb_arn=$(get_lb_arn_by_name "$lb_name"); then
        return 1
    fi

    if ! listener_arn=$(aws elbv2 describe-listeners --load-balancer-arn "$lb_arn" \
        --query 'Listeners[?Port==`443`].ListenerArn' --output text 2>/dev/null); then
        log_error "Failed to get listener ARN for $type"
        return 1
    fi

    LB_ARN_CACHE[$cache_key]="$listener_arn"
    echo "$listener_arn"
}

# Cleanup function for failed deployments
cleanup_failed_deployment() {
    local target=$1
    local next_color=$2
    local env_version=$3

    log_info "Cleaning up failed deployment"

    local cleanup_asgs=(
        "asg-etime-${target}-skt-${next_color}-${env_version}"
        "asg-etime-${target}-${next_color}-${env_version}"
        "asg-etime-${target}-cmd-${next_color}-${env_version}"
    )

    for asg in "${cleanup_asgs[@]}"; do
        log_info "Scaling down ASG: $asg"
        set_asg_count "$asg" 0 0 0 || log_error "Failed to scale down $asg"
    done
}

# Main deployment function
main() {
    local script_start_time=$(start_timer)

    # Parse arguments and validate
    parse_arguments "$@"

    # Initialize
    validate_dependencies
    validate_aws_credentials
    initialize_variables
    generate_resource_names
    set_configuration

    log_info "Starting optimized blue-green deployment"

    # Check if deployment script exists
    if [[ ! -f "$DEPLOY_BG_SCRIPT" ]]; then
        log_error "Deploy blue-green script not found: $DEPLOY_BG_SCRIPT"
        exit 1
    fi

    # Start background deployments with job control
    log_info "Starting parallel ASG deployments"
    local bg_pids=()

    # Launch background processes
    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t skt -s "$ENV_VERSION" &
    bg_pids+=($!)

    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t normal -s "$ENV_VERSION" &
    bg_pids+=($!)

    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t cmd -s "$ENV_VERSION" &
    bg_pids+=($!)

    # Wait for background processes to complete
    log_info "Waiting for ASG deployments to complete"
    for pid in "${bg_pids[@]}"; do
        if ! wait "$pid"; then
            log_error "Background deployment process failed (PID: $pid)"
            # Kill remaining processes
            for remaining_pid in "${bg_pids[@]}"; do
                if kill -0 "$remaining_pid" 2>/dev/null; then
                    kill "$remaining_pid" 2>/dev/null || true
                fi
            done
            exit 1
        fi
    done

    log_info "All ASG deployments completed successfully"

    # Determine current and next colors
    log_info "Determining deployment colors"
    local current_color
    if ! current_color=$(get_current_color_from_traffic_weight "$TARGET" skt "$ENV_VERSION"); then
        log_error "Failed to determine current color"
        exit 1
    fi

    # Handle fresh deployment scenario
    local status_skt status_normal
    status_skt=$(status_check_both_asg_counts "$asg_skt_b" "$asg_skt_g")
    status_normal=$(status_check_both_asg_counts "$asg_normal_b" "$asg_normal_g")

    if [[ "$status_normal" == "True" && "$status_skt" == "True" ]]; then
        log_info "Fresh deployment detected - setting default colors"
        current_color="g"
    fi

    local next_color
    if [[ "$current_color" == "b" ]]; then
        next_color="g"
    elif [[ "$current_color" == "g" ]]; then
        next_color="b"
    else
        log_error "Invalid current color: $current_color"
        exit 1
    fi

    log_info "Deployment colors: current=$current_color, next=$next_color"

    # Get target group ARNs
    log_info "Retrieving target group ARNs"
    local tg_current_arn_skt tg_current_arn_normal tg_current_arn_cmd
    local tg_next_arn_skt tg_next_arn_normal tg_next_arn_cmd

    # Current target groups
    tg_current_arn_skt=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-skt-${current_color}-${ENV_VERSION}")
    tg_current_arn_normal=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-${current_color}-${ENV_VERSION}")
    tg_current_arn_cmd=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-cmd-${current_color}-${ENV_VERSION}")

    # Next target groups
    tg_next_arn_skt=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-skt-${next_color}-${ENV_VERSION}")
    tg_next_arn_normal=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-${next_color}-${ENV_VERSION}")
    tg_next_arn_cmd=$(get_tg_arn_by_pattern "etime-tg-${TARGET}-cmd-${next_color}-${ENV_VERSION}")

    # Perform health checks
    log_info "Starting health checks for new instances"
    local health_check_start=$(start_timer)

    # Check health for socket and normal (cmd typically has 0 instances)
    local health_status
    if ! health_status=$(check_all_health_status skt normal); then
        log_error "Health checks failed"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    if [[ "$health_status" != "healthy" ]]; then
        log_error "Target groups are not healthy"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    local health_check_duration=$(end_timer $health_check_start)
    log_info "Health checks completed successfully in ${health_check_duration}s"

    # Get listener ARNs
    log_info "Retrieving load balancer listener ARNs"
    local lb_listener_443_arn_skt lb_listener_443_arn_normal lb_listener_443_arn_cmd

    lb_listener_443_arn_skt=$(get_lb_listener_443_arn "$TARGET" skt "$ENV_VERSION")
    lb_listener_443_arn_normal=$(get_lb_listener_443_arn "$TARGET" normal "$ENV_VERSION")
    lb_listener_443_arn_cmd=$(get_lb_listener_443_arn "$TARGET" cmd "$ENV_VERSION")

    # Switch traffic
    log_info "Switching traffic from $current_color to $next_color"

    if ! modify_listener_traffic "$lb_listener_443_arn_skt" "$tg_current_arn_skt" "$tg_next_arn_skt" "Socket"; then
        log_error "Failed to switch Socket traffic"
        exit 1
    fi

    if ! modify_listener_traffic "$lb_listener_443_arn_normal" "$tg_current_arn_normal" "$tg_next_arn_normal" "Normal"; then
        log_error "Failed to switch Normal traffic"
        exit 1
    fi

    if ! modify_listener_traffic "$lb_listener_443_arn_cmd" "$tg_current_arn_cmd" "$tg_next_arn_cmd" "Command"; then
        log_error "Failed to switch Command traffic"
        exit 1
    fi

    log_info "Traffic switching completed successfully"

    # Wait before scaling down old instances
    if [[ $SLEEP_BEFORE_SCALE_DOWN -gt 0 ]]; then
        log_info "Waiting ${SLEEP_BEFORE_SCALE_DOWN}s before scaling down old instances"
        sleep "$SLEEP_BEFORE_SCALE_DOWN"
    fi

    # Scale down old ASGs
    log_info "Scaling down old ASGs (color: $current_color)"
    local old_asgs=(
        "asg-etime-${TARGET}-skt-${current_color}-${ENV_VERSION}"
        "asg-etime-${TARGET}-${current_color}-${ENV_VERSION}"
        "asg-etime-${TARGET}-cmd-${current_color}-${ENV_VERSION}"
    )

    for asg in "${old_asgs[@]}"; do
        log_info "Scaling down ASG: $asg"
        if ! set_asg_count "$asg" 0 0 0; then
            log_error "Failed to scale down $asg"
        fi
    done

    local total_duration=$(end_timer $script_start_time)
    log_info "Deployment completed successfully in ${total_duration}s"
    log_info "New active color: $next_color"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
