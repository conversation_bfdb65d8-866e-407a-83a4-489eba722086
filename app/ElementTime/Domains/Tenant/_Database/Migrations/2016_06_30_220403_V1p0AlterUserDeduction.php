<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('User', function (Blueprint $table) {
            // Student Loan Repayment
            $table->longText('studentLoan')->nullable(); // JSON format:
            //  [
            //      {
            //          "amount": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "amount": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]

            // Court Fines
            $table->longText('courtFines')->nullable(); // JSON format:
            //  [
            //      {
            //          "amount": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "amount": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]

            // Child Support
            $table->longText('childSupport')->nullable(); // JSON format:
            //  [
            //      {
            //          "amount": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "amount": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]

            // Medicare Adjustment
            $table->longText('medicareAdjustment')->nullable(); // JSON format:
            //  [
            //      {
            //          "amount": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "amount": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]
        });

        Schema::create('User_Deduction', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('deduction_id')->unsigned()->nullable()->default(null);

            $table->float('amount')->nullable()->default(null);
            $table->char('rateType', 1)->nullable()->default('P'); // Values: (P)ercentage | (F)ixed
            $table->date('startDate')->nullable()->default(null);
            $table->date('endDate')->nullable()->default(null);

            $table->foreign('user_id', 'user_deduction_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('deduction_id', 'user_deduction_d_fn')->references('id')->on('Deduction')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('User', function (Blueprint $table) {
            $table->dropColumn([
                'studentLoan',
                'courtFines',
                'childSupport',
                'medicareAdjustment',
            ]);
        });
        Schema::drop('User_Deduction');
    }
};
