<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\User;

use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'UserTeenyTinyWithMasterRoleResource',
    title: 'User Teeny TinyResource with role',
    required: ['id', 'externalId', 'fullName', 'photoUrl', 'role'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'externalId', type: 'string', example: '2'),
        new OA\Property(property: 'fullName', type: 'string', example: 'Yoda Jedi master'),
        new OA\Property(property: 'photoUrl', type: 'string', example: 'https:yoda.so', nullable: true),
        new OA\Property(property: 'role', type: 'string', example: 'Jedi master'),
    ],
)]
class UserTeenyTinyWithMasterRoleResource extends Resource
{
    public static $relations = [
        'userRoles.userRoleMasters',
        'userRoles.role',
    ];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var User $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'externalId' => $r->externalId,
            'fullName' => $r->fullName,
            'photoUrl' => $r->photoUrl,
            'role' => $r->masterRole,
        ];
    }
}
