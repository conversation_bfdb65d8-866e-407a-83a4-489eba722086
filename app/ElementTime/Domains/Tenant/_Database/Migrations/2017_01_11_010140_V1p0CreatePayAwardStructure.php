<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            // General Settings
            $table->float('fteHoursPerWeek')->default('40');

            // Pay Rate Settings
            $table->boolean('allowsIndividualPayRates')->default(true);
        });

        Schema::create('PayBand', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->boolean('allowAutomaticIncrease');

            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();
            $table->text('description');

            $table->char('status', 1);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('project_id', 'abt_proj_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('projectActivity_id', 'abt_projact_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('PayBand_PayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('payBand_id')->unsigned();
            $table->bigInteger('payGroupType_id')->unsigned();

            $table->primary(['payBand_id', 'payGroupType_id']);
            $table->foreign('payBand_id', 'pb_pgt_pb_fn')->references('id')->on('PayBand')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payGroupType_id', 'pb_pgt_pgt_fn')->references('id')->on('PayGroupType')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('PayType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->bigInteger('payBand_id')->unsigned()->nullable();
            $table->string('subGroup');
            $table->text('description');

            $table->char('rateClass', 1);
            $table->char('rateType', 1);
            $table->decimal('amount', 16, 10)->nullable();
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();

            $table->char('status', 1);
            $table->integer('order');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payBand_id', 'pt_pb_fn')->references('id')->on('PayBand')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('incrementRate_id', 'apt_ir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserRole', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('role_id')->unsigned();

            $table->date('startDate');
            $table->date('endDate')->default('0000-00-00');
            $table->boolean('isMain')->default(false);

            $table->unique(['user_id', 'role_id', 'endDate'], 'userrole_active_u');

            $table->foreign('user_id', 'userrole_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('role_id', 'userrole_role_fn')->references('id')->on('Role')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::create('UserPayType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('payType_id')->unsigned()->nullable();
            $table->bigInteger('userRole_id')->unsigned()->nullable();

            $table->date('startDate');
            $table->date('endDate')->default('0000-00-00');
            $table->text('comments')->nullable();

            $table->boolean('autoAssigned')->default(false);
            $table->bigInteger('firstUserPayType_id')->unsigned()->nullable();

            $table->decimal('customAmount', 16, 10)->nullable();
            $table->char('customRateType', 1)->nullable();
            $table->bigInteger('customIncrementRate_id')->unsigned()->nullable();

            $table->unique(['user_id', 'userRole_id', 'endDate'], 'upt_active_u');

            $table->foreign('user_id', 'upt_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payType_id', 'upt_pt_fn')->references('id')->on('PayType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userRole_id', 'upt_ur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('firstUserPayType_id', 'upt_fupt_fn')->references('id')->on('UserPayType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('customIncrementRate_id', 'upt_cir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserShift', function (Blueprint $table) {
            $table->bigInteger('userRole_id')->unsigned()->nullable();
            $table->foreign('userRole_id', 'user_shift_ur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserShift', 'userRole_id')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropForeign('user_shift_ur_fn');
                $table->dropColumn('userRole_id');
            });
        }
        Schema::dropIfExists('UserPayType');
        Schema::dropIfExists('UserRole');
        Schema::dropIfExists('PayType');
        Schema::dropIfExists('PayBand_PayGroupType');
        Schema::dropIfExists('PayBand');
        if (Schema::hasColumn('Settings', 'fteHoursPerWeek')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('fteHoursPerWeek');
            });
        }
        if (Schema::hasColumn('Settings', 'allowsIndividualPayRates')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsIndividualPayRates');
            });
        }
    }
};
