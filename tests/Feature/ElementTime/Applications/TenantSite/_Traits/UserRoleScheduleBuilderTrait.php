<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Feature\ElementTime\Applications\TenantSite\_Traits;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\Core\Exceptions\NotImplementedException;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\TimeType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\ExcessTimeGroup;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayType;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Support\EmployeeTypeStatusTypes\FullTimeType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Faker\Provider\Lorem;

trait UserRoleScheduleBuilderTrait
{
    use BasePayloadBuilderTrait;

    public function getUserRoleSchedule(int $user_id = null): UserRoleSchedule|null
    {
        $minDate = TenantSystemSettings::getEarliestOpenPayRun()?->startDate;

        $constraints = [
            ['Role.status', '=', ActiveStatus::ID],
            ...UserRole::availabilityConstraints()->notPast($minDate)->build()
        ];

        if (!is_null($user_id)) {
            $constraints[] = ['User.id', '=', $user_id];
        }

        return UserRoleSchedule::q()
            ->constraints($constraints)
            ->order('RAND()')
            ->first();
    }

    public function buildNewUserRoleServicePayload(User $user, \stdClass|null $settings = null): array
    {
        $settings = $settings ?? new \stdClass;
        $role_id = $settings->role_id ?? null;
        $startDate = $settings->startDate ?? TenantSystemSettings::getEarliestOpenPayRun()->startDate;

        if (is_null($role_id)) {
            $role = Role::q()
                ->constraints([
                    ['NOT IN', 'Role.id', $user->userRoles->pluck('role_id')->toArray()],
                ])
                ->order('RAND()')
                ->first();

            $role_id = $role->id;
        }

        if (!isset($settings->roleManagerData)) {
            $managerConstraints = [['User.id', '<>', $user->id]];
            $manager = User::q()
                ->constraints($managerConstraints)
                ->onlyActive()
                ->order('RAND()')
                ->many()
                ->first();

            $settings->roleManagerData = ['manager_id' => $manager->id];
        }

        return [
            'user_id' => $user->id,
            'role_id' => $role_id,
            'isMaster' => false,
            'startDate' => $startDate->toDateString(),
            'doesOverrideExternalId' => false,
            'externalId' => Lorem::randomNumber(5),
            'contractedHours' => 80,
            'employeeTypeStatusType' => FullTimeType::ID,
            'costing' => $settings->costingData ?? [
                'doesAllowNonStandardPlant' => false,
                'doesAllowUserToUpdateWorkDetails' => false,
            ],
            'department' => $settings->departmentData ?? [
                'department_id' => Department::q()->onlyActive()->many()->random()->id,
                'isManager' => false,
                'comments' => 'lorem',
            ],
            'master_project' => $settings->masterProjectData ?? [
                'project_id' => Project::q()->onlyActive()->many()->random()->id,
                'isMaster' => false,
            ],
            'pay_type' => $settings->payTypesData ?? [
                'payType_id' => PayType::q()->onlyActive()->many()->random()->id,
                'overtimeCustomAmount' => null,
                'overtimeCustomRateType' => 'Y',
            ],
            'role_manager' => $settings->roleManagerData,
            'time_types' => $settings->timeTypesData ?? [
                [
                    'timeType_id' => TimeType::q()->onlyActive()->many()->random()->id,
                    'excessTimeGroup_id' => ExcessTimeGroup::q()->onlyActive()->many()->random()->id,
                    'isActive' => true,
                    'isMaster' => true,
                ]
            ],
        ];
    }

    public function getZeroBasedScheduleSettings(): array
    {
        return [
            'zeroBased' => [
                'dailyMaxDuration' => null,
                'doesHaveDailyMaxDuration' => false,
                'doesHaveFixedLocation' => false,
                'doesHaveMaxDuration' => false,
                'maxDuration' => null,
                'scheduleLocation_id' => null,
            ],
        ];
    }

    public function buildNewUserRoleSchedulePayload(UserRole $userRole, UserRoleScheduleType $type, CarbonInterface|null $startDate = null, array|null $scheduleSettings = null): array
    {
        if (is_null($scheduleSettings)) {
            $scheduleSettings = match($type) {
                UserRoleScheduleType::ZeroBased => $this->getZeroBasedScheduleSettings(),
                default => throw new NotImplementedException(),
            };
        }

        return [
            'userRole_id' => $userRole->id,
            'startDate' => Carbon::make($startDate ?? TenantSystemSettings::getEarliestOpenPayRun()->startDate)->toDateString(),
            'type' => $type->id(),
            ...$scheduleSettings,
        ];
    }
}
