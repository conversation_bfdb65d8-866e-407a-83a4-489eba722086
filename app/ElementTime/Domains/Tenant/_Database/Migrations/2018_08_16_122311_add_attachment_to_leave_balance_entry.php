<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserLeaveTypeEntry

        Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'ulte_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');
        });

        //endregion Table: UserLeaveTypeEntry

        //region Table: UserToilTypeEntry

        Schema::table('UserToilTypeEntry', function (Blueprint $table) {
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'utte_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');
        });

        //endregion Table: UserToilTypeEntry

        //region Table: UserRosteredTimeOffTypeEntry

        Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'urtote_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');
        });

        //endregion Table: UserRosteredTimeOffTypeEntry
    }

    public function down(): void
    {
        //region Table: UserRosteredTimeOffTypeEntry

        try {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropForeign('urtote_af_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserRosteredTimeOffTypeEntry', 'attachmentFile_id')) {
            Schema::table('UserRosteredTimeOffTypeEntry', function (Blueprint $table) {
                $table->dropColumn('attachmentFile_id');
            });
        }

        //endregion Table: UserRosteredTimeOffTypeEntry

        //region Table: UserToilTypeEntry

        try {
            Schema::table('UserToilTypeEntry', function (Blueprint $table) {
                $table->dropForeign('utte_af_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserToilTypeEntry', 'attachmentFile_id')) {
            Schema::table('UserToilTypeEntry', function (Blueprint $table) {
                $table->dropColumn('attachmentFile_id');
            });
        }

        //endregion Table: UserToilTypeEntry

        //region Table: UserLeaveTypeEntry

        try {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropForeign('ulte_af_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserLeaveTypeEntry', 'attachmentFile_id')) {
            Schema::table('UserLeaveTypeEntry', function (Blueprint $table) {
                $table->dropColumn('attachmentFile_id');
            });
        }

        //endregion Table: UserLeaveTypeEntry
    }
};
