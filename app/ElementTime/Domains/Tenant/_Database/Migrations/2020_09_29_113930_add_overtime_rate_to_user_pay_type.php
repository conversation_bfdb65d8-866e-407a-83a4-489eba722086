<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserPayType
        Schema::table('UserPayType', function (Blueprint $table) {
            $table->boolean('hasOvertimePayType')->default(false);
            $table->unsignedBigInteger('overtimePayType_id')->nullable();
            $table->foreign('overtimePayType_id', 'upt_opt_fn')->references('id')->on('PayType')->onUpdate('cascade')->onDelete('set null');
            $table->decimal('overtimeCustomAmount', 16, 10)->nullable();
            $table->char('overtimeCustomRateType', 1)->nullable();
        });
        //endregion Table: UserPayType

        //region Table: UserShiftDay
        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->decimal('hourlyRateOvertime', 16, 10)->nullable();
            $table->decimal('glidePeriodHourlyRateOvertime', 16, 10)->nullable();
        });
        //endregion Table: UserShiftDay
    }

    public function down(): void
    {
        //region Table: UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'hourlyRateOvertime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('hourlyRateOvertime');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'glidePeriodHourlyRateOvertime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHourlyRateOvertime');
            });
        }
        //endregion Table: UserShiftDay

        //region Table: UserPayType
        if (Schema::hasColumn('UserPayType', 'hasOvertimePayType')) {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropColumn('hasOvertimePayType');
            });
        }

        try {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropForeign('upt_opt_fn');
            });
        } catch (Throwable) {
        }

        try {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropIndex('upt_opt_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('UserPayType', 'overtimePayType_id')) {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropColumn('overtimePayType_id');
            });
        }

        if (Schema::hasColumn('UserPayType', 'overtimeCustomAmount')) {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropColumn('overtimeCustomAmount');
            });
        }

        if (Schema::hasColumn('UserPayType', 'overtimeCustomRateType')) {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropColumn('overtimeCustomRateType');
            });
        }
        //endregion Table: UserPayType
    }
};
