<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('allowToDecideOvertimeOrToil')->default(true);
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->boolean('allowToDecideOvertimeOrToil')->default(true);

            $table->decimal('fTotalWorked', 16, 10)->nullable();
            $table->decimal('fTotalAllowance', 16, 10)->nullable();
            $table->decimal('fTotalHoursLess', 16, 10)->nullable();
            $table->decimal('fTotalOvertime', 16, 10)->nullable();
            $table->decimal('fTotalLeaveEarnt', 16, 10)->nullable();
            $table->decimal('fTotalLeaveUsed', 16, 10)->nullable();
            $table->decimal('fTotalToilUsed', 16, 10)->nullable();
            $table->decimal('fTotalRosteredTimeOffEarnt', 16, 10)->nullable();
            $table->decimal('fTotalRosteredTimeOffUsed', 16, 10)->nullable();
            $table->decimal('fTotal', 16, 10)->nullable();
        });

        DB::table('User')
          ->update(
              [
                  'hasTOIL' => 1,
                  'hasOvertime' => 0,
              ]
          );
    }

    public function down(): void
    {
        if (Schema::hasColumn('Settings', 'allowToDecideOvertimeOrToil')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowToDecideOvertimeOrToil');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotal')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotal');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalToilUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalToilUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalOvertime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalOvertime');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalHoursLess')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalHoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalWorked')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalWorked');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'allowToDecideOvertimeOrToil')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('allowToDecideOvertimeOrToil');
            });
        }
    }
};
