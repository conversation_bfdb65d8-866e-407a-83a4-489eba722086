<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('hoursExcessTimeUnpaid', 16, 10)->default(0)->after('valueExcessTimePaid');
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('hoursExcessTimeUnpaid', 16, 10)->default(0)->after('valueExcessTimePaid');
            $table->decimal('glidePeriodHoursExcessTimeUnpaid', 16, 10)->default(0)->after('glidePeriodValueExcessTimePaid');
        });

        Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
            $table->decimal('totalHoursUnpaid', 16, 10)->default(0)->after('totalHoursAccrued'); // calculated - from rules
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'totalHoursUnpaid')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('totalHoursUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeUnpaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeUnpaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUnpaid');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeUnpaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUnpaid');
            });
        }
    }
};
