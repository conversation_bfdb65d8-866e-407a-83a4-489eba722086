div
  .row
    .col-xs-12.col-md-6.mb-lg
      elt-toggle(
        ng-model='$ctrl.form.data.isFullDay'
        ng-change='$ctrl.form.specific.$events.onChangeAnyValue()'
        ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
        elt-name='isFullDay'
        elt-label='Applying for full day(s) leave'
        elt-help-text='modals.leave-request|fields.isFullDay'
        elt-line='false'
        elt-change-tt=''
        elt-group-class='m0'
        elt-layout='checkbox-box'
        elt-selected-box-color='green'
      )

  div(ng-if='!!$ctrl.form.data.isFullDay')
    .row
      .col-xs-12.col-md-6
        elt-form-date-field(
          ng-model='$ctrl.form.data.startDate'
          ng-required='true'
          ng-change='$ctrl.form.specific.$events.onChangeFullDayStartDate()'
          ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
          elt-label='Start date'
          elt-name='startDate'
          elt-date-picker-options='$ctrl.form.specific.options.startDate.datePicker'
          elt-form='$ctrl.form.form'
          elt-validation-service='$ctrl.form._v'
          elt-validations='[{validation: "required", message: "form.validate.FIELD_IS_REQUIRED"}]'
        )

      .col-xs-12.col-md-6
        elt-form-date-field(
          ng-model='$ctrl.form.data.endDate'
          ng-required='true'
          ng-change='$ctrl.form.specific.$events.onChangeFullDayEndDate()'
          ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
          elt-label='End date'
          elt-name='endDate'
          elt-date-picker-options='$ctrl.form.specific.options.endDate.datePicker'
          elt-form='$ctrl.form.form'
          elt-validation-service='$ctrl.form._v'
          elt-validations='[{validation: "required", message: "form.validate.FIELD_IS_REQUIRED"}]'
        )

  div(ng-if='!$ctrl.form.data.isFullDay')
    .row
      .col-xs-12.col-md-6
        elt-form-date-field(
          ng-model='$ctrl.form.data.startDate'
          ng-required='true'
          ng-change='$ctrl.form.specific.$events.onChangePartDayDate()'
          ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
          elt-label='Leave date'
          elt-name='startDate'
          elt-date-picker-options='$ctrl.form.specific.options.startDate.datePicker'
          elt-form='$ctrl.form.form'
          elt-validation-service='$ctrl.form._v'
          elt-validations='[{validation: "required", message: "form.validate.FIELD_IS_REQUIRED"}]'
        )

      .col-xs-12.col-md-6
        .row
          label
            |
          elt-toggle(
            ng-model='$ctrl.form.data.isApplyingByDuration'
            ng-change='$ctrl.form.specific.$events.onChangeAnyValue()'
            ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
            elt-name='isApplyingByDuration'
            elt-label='Apply by duration'
            elt-help-text='modals.leave-request|fields.isApplyingByDuration'
            elt-line='false'
            elt-change-tt=''
            elt-group-class='m0'
          )

    .row
      .col-xs-12.col-md-6
        .form-group(elt-help-text='modals.leave-request|fields.startTime')
          label.label-control(for='Leave_startTime') Start time
          div.input-group(
            ng-model="$ctrl.form.data.startTime"
            ng-required='true'
            ng-change='$ctrl.form.specific.$events.onChangeAnyValue()'
            ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
            id='Leave_startTime'
            name='startTime'
            timepicker-popup
            is-meridian='settings.system.settings.meridian'
            is-date='true'
            input-class='pl pr'
          )

      .col-xs-12.col-md-6(ng-if='!$ctrl.form.data.isApplyingByDuration')
        .form-group(elt-help-text='modals.leave-request|fields.endTime')
          label.label-control(for='Leave_endTime') End time
          div.input-group(
            ng-model="$ctrl.form.data.endTime"
            ng-required='true'
            ng-change='$ctrl.form.specific.$events.onChangeAnyValue()'
            ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
            id='Leave_endTime'
            name='endTime'
            timepicker-popup
            is-meridian='settings.system.settings.meridian'
            is-date='true'
            input-class='pl pr'
          )

      .col-xs-12.col-md-6(ng-if='!!$ctrl.form.data.isApplyingByDuration')
        .form-group
          label(for='Leave_duration') Duration
          input.form-control.pl.pr(
            ng-model="$ctrl.form.data.duration"
            ng-required='true'
            ng-change='$ctrl.form.specific.$events.onChangeAnyValue()'
            ng-disabled='!!$ctrl.form.calculation.isRunning || !!$ctrl.form.isSubmitting'
            type='text'
            id='Leave_duration'
            name='duration'
            input-mask
            input-options='{"name": "number", "min": 0, "max": 24, "allowMinus": false}'
          )
