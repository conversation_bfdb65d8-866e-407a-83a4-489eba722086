<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetHoliday;

use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetHoliday;

class SyncHolidaysToTimeSheet extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheet $timeSheet,
    ): void
    {
        $holidayIds = [];
        $hasChanges = false;

        $scheduledPeriods = $timeSheet->scheduledPayRunPeriods;

        foreach ($scheduledPeriods as $scheduledPeriod) {
            foreach ($scheduledPeriod->scheduledHolidays as $scheduledHoliday) {
                $timeSheetHoliday = $timeSheet->timeSheetHolidays->where('scheduledHoliday_id', '=', $scheduledHoliday->id)->first();

                if (is_null($timeSheetHoliday)) {
                    $timeSheetHoliday = new TimeSheetHoliday;
                    $timeSheetHoliday->timeSheetDay_id = $scheduledHoliday->scheduledPayRunPeriodDay->timeSheetDay_id;
                    $timeSheetHoliday->timeSheetPeriod_id = $scheduledPeriod->timeSheetPeriod->id;
                    $timeSheetHoliday->scheduledHoliday_id = $scheduledHoliday->id;
                }

                $timeSheetHoliday->duration = $scheduledHoliday->duration;
                $timeSheetHoliday->durationAdjusted = $scheduledHoliday->durationAdjusted;
                $timeSheetHoliday->start = $scheduledHoliday->start?->copy();
                $timeSheetHoliday->end = $scheduledHoliday->end?->copy();

                if ($timeSheetHoliday->isDirty()) {
                    $timeSheetHoliday->saveOrFail();
                    $hasChanges = true;
                }

                $holidayIds[] = $timeSheetHoliday->id;
            }
        }

        $otherHolidays = $timeSheet->timeSheetHolidays->whereNotIn('id', $holidayIds);

        foreach ($otherHolidays as $otherHoliday) {
            $otherHoliday->forceDelete();
            $hasChanges = true;
        }

        if ($hasChanges) {
            $timeSheet->load([
                'timeSheetHolidays',
                'timeSheetDays.timeSheetHolidays',
                'timeSheetPeriods.timeSheetHolidays',
            ]);
        }
    }
}
