<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Support\Domains\Type\StatusTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table PlantClass

        Schema::create('PlantClass', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('externalId')->nullable();
            $table->string('name')->unique('pc_name_uq');
            $table->string('code')->nullable();
            $table->text('description')->nullable();
            $table->char('status', 1)->default(StatusTypes\ActiveStatus::ID);

            $table->timestamps();
        });

        //endregion Table PlantClass

        //region Table PlantItem
        Schema::create('PlantItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('plantClass_id');
            $table->foreign('plantClass_id', 'pit_pcl_fn')
                ->references('id')->on('PlantClass')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('externalId')->unique('pi_externalId_uq');
            $table->string('name');
            $table->text('description');
            $table->string('assetNumber')->nullable();

            $table->unsignedBigInteger('ownerUser_id');
            $table->foreign('ownerUser_id', 'pit_usr_ownr_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('registration')->nullable();
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->string('relationshipType');

            $table->boolean('doesHaveHours')->default(true);
            $table->boolean('isHoursRequired')->default(true);

            $table->boolean('doesHaveCartage')->default(false);
            $table->string('cartageLabel')->nullable();
            $table->string('cartageUnit')->nullable();
            $table->decimal('cartageCost', 16, 10)->nullable();
            $table->boolean('isCartageRequired')->default(false);

            $table->boolean('doesHaveMileage')->default(false);
            $table->string('mileageLabel')->nullable();
            $table->string('mileageUnit')->nullable();
            $table->string('mileageRecordingType')->nullable();
            $table->decimal('mileageCost', 16, 10)->nullable();
            $table->boolean('isMileageRequired')->default(false);

            $table->boolean('doesHaveCostRate')->default(false);
            $table->string('costRateLabel')->nullable();
            $table->string('costRateUnit')->nullable();
            $table->decimal('costRateCost', 16, 10)->nullable();
            $table->boolean('isCostRateRequired')->default(false);

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
            $table->string('workOrderProjectCode')->nullable();
            $table->string('workOrderCostCode')->nullable();
            $table->string('workOrderActivityCode')->nullable();

            $table->string('titleMask')->default('[[name]] - [[number]]');
            $table->char('status', 1)->default(StatusTypes\ActiveStatus::ID);
            $table->timestamps();
        });
        //endregion Table PlantItem

        //region Table PlantItemParenting
        Schema::create('PlantItemParenting', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('parent_id');
            $table->foreign('parent_id', 'pitp_pit_prnt_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('child_id');
            $table->foreign('child_id', 'pitp_pit_chld_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('isLocked')->default(false);
            $table->timestamps();
        });
        //endregion Table PlantItemParenting

        //region Table UserRolePlantItem
        Schema::create('UserRolePlantItem', function (Blueprint $table) {
            $table->engine = 'innoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urpi_ur_fn')
                ->references('id')->on('UserRole')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('plantItem_id');
            $table->foreign('plantItem_id', 'urpi_pi_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->date('startDate');
            $table->date('endDate')->nullable();
            $table->string('comments')->nullable();

            $table->timestamps();
        });
        //endregion Table UserRolePlantItem

        //region Table PlantSheetDayTime
        Schema::create('PlantSheetDayTime', function (Blueprint $table) {
            $table->engine = 'innoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('userRolePlantItem_id');
            $table->foreign('userRolePlantItem_id', 'psdt_urpi_fn')
                ->references('id')->on('UserRolePlantItem')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('timeSheetDayTime_id');
            $table->foreign('timeSheetDayTime_id', 'psdt_tsdt_fn')
                ->references('id')->on('TimeSheetDayTime')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('timeSheetDayTimeWork_id')->nullable();
            $table->foreign('timeSheetDayTimeWork_id', 'psdt_tsdtw_fn')
                ->references('id')->on('TimeSheetDayTimeWork')
                ->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('parentPlantSheetDayTime_id')->nullable();
            $table->foreign('parentPlantSheetDayTime_id', 'psdt_psdt_fn')
                ->references('id')->on('PlantSheetDayTime')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->integer('minutes')->nullable();
            $table->decimal('mileage', 16, 10)->nullable();
            $table->decimal('mileageStart', 16, 10)->nullable();
            $table->decimal('mileageEnd', 16, 10)->nullable();
            $table->decimal('cartage', 16, 10)->nullable();
            $table->decimal('costRate', 16, 10)->nullable();
            $table->text('description')->nullable();

            $table->timestamps();
        });
        //endregion Table PlantSheetDayTime
    }

    public function down(): void
    {
        Schema::dropIfExists('PlantSheetDayTime');
        Schema::dropIfExists('UserRolePlantItem');
        Schema::dropIfExists('PlantItemParenting');
        Schema::dropIfExists('PlantItem');
        Schema::dropIfExists('PlantClass');
    }
};
