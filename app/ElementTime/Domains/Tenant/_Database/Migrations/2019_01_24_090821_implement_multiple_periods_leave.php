<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Leave', function (Blueprint $table) {
            $table->unsignedBigInteger('originalOfPartial_id')->nullable()->after('originalLeave_id');
            $table->foreign('originalOfPartial_id', 'leave_oopl_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('ExcessTimeLeave', function (Blueprint $table) {
            $table->unsignedBigInteger('originalOfPartial_id')->nullable()->after('originalExcessTimeLeave_id');
            $table->foreign('originalOfPartial_id', 'etl_oopl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->unsignedBigInteger('originalOfPartial_id')->nullable()->after('originalRosteredTimeOff_id');
            $table->foreign('originalOfPartial_id', 'rto_oopl_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropForeign('rto_oopl_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('RosteredTimeOff', 'originalOfPartial_id')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('originalOfPartial_id');
            });
        }

        try {
            Schema::table('ExcessTimeLeave', function (Blueprint $table) {
                $table->dropForeign('etl_oopl_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('ExcessTimeLeave', 'originalOfPartial_id')) {
            Schema::table('ExcessTimeLeave', function (Blueprint $table) {
                $table->dropColumn('originalOfPartial_id');
            });
        }

        try {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropForeign('leave_oopl_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('Leave', 'originalOfPartial_id')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('originalOfPartial_id');
            });
        }
    }
};
