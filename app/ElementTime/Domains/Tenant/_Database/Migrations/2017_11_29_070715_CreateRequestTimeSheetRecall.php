<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->dateTime('recallRequestDateTime')->nullable();
            $table->text('recallRequestReason')->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('TimeSheet', 'recallRequestReason')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('recallRequestReason');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'recallRequestDateTime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('recallRequestDateTime');
            });
        }
    }
};
