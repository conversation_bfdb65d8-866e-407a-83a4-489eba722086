<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\CarbonInterface;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays\GetScheduledDaysZeroBased;
use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeaveRequestPreAvailabilityErrorType\LeaveRequestPreAvailabilityErrorType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeType\ModelActionTypeType;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeValidationFlag\ModelActionTypeValidationFlagLeave;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

class CalculateLeavePreAvailabilityFromScheduleZeroBased extends BaseCalculateLeavePreAvailabilityFromSchedule
{
    /**
     * @throws \Throwable
     */
    public function handle(
        UserLeaveBankType $userLeaveBankType,
        UserRoleSchedule $userRoleSchedule,
        CarbonInterface $startDate,
        TimeDuration $duration,
        LeaveRequest|null $replaced = null,
        User|null $actorUser = null,
    ): StructLeaveRequestPreAvailability
    {
        $this->validateUserLeaveBankType($userLeaveBankType);
        $this->checkUserRoleScheduleAvailability($userRoleSchedule, $startDate);

        if (is_null($actorUser)) {
            $actorUser = $userLeaveBankType->bank->user;
        }

        $payRun = TenantSystemSettings::getPayRunByDate($startDate);

        $ret = StructLeaveRequestPreAvailability::make();

        $ret->scheduleType = $userRoleSchedule->type;
        $ret->canBeRequested = true;
        $ret->warningMessages = [];

        $ret->userLeaveBankType_id = $userLeaveBankType->id;
        $ret->userRoleSchedule_id = $userRoleSchedule->id;
        $ret->startDate = $payRun->startDate->copy();
        $ret->endDate = $payRun->endDate->copy();
        $ret->duration = $duration;
        $ret->durationAdjusted = $duration;

        $scheduledDaysData = GetScheduledDaysZeroBased::make()->handle($userRoleSchedule, $payRun->startDate);
        $maxOnPeriod = $scheduledDaysData[0]->maxPeriodDuration?->copy() ?? TimeDuration::max();
        $doesExceedMaximumDuration = $this->doesExceedMaximumDuration($ret, $userRoleSchedule, $maxOnPeriod, $replaced);

        if ($doesExceedMaximumDuration) {
            $ret->canBeRequested = false;
            $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::OverMaximumAllowed, 'Request is over the maximum hours allowed in the payrun period');

            return $ret;
        }

        $this->calculateBalances($userLeaveBankType, $replaced, $ret);
        $this->calculateNegativeBalancesAvailability($userLeaveBankType, $actorUser, $ret);

        $model = new LeaveRequest;
        $model->scheduleType = $userRoleSchedule->type;
        $model->actor_id = $actorUser->id;
        $model->userLeaveBankType_id = $userLeaveBankType->id;
        $model->userRoleSchedule_id = $userRoleSchedule->id;
        $model->startDate = $ret->startDate->copy();
        $model->endDate = $ret->endDate->copy();
        $model->duration = $ret->duration;
        $model->durationAdjusted = $ret->durationAdjusted;

        $submitModelActionFlags = ModelActionTypeType::Leave->getValidationFlags( ModelActionTypeValidationFlagLeave::Submit, $model, [$userLeaveBankType->bank->user]);
        $approveModelActionFlags = ModelActionTypeType::Leave->getValidationFlags( ModelActionTypeValidationFlagLeave::Approve, $model, [$userLeaveBankType->bank->user]);

        $ret->doesRequireAttachmentsOnSubmit = $submitModelActionFlags->isAttachmentRequired;
        $ret->doesRequireAttachmentsOnApprove = $approveModelActionFlags->isAttachmentRequired;
        $ret->doesRequireCommentsOnSubmit = $submitModelActionFlags->isCommentRequired;
        $ret->doesRequireCommentsOnApprove = $approveModelActionFlags->isCommentRequired;

        return $ret;
    }

    protected function doesExceedMaximumDuration(StructLeaveRequestPreAvailability $availability, UserRoleSchedule $userRoleSchedule, TimeDuration|null $maxOnPeriod, LeaveRequest|null $replaced = null): bool
    {
        if (is_null($maxOnPeriod)) {
            return false;
        }

        $constraints = [
            ['LeaveRequest.userRoleSchedule_id', '=', $userRoleSchedule->id],
            ['LeaveRequest.startDate', '=', $availability->startDate->toDateString()],
            ['LeaveRequest.endDate', '=', $availability->endDate->toDateString()],
            ['IN', 'Workflow.status', [
                NewType::ID,
                SubmittedType::ID,
                PartiallyApprovedType::ID,
                ApprovedType::ID,
            ]],
        ];

        if (!is_null($replaced)) {
            $constraints[] = ['LeaveRequest.id', '<>', $replaced->id];
        }

        $leaveRequests = LeaveRequest::q()
            ->constraints($constraints)
            ->many();

        /** @var TimeDuration $totalDuration */
        $totalDuration = $leaveRequests->reduce(
            fn (TimeDuration $sum, LeaveRequest $leaveRequest) => $sum->add($leaveRequest->duration),
            $availability->duration->copy(),
        );

        return $totalDuration->gt($maxOnPeriod);
    }
}
