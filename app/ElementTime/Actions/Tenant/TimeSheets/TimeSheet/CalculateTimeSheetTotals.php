<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet;

use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;

class CalculateTimeSheetTotals extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheet $timeSheet,

        bool $allValues = false,
        bool $scheduleValues = false,
        bool $recordedValues = false,
        bool $ordinaryValues = false,
        bool $workedValues = false,
        bool $leaveValues = false,
        bool $holidayValues = false,
        bool $excessTimeValues = false,
        bool $missingValues = false,
        bool $penaltyValues = false,
        bool $autoAllowanceValues = false,
        bool $adHocAllowanceValues = false,

        bool $upstream = false,
    ): TimeSheet
    {
        // TODO: Implement specific value calculations
        // TODO: Implement specific value calculations
        // TODO: Implement specific value calculations
        // TODO: Implement specific value calculations

        if ($timeSheet->isDirty()) {
            /// TODO: Change this to normal save when cleared
            $timeSheet->clearSaveOrFail();
        }

        return $timeSheet;
    }

    /** @throws \Throwable */
    public function asJob(TimeSheet $timeSheet): void
    {
        $this->handle($timeSheet);
    }
}
