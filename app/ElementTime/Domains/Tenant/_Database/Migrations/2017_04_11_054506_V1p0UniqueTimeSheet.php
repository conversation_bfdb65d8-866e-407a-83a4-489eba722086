<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->unique(['user_id', 'payRun_id'], 'timesheet_u_p_unique');
        });
    }

    public function down(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheet WHERE Key_name=\'timesheet_u_p_unique\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropUnique('timesheet_u_p_unique');
            });
        }
    }
};
