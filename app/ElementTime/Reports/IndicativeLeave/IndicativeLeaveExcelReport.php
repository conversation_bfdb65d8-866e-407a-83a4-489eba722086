<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\IndicativeLeave;

use Carbon\Carbon;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBank;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Reports\IndicativeLeave\Traits\IndicativeLeaveDataTrait;
use Element\ElementTime\Reports\IndicativeLeave\Traits\IndicativeLeaveHelperTrait;
use Element\ElementTime\Support\Facades\TenantOrganisationSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Support\Collection;

class IndicativeLeaveExcelReport extends TenantExcelReport
{
    use IndicativeLeaveDataTrait, IndicativeLeaveHelperTrait;

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'indicative-leave';
    const TYPE_SLUG = 'indicative-leave.xls';

    public $c = IndicativeLeaveReportConfig::class;

    protected function setData(array $info): void
    {
        $this->config->title = $this->c::NAME;
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        $this->reportValidity = Carbon::now()->subSeconds(1);

        return $this;
    }

    /** @throws \Throwable */
    protected function buildReportData(): void
    {
        $this->sheets = [];

        $sourceData = $this->getData();

        foreach ($sourceData as $sourceDatum) {
            $this->sheets[] = $this->buildSheet($sourceDatum);
        }
    }

    /** @throws \Throwable */
    protected function buildSheet($data): \stdClass|null
    {
        $this->fetchSheetSettings($sheet, $data);
        $this->fetchSheetTitle($sheet, $data);
        $this->fetchSheetData($sheet, $data);

        return $sheet;
    }

    protected function fetchSheetSettings(\stdClass &$sheet = null, $data = null): void
    {
        $this->validateSheet($sheet);

        $sheet->label = Str::cleanMaxSize($data->name, 30);
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    protected function fetchSheetTitle(\stdClass &$sheet = null, $data = null): void
    {
        $this->validateSheet($sheet);

        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = 'Indicative ' . $data->name . ' ' . $this->getYears();
        $sheet->data[] = $title;
    }

    /** @throws \Throwable */
    protected function fetchSheetData(\stdClass &$sheet = null, $data = null): void
    {
        $this->validateSheet($sheet);

        $leaveCount = $this->getLeaveCount($data->bankTypes);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = $this->getSheetDataParentHeader($leaveCount);
        $table->columns = $this->getSheetDataHeader($leaveCount);
        $table->data = $this->getSheetDataData($data->bankTypes, $leaveCount);
        $sheet->data[] = $table;
    }

    protected function getSheetDataParentHeader(int $count = 0): array
    {
        $columns = [
            $this->createColumn('Staff', 'text', 'left', 7),
            $this->createColumn('Totals', 'text', 'left', 6),
        ];

        for ($i = 0; $i < $count; $i++) {
            $columns[] = $this->createColumn('Proposed leave', 'text', 'left', 3);
        }

        return $columns;
    }

    protected function getSheetDataHeader(int $leaveCount = 0): array
    {
        $columns = [
            $this->createColumn('Ref'),
            $this->createColumn('Last name'),
            $this->createColumn('First name'),
            $this->createColumn('Status'),
            $this->createColumn('Role'),
            $this->createColumn('Manager'),
            $this->createColumn('Department'),
            $this->createColumn('Current pro-rata', 'double', 'right'),
            $this->createColumn('Current available', 'double', 'right'),
            $this->createColumn('Cap', 'double', 'right'),
            $this->createColumn('Excess balance', 'double', 'right'),
            $this->createColumn('Taken this year', 'double', 'right'),
            $this->createColumn('Future leave', 'double', 'right'),
        ];

        for ($i = 0; $i < $leaveCount; $i++) {
            $columns[] = $this->createColumn('From', 'date');
            $columns[] = $this->createColumn('To', 'date');
            $columns[] = $this->createColumn('Hours', 'double', 'right');
        }

        return $columns;
    }

    /**
     * @param Collection|UserLeaveBankType[] $bankTypes
     * @throws \Throwable
     */
    protected function getSheetDataData(Collection|array $bankTypes, int $leaveCount): array
    {
        $tableData = [];

        foreach ($bankTypes as $bankType) {
            if (!$this->isBankTypeVisible($bankType)) {
                continue;
            }

            $tableData[] = [
                'config' => [],
                'data' => $this->getSheetDataRowData($bankType, $leaveCount),
            ];
        }

        return $tableData;
    }

    /** @throws \Throwable */
    protected function getSheetDataRowData(UserLeaveBankType $bankType, int $leaveCount): array
    {
        $bank = $bankType->bank;
        $user = $bank->user;

        $ret = [
            $user->externalId,
            $user->nameLast,
            $user->nameFirst,
            $user->statusClass::NAME,
            $user->masterRole,
            !is_null($user->currentManager) ? $user->currentManager->fullName : '',
            $this->getDepartmentName($user),

            $this->getProRataBalance($bank),
            $this->getTotalBalance($bank),
            $this->getCap($bank),
            $this->getExcessBalance($bank),
            $this->getTakenThisYear($bankType),
            $this->getFutureLeaveHours($bankType),
        ];

        $thisCount = 0;
        foreach ($this->getLeaveRequestsFromBankType($bankType) as $leaveRequest) {
            if (!$this->isLeaveVisible($leaveRequest)) {
                continue;
            }

            $ret[] = $leaveRequest->startDateTime->toDateString();
            $ret[] = $leaveRequest->endDateTime->toDateString();
            $ret[] = $leaveRequest->calculatedTotalHours;

            $thisCount++;
        }

        for (; $thisCount <= $leaveCount; $thisCount++) {
            $ret[] = '';
            $ret[] = '';
            $ret[] = '';
        }

        return $ret;
    }

    protected function getDepartmentName(User $user): string
    {
        $ret = '';
        if (!is_null($user->masterUserRole)) {
            foreach ($user->masterUserRole->getActiveUserRoleDepartments() as $userRoleDepartment) {
                if (strlen($ret) > 0) {
                    $ret .= ', ';
                }

                $ret .= $userRoleDepartment->department->name;
            }
        }

        if (strlen($ret) == 0) {
            return '-';
        }

        return $ret;
    }

    protected function getProRataBalance(UserLeaveBank $bank): float|null
    {
        return etime_round($bank->totalAccruedBalance, 2);
    }

    protected function getTotalBalance(UserLeaveBank $bank): float|null
    {
        return etime_round($bank->totalAvailableBalance + $bank->totalAccruedBalance + $bank->totalCommittedBalance, 2);
    }

    /** @throws \Throwable */
    protected function getCap(UserLeaveBank $bank): float|null
    {
        return $bank->repository->doesHaveCap() ? $bank->repository->getCapAmount() : null;
    }

    /** @throws \Throwable */
    protected function getExcessBalance(UserLeaveBank $bank): float|null
    {
        if (!$bank->repository->doesHaveCap()) {
            return null;
        }

        return $this->getTotalBalance($bank) - $this->getCap($bank);
    }

    protected function getTakenThisYear(UserLeaveBankType $bankType): float|null
    {
        $ret = 0;

        foreach ($this->getLeaveRequestsFromBankType($bankType) as $leaveRequest) {
            if ($leaveRequest->endDateTime->gte(Carbon::today())) {
                continue;
            }

            if ($leaveRequest->endDateTime->lt(Carbon::parse(TenantOrganisationSettings::getValue('fiscalYearStart')))) {
                continue;
            }

            $ret += $leaveRequest->calculatedTotalHours;
        }

        return etime_round($ret, 2);
    }

    protected function getFutureLeaveHours(UserLeaveBankType $bankType): float|null
    {
        $ret = 0;

        foreach ($this->getLeaveRequestsFromBankType($bankType) as $leaveRequest) {
            if (!$this->isLeaveVisible($leaveRequest)) {
                continue;
            }

            $ret += $leaveRequest->calculatedTotalHours;
        }

        return etime_round($ret, 2);
    }
}
