<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Domains\Tenant\Schedules\Enums\LeaveCalculationType;


use Carbon\Carbon;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\LeaveCalculationType\LeaveCalculationType;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayWorkPattern;

describe('LeaveCalculationExpectedCase test', function () {
     it('calculate fixed hours case', function () {
         $fixedDuration = TimeDuration::parseFromHours(12);
         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = true;
         $dayData->duration = $fixedDuration;
         $dayData->ordinarySpan = TimeSpan::createFromStartAndDuration('06:00:00', $fixedDuration);
         $dayData->breaks = TimeSpanList::make([]);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);

         expect($result['duration']->getTotalSeconds())->toEqual($fixedDuration->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($dayData->ordinarySpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     });

     it('calculate expected timespan case', function () {
         $ignoredDuration = TimeDuration::parseFromHours(12);
         $expectedDuration = TimeDuration::parseFromHours(8);

         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = false;
         $dayData->duration = $ignoredDuration;
         $dayData->doesHaveExpectedTimeSpan = true;
         $dayData->expectedDuration = $expectedDuration;
         $dayData->ordinarySpan = TimeSpan::createFromStartAndDuration('06:00:00', $ignoredDuration);
         $dayData->expectedSpan = TimeSpan::createFromStartAndDuration('09:00:00', $expectedDuration);
         $dayData->breaks = TimeSpanList::make([]);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);

         expect($result['duration']->getTotalSeconds())->toEqual($expectedDuration->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($dayData->expectedSpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     });

     it('calculate expected duration case without break from ordinary span', function () {
         $ordinaryDuration = TimeDuration::parseFromHours(12);
         $ordinarySpan = TimeSpan::createFromStartAndDuration('06:00:00', $ordinaryDuration);
         $ordinarySpanMidDay = $ordinarySpan->getStartAsCarbon()->add($ordinaryDuration->copy()->divide(2)->getValue());

         $expectedDuration = TimeDuration::parseFromHours(4);
         $halfExpectedDuration = $expectedDuration->copy()->divide(2)->getValue();
         $expectedSpan = TimeSpan::createFromStartAndDuration('09:00:00', $expectedDuration);

         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = false;
         $dayData->duration = $ordinaryDuration;
         $dayData->expectedDuration = $expectedDuration;
         $dayData->ordinarySpan = $ordinarySpan;
         $dayData->expectedSpan = $expectedSpan;
         $dayData->breaks = TimeSpanList::make([]);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);
         $expectedResultTimeSpan = TimeSpan::make(
             $ordinarySpanMidDay->copy()->sub($halfExpectedDuration),
             $ordinarySpanMidDay->copy()->add($halfExpectedDuration),
         );

         expect($result['duration']->getTotalSeconds())->toEqual($expectedDuration->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($expectedResultTimeSpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     });

     it('calculate expected duration case with single break', function () {
         $ordinaryDuration = TimeDuration::parseFromHours(12);
         $ordinarySpan = TimeSpan::createFromStartAndDuration('06:00:00', $ordinaryDuration);

         $expectedDuration = TimeDuration::parseFromHours(8);
         $halfExpectedDuration = $expectedDuration->copy()->divide(2)->getValue();
         $expectedSpan = TimeSpan::createFromStartAndDuration('09:00:00', $expectedDuration);

         $break = TimeSpan::make('10:00:00', '10:30:00');

         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = false;
         $dayData->duration = $ordinaryDuration;
         $dayData->expectedDuration = $expectedDuration;
         $dayData->ordinarySpan = $ordinarySpan;
         $dayData->expectedSpan = $expectedSpan;
         $dayData->breaks = TimeSpanList::make([$break]);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);
         $expectedResultTimeSpan = TimeSpan::make(
             $break->getStartAsCarbon()->sub($halfExpectedDuration),
             $break->getEndAsCarbon()->add($halfExpectedDuration),
         );

         expect($result['duration']->getTotalSeconds())->toEqual($expectedDuration->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($expectedResultTimeSpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     });

     it('calculate expected duration case with single break and duration is adjusted due to break on half block of ordinary span', function (TimeSpan $break) {
         $ordinaryDuration = TimeDuration::parseFromHours(10);
         $ordinarySpan = TimeSpan::createFromStartAndDuration('08:00:00', $ordinaryDuration);

         $expectedDuration = TimeDuration::parseFromHours(8);
         $expectedSpan = TimeSpan::createFromStartAndDuration('09:00:00', $expectedDuration);

         $leaveEndAdjustedByBreak = $ordinarySpan->getStartAsCarbon()
             ->add($expectedDuration->getValue())
             ->add($break->getDuration()->getValue());
         $durationToBeDeducted = $ordinarySpan->getEndAsCarbon()->diff($leaveEndAdjustedByBreak);

         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = false;
         $dayData->duration = $ordinaryDuration;
         $dayData->expectedDuration = $expectedDuration;
         $dayData->ordinarySpan = $ordinarySpan;
         $dayData->expectedSpan = $expectedSpan;
         $dayData->breaks = TimeSpanList::make([$break]);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);
         $expectedResultTimeSpan = TimeSpan::make(
             $ordinarySpan->getStartAsCarbon(),
             $ordinarySpan->getEndAsCarbon(),
         );

         expect($result['duration']->getTotalSeconds())->toEqual($expectedDuration->copy()->deduct($durationToBeDeducted)->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($expectedResultTimeSpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     })->with([
         TimeSpan::make('10:00:00', '14:00:00'),
         TimeSpan::make('13:00:00', '17:00:00'),
     ]);

     it('calculate expected duration case with multiple breaks', function () {
         $ordinaryDuration = TimeDuration::parseFromHours(10);
         $ordinarySpan = TimeSpan::createFromStartAndDuration('08:00:00', $ordinaryDuration->copy());

         $expectedDuration = TimeDuration::parseFromHours(6);
         $expectedSpan = TimeSpan::createFromStartAndDuration('09:00:00', $expectedDuration->copy());
         $break1 = TimeSpan::make('10:00:00', '10:30:00');
         $break2 = TimeSpan::make('13:00:00', '13:30:00');

         $dayData = new StructScheduledDayWorkPattern;
         $dayData->date = Carbon::today();
         $dayData->hasFixedHours = false;
         $dayData->duration = $ordinaryDuration;
         $dayData->expectedDuration = $expectedDuration;
         $dayData->ordinarySpan = $ordinarySpan;
         $dayData->expectedSpan = $expectedSpan;
         $dayData->breaks = TimeSpanList::make([$break1, $break2]);

         $remainingDuration = $expectedDuration->copy()->deduct($break1->getEndAsCarbon()->diff($break2->getStartAsCarbon()));
         $halfRemainingDuration = $remainingDuration->copy()->divide(2);

         $result = LeaveCalculationType::Expected->calculateDay($dayData);
         $expectedResultTimeSpan = TimeSpan::make(
             $break1->getStartAsCarbon()->sub($halfRemainingDuration->getValue()),
             $break2->getEndAsCarbon()->add($halfRemainingDuration->getValue()),
         );

         expect($result['duration']->getTotalSeconds())->toEqual($expectedDuration->copy()->getTotalSeconds())
             ->and($result['timeSpan']->toArray())->toEqual($expectedResultTimeSpan->toArray())
             ->and($result['breaks']->toArray())->toEqual($dayData->breaks->toArray());
     });
});
