<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Routes\Kiosk;

use Element\ElementTime\Applications\TenantApi\Http\Middleware\DeviceAuthenticationMiddleware;
use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\Kiosk\KioskController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Routes\BaseTenantApiV4Router;
use Element\ElementTime\Applications\TenantApi\Http\V4\Swagger;
use OpenApi\Attributes as OA;

class MainRoutes extends BaseTenantApiV4Router
{
    protected string|null $defaultPrefix = 'kiosk';

    protected function routes(): void
    {
        $this->getCurrentDevice();
        $this->getDeviceRequestId();
        $this->postDeviceRequest();
        $this->deleteDeviceRequestId();
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/kiosk/current-device',
        operationId: 'get_api_v4_kiosk_current-device',
        description: 'Get authenticated current kiosk device',
        summary: 'Get authenticated current kiosk device',
        security: Swagger::DEVICE_AUTH_ONLY,
        tags: ['kiosk'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful kiosk device',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(
                            property: 'content',
                            oneOf: [
                                new OA\Schema('#/components/schemas/KioskDeviceBasicResource'),
                            ],
                        ),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getCurrentDevice(): void
    {
        $this->router->get('current-device', [KioskController::class, 'getDevice'])->middleware(DeviceAuthenticationMiddleware::class);
    }

    /** @throws \Throwable */
    #[OA\Get(
        path: '/api/v4/kiosk/device-request/{id}',
        operationId: 'get_api_v4_kiosk_device-request_{id}',
        description: 'Get kiosk request by id',
        summary: 'Get kiosk request by id',
        security: [],
        tags: ['kiosk'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful kiosk request',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(
                            property: 'content',
                            oneOf: [
                                new OA\Schema('#/components/schemas/KioskDeviceRequestBasicResource'),
                            ],
                        ),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function getDeviceRequestId(): void
    {
        $this->router->get('device-request/{id}', [KioskController::class, 'getDeviceRequest']);
    }

    /** @throws \Throwable */
    #[OA\Post(
        path: '/api/v4/kiosk/device-request',
        operationId: 'post_api_v4_kiosk_device-request',
        description: 'Creates a kiosk request',
        summary: 'Creates a kiosk request',
        security: [],
        requestBody: new OA\RequestBody(
            description: 'Data to start',
            content: new OA\JsonContent(
                properties: [
                    new OA\Property(property: 'deviceId', type: 'string', example: 'unique-device-id'),
                    new OA\Property(property: 'ipAddress', type: 'string', example: 'tablet-ip-address'),
                    new OA\Property(property: 'deviceName', type: 'string', example: 'kiosk front door'),
                 ],
            ),
        ),
        tags: ['kiosk'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful kiosk request',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(
                            property: 'content',
                            oneOf: [
                                new OA\Schema('#/components/schemas/KioskDeviceRequestBasicResource'),
                            ],
                        ),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function postDeviceRequest(): void
    {
        $this->router->post('device-request', [KioskController::class, 'createDeviceRequest']);
    }

    /** @throws \Throwable */
    #[OA\Delete(
        path: '/api/v4/kiosk/device-request/{id}',
        operationId: 'delete_api_v4_kiosk_device-request_{id}',
        description: 'Cancels a kiosk request by id',
        summary: 'Cancels a kiosk request by id',
        security: [],
        tags: ['kiosk'],
        responses: [
            new OA\Response(
                response: 200,
                description: 'Successful kiosk request',
                content: new OA\JsonContent(
                    required: ['status', 'content'],
                    properties: [
                        new OA\Property(property: 'status', type: 'string', example: 'S'),
                        new OA\Property(
                            property: 'content',
                            oneOf: [
                                new OA\Schema('#/components/schemas/KioskDeviceRequestBasicResource'),
                            ],
                        ),
                    ],
                ),
            ),
            new OA\Response(
                ref: '#/components/responses/Unauthorized',
                response: 401,
            ),
        ],
    )]
    protected function deleteDeviceRequestId(): void
    {
        $this->router->delete('device-request/{id}', [KioskController::class, 'cancelDeviceRequest']);
    }
}
