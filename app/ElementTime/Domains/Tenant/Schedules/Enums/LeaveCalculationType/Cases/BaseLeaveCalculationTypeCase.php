<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\Schedules\Enums\LeaveCalculationType\Cases;

use Element\Core\Exceptions\NotImplementedException;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayWorkPattern;
use JetBrains\PhpStorm\ArrayShape;
use Leocello\SweetEnum\SweetCaseClass;

class BaseLeaveCalculationTypeCase extends SweetCaseClass
{
    /// TODO: Update method for other types of schedule
    /** @throws \Throwable */
    #[ArrayShape([
        'duration' => TimeDuration::class,
        'timeSpan' => TimeSpan::class,
        'breaks' => TimeSpanList::class,
    ])]
    public function calculateDay(StructScheduledDayWorkPattern $dayData, float|null $customTime = null): array|null
    {
        throw new NotImplementedException('');
    }

    // ---------------------------------------------------

    /**
     * @throws \Throwable
     */
    protected function calculateDayFlexiblyBasedOnDuration(StructScheduledDayWorkPattern $dayData, TimeDuration $duration): array
    {
        $ordinaryStart = $dayData->ordinarySpan->getStartAsCarbon();
        $ordinaryEnd = $dayData->ordinarySpan->getEndAsCarbon();
        $breaks = TimeSpanList::make($dayData->breaks);

        if ($dayData->breaks->count() < 1) {
            $midDay = $ordinaryStart->copy();
            $midDay->add($dayData->ordinarySpan->getDuration()->copy()->divide(2)->getValue());
            $halfFteDuration = $duration->copy()->divide(2);

            return [
                'duration' => $duration->copy(),
                'timeSpan' => TimeSpan::createFromStartAndDuration(
                    start: $midDay->copy()->sub($halfFteDuration->getValue()),
                    duration: $duration,
                )->setDate($dayData->date),
                'breaks' => $breaks->setDate($dayData->date),
            ];
        }

        $remainingDuration = $duration->copy();
        $breaksList = $dayData->breaks->getList();

        for ($i = 1; $i < $breaksList->count(); $i++) {
            $previousBreakEnd = $breaksList[$i - 1]->getEndAsCarbon();
            $currentBreakStart = $breaksList[$i]->getStartAsCarbon();
            $remainingDuration->deduct($previousBreakEnd->diff($currentBreakStart));
        }

        /** @var TimeSpan $firstBreak */
        $firstBreak = $dayData->breaks->getList()->first();
        $breakStart = $firstBreak->getStartAsCarbon();

        /** @var TimeSpan $lastBreak */
        $lastBreak = $dayData->breaks->getList()->last();
        $breakEnd = $lastBreak->getEndAsCarbon();

        $diffBeforeBreak = TimeDuration::make($ordinaryStart->diff($breakStart));
        $diffAfterBreak = TimeDuration::make($breakEnd->diff($ordinaryEnd));
        $halfDuration = $remainingDuration->copy()->divide(2);

        if ($diffBeforeBreak->gte($halfDuration) && $diffAfterBreak->gte($halfDuration)) {
            $start = $breakStart->copy()->sub($halfDuration->getValue());
            $end = $breakEnd->copy()->add($halfDuration->getValue());
        } elseif ($diffBeforeBreak->lt($halfDuration)) {
            $start = $ordinaryStart;
            $remainingDuration->deduct($start->diff($breakStart));
            $end = $breakEnd->copy()->add($remainingDuration->getValue());

            if ($end->isAfter($ordinaryEnd)) {
                $duration->deduct($ordinaryEnd->diff($end));
                $end = $ordinaryEnd;
            }
        } else {
            $end = $ordinaryEnd;
            $remainingDuration->deduct($breakEnd->diff($end));
            $start = $breakStart->copy()->sub($remainingDuration->getValue());

            if ($start->isBefore($ordinaryStart)) {
                $duration->deduct($start->diff($ordinaryStart));
                $start = $ordinaryStart;
            }
        }

        return [
            'duration' => $duration,
            'timeSpan' => TimeSpan::createFromStartAndEnd($start, $end)->setDate($dayData->date),
            'breaks' => $breaks->setDate($dayData->date),
        ];
    }
}
