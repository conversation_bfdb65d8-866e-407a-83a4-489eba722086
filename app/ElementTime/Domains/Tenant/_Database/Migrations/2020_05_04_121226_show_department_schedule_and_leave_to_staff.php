<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserDepartmentShowData
        Schema::create('UserDepartmentShowData', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            // Type of relation: schedule (S) / leave (L)
            $table->char('type', 1);

            // User
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'udss_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            // Department
            $table->unsignedBigInteger('department_id')->nullable();
            $table->foreign('department_id', 'udss_d_fn')->references('id')->on('Department')->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();
            $table->unique(['type', 'user_id', 'department_id']);
        });
        //endregion Table: UserDepartmentShowData
    }

    public function down(): void
    {
        //region Table: UserDepartmentShowData
        Schema::dropIfExists('UserDepartmentShowData');
        //endregion Table: UserDepartmentShowData
    }
};
