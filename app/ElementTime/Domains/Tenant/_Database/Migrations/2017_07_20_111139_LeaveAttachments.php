<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region MediaFile
        Schema::create('MediaFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->string('slug');

            $table->string('disk')->nullable();
            $table->string('bucket');
            $table->string('path');
            $table->string('mimeType')->nullable();
            $table->string('name')->nullable();
            $table->string('description')->nullable();
            $table->string('originalName')->nullable();
            $table->boolean('isPrivate')->default(false);
            $table->string('systemAccesses')->nullable();
            $table->char('status', 1);
            $table->string('type')->nullable();

            $table->bigInteger('user_id')->unsigned()->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['slug']);
            $table->foreign('user_id', 'mfile_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('MediaFileView', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->dateTime('dateTime');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('user_id')->unsigned()->nullable();
            $table->char('method', 1)->nullable(); // V => View | D => Download

            $table->index(['dateTime', 'mediaFile_id'], 'mediafileview_dt_mf_i');
            $table->foreign('mediaFile_id', 'mfileview_mfile_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('user_id', 'mfileview_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });
        //endregion

        //region Rules configurations
        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('attachmentRequired')->default(false);
            $table->boolean('attachmentRequiredHasDuration')->default(false);
            $table->decimal('attachmentRequiredDurationValue', 16, 10)->nullable();
            $table->char('attachmentRequiredDurationType', 1)->nullable();
        });
        //endregion

        //region Attachment
        Schema::table('Leave', function (Blueprint $table) {
            $table->boolean('attachmentRequired')->default(false);
            $table->boolean('attachmentRequiredHasDuration')->default(false);
            $table->decimal('attachmentRequiredDurationValue', 16, 10)->nullable();
            $table->char('attachmentRequiredDurationType', 1)->nullable();
        });

        Schema::create('LeaveAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('leave_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mediaFile_id', 'laf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('leave_id', 'laf_leave_fn')->references('id')->on('Leave')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->boolean('attachmentRequired')->default(false);
            $table->boolean('attachmentRequiredHasDuration')->default(false);
            $table->decimal('attachmentRequiredDurationValue', 16, 10)->nullable();
            $table->char('attachmentRequiredDurationType', 1)->nullable();
        });

        Schema::create('ToilAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('toil_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mediaFile_id', 'taf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('toil_id', 'taf_toil_fn')->references('id')->on('Toil')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->boolean('attachmentRequired')->default(false);
            $table->boolean('attachmentRequiredHasDuration')->default(false);
            $table->decimal('attachmentRequiredDurationValue', 16, 10)->nullable();
            $table->char('attachmentRequiredDurationType', 1)->nullable();
        });

        Schema::create('RosteredTimeOffAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('rosteredTimeOff_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mediaFile_id', 'rtoaf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOff_id', 'rtoaf_rto_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('cascade');
        });
        //endregion
    }

    public function down(): void
    {
        //region Attachment
        //region RosteredTimeOff
        Schema::dropIfExists('RosteredTimeOffAttachmentFile');

        if (Schema::hasColumn('RosteredTimeOff', 'attachmentRequiredDurationType')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'attachmentRequiredDurationValue')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'attachmentRequiredHasDuration')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'attachmentRequired')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('attachmentRequired');
            });
        }
        //endregion

        //region Toil
        Schema::dropIfExists('ToilAttachmentFile');

        if (Schema::hasColumn('Toil', 'attachmentRequiredDurationType')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('Toil', 'attachmentRequiredDurationValue')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('Toil', 'attachmentRequiredHasDuration')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('Toil', 'attachmentRequired')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('attachmentRequired');
            });
        }
        //endregion

        //region Leave
        Schema::dropIfExists('LeaveAttachmentFile');

        if (Schema::hasColumn('Leave', 'attachmentRequiredDurationType')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('Leave', 'attachmentRequiredDurationValue')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('Leave', 'attachmentRequiredHasDuration')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('Leave', 'attachmentRequired')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('attachmentRequired');
            });
        }
        //endregion
        //endregion

        //region Rules
        if (Schema::hasColumn('LeaveType', 'attachmentRequiredDurationType')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationType');
            });
        }
        if (Schema::hasColumn('LeaveType', 'attachmentRequiredDurationValue')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredDurationValue');
            });
        }
        if (Schema::hasColumn('LeaveType', 'attachmentRequiredHasDuration')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('attachmentRequiredHasDuration');
            });
        }
        if (Schema::hasColumn('LeaveType', 'attachmentRequired')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('attachmentRequired');
            });
        }
        //endregion

        //region MediaFile
        Schema::dropIfExists('MediaFileView');
        Schema::dropIfExists('MediaFile');
        //endregion
    }
};
