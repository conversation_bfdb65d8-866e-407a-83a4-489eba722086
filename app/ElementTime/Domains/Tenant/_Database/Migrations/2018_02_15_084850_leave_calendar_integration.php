<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Leave', function (Blueprint $table) {
            $table->string('externalCalendarEmail')->nullable();
            $table->string('externalCalendarId')->nullable();
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->string('externalCalendarEmail')->nullable();
            $table->string('externalCalendarId')->nullable();
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->string('externalCalendarEmail')->nullable();
            $table->string('externalCalendarId')->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('Leave', 'externalCalendarId')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('externalCalendarId');
            });
        }
        if (Schema::hasColumn('Leave', 'externalCalendarEmail')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('externalCalendarEmail');
            });
        }

        if (Schema::hasColumn('Toil', 'externalCalendarId')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('externalCalendarId');
            });
        }
        if (Schema::hasColumn('Toil', 'externalCalendarEmail')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('externalCalendarEmail');
            });
        }

        if (Schema::hasColumn('RosteredTimeOff', 'externalCalendarId')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('externalCalendarId');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'externalCalendarEmail')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('externalCalendarEmail');
            });
        }
    }
};
