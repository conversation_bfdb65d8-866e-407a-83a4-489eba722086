<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table AsyncProcess
        Schema::table('AsyncProcess', function (Blueprint $table) {
            $table->index(['rel_type', 'rel_id'], 'asprs_rel_ix');
            $table->index(['finishDateTime'], 'asprs_finished_ix');
        });
        //endregion Table AsyncProcess

        //region Table CustomContent
        Schema::table('CustomContent', function (Blueprint $table) {
            $table->index(['model_type', 'model_id'], 'cc_model_ix');
        });
        //endregion Table CustomContent

        //region Table LeaveRequest
        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->index(['startDateTime', 'endDateTime'], 'lr_period_ix');
        });
        //endregion Table LeaveRequest

        //region Table LeaveRequestDay
        Schema::table('LeaveRequestDay', function (Blueprint $table) {
            $table->index(['date'], 'lrd_date_ix');
        });
        //endregion Table LeaveRequestDay

        //region Table ModelIssue
        Schema::table('ModelIssue', function (Blueprint $table) {
            $table->index(['model_type', 'model_id'], 'missue_model_ix');
            $table->index(['rel_type', 'rel_id'], 'missue_rel_ix');
        });
        //endregion Table ModelIssue

        //region Table Notification
        Schema::table('Notification', function (Blueprint $table) {
            $table->index(['supId'], 'notif_supId_ix');
        });
        //endregion Table Notification

        //region Table PayRun
        Schema::table('PayRun', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'payrun_period_ix');
        });
        //endregion Table PayRun

        //region Table TimeSheetDay
        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->index(['date'], 'tsd_date_ix');
        });
        //endregion Table TimeSheetDay

        //region Table TimeSheetDayTime
        Schema::table('TimeSheetDayTime', function (Blueprint $table) {
            $table->index(['recordType'], 'tsdt_recordType_ix');
            $table->index(['model_type', 'model_id'], 'tsdt_model_ix');
            $table->index(['type_type', 'type_id'], 'tsdt_type_ix');
        });
        //endregion Table TimeSheetDayTime

        //region Table UserHigherDuty
        Schema::table('UserHigherDuty', function (Blueprint $table) {
            $table->foreign('user_id', 'uhd_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('onBehalfUserRole_id', 'uhd_obur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payType_id', 'uhd_pt_fn')->references('id')->on('PayType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('customIncrementRate_id', 'uhd_ir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('cascade');
            $table->index(['startDate', 'endDate'], 'uhd_period_ix');
        });
        //endregion Table UserHigherDuty

        //region Table UserLeaveBankEntry
        Schema::table('UserLeaveBankEntry', function (Blueprint $table) {
            $table->index(['entryType'], 'ulbe_entryType_ix');
        });
        //endregion Table UserLeaveBankEntry

        //region Table UserLeaveBankType
        Schema::table('UserLeaveBankType', function (Blueprint $table) {
            $table->index(['type_type', 'type_id'], 'ulbt_type_ix');
        });
        //endregion Table UserLeaveBankType

        //region Table UserManager
        Schema::table('UserManager', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'uman_period_ix');
        });
        //endregion Table UserManager

        //region Table UserPayType
        Schema::table('UserPayType', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'upt_period_ix');
        });
        //endregion Table UserPayType

        //region Table UserPenaltyType
        Schema::table('UserPenaltyType', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'upnlty_period_ix');
        });
        //endregion Table UserPenaltyType

        //region Table UserRole
        Schema::table('UserRole', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'userrole_period_ix');
        });
        //endregion Table UserRole

        //region Table UserRoleMaster
        Schema::table('UserRoleMaster', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'urm_period_ix');
        });
        //endregion Table UserRoleMaster

        //region Table UserRoleProject
        Schema::table('UserRoleProject', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'urp_period_ix');
        });
        //endregion Table UserRoleProject

        //region Table UserShift
        Schema::table('UserShift', function (Blueprint $table) {
            $table->index(['startDate', 'endDate'], 'user_shift_period_ix');
        });
        //endregion Table UserShift

        //region Table UserShiftDay
        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->index(['userShift_id', 'date'], 'usd_assdate_ix');
        });
        //endregion Table UserShiftDay

        //region Table UserShiftDayTime
        Schema::table('UserShiftDayTime', function (Blueprint $table) {
            $table->index(['type'], 'usdt_type_ix');
        });
        //endregion Table UserShiftDayTime

        //region Table UserShiftProject
        Schema::table('UserShiftProject', function (Blueprint $table) {
            $table->index(['doesUseParentDates'], 'usp_pdates_ix');
            $table->index(['startDate', 'endDate'], 'usp_period_ix');
        });
        //endregion Table UserShiftProject

        //region Table UserShiftProjectActivity
        Schema::table('UserShiftProjectActivity', function (Blueprint $table) {
            $table->index(['doesUseParentDates'], 'uspa_pdates_ix');
            $table->index(['startDate', 'endDate'], 'uspa_period_ix');
        });
        //endregion Table UserShiftProjectActivity

        //region Table UserShiftTimeType
        Schema::table('UserShiftTimeType', function (Blueprint $table) {
            $table->index(['doesUseParentDates'], 'ustt_pdates_ix');
            $table->index(['startDate', 'endDate'], 'ustt_period_ix');
        });
        //endregion Table UserShiftTimeType

        //region Table UserShiftTimeTypeExcessTimeGroup
        Schema::table('UserShiftTimeTypeExcessTimeGroup', function (Blueprint $table) {
            $table->index(['doesUseParentDates'], 'usttetg_pdates_ix');
            $table->index(['startDate', 'endDate'], 'usttetg_period_ix');
        });
        //endregion Table UserShiftTimeTypeExcessTimeGroup

        //region Table Workflow
        Schema::table('Workflow', function (Blueprint $table) {
            $table->index(['rel_type', 'rel_id'], 'wflow_rel_ix');
            $table->index(['type'], 'wflow_type_ix');
            $table->index(['status'], 'wflow_status_ix');
        });
        //endregion Table Workflow

        //region Table WorkflowStepItem
        Schema::table('WorkflowStepItem', function (Blueprint $table) {
            $table->index(['order'], 'wfsi_order_ix');
            $table->index(['type'], 'wfsi_type_ix');
        });
        //endregion Table WorkflowStepItem

        //region Table WorkflowTimeline
        Schema::table('WorkflowTimeline', function (Blueprint $table) {
            $table->index(['dateTime'], 'wftl_dateTime_ix');
        });
        //endregion Table WorkflowTimeline
    }

    public function down(): void
    {
        //region Table AsyncProcess
        try {
            Schema::table('AsyncProcess', function (Blueprint $table) {
                $table->dropIndex('asprs_rel_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('AsyncProcess', function (Blueprint $table) {
                $table->dropIndex('asprs_finished_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table AsyncProcess

        //region Table CustomContent
        try {
            Schema::table('CustomContent', function (Blueprint $table) {
                $table->dropIndex('cc_model_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table CustomContent

        //region Table LeaveRequest
        try {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropIndex('lr_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table LeaveRequest

        //region Table LeaveRequestDay
        try {
            Schema::table('LeaveRequestDay', function (Blueprint $table) {
                $table->dropIndex('lrd_date_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table LeaveRequestDay

        //region Table ModelIssue
        try {
            Schema::table('ModelIssue', function (Blueprint $table) {
                $table->dropIndex('missue_model_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('ModelIssue', function (Blueprint $table) {
                $table->dropIndex('missue_rel_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table ModelIssue

        //region Table Notification
        try {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropIndex('notif_supId_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table Notification

        //region Table PayRun
        try {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropIndex('payrun_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table PayRun

        //region Table TimeSheetDay
        try {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropIndex('tsd_date_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table TimeSheetDay

        //region Table TimeSheetDayTime
        try {
            Schema::table('TimeSheetDayTime', function (Blueprint $table) {
                $table->dropIndex('tsdt_recordType_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('TimeSheetDayTime', function (Blueprint $table) {
                $table->dropIndex('tsdt_model_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('TimeSheetDayTime', function (Blueprint $table) {
                $table->dropIndex('tsdt_type_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table TimeSheetDayTime

        //region Table UserHigherDuty
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_u_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_obur_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_pt_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropForeign('uhd_ir_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserHigherDuty', function (Blueprint $table) {
                $table->dropIndex('uhd_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserHigherDuty

        //region Table UserLeaveBankEntry
        try {
            Schema::table('UserLeaveBankEntry', function (Blueprint $table) {
                $table->dropIndex('ulbe_entryType_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserLeaveBankEntry

        //region Table UserLeaveBankType
        try {
            Schema::table('UserLeaveBankType', function (Blueprint $table) {
                $table->dropIndex('ulbt_type_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserLeaveBankType

        //region Table UserManager
        try {
            Schema::table('UserManager', function (Blueprint $table) {
                $table->dropIndex('uman_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserManager

        //region Table UserPayType
        try {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->dropIndex('upt_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserPayType

        //region Table UserPenaltyType
        try {
            Schema::table('UserPenaltyType', function (Blueprint $table) {
                $table->dropIndex('upnlty_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserPenaltyType

        //region Table UserRole
        try {
            Schema::table('UserRole', function (Blueprint $table) {
                $table->dropIndex('userrole_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserRole

        //region Table UserRoleMaster
        try {
            Schema::table('UserRoleMaster', function (Blueprint $table) {
                $table->dropIndex('urm_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserRoleMaster

        //region Table UserRoleProject
        try {
            Schema::table('UserRoleProject', function (Blueprint $table) {
                $table->dropIndex('urp_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserRoleProject

        //region Table UserShift
        try {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropIndex('user_shift_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShift

        //region Table UserShiftDay
        try {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropIndex('usd_assdate_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftDay

        //region Table UserShiftDayTime
        try {
            Schema::table('UserShiftDayTime', function (Blueprint $table) {
                $table->dropIndex('usdt_type_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftDayTime

        //region Table UserShiftProject
        try {
            Schema::table('UserShiftProject', function (Blueprint $table) {
                $table->dropIndex('usp_pdates_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserShiftProject', function (Blueprint $table) {
                $table->dropIndex('usp_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftProject

        //region Table UserShiftProjectActivity
        try {
            Schema::table('UserShiftProjectActivity', function (Blueprint $table) {
                $table->dropIndex('uspa_pdates_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserShiftProjectActivity', function (Blueprint $table) {
                $table->dropIndex('uspa_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftProjectActivity

        //region Table UserShiftTimeType
        try {
            Schema::table('UserShiftTimeType', function (Blueprint $table) {
                $table->dropIndex('ustt_pdates_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserShiftTimeType', function (Blueprint $table) {
                $table->dropIndex('ustt_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftTimeType

        //region Table UserShiftTimeTypeExcessTimeGroup
        try {
            Schema::table('UserShiftTimeTypeExcessTimeGroup', function (Blueprint $table) {
                $table->dropIndex('usttetg_pdates_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('UserShiftTimeTypeExcessTimeGroup', function (Blueprint $table) {
                $table->dropIndex('usttetg_period_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table UserShiftTimeTypeExcessTimeGroup

        //region Table Workflow
        try {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropIndex('wflow_rel_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropIndex('wflow_type_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropIndex('wflow_status_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table Workflow

        //region Table WorkflowStepItem
        try {
            Schema::table('WorkflowStepItem', function (Blueprint $table) {
                $table->dropIndex('wfsi_order_ix');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('WorkflowStepItem', function (Blueprint $table) {
                $table->dropIndex('wfsi_type_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table WorkflowStepItem

        //region Table WorkflowTimeline
        try {
            Schema::table('WorkflowTimeline', function (Blueprint $table) {
                $table->dropIndex('wftl_dateTime_ix');
            });
        } catch (Throwable) {
        }
        //endregion Table WorkflowTimeline
    }
};
