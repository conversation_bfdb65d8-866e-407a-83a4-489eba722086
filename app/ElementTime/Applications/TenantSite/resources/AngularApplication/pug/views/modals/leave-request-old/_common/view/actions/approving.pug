form.form-validate(role='form' method='POST' action='javascript:' name='$f.form')
  .panel.panel-default
    .panel-heading
      h4.h4.m0 Approve / decline leave request
    .panel-body
      .row
        .col-xs-12
          .form-group
            label.label-control(for='LeaveApproveDecline_inputReason') Your comments
            textarea.form-control(id='LeaveApproveDecline_inputReason' ng-model='$f.data.reason')
      .row
        .col-xs-12
          button.btn.btn-success.mr(ng-if='!!$f.canApprove()' type='button' ng-click='$f.approve()' ng-disabled='!!$f.isSubmitting')
            | Approve
          button.btn.btn-danger.mr(type='button' ng-click='$f.decline()' ng-disabled='!!$f.isSubmitting')
            | Decline

          span.pull-right.text-danger.pt-sm(ng-if='!req.isThereEnoughBalance') Due to other recent approved requests the user does not have enough balance for you to now approve this request.
