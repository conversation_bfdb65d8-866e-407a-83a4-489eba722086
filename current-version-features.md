# RELEASE aluminium-14.23  -  [Wrike](https://www.wrike.com/open.htm?id=1673553260)  

## Dev env:  
#### Fixes:  
* [CHORES] Add flags `--build` and `--wait` when starting application so if there is a new version of the docker image, it updates it automatically  

## General:  
#### Fixes:  
* [FIX] Fix warnings/errors shown on composer on CI and dev env  

## Reports:  
#### Chores:  
* [CHORE] Alter report "NGSC Business central financial costing journal" to exclude all public holidays  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] Alter report "SGSC Business central financial costing journal" to have a new account number for time recorded  

---

# RELEASE aluminium-14.22  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Chores:  
* [CHORE] Add filter by departments to the Timesheet Approval Audit Report  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## General:  
#### Fixes:  
* [FIX] Fix error on forecasting leave balances when leave is realised on anniversary with stand-down period  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-14.21  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Authority payroll integration:  
#### Features:  
* [FEAT] Add flag to allow passing plant values as zero when not filled on Authority payroll integration  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-14.20  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Chores:  
* [CHORE] Update timesheet detailed PDF report to show details on worked hours  -  [Wrike](https://www.wrike.com/open.htm?id=1658901062)  

---

# RELEASE aluminium-14.19  -  [Wrike](https://www.wrike.com/open.htm?id=1658665172)  

## Reports:  
#### Hardcode:  
* [HARDCODED] Remove splits from user 85685 on NGSC version of keypay batch file  -  [Wrike](https://www.wrike.com/open.htm?id=1657757242)  

## General:  
#### Fixes:  
* [FIX] Fix error where ad hoc allowances show incorrectly on timesheet project view when copying from previous timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1657758654)  

## Reports:  
#### Fixes:  
* [FIX] Fix "All leave balances" report that is returning a blank file  -  [Wrike](https://www.wrike.com/open.htm?id=1658653123)  

---

# RELEASE aluminium-14.18  -  [Wrike](https://www.wrike.com/open.htm?id=1655894082)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issues with masking plant usage values in the timesheet plant summary resource class  

---

# RELEASE aluminium-14.17  -  [Wrike](https://www.wrike.com/open.htm?id=1654939234)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix eager loading model on timesheet plant summary resource class  

---

# RELEASE aluminium-14.16  -  [Wrike](https://www.wrike.com/open.htm?id=1654046664)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create/change services to get advisory notes for managers mode  -  [Wrike](https://www.wrike.com/open.htm?id=1642745537)  
* [FEAT] #API Create new API services on to allow recording standalone plant on timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1623967594)  

---

# RELEASE aluminium-14.15  -  [Wrike](https://www.wrike.com/open.htm?id=1651744283)  

## General:  
#### Fixes:  
* [FIX] Fix retrying send report via email when it fails more than 5 times  

---

# RELEASE aluminium-14.14  -  [Wrike](https://www.wrike.com/open.htm?id=1650711552)  

## Libraries:  
* [LIB] Update composer libraries  
* [LIB] Remove unused composer libraries (`h4cc/wkhtmltoimage-amd64`, `"h4cc/wkhtmltoimage-i386`, `h4cc/wkhtmltopdf-amd6` and `h4cc/wkhtmltopdf-i386`)  

## General:  
#### Fixes:  
* [FIX] Fix error on excluding all timesheets from payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1649639536)  

---

# RELEASE aluminium-14.13  -  [Wrike](https://www.wrike.com/open.htm?id=1650702281)  

## Environment:  
#### Fixes:  
* [FIX] Fix error where PDF files cannot be generated on ARM processors  -  [Wrike](https://www.wrike.com/open.htm?id=1650587182)  

---

# RELEASE aluminium-14.12  -  [Wrike](https://www.wrike.com/open.htm?id=1641745519)  

## Infrastructure:  
* [INFRA] Update architecture to use ARM based processors  

## General:  
#### Chores:  
* [CHORE] Add field to capture what application recorded a workflow entry - show for timesheet submissions when done via mobile app  -  [Wrike](https://www.wrike.com/open.htm?id=1338720280)  
* [CHORE] Create new workflow entries to trigger timesheet recalc  -  [Wrike](https://www.wrike.com/open.htm?id=1645932463)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix error where some times excess-time duplicates when time is recorded using mobile app's timer  -  [Wrike](https://www.wrike.com/open.htm?id=1635265570)  

---

# RELEASE aluminium-14.11  -  [Wrike](https://www.wrike.com/open.htm?id=1640840108)  

## General:  
#### Fixes:  
* [FIX] Fix excess-time calculation error when multiple levels are skipped at once and calculation type is "scheduled + recorded"  -  [Wrike](https://www.wrike.com/open.htm?id=1639974344)  
* [FIX] Fix issue with timesheets listing showing reports from higher duty requests not approved  -  [Wrike](https://www.wrike.com/open.htm?id=1639969565)  
#### Chores:  
* [CHORE] Show masked title for plant item on timesheet plant summary view  -  [Wrike](https://www.wrike.com/open.htm?id=1639969953)  

---

# RELEASE aluminium-14.10  -  [Wrike](https://www.wrike.com/open.htm?id=1640742222)  

## Reports:  
#### Features:  
* [FEAT] Create new report "Single timesheet recorded hours report"  -  [Wrike](https://www.wrike.com/open.htm?id=1632188269)  

---

# RELEASE aluminium-14.09  -  [Wrike](https://www.wrike.com/open.htm?id=1632188270)  

## General:  
#### Chores:  
* [CHORE] Add ability to set plant use code by field capture type  -  [Wrike](https://www.wrike.com/open.htm?id=1630201750)  
* [CHORE] Add link from leave balances on dashboard to "my leave requests" page  -  [Wrike](https://www.wrike.com/open.htm?id=1625255725)  
#### Features:  
* [FEAT] Add condition to leave cancellation workflow to control by leave status  -  [Wrike](https://www.wrike.com/open.htm?id=1632135012)  
* [FEAT] Add workflow condition for canceling leave workflow based on balance cap  -  [Wrike](https://www.wrike.com/open.htm?id=1632183817)  

---

# RELEASE aluminium-14.08  -  [Wrike](https://www.wrike.com/open.htm?id=1631041985)  

## Notifications:  
#### Fixes:  
* [FIX] Fix user note reminders on the old structure is generating errors in the logs when trying to send  

## Web application:  
#### Fixes:  
* [FIX] Fix error 500 shown on timesheet when trying to access plant data if user started after start of payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1630244052)  

---

# RELEASE aluminium-14.07  -  [Wrike](https://www.wrike.com/open.htm?id=1628872342)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create/increment needed API services to get relevant data to allow applying for leave from time-card  -  [Wrike](https://www.wrike.com/open.htm?id=1608016600)  
* [FEAT] #API Create API service to complete missing hours in a timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1617638115)  

## General:  
#### Fixes:  
* [FIX] Fix weird error that says project/activity is inactive when trying to copy from previous day when there were time against activities  
* [FIX] Fix issue with leave cancellation condition that checks if request is within certain number of days - it doesn't work if it's exactly the number of days in the rule  

---

# RELEASE aluminium-14.06  -  [Wrike](https://www.wrike.com/open.htm?id=1627997882)  

__This was just to have a new tag because of the conflict issues with `silicon` on deploy__  

---

# RELEASE aluminium-14.05  -  [Wrike](https://www.wrike.com/open.htm?id=1627976777)  

## General:  
#### Fixes:  
* [FIX] Fix error 500 when recording plant when the class allows recording as standalone and assignment is not direct  -  [Wrike](https://www.wrike.com/open.htm?id=1627969827)  
#### Chores:  
* [CHORE] Allow non-standard plant items to be used as standalone  -  [Wrike](https://www.wrike.com/open.htm?id=1627951719)  

---

# RELEASE aluminium-14.04  -  [Wrike](https://www.wrike.com/open.htm?id=1627893329)  

## General:  
#### Fixes:  
* [FIX] Fix issue where leave request cancellation workflows are not triggering correctly if rule "Leave starts within certain number of days" is used  -  [Wrike](https://www.wrike.com/open.htm?id=1627301929)  

---

# RELEASE aluminium-14.03  -  [Wrike](https://www.wrike.com/open.htm?id=1627188378)  

## Merges:  
* [MERGE] Merge from [aluminium-13.11](https://www.wrike.com/open.htm?id=1627187868)  

## General:  
#### Fixes:  
* [FIX] Fix issue with wrong balances calculations when editing leave requests  -  [Wrike](https://www.wrike.com/open.htm?id=1603408397)  
#### Chores:  
* [CHORE] Add default option to plant types to allow costing to non worked hours  -  [Wrike](https://www.wrike.com/open.htm?id=1627089993)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where when adding plant to worked hours after adding as standalone they disappear from project view  -  [Wrike](https://www.wrike.com/open.htm?id=1627144447)  
* [FIX] Fix advisory notes count in the timesheet listing - it's counting the inactive notes and it should count only the active ones  -  [Wrike](https://www.wrike.com/open.htm?id=1627156869)  
* [FIX] Fix issue where workflow modals cannot be open for other records bu timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1627168075)  
* [FIX] Fix issue with URL parsing in comments that was generating hundreds of errors in the browser console  
#### Chores:  
* [CHORE] Focus plant dropdown when clicking button to add plant item to timesheet in case there is more than one item to select  -  [Wrike](https://www.wrike.com/open.htm?id=1627087265)  
* [CHORE] Highlight outstanding task and fix the grammar on personal dashboard  -  [Wrike](https://www.wrike.com/open.htm?id=1627145633)  
* [CHORE] Show advisory notes on submit page if timesheet owner can view it  -  [Wrike](https://www.wrike.com/open.htm?id=1627153119)  
* [CHORE] Show comments made against worked hours on work-orders in the timesheet excess-time view  -  [Wrike](https://www.wrike.com/open.htm?id=1607200391)  
* [CHORE] Add ability to filter comments in all comments modal  -  [Wrike](https://www.wrike.com/open.htm?id=1607199065)  

---

# RELEASE aluminium-14.02  -  [Wrike](https://www.wrike.com/open.htm?id=1622200499)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues with plant when editing standalone records it duplicates the form  
* [FIX] Fix some performance issues on new plant functionality  

---

# RELEASE aluminium-14.01  -  [Wrike](https://www.wrike.com/open.htm?id=1621386354)  

## General:  
#### Features:  
* [FEAT] Create ability to cost to plant outside of worked hours  -  [Wrike](https://www.wrike.com/open.htm?id=1587099638)  

# RELEASE aluminium-14.00  -  [Wrike](https://www.wrike.com/open.htm?id=1619411402)  

__Base version: [aluminium-13.09](https://www.wrike.com/open.htm?id=1615778811)__  

## General:  
#### Features:  
* [FEAT] Create advisory note class and functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1566715013)  
#### Chores:  
* [CHORE] Fix user profile notes so reminders are sent on date set and add ability to select recipient and repeat yearly  -  [Wrike](https://www.wrike.com/open.htm?id=1363909641)  

## Web application:  
#### Features:  
* [FEAT] Add alert to personal and manager dashboards to alert users when they have open leave action tasks  -  [Wrike](https://www.wrike.com/open.htm?id=1580959763)  
* [FEAT] Access to plant reports for plant coordinator  -  [Wrike](https://www.wrike.com/open.htm?id=1606857201)  
#### Fixes:  
* [FIX] Fix issue with breaks disappearing when person is recording across midnight  -  [Wrike](https://www.wrike.com/open.htm?id=1618399994)  
* [FIX] Fix timesheet allowance view - sum of values in the totals summary area is not correct when there are multiple instances  -  [Wrike](https://www.wrike.com/open.htm?id=1618407111)  
* [FIX] Fix timesheet allowance view - hide the number of triggers from non-hourly allowances  -  [Wrike](https://www.wrike.com/open.htm?id=1618407777)  
* [FIX] Fix error with syncing data when saving public holidays shown on logs  

---
---
---

# RELEASE aluminium-13.11  -  [Wrike](https://www.wrike.com/open.htm?id=1627187868)  

## Mobile API:  
#### Chores:  
* [CHORE] #API Map pending cancellation for leave request issue on timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1606849813)  

---

# RELEASE aluminium-13.10  -  [Wrike](https://www.wrike.com/open.htm?id=1626215008)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Update reports Authority batch file Bega version (with and without plant) with leave penalty rules  -  [Wrike](https://www.wrike.com/open.htm?id=1615743941)  

---

# RELEASE aluminium-13.09  -  [Wrike](https://www.wrike.com/open.htm?id=1615778811)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create new API service to get dashboard widgets info  -  [Wrike](https://www.wrike.com/open.htm?id=1590820490)  

## General:  
#### Fixes:  
* [FIX] Fix end time calculation error when applying for full-day leave on flexible shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1575611399)  

---

# RELEASE aluminium-13.08  -  [Wrike](https://www.wrike.com/open.htm?id=1612720885)  

## General:  
#### Fixes:  
* [FIX] Fix issue with role managers opening timesheets and some values get doubled  -  [Wrike](https://www.wrike.com/open.htm?id=1612719145)  

---

# RELEASE aluminium-13.07  -  [Wrike](https://www.wrike.com/open.htm?id=1611892743)  

## Mobile API:  
#### Chores:  
* [CHORE] Add API version to tenant service to be used to identify if tenant mas migrated to `silicon`  

## Web application:  
#### Fixes:  
* [FIX] Fix real-time issue where when approved requests are cancelled without triggering cancellation workflow they are not being removed from timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1611782915)  
* [FIX] Fix permission error when skip submit removes the user from workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1611877681)  
* [FIX] Fix comments requirements when taking decision around a leave cancellation so it is required when declining not approving  -  [Wrike](https://www.wrike.com/open.htm?id=1611890733)  
#### Chores:  
* [CHORE] Change message on issues modal when timesheet cannot be approved by workflow reasons but it has no stopper issues  -  [Wrike](https://www.wrike.com/open.htm?id=1611874078)  
#### Features:  
* [FEAT] Create new workflow condition for leave cancellation for requests that started before the cancellation  -  [Wrike](https://www.wrike.com/open.htm?id=1611892277)  

## Notification:  
#### Fixes:  
* [FIX] Fix issue with notification for new leave cancellation request that was not being sent  -  [Wrike](https://www.wrike.com/open.htm?id=1611865442)  

---

# RELEASE aluminium-13.06  -  [Wrike](https://www.wrike.com/open.htm?id=1608935049)  

## General:  
#### Fixes:  
* [FIX] Approve own cancellation request in case it is requested by a manager that can approve it after  
* [FIX] Fix notification not being sent correctly to managers when cancellation request is placed  
#### Chores:  
* [CHORE] Add comments made by user when requested cancellation to request  

---

# RELEASE aluminium-13.05  -  [Wrike](https://www.wrike.com/open.htm?id=1608929485)  

## Web application:  
#### Chores:  
* [CHORE] Temporarily hide "approve" button from leave cancellation issue on time-sheet issues listing  -  [Wrike](https://www.wrike.com/open.htm?id=1607991523)  
#### Fixes:  
* [FIX] Fix error on my teams timesheets listing for when viewing as role manager  -  [Wrike](https://www.wrike.com/open.htm?id=1608717131)  

---

# RELEASE aluminium-13.04  -  [Wrike](https://www.wrike.com/open.htm?id=1607974152)  

## External Integrations:  
#### Chores:  
* [CHORE] Make changes to exclusion of work orders on integration to allow range of codes  -  [Wrike](https://www.wrike.com/open.htm?id=1606120301)  

## General:  
#### Fixes:  
* [FIX] Fix issues with workflows that show incorrect data to users on app managers mode, as well as some pages on core  -  [Wrike](https://www.wrike.com/open.htm?id=1607972847)  
* [FIX] Fix sync issue with committed balances calculations in the end of payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1575628955)  

---

# RELEASE aluminium-13.03  -  [Wrike](https://www.wrike.com/open.htm?id=1600188104)  

## Mobile API:  
#### Features:  
* [FEAT] #API Update API services to reflect changes made on leave cancellation workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1590823614)  
* [FEAT] #API Create API service to list leave cancellation requests  -  [Wrike](https://www.wrike.com/open.htm?id=1596654750)  

---

# RELEASE aluminium-13.02  -  [Wrike](https://www.wrike.com/open.htm?id=1599379209)  

## Merges:  
* [MERGE] Merge from [aluminium-12.15](https://www.wrike.com/open.htm?id=1599374920)  

## General:  
#### Fixes:  
* [FIX] Fix issue with relationship not found where all workflows are broken returning a 500 error  

---

# RELEASE aluminium-13.01  -  [Wrike](https://www.wrike.com/open.htm?id=1597688938)  

## Merges:  
* [MERGE] Merge from [aluminium-12.13](https://www.wrike.com/open.htm?id=1597688268)  

---

# RELEASE aluminium-13.00  -  [Wrike](https://www.wrike.com/open.htm?id=1595916662)  

__Base version: [aluminium-12.12](https://www.wrike.com/open.htm?id=1594920755)__  

## General:  
#### Features:  
* [FEAT] Create cancel leave request workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1564927067)  
* [FEAT] Add notifications when leave cancellation requests are approved or declined  -  [Wrike](https://www.wrike.com/open.htm?id=1590979773)  
* [FEAT] Add list of leave cancellation requests as a tab to "My teams other requests" page  -  [Wrike](https://www.wrike.com/open.htm?id=1592192614)  

---
---
---

# RELEASE aluminium-12.17  -  [Wrike](https://www.wrike.com/open.htm?id=1606072215)  

## General:  
#### Fixes:  
* [FIX] Fix issue with 500 error on new instances due to payrun config  

---

# RELEASE aluminium-12.16  -  [Wrike](https://www.wrike.com/open.htm?id=1603185464)  

## General:  
#### Fixes:  
* [FIX] Fix issue with 500 error on new instances due to payrun config  

---

# RELEASE aluminium-12.15  -  [Wrike](https://www.wrike.com/open.htm?id=1599374920)  

## Reports:  
#### Chores:  
* [CHORE] Swap cost codes for accrued excess-time between positive and negative lines on SGSC Keypay batch file  -  [Wrike](https://www.wrike.com/open.htm?id=1599196750)  

## General:  
#### Fixes:  
* [FIX] Fix issue with allowance getting removed when the same allowance type is used twice in the same time-card  -  [Wrike](https://www.wrike.com/open.htm?id=1599192492)  
* [FIX] Fix issues on ad hoc allowance calculations when copying from previous day also the totals in payment view  -  [Wrike](https://www.wrike.com/open.htm?id=1599284508)  

---

# RELEASE aluminium-12.14  -  [Wrike](https://www.wrike.com/open.htm?id=1599265977)  

## Import tools:  
#### Fixes:  
* [FIX] Fix issue with date parsing time when trying to import timesheets  

---

# RELEASE aluminium-12.13  -  [Wrike](https://www.wrike.com/open.htm?id=1597688268)  

## Reports:  
#### Fixes:  
* [FIX] Fix cost code not populating correctly into "job code" column for plant import file for Synergy  

---

# RELEASE aluminium-12.12  -  [Wrike](https://www.wrike.com/open.htm?id=1594920755)  

## General:  
#### Features:  
* [FEAT] Create new penalty condition for blocks starting before or after a defined time  -  [Wrike](https://www.wrike.com/open.htm?id=1585068271)  

---

# RELEASE aluminium-12.11  -  [Wrike](https://www.wrike.com/open.htm?id=1594821160)  

## Authority payroll integration:  
#### Features:  
* [FEAT] Create new integration rule for Authority payroll integration to allow applying splits to leave hours based on user  -  [Wrike](https://www.wrike.com/open.htm?id=1591005412)  

---

# RELEASE aluminium-12.10  -  [Wrike](https://www.wrike.com/open.htm?id=1590991922)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Port Pirie - hard code splits into synergy import file  -  [Wrike](https://www.wrike.com/open.htm?id=1590020761)  
* [HARDCODE] Create copy of reports "Synergy Import File" and "Synergy Plant Import File" for PPC with overrides on job and cost codes  -  [Wrike](https://www.wrike.com/open.htm?id=1587858008)  

---

# RELEASE aluminium-12.09  -  [Wrike](https://www.wrike.com/open.htm?id=1589029534)  

## Reports:  
#### Features:  
* [FEAT] Create new report for Authority batch file (bega version) including columns for plant data  -  [Wrike](https://www.wrike.com/open.htm?id=1569960680)  

---

# RELEASE aluminium-12.08  -  [Wrike](https://www.wrike.com/open.htm?id=1585940944)  

## Reports:  
#### Features:  
* [FEAT] Create new report for batch upload for Plant on Synergy format  -  [Wrike](https://www.wrike.com/open.htm?id=1585024152)  

## General:  
#### Logs:  
* [LOG] Improve logging for a specific exception as there is an error that is not clear in the logs  
* [LOG] Clean logging of an unnecessary entry that is creating noise that hinders the errors tracing  

---

# RELEASE aluminium-12.07  -  [Wrike](https://www.wrike.com/open.htm?id=1579678615)  

## Reports:  
#### Chores:  
* [CHORE] Amend import file and process to allow for plant to be imported with ext ref ID and cost prefix/suffix  -  [Wrike](https://www.wrike.com/open.htm?id=1566692068)  

---

# RELEASE aluminium-12.06  -  [Wrike](https://www.wrike.com/open.htm?id=1578937611)  

## General:  
#### Performance improvements:  
* [PERF] Cache some workflow information to improve timesheet listing page when checking users and managers away in the period  
* [PERF] Cache flag to determine if user is on leave for 5 minutes  
* [PERF] Change order of joins into timesheet config class to prevent unnecessary joins to exist on timesheet listing  
* [PERF] Set specific order clauses for different types of timesheet listings to remove unnecessary joins on select  

## Integrations:  
#### Logs:  
* [LOG] Improve process logging for payrun integration  
#### Fixes:  
* [FIX] Fix issue with cutting leave comments to maximum number of characters - it errors when it tries to cut in the middle of a special character  
* [FIX] Fix implementation of a cache in the payroll integration that was deprecated  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with cutting leave comments to maximum number of characters - it errors when it tries to cut in the middle of a special character  

---

# RELEASE aluminium-12.05  -  [Wrike](https://www.wrike.com/open.htm?id=1577867206)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with incorrect error message re work-order hours shown on time-card  -  [Wrike](https://www.wrike.com/open.htm?id=1577821662)  

## General:  
#### Fixes:  
* [FIX] Fix async issue with work-order types being created and group sets are not always applied (seen on logs)  

## Leave external calendars integration:  
#### Fixes:  
* [FIX] When leave is applied by manager and approved straight away the process to push event to calendar is out of sync what causes errors on the integration (seen on logs)  

---

# RELEASE aluminium-12.04  -  [Wrike](https://www.wrike.com/open.htm?id=1577812155)  

## General:  
#### Fixes:  
* [FIX] Fix error with auto-submit / auto-approve timesheets (seen on logs)  

## Web application:  
#### Fixes:  
* [FIX] Fix 500 error happening when approving timesheets if the modal to add comments is turned ON (seen on logs)  

---

# RELEASE aluminium-12.03  -  [Wrike](https://www.wrike.com/open.htm?id=1576771149)  

## Web application:  
#### Fixes:  
* [REVERT]/[FIX] Fix end time calculation error when applying for full-day leave on flexible shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1575611399)  

---

# RELEASE aluminium-12.02  -  [Wrike](https://www.wrike.com/open.htm?id=1575614945)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue time-card calculations when adding time using start and duration  -  [Wrike](https://www.wrike.com/open.htm?id=1575601809)  
* [FIX] Fix end time calculation error when applying for full-day leave on flexible shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1575611399)  
* [FIX] Fix issue where user settings do not show updated value for new flag to add indirect reports to download reports  -  [Wrike](https://www.wrike.com/open.htm?id=1575613079)  
* [FIX] Fix issue where issues that prevent timesheet to be approved is preventing it to be submit by "skip submit" functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1575614654)  

---

# RELEASE aluminium-12.01  -  [Wrike](https://www.wrike.com/open.htm?id=1575567142)  

## Merges:  
* [MERGE] [aluminium-11.42](https://www.wrike.com/open.htm?id=1575567377)__  

## Web application:  
#### Chores:  
* [CHORE] Update help text for excluding all open timesheets in a payrun to add info about timesheets with requests not approved  -  [Wrike](https://www.wrike.com/open.htm?id=1575429841)  
* [CHORE] Hide button to add an attachment when cancelling a leave request  -  [Wrike](https://www.wrike.com/open.htm?id=1575445267)  
* [CHORE] Move flag "Include staff that do not report directly when downloading reports" to "Other settings" tab under user profile  -  [Wrike](https://www.wrike.com/open.htm?id=1575470215)  
* [CHORE] Remove ability to skip submit a timesheet when the timesheet has submission issues  -  [Wrike](https://www.wrike.com/open.htm?id=1575566089)  

---

# RELEASE aluminium-12.00  -  [Wrike](https://www.wrike.com/open.htm?id=1572655109)  

__Base version: [aluminium-11.41](https://www.wrike.com/open.htm?id=1571711358)__  

## Web application:  
#### Features:  
* [FEAT] Add ability to turn URL into clickable links from comments  -  [Wrike](https://www.wrike.com/open.htm?id=1544726269)  
#### Chores:  
* [CHORE] Remove validation for missing pay location on user profile page  
* [CHORE] Add special public holidays to show in the leave calendars  -  [Wrike](https://www.wrike.com/open.htm?id=1553470930)  
* [CHORE] Amend leave calendar to only show value of leave with maximum of 2 decimal places  -  [Wrike](https://www.wrike.com/open.htm?id=1562348613)  
* [CHORE] Alter message when staff doesn't have a main role for the whole timesheet period  -  [Wrike](https://www.wrike.com/open.htm?id=1533017946)  
* [CHORE] Add ability to apply for duties request direct from the My teams duty calendars and show public holidays  -  [Wrike](https://www.wrike.com/open.htm?id=1562329466)  
* [CHORE] Auto populate / not allow choice if only single value exists at any part of a cost centre string in the drop down  -  [Wrike](https://www.wrike.com/open.htm?id=1523151094)  
* [CHORE] Add totals to plant view in timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1554434282)  
* [CHORE] The info notes should still show on a timesheet after it is approved  -  [Wrike](https://www.wrike.com/open.htm?id=1525768268)  
* [CHORE] Freeze header on the integration modal when scrolling through integration records  -  [Wrike](https://www.wrike.com/open.htm?id=1569881468)  
* [CHORE] Add button 'Back to timesheet details' above the timesheet issue and summary listing on the timesheet approve page  -  [Wrike](https://www.wrike.com/open.htm?id=1566692301)  
#### Fixes:  
* [FIX] Fix bug with leave balances search on leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1539190872)  
* [FIX] Fix error if trying to add an attachment at the same time as cancelling a leave request  -  [Wrike](https://www.wrike.com/open.htm?id=1539173098)  
* [FIX] Fix weird issues happening on workflow modal  -  [Wrike](https://www.wrike.com/open.htm?id=1544992990)  
* [FIX] Fix error where leave request doesn't display on timesheet if booked on a Public Holiday  -  [Wrike](https://www.wrike.com/open.htm?id=1558824272)  

## General:  
#### Chores:  
* [CHORE] Prevent timesheets with unapproved requests from being able to be excluded  -  [Wrike](https://www.wrike.com/open.htm?id=1544991569)  
#### Fixes:  
* [FIX] Fix workflow errors when if payroll return timesheets they can no longer approve or if they are the approver on more than one step they cannot do final approval  -  [Wrike](https://www.wrike.com/open.htm?id=1539188373)  
* [FIX] Fix characters case issue on using tags  -  [Wrike](https://www.wrike.com/open.htm?id=1553552316)  
* [FIX] Fix error when applying for a part day leave across public holidays  -  [Wrike](https://www.wrike.com/open.htm?id=1558824064)  
* [FIX] Fix issue where notification is sent even if notifications are turned off when leave request is cancelled  -  [Wrike](https://www.wrike.com/open.htm?id=1564920216)  

## Reports:  
#### Features:  
* [FEAT] Add flag allowing managers to view indirect reports details within reports  -  [Wrike](https://www.wrike.com/open.htm?id=1500166839)  
* [FEAT] Create a download report that allows us to generate an excel file of the integration log  -  [Wrike](https://www.wrike.com/open.htm?id=1563221869)  

## Libraries:  
* [LIB] Update NPM `caniuse`  

---
---
---

# RELEASE aluminium-11.42  -  [Wrike](https://www.wrike.com/open.htm?id=1575567377)  

## Web application:  
#### Fixes:  
* [FIX] Remove `hacking` error message when editing leave and the shift is not selected (because it was ended, possibly?)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create new API service to populate "view all comments" in a timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1563170865)  
* [FEAT] #API Create new API service to copy records from previous day in a time-card  -  [Wrike](https://www.wrike.com/open.htm?id=1563168559)  

---

# RELEASE aluminium-11.41  -  [Wrike](https://www.wrike.com/open.htm?id=1571711358)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Yorke - Changes to export report for batch upload to put accrued hours at as adjusted value and pass higher/additional duties hour code for ordinary hours  -  [Wrike](https://www.wrike.com/open.htm?id=1570909904)  

---

# RELEASE aluminium-11.40  -  [Wrike](https://www.wrike.com/open.htm?id=1568995565)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Add split to Synergy import file for Port Pirie user  -  [Wrike](https://www.wrike.com/open.htm?id=1566691472)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where "Plant Summary" view does not show if user only records plant against non-standard plant items  -  [Wrike](https://www.wrike.com/open.htm?id=1568966951)  
* [FIX] Fix issue with button to reassign manager into user termination that is trying to submit the form  

---

# RELEASE aluminium-11.39  -  [Wrike](https://www.wrike.com/open.htm?id=1563997100)  

## General:  
#### Fixes:  
* [FIX] Fix issue with PH cost centre when checking holidays from timesheets part of historic roles  -  [Wrike](https://www.wrike.com/open.htm?id=1563179193)  

---

# RELEASE aluminium-11.38  -  [Wrike](https://www.wrike.com/open.htm?id=1563176319)  

## Reports:  
#### Features:  
* [FEAT] Create Authority batch upload (adj accrued) - Bega version  -  [Wrike](https://www.wrike.com/open.htm?id=1562345964)  

---

# RELEASE aluminium-11.37  -  [Wrike](https://www.wrike.com/open.htm?id=1558768492)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Setup splits for SGSC key pay file  -  [Wrike](https://www.wrike.com/open.htm?id=1555222552)  

---

# RELEASE aluminium-11.36  -  [Wrike](https://www.wrike.com/open.htm?id=1556427661)  

## General:  
#### Fixes:  
* [FIX] Fix error on dropdown for role assignments used in several places  -  [Wrike](https://www.wrike.com/open.htm?id=1556419400)  

---

# RELEASE aluminium-11.35  -  [Wrike](https://www.wrike.com/open.htm?id=1555097584)  

## Mobile API:  
#### Fixes:  
* [FIX] #API Fix issue with excess-time ID returned by issue when excess-time is not commented is not valid  -  [Wrike](https://www.wrike.com/open.htm?id=1554300203)  

---

# RELEASE aluminium-11.34  -  [Wrike](https://www.wrike.com/open.htm?id=1554305867)  

## Mobile API:  
#### Fixes:  
* [FIX] #API Fix issue with excess-time ID returned by issue when excess-time is not commented is not valid  -  [Wrike](https://www.wrike.com/open.htm?id=1554300203)  
#### Chores:  
* [CHORE] #API Add flag to list of users API service to filter users that can apply for duty  -  [Wrike](https://www.wrike.com/open.htm?id=1554304656)  

## General:  
#### Fixes:  
* [FIX] Fix user external ID shown in several different places that are not showing the overwritten ID set against role assignment  -  [Wrike](https://www.wrike.com/open.htm?id=1539169979)  

---

# RELEASE aluminium-11.33  -  [Wrike](https://www.wrike.com/open.htm?id=1540003712)  

## Infrastructure:  
#### Chores:  
* [CHORE] Update scripts that run deploy to create dedicated `CMD` instance  

---

# RELEASE aluminium-11.32  -  [Wrike](https://www.wrike.com/open.htm?id=1539169569)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when adding work orders the total duration of time worked don't round correctly  -  [Wrike](https://www.wrike.com/open.htm?id=1537477143)  

## Mobile API:  
#### Chores:  
* [CHORE] #API Add missing information to leave request details service  -  [Wrike](https://www.wrike.com/open.htm?id=1528595944)  

## Reports:  
#### Fixes:  
* [FIX] Make department value required for report "Hours by employee by role by department" as it errors if try to generate without department selected  

---

# RELEASE aluminium-11.31  -  [Wrike](https://www.wrike.com/open.htm?id=1533918924)  

## General:  
#### Fixes:  
* [FIX] Fix issue when changing user from department without work order groups to a department with work order groups, the assignment rules do not apply  -  [Wrike](https://www.wrike.com/open.htm?id=1533899380)  

---

# RELEASE aluminium-11.30  -  [Wrike](https://www.wrike.com/open.htm?id=1533008610)  

## Web application:  
#### Fixes:  
* [FIX] Fix 500 error on timesheet payment view when staff started half way through a payrun period  

## Calendars integration:  
#### Chores:  
* [CHORE] Add "Out of office" status for leave calendar events when pushed to MS  -  [Wrike](https://www.wrike.com/open.htm?id=1522242037)  

---

# RELEASE aluminium-11.29  -  [Wrike](https://www.wrike.com/open.htm?id=1532064979)  

## Console application:  
#### Fixes:  
* [FIX] Some small amendments to prevent timesheets from not being created automatically on the start of the payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1530222853)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where when navigating among timesheets - if trying to access next timesheet it opens the last  -  [Wrike](https://www.wrike.com/open.htm?id=1525761127)  

---

# RELEASE aluminium-11.28  -  [Wrike](https://www.wrike.com/open.htm?id=1520636651)  

## General:  
#### Chores:  
* [CHORE] Remove old work-order assignment set up  -  [Wrike](https://www.wrike.com/open.htm?id=1520574653)  

## Mobile API:  
#### Fixes:  
* [FIX] #API Return flag `canRecordNonStandardWork` that is deprecated but breaks the mobile app if not present in the current versions  
#### Deprecations:  
* [DEPRECATE] #API Mark `canRecordNonStandardWork` as deprecated on response for time-card config service  

## Reports / integration data sources:  
#### Fixes:  
* [FIX] Fix error on payrun details datasource where employee external ID is not returning the correct overwritten value if ordinary hours against work-orders  

## EDRMS integrations:  
#### Fixes:  
* [FIX] Fix issue when posting to EDRMS where if timesheet is turned OFF, then leave requests won't be posted as well  

---

# RELEASE aluminium-11.27  -  [Wrike](https://www.wrike.com/open.htm?id=1527302124)  

## Web application:  
#### Fixes:  
* [FIX] Fix value shown for paid adjusted hours on summary of timesheet project view that deducts accrued hours on a ratio after the adjustment where it should deduct the recorded hours that trigger the accrual  -  [Wrike](https://www.wrike.com/open.htm?id=1524719855)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue where when attaching a new file to a leave request it doesn't finish the related tasks  

---

# RELEASE aluminium-11.26  -  [Wrike](https://www.wrike.com/open.htm?id=1524668728)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create service to list duty requests for a user in a timecard  -  [Wrike](https://www.wrike.com/open.htm?id=1522996539)  
#### Chores:  
* [CHORE] #API Amend parameters of API service that lists own duty requests  -  [Wrike](https://www.wrike.com/open.htm?id=1521406624)  

---

# RELEASE aluminium-11.25  -  [Wrike](https://www.wrike.com/open.htm?id=1523087197)  

## General:  
#### Fixes:  
* [FIX] Fix issue where if there is a public holiday and a timesheet is set to auto-submit it does not actually auto-submit  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-11.24  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Chores:  
* [CHORE] Change indicative leave report and predicative leave report so where leave type assigned to user can no longer be taken then the balance no longer shows on the indicative leave report  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Chores:  
* [CHORE] Show proper error message when trying to remove a leave type from a leave bank when user has used the leave type historically  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Fixes:  
* [FIX] Spelling mistake on My Teams Leave Details page copy  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## General:  
#### Fixes:  
* [FIX] Leave action groups seem to be using wrong date to calculate trigger  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-11.23  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create API services to list outstanding tasks  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Chores:  
* [CHORE] #API Update main GET leave api service to add flags for edit and cancel request  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] #API Add flags for duty requests on different services  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Calendar integrations:  
#### Fixes:  
* [FIX] Fix issue with calendar integration where a full single day of leave is being passed with the same start/end times  -  [Wrike](https://www.wrike.com/open.htm?id=1521505274)  

---

# RELEASE aluminium-11.22  -  [Wrike](https://www.wrike.com/open.htm?id=1511494286)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "SGSC Business central financial costing journal"  -  [Wrike](https://www.wrike.com/open.htm?id=1511477261)  

---

# RELEASE aluminium-11.21  -  [Wrike](https://www.wrike.com/open.htm?id=1511437469)  

## General:  
#### Fixes:  
* [FIX] Fix issue with leave workflow found on logs where for some reason the status was not submitted for existing leave and it was causing an error when system was trying to automatically amend the workflow  
* [FIX] Fix issue with rebuilding workflow for leave requests and duty requests - it was causing them to have an unsubmitted status  

---

# RELEASE aluminium-11.20  -  [Wrike](https://www.wrike.com/open.htm?id=1511402978)  

## Payroll integration:  
#### Chores:  
* [CHORE] Update integration rule that offsets leave hours based on day of the week to allow adding other types of conditions  -  [Wrike](https://www.wrike.com/open.htm?id=1511401946)  

---

# RELEASE aluminium-11.19  -  [Wrike](https://www.wrike.com/open.htm?id=1510523562)  

## Web application:  
#### Fixes:  
* [FIX] Fix error where adding new departments doesn't work anymore  -  [Wrike](https://www.wrike.com/open.htm?id=1510488748)  
* [FIX] Fix error when taking session in a locked screen doesn't actually take the session anymore  

---

# RELEASE aluminium-11.18  -  [Wrike](https://www.wrike.com/open.htm?id=1509737775)  

## Calendars integration:  
#### Fixes:  
* [FIX] Fix error that happened when trying to push a leave calendar event where leave dates were incorrect for full day in some random cases  

---

# RELEASE aluminium-11.17  -  [Wrike](https://www.wrike.com/open.htm?id=1509702726)  

## General:  
#### Fixes:  
* [FIX] Fix exception thrown all the time a current timesheet opened in the app  

---

# RELEASE aluminium-11.16  -  [Wrike](https://www.wrike.com/open.htm?id=1509696894)  

Roll back to 14

---

# RELEASE aluminium-11.15  -  [Wrike](https://www.wrike.com/open.htm?id=1509670942)  

## Console application:  
#### Features:  
* [FEAT] Create command to push leave to external calendar by ID  

## Web application:  
#### Fixes:  
* [FIX] Fix error on dashboard for when users log in and they have no active role  

## General:  
#### Fixes:  
* [FIX] Remove caching from timesheets as it was causing a lot of issues  

---

# RELEASE aluminium-11.14  -  [Wrike](https://www.wrike.com/open.htm?id=1509008225)  

## Web application:  
#### Fixes:  
* [FIX] Fix random errors happening when navigating on user profile pages  

## General:  
#### Fixes:  
* [FIX] Fix issue when terminating a shift assignment where timesheets are still cached causing an in-consistence of data  

---

# RELEASE aluminium-11.13  -  [Wrike](https://www.wrike.com/open.htm?id=1508184696)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create API services to apply for duty requests  -  [Wrike](https://www.wrike.com/open.htm?id=1486403175)  

---

# RELEASE aluminium-11.12  -  [Wrike](https://www.wrike.com/open.htm?id=1508123847)  

## Mobile API:  
#### Fixes:  
* [FIX] #API Fix issue with workflow querying null records  
#### Chores:  
* [CHORE] #API Add flag `canReplace` on return of API service that gets leave request details  

## Web application:  
#### Fixes:  
* [FIX] Fix number of outstanding tasks on my teams other requests page  

---

# RELEASE aluminium-11.11  -  [Wrike](https://www.wrike.com/open.htm?id=1507241576)  

## Integrations:  
#### Features:  
* [FEAT] Add functionality allowing leave requests to be posted to group calendars in Microsoft  -  [Wrike](https://www.wrike.com/open.htm?id=1330254972)  

## Web application:  
#### Chores:  
* [CHORE] Allow searching group names on work-orders table under settings page  -  [Wrike](https://www.wrike.com/open.htm?id=1491585596)  
* [CHORE] Add ability to hide pay rates from dropdown when applying for duties  -  [Wrike](https://www.wrike.com/open.htm?id=1495006967)  
* [CHORE] Update filter buttons on audit dashboard Staff overview table  -  [Wrike](https://www.wrike.com/open.htm?id=1500180438)  

---

# RELEASE aluminium-11.10  -  [Wrike](https://www.wrike.com/open.htm?id=1507306407)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create API services to skip timesheet submission and control if it can be done  -  [Wrike](https://www.wrike.com/open.htm?id=1500208931)  
* [FEAT] #API Create API services and options to recalculate timesheet and flag if there are changes after submission  -  [Wrike](https://www.wrike.com/open.htm?id=1500208066)  
* [FEAT] #API Create API services to get outstanding task details  -  [Wrike](https://www.wrike.com/open.htm?id=1501163731)  
#### Chores:  
* [CHORE] #API Update timesheet summary API service to add flag if user can return timesheet (un-submit)  -  [Wrike](https://www.wrike.com/open.htm?id=1500206248)  
* [CHORE] #API Update timesheet main service to add non-approved leave taken hours  -  [Wrike](https://www.wrike.com/open.htm?id=1501034267)  
* [CHORE] #API Add flag `isOnHigherDuty` for each `time_sheet_days` on timesheet general service response  
* [CHORE] #API Update timesheet summary / issues API service to add flag if individual items can be directly approved  -  [Wrike](https://www.wrike.com/open.htm?id=1501027673)  

---

# RELEASE aluminium-11.09  -  [Wrike](https://www.wrike.com/open.htm?id=1507268836)  

## General:  
#### Fixes:  
* [FIX] Fix cache issue when getting earliest editable timesheet that was approved and returned  

## Mobile API:  
#### Features:  
* [FEAT] #API Create API services to manage duty requests for logged user  -  [Wrike](https://www.wrike.com/open.htm?id=1501164253)  

---

# RELEASE aluminium-11.08  -  [Wrike](https://www.wrike.com/open.htm?id=1502834643)  

## General:  
#### Chores:  
* [CHORE] Update workflow base structure to improve performance when accessing workflow data from API and Core  -  [Wrike](https://www.wrike.com/open.htm?id=1498503847)  
* [CHORE] Improve general performance by caching data that doesn't change  

## Web application:  
#### Fixes:  
* [FIX] Fix issue on my teams leave requests page where when approving a request from the list, the calendar does not update  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with duty workflows when role manager condition is used it doesn't show as outstanding  

## Libraries:  
* [LIB] Update composer libraries  

---

# RELEASE aluminium-11.07  -  [Wrike](https://www.wrike.com/open.htm?id=1503847944)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix error on attachment requirement when returning leave request data  

---

# RELEASE aluminium-11.06  -  [Wrike](https://www.wrike.com/open.htm?id=1502829766)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue - (Remove existing 'Require attachment to support request' fields from leave types)  -  [Wrike](https://www.wrike.com/open.htm?id=1378520104)  
* [FIX] Fix labels and values on task modal  

---

# RELEASE aluminium-11.05  -  [Wrike](https://www.wrike.com/open.htm?id=1500185801)  

## Merges:  
* [MERGE] Merge from [aluminium-10.24](https://www.wrike.com/open.htm?id=1499275649)  
* [MERGE] Merge from [aluminium-10.25](https://www.wrike.com/open.htm?id=1500153158)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues with timesheet comments  -  [Wrike](https://www.wrike.com/open.htm?id=1500161777)  
    * Show button on main timesheet page regardless of status of the timesheet  
    * Fix issues with caching data to show in the comments modal - clear cache if timesheet is returned after final approved  
    * Fix issue on plant lines for link to open the correct time card  
* [FIX] Fix records shown on my Tasks vs My teams Tasks  -  [Wrike](https://www.wrike.com/open.htm?id=1500153862)  

---

# RELEASE aluminium-11.04  -  [Wrike](https://www.wrike.com/open.htm?id=1498488690)  

## Web application:  
#### Features:  
* [FEAT] Create modal to show all comments entered into records related to a timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1464947117)  

## General:  
#### Fixes:  
* [FIX] Fix issue where when creating an action group with some conditions that were not applying as expected  -  [Wrike](https://www.wrike.com/open.htm?id=1496808172)  

---

# RELEASE aluminium-11.03  -  [Wrike](https://www.wrike.com/open.htm?id=1497599448)  

## Merges:  
* [MERGE] Merge from [aluminium-10.23](https://www.wrike.com/open.htm?id=1497538233)  

## Web application:  
#### Features:  
* [FEAT] Create modal to show all leave requests with attachments from the my teams > my teams timesheets > detailed view page  -  [Wrike](https://www.wrike.com/open.htm?id=1475156150)  
* [FEAT] Create modal to show all leave requests with (and without) attachment under leave instances table on my team leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1467536996)  
#### Chores:  
* [CHORE] Allow plant coordinator to access settings page "Manage plant"  -  [Wrike](https://www.wrike.com/open.htm?id=1460528472)  
* [CHORE] Amend all leave listings to show all attachments if multiple instead of just the first added  
* [CHORE] Add link to user summary from leave instances table on my team leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1467536996)  
#### Fixes:  
* [FIX] Fix number format to remove decimal places from whole numbers on leave instances table on my team leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1467536996)  
* [FIX] Fix issue where action groups form doesn't clean if started adding new and cancelled  -  [Wrike](https://www.wrike.com/open.htm?id=1496804375)  
* [FIX] Fix error on editing action group and try to delete a trigger leave type  -  [Wrike](https://www.wrike.com/open.htm?id=1496804687)  
* [FIX] Fix errors when trying to edit an action group that has been already used  -  [Wrike](https://www.wrike.com/open.htm?id=1496807903)  
* [FIX] Fix errors when applying an action group with multiple parallel conditions - it errors when trying to submit request that meet them  -  [Wrike](https://www.wrike.com/open.htm?id=1496806036)  
* [FIX] Fix error when trying to submit leave requests that meet the conditions of an action groups with more than one outcome  -  [Wrike](https://www.wrike.com/open.htm?id=1496806643)  
* [FIX] Fix some wording on the leave action groups form options and descriptions  

---

# RELEASE aluminium-11.02  -  [Wrike](https://www.wrike.com/open.htm?id=1495082998)  

## Merges:  
* [MERGE] Merge from [aluminium-10.21](https://www.wrike.com/open.htm?id=1495027316)  
* [MERGE] Merge from [aluminium-10.22](https://www.wrike.com/open.htm?id=1495054687)  

## Web application:  
#### Chores:  
* [CHORE] Allow open task details in a modal (not related record)  
* [CHORE] Create entries timeline into task details modal  
* [CHORE] Make amendments and improvements on layout for tasks list and modal  

---

# RELEASE aluminium-11.01  -  [Wrike](https://www.wrike.com/open.htm?id=1495013462)  

## Merges:  
* [MERGE] Merge from [aluminium-10.19](https://www.wrike.com/open.htm?id=**********)  
* [MERGE] Merge from [aluminium-10.20](https://www.wrike.com/open.htm?id=1494983458)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where ignored tasks could still be ignored again  
* [FIX] Fix information shown on tasks table where leave request data didn't show  
#### Chores:  
* [CHORE] Improve layout of tasks list  

---

# RELEASE aluminium-11.00  -  [Wrike](https://www.wrike.com/open.htm?id=1492434068)  

__Base version: aluminium-10.18  -  [Wrike](https://www.wrike.com/open.htm?id=**********)__  

## Web application:  
#### Features:  
* [FEAT] Create Leave action groups to manage attachment and other leave action requirements  -  [Wrike](https://www.wrike.com/open.htm?id=1382643224)  
* [FEAT] Create new notification type for leave action group outcomes  -  [Wrike](https://www.wrike.com/open.htm?id=1395135555)  
#### Chores:  
* [CHORE] Remove require comments settings from leave types  -  [Wrike](https://www.wrike.com/open.htm?id=1380166406)  
* [CHORE] Alter timecard to show work-order comments withing records of work  -  [Wrike](https://www.wrike.com/open.htm?id=1464726951)  
* [CHORE] Alter workflow modals to display workflow name and missing roles  -  [Wrike](https://www.wrike.com/open.htm?id=1425151854)  
* [CHORE] Update workflow model to display gaps within workflow steps if they cannot be completed  -  [Wrike](https://www.wrike.com/open.htm?id=1425154415)  
#### Fixes:  
* [FIX] Fix issue with status not updating for leave requests when requests are approved in calendar view  -  [Wrike](https://www.wrike.com/open.htm?id=1465708631)  

---
---
---

# RELEASE aluminium-10.25  -  [Wrike](https://www.wrike.com/open.htm?id=1500153158)  

## Web application:  
#### Fixes:  
* [FIX] Fix filter on my teams timesheets page  -  [Wrike](https://www.wrike.com/open.htm?id=1500150237)  

---

# RELEASE aluminium-10.24  -  [Wrike](https://www.wrike.com/open.htm?id=1499275649)  

## General:  
#### Fixes:  
* [FIX] Fix performance of services that read data from work-order types to be used on time-cards for core and mobile app  

---

# RELEASE aluminium-10.23  -  [Wrike](https://www.wrike.com/open.htm?id=1497538233)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue where B.I. users cannot select the right options when filtering reports  -  [Wrike](https://www.wrike.com/open.htm?id=1481730273)  

## General:  
#### Chores:  
* [CHORE] Add validation to custom comment fields so if subject added then content must also be added  -  [Wrike](https://www.wrike.com/open.htm?id=1469253536)  

---

# RELEASE aluminium-10.22  -  [Wrike](https://www.wrike.com/open.htm?id=1495054687)  

## General:  
#### Fixes:  
* [FIX] Fix error on workflow query builder where when searching for records waiting for my approval and only on active step it returned all outstanding records from whole organisation if logged user didn't match any workflow condition  

---

# RELEASE aluminium-10.21  -  [Wrike](https://www.wrike.com/open.htm?id=1495027316)  

## Mobile API:  
#### Fixes:  
* [FIX] #API Fix error on workflow response where an exception is thrown when `canUnSubmit` is `false`  
* [FIX] #API Fix error on accessing records that users as manager have visibility but cannot make any workflow interactions  

---

# RELEASE aluminium-10.20  -  [Wrike](https://www.wrike.com/open.htm?id=1494983458)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create new service for managers to return a timesheet to owner  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FEAT] #API Create new service for managers to send a notification to a timesheet owner  -  [Wrike](https://www.wrike.com/open.htm?id=1490725212)  
#### Chores:  
* [CHORE] #API Add flag to response of workflow main service to check if user can unSubmit related record  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Fixes:  
* [FIX] #API Fix error on response of leave requests where attachment requirement on approval is using the wrong value and it throws an exception  

---

# RELEASE aluminium-10.19  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## General:  
#### Fixes:  
* [FIX] Fix error with balances calculations when more than one leave type is assigned to the same bank and both accrue balances  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-10.18  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue on managers and payroll officers dashboards where users with historic roles and departments still show the ended department association  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Fix issue on schedule listing when searching schedules by department it shows users that are not related to a department anymore  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Hardcode:  
* [HARDCODE] NGSC - Remove GL splits from keypay file  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-10.17  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix sync issue on page "Setting" > "Leave / RDO" where custom contents for excess-time as leave doesn't always show  

## Mobile API:  
#### Chores:  
* [CHORE] #API Update API services to apply for leave to allow to apply on behalf as manager  -  [Wrike](https://www.wrike.com/open.htm?id=1486408682)  
* [CHORE] #API Update issues format for timesheet approve summary  -  [Wrike](https://www.wrike.com/open.htm?id=1486408246)  
* [CHORE] Added flag `canSendReminderSubmission` to timesheet approve summary api service  

---

# RELEASE aluminium-10.16  -  [Wrike](https://www.wrike.com/open.htm?id=1486398723)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with timesheet submit issues format that were getting core format instead of specific for API  
#### Chores:  
* [CHORE] Update timesheet approve issues (for managers mode) to use API specific format  

---

# RELEASE aluminium-10.15  -  [Wrike](https://www.wrike.com/open.htm?id=1482396187)  

## General:  
#### Fixes:  
* [FIX] Fix error where attachments are being removed when the leave request is split between payruns  -  [Wrike](https://www.wrike.com/open.htm?id=1481634817)  

---

# RELEASE aluminium-10.14  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api service to list worked hours for a timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Chores:  
* [CHORE] #API Update timesheet submit summary API service to add info for approval summary page  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] #API Update value of `isOpen` into timecard api service to take managers into account  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-10.13  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Mobile API:  
#### Chores:  
* [CHORE] #API Add non-standard plant items to allow to be used to record time on timer or timesheet  

## Web application:  
#### Chores:  
* [CHORE] If a user does not have any plant item assigned but has access to non-standard plant, then the list of plant items shows non-standard straight away  

---

# RELEASE aluminium-10.12  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix error with managers still seeing staff who use to report to them  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-10.11  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Mobile API:  
#### Chores:  
* [UNDO] - [CHORE] #API Add non-standard plant items to allow to be used to record time on timer or timesheet  

---

# RELEASE aluminium-10.10  -  [Wrike](https://www.wrike.com/open.htm?id=1475889582)  

## Mobile API:  
#### Chores:  
* [CHORE] #API Add non-standard plant items to allow to be used to record time on timer or timesheet  

## Web application:  
#### Chores:  
* [CHORE] If a user does not have any plant item assigned but has access to non-standard plant, then the list of plant items shows non-standard straight away  

---

# RELEASE aluminium-10.09  -  [Wrike](https://www.wrike.com/open.htm?id=1475855466)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api services to cancel a duty request  -  [Wrike](https://www.wrike.com/open.htm?id=1462790553)  
* [FEAT] #API Create api services to attach a new file to a duty request  -  [Wrike](https://www.wrike.com/open.htm?id=1462786181)  
* [FEAT] #API Create API services to list timesheets for a manager  -  [Wrike](https://www.wrike.com/open.htm?id=1410621461)  
#### Chores:  
* [CHORE] #API Update main timesheet api service to add new flags for manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1473463738)  
* [CHORE] #API Update api services for manager mode to remove records that are not outstanding from outstanding list  -  [Wrike](https://www.wrike.com/open.htm?id=1462841436)  

---

# RELEASE aluminium-10.08  -  [Wrike](https://www.wrike.com/open.htm?id=1470258973)  

## General:  
#### Fixes:  
* [FIX] Fix issue where staff manager cannot submit a HD request on behalf of one of their direct reports if they are not part of the workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1467667688)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create API services to manage timesheet workflow approvals  -  [Wrike](https://www.wrike.com/open.htm?id=1410624085)  
#### Chores:  
* [CHORE] #API Update timesheet details API services to allow them to be accessed by workflow managers  -  [Wrike](https://www.wrike.com/open.htm?id=1410622548)  
* [CHORE] #API Update time-card details API services to allow them to be accessed by workflow managers  -  [Wrike](https://www.wrike.com/open.htm?id=1410625729)  

## Reports:  
#### Features:  
* [FEAT] Create new report for managers with all comments within a payrun period  -  [Wrike](https://www.wrike.com/open.htm?id=1454792019)  
#### Chores:  
* [CHORE] Replace the 'value' of penalties with the quantity of penalties (number of hours) in the detailed check file  -  [Wrike](https://www.wrike.com/open.htm?id=1460595267)  

---

# RELEASE aluminium-10.07  -  [Wrike](https://www.wrike.com/open.htm?id=1466585236)  

## General:  
#### Libraries:  
* [LIB] Add support to `sweet-enum` into elementTIME model config classes  

## Payroll integrations:  
#### Chores:  
* [CHORE] Update format for date values passed to Altitude from `Iso8601` to custom format without timezone  -  [Wrike](https://www.wrike.com/open.htm?id=1464759193)  

---

# RELEASE aluminium-10.06  -  [Wrike](https://www.wrike.com/open.htm?id=1466574130)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Add manual hour codes to accrued excess-time based on rules to SGSC keypay CSV file  -  [Wrike](https://www.wrike.com/open.htm?id=1463946445)  
#### Chores:  
* [CHORE] Round values to 2 decimal places on SGSC keypay file and remove rows where rounded value is zero  

---

# RELEASE aluminium-10.05  -  [Wrike](https://www.wrike.com/open.htm?id=1464648058)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api services to list excess-time items on manage mode  -  [Wrike](https://www.wrike.com/open.htm?id=1453820789)  
* [FEAT] #API Create api services to get excess-time item details on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1453821476)  
* [FEAT] #API Create api services to approve/decline/cancel excess-time items on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1453822215)  
* [FEAT] #API Create api services to switch excess-time item to primary/secondary  -  [Wrike](https://www.wrike.com/open.htm?id=1453821957)  

## Authority payroll integration:  
#### Features:  
* [FEAT] Add option to integration rule for accrued excess-time as positive/negative - to pass negative lines with normal rate (hot higher) in case of higher duty  -  [Wrike](https://www.wrike.com/open.htm?id=1464646473)  

---

# RELEASE aluminium-10.04  -  [Wrike](https://www.wrike.com/open.htm?id=1461667129)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api services to approve and decline duty requests on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1426634307)  
* [FEAT] #API Create API services to allow managers to view workflows information  -  [Wrike](https://www.wrike.com/open.htm?id=1437144142)  

## General:  
#### Chores:  
* [CHORE] Update leave assignment structure and statements to remove soft deletes to prevent weird errors to deleted things  -  [Wrike](https://www.wrike.com/open.htm?id=1410731134)  

---

# RELEASE aluminium-10.03  -  [Wrike](https://www.wrike.com/open.htm?id=1461493095)  

## Reports:  
#### Chores:  
* [CHORE] Add additional field values to Predictive Leave Report  -  [Wrike](https://www.wrike.com/open.htm?id=1459266015)  

---

# RELEASE aluminium-10.02  -  [Wrike](https://www.wrike.com/open.htm?id=1460514353)  

## General:  
#### Libraries:  
* [LIB] Update node `caniuse` browser list  
* [LIB] Update composer libraries minor versions  

---

# RELEASE aluminium-10.01  -  [Wrike](https://www.wrike.com/open.htm?id=1460453706)  

## Web application:  
#### Chores:  
* [CHORE] Undo dropping leave attachment requirements from leave types  -  [Wrike](https://www.wrike.com/open.htm?id=1460351203)  
#### Fixes:  
* [FIX] Fix issues on un-submitting timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1460430150)  
* [FIX] Fix issue where complete missing hours is not allowed if it goes into negative balance - even it's allowed by the type  -  [Wrike](https://www.wrike.com/open.htm?id=1460436776)  
* [FIX] Fix issue where leave requests still not approved are causing some miscalculation and misinformation when completing missing hours  -  [Wrike](https://www.wrike.com/open.htm?id=1460447991)  
* [FIX] Fix issue with not rounded value shown when completing missing hours  -  [Wrike](https://www.wrike.com/open.htm?id=1460449788)  

---

# RELEASE aluminium-10.00  -  [Wrike](https://www.wrike.com/open.htm?id=1455902526)  

__Base version: aluminium-9.56  -  [Wrike](https://www.wrike.com/open.htm?id=1455852111)__  

## Web application:  
#### Features:  
* [FEAT] Make changes to the My team leave details page including new table to show instances  -  [Wrike](https://www.wrike.com/open.htm?id=1380190574)  
#### Chores:  
* [CHORE] Alter dashboard on My Team > My teams leave details and shift calendar  -  [Wrike](https://www.wrike.com/open.htm?id=1379975237)  
* [CHORE] Remove existing 'Require attachment to support request' fields from leave types  -  [Wrike](https://www.wrike.com/open.htm?id=1378520104)  
* [CHORE] Remove notifications section from leave types  -  [Wrike](https://www.wrike.com/open.htm?id=1380163219)  
* [CHORE] Separate out workflows and notifications / reminders settings into two separate pages  -  [Wrike](https://www.wrike.com/open.htm?id=1384052501)  
* [CHORE] Remove fields with errors on Payrun > Payrun processing page  -  [Wrike](https://www.wrike.com/open.htm?id=1397359178)  
* [CHORE] Stop terminated users from being able to log in into elementTIME  -  [Wrike](https://www.wrike.com/open.htm?id=1384055541)  
* [CHORE] Ability to manager and payroll to add attachments on behalf to existing leave requests prior to approval  -  [Wrike](https://www.wrike.com/open.htm?id=1384055061)  
* [CHORE] Add ability to navigate from payrun to payrun while in individual plantsheet  -  [Wrike](https://www.wrike.com/open.htm?id=1384050830)  
* [CHORE] Hide inactive leave assignments from users dashboard  -  [Wrike](https://www.wrike.com/open.htm?id=1410731134)  
* [CHORE] Remove 'Approve all waiting' button from my teams timesheets page if payrun is finished  -  [Wrike](https://www.wrike.com/open.htm?id=1397355921)  
* [CHORE] Stop users from applying for own duty requests if flag is not true  -  [Wrike](https://www.wrike.com/open.htm?id=1410736886)  
* [CHORE] Update balances shown on leave types when completing missing hours in a timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1426712210)  
#### Fixes:  
* [FIX] Fix issue with dashboard that breaks if users is an external users and has no direct reports  -  [Wrike](https://www.wrike.com/open.htm?id=1287361113)  
* [FIX] If plant field is disabled but the plant field has flag of required then plant costing will not save  -  [Wrike](https://www.wrike.com/open.htm?id=1384056025)  
* [FIX] Fix error with higher duties pay rate shows as custom value even when not custom value  -  [Wrike](https://www.wrike.com/open.htm?id=1447503286)  

## Reports:  
#### Chores:  
* [CHORE] Make change to the daily costing report to show name of work-order details and task details  -  [Wrike](https://www.wrike.com/open.htm?id=1407509412)  

## General:  
#### Features:  
* [FEAT] Add flag to control if leave/RDO type can be used to complete missing hours in timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1412398906)  
#### Chores:  
* [CHORE] Amend recall process for timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1384051003)  

---
---
---

# RELEASE aluminium-9.56  -  [Wrike](https://www.wrike.com/open.htm?id=1455852111)  

## Web application:  
#### Chores:  
* [CHORE] Alter the list of departments on dashboard to only show active department names  -  [Wrike](https://www.wrike.com/open.htm?id=1453749881)  
#### Fixes:  
* [FIX] Fix error where RDO does not calculate when timesheet auto-completes, auto-submits and is auto-approved  -  [Wrike](https://www.wrike.com/open.htm?id=1446642147)  

---

# RELEASE aluminium-9.55  -  [Wrike](https://www.wrike.com/open.htm?id=1454724337)  

## Import tools:  
#### Features:  
* [FEAT] Create timesheets import from file  -  [Wrike](https://www.wrike.com/open.htm?id=1426677505)  

---

# RELEASE aluminium-9.54  -  [Wrike](https://www.wrike.com/open.htm?id=1453749668)  

## General:  
#### Fixes:  
* [FIX] Fix issue with excess-time calculations - rules order are being applied wrongly  -  [Wrike](https://www.wrike.com/open.htm?id=1452804917)  

## Console application:  
#### Chores:  
* [CHORE] Add option to command that recalculates all open timesheets to run sync/async  

---

# RELEASE aluminium-9.53  -  [Wrike](https://www.wrike.com/open.htm?id=1453660173)  

## General:  
#### Fixes:  
* [FIX] Fix rounding issue when checking missing hours in a timesheet with tolerance applied  -  [Wrike](https://www.wrike.com/open.htm?id=1446642084)  

## Reports:  
#### Features:  
* [FEAT] Create new report to list exceptions by payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1363909883)  

## Console application:  
#### Features:  
* [FEAT] Create new command to bulk change assignments  

---

# RELEASE aluminium-9.52  -  [Wrike](https://www.wrike.com/open.htm?id=1447446861)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api services to manage leave details page on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1383200398)  
* [FEAT] #API Create api services to list duty requests on manage mode  -  [Wrike](https://www.wrike.com/open.htm?id=1426629799)  
* [FEAT] #API Create api services to view duty requests details on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1426631160)  

## General:  
#### Fixes:  
* [FIX] Fix issue found on log on automatic actions when updating issues from a timesheet asynchronously  

---

# RELEASE aluminium-9.51  -  [Wrike](https://www.wrike.com/open.htm?id=1437140796)  

## Web application:  
#### Fixes:  
* [FIX] Fix label for new field for auto submit / approve timesheets under payrun settings  

---

# RELEASE aluminium-9.50  -  [Wrike](https://www.wrike.com/open.htm?id=1437124733)  

## General:  
#### Features:  
* [FEAT] Create dynamic controller for auto submit / approve timesheets day  -  [Wrike](https://www.wrike.com/open.htm?id=1425127015)  

---

# RELEASE aluminium-9.49  -  [Wrike](https://www.wrike.com/open.htm?id=1425158517)  

## General:  
#### Fixes:  
* [FIX] Fix error on excess time costing when multiple blocks on the same day are calculated or the wrong order  -  [Wrike](https://www.wrike.com/open.htm?id=1408282604)  
* [FIX] Fix excess-time that is allowing to cost against public holiday hours  -  [Wrike](https://www.wrike.com/open.htm?id=1425156451)  

---

# RELEASE aluminium-9.48  -  [Wrike](https://www.wrike.com/open.htm?id=1397357092)  

## General:  
#### Features:  
* [FEAT] Create new workflow condition for timesheets to trigger based on additional hours  -  [Wrike](https://www.wrike.com/open.htm?id=1378167541)  

## Mobile API:  
#### Features:  
* [FEAT] Create API service to get general data for managers dashboard  -  [Wrike](https://www.wrike.com/open.htm?id=1380710499)  
* [FEAT] #API Create new api services to manage leave requests on manager mode  -  [Wrike](https://www.wrike.com/open.htm?id=1383199058)  

# RELEASE aluminium-9.47  -  [Wrike](https://www.wrike.com/open.htm?id=1396591783)  

## Mobile API:  
#### Chores:  
* [CHORE] Make some updates on mobile API docs and formats to follow application patterns  

---

# RELEASE aluminium-9.46  -  [Wrike](https://www.wrike.com/open.htm?id=1396548294)  

## Web application:  
#### Chores:  
* [CHORE] Temporarily hide work order groups from work orders table  -  [Wrike](https://www.wrike.com/open.htm?id=1395201347)  
#### Fixes:  
* [FIX] Fix "Not found ID" error on time-card when editing role / project / time-type of existing record if it has ad hoc allowances related  -  [Wrike](https://www.wrike.com/open.htm?id=1342942681)  

---

# RELEASE aluminium-9.45  -  [Wrike](https://www.wrike.com/open.htm?id=1395961218)  

## Mobile API:  
#### Features:  
* [FEAT] #API Create api services to control leave calendars on manage mode  -  [Wrike](https://www.wrike.com/open.htm?id=1383201620)  

## Reports:  
#### Fixes:  
* [FIX] Fix the login method field into staff summary report  -  [Wrike](https://www.wrike.com/open.htm?id=1395178701)  

## General:  
#### Chores:  
* [CHORE] Improve the capture of login methods - some cases had some missing options  

---

# RELEASE aluminium-9.44  -  [Wrike](https://www.wrike.com/open.htm?id=1383293033)  

## General:  
#### Fixes:  
* [FIX] Fix error on costing excess time, when one block of time has two triggers for excess time  -  [Wrike](https://www.wrike.com/open.htm?id=1380687943)  

---

# RELEASE aluminium-9.43  -  [Wrike](https://www.wrike.com/open.htm?id=1380711540)  

## Mobile API:  
#### Features:  
* [FEAT] Create work-order groups service  -  [Wrike](https://www.wrike.com/open.htm?id=1372374365)  
* [FEAT] Create time card leave API services  -  [Wrike](https://www.wrike.com/open.htm?id=1372373401)  
* [FEAT] Create base structure for managers API services  -  [Wrike](https://www.wrike.com/open.htm?id=1380630154)  
    * [FEAT] Create service to list available modes for logged user (if available and number of outstanding tasks for each mode)  -  [Wrike](https://www.wrike.com/open.htm?id=1380630522)  
    * [FEAT] Create manager base path for services (protected by new middleware)  -  [Wrike](https://www.wrike.com/open.htm?id=1380630820)  
#### Chores:  
* [CHORE] Alter personal dashboard URI (old one is deprecated)  -  [Wrike](https://www.wrike.com/open.htm?id=1380680074)  

## Task scheduling:  
#### Chores:  
* [CHORE] Update time of scheduled tasks to spread some of the heaviest ones, also bring the morning ones a bit earlier as they were affecting usage  

---

# RELEASE aluminium-9.42  -  [Wrike](https://www.wrike.com/open.htm?id=1379056123)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where recalculating timesheet returns workflow for period excess-time  -  [Wrike](https://www.wrike.com/open.htm?id=1378269906)  

## General:  
#### Fixes:  
* [FIX] Fix issue with breaks trying to calculate on public holidays and duration-only shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1379045888)  

---

# RELEASE aluminium-9.41  -  [Wrike](https://www.wrike.com/open.htm?id=1378228495)  

## Data-sources:  
#### Fixes:  
* [FIX] Fix excess-time grouping issue with payrun details data-source  -  [Wrike](https://www.wrike.com/open.htm?id=1378206310)  

## General:  
#### Fixes:  
* [FIX] Fix error when calculating excess-time where hours are not coded correctly when multiple blocks on the same day are recorded on different work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1378217454)  
* [FIX] Fix error where breaks are not saving for when end time of hours recorded is 00:00  -  [Wrike](https://www.wrike.com/open.htm?id=1360937128)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Update SHRCC batch file report to remove accrued hours completely  -  [Wrike](https://www.wrike.com/open.htm?id=1366509522)  

---

# RELEASE aluminium-9.40  -  [Wrike](https://www.wrike.com/open.htm?id=1376262869)  

## Reports:  
#### Chores:  
* [CHORE] Amend Yorke batch file report to get only project's or work-order's top level external ID (not concatenate with activity) as hour code  -  [Wrike](https://www.wrike.com/open.htm?id=1369676232)  

---

# RELEASE aluminium-9.39  -  [Wrike](https://www.wrike.com/open.htm?id=1375444328)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with ad hoc allowances when added via API directly to time-card they don't calculate values  -  [Wrike](https://www.wrike.com/open.htm?id=1374618591)  

---

# RELEASE aluminium-9.38  -  [Wrike](https://www.wrike.com/open.htm?id=1371644399)  

## General:  
#### Fixes:  
* [FIX] Fix issue with work-orders caching when listing them into time-cards  

---

# RELEASE aluminium-9.37  -  [Wrike](https://www.wrike.com/open.htm?id=1369560961)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue on time-card where it's too slow to load work-orders and groups  
* [FIX] Fix slowness issue with work-order groups listing on user profile  
#### Cache:  
* [CACHE] Add 15 minutes cache on work-orders availability listing per user/role  

---

# RELEASE aluminium-9.36  -  [Wrike](https://www.wrike.com/open.htm?id=1369021460)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue on work-order groups listing that was very slow for items with many work-orders related  

---

# RELEASE aluminium-9.35  -  [Wrike](https://www.wrike.com/open.htm?id=1368948042)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue where SSO on API v3 was returning using deprecated API v2 - causing a 500 error  

---

# RELEASE aluminium-9.34  -  [Wrike](https://www.wrike.com/open.htm?id=1368166302)  

## Importing:  
#### Fixes:  
* [FIX] Fix column name for RDO importing  

## Reports:  
#### Chores:  
* [CHORE] Amend yorke batch file to also include unpaid excess time  -  [Wrike](https://www.wrike.com/open.htm?id=1366485442)  
* [CHORE] Add leave at half pay to SGSC batch file download  -  [Wrike](https://www.wrike.com/open.htm?id=1366476776)  
#### Features:  
* [FEAT] Create Swan Hill batch file to exclude all accrued hours earnt (negative line)  -  [Wrike](https://www.wrike.com/open.htm?id=1366509522)  

## Mobile API:  
* [DEPRECATE] Remove API v2 (old app's API)  -  [Wrike](https://www.wrike.com/open.htm?id=1354662200)  

## Web application:  
#### Chores:  
* [CHORE] Update work-orders to classifications may be added/removed to existing and used records  -  [Wrike](https://www.wrike.com/open.htm?id=1366472835)  
#### Fixes:  
* [FIX] Fix issue where some work-orders displays as not being linked to a group even though they are  -  [Wrike](https://www.wrike.com/open.htm?id=1353865912)  

---

# RELEASE aluminium-9.33  -  [Wrike](https://www.wrike.com/open.htm?id=1364715761)  

## Importing:  
#### Chores:  
* [CHORE] Update leave import tool to accept external IDs for leave types and RDO types  

---

# RELEASE aluminium-9.32  -  [Wrike](https://www.wrike.com/open.htm?id=1363929358)  

## Web application:  
#### Chores:  
* [CHORE] Remove link to view manager profile on user view profile page  -  [Wrike](https://www.wrike.com/open.htm?id=1363908421)  
* [CHORE] Remove eNPS info from view profile page if logged user is not payroll officer  

---

# RELEASE aluminium-9.31  -  [Wrike](https://www.wrike.com/open.htm?id=1361598213)  

## Mobile API:  
#### Chores:  
* [CHORE] Update timesheet service to return issues flags on days only if the day has daily requirements  

---

# RELEASE aluminium-9.30  -  [Wrike](https://www.wrike.com/open.htm?id=1360916702)  

## Reports:  
#### Fixes:  
* [FIX] Fix report "Authority batch upload File (adj actual) - SGSC version" to pass actual adjusted hours for accrued excess-time instead of final adjusted  -  [Wrike](https://www.wrike.com/open.htm?id=1337291793)  
#### Features:  
* [FEAT] Create new version of batch upload report for Yorke  -  [Wrike](https://www.wrike.com/open.htm?id=1360911940)  

## General:  
#### Fixes:  
* [FIX] Fix error on filtering work-order group assignment rules that was searching for a field that doesn't exist  

---

# RELEASE aluminium-9.29  -  [Wrike](https://www.wrike.com/open.htm?id=1356279257)  

## Web application:  
#### Chores:  
* [CHORE] Force managers to re-calculate auto allowances when they are approving a timesheet if changes have been made after submission  -  [Wrike](https://www.wrike.com/open.htm?id=1342947011)  
#### Fixes:  
* [FIX] Fix issue where submit and skip submit does not re-trigger workflow in some occasions  -  [Wrike](https://www.wrike.com/open.htm?id=1354660269)  

---

# RELEASE aluminium-9.28  -  [Wrike](https://www.wrike.com/open.htm?id=1354713610)  

## Web application:  
#### Fixes:  
* [FIX] Fix reporting hierarchy that is broken  -  [Wrike](https://www.wrike.com/open.htm?id=1352375624)  
* [FIX] Fix issue with plant items when made inactive are still available for users  -  [Wrike](https://www.wrike.com/open.htm?id=1267100734)  
* [FIX] Fix error with breaks not being correctly placed when a part day's leave request replaces recorded hours in the day  -  [Wrike](https://www.wrike.com/open.htm?id=1342943435)  
* [FIX] Fix issue where department managers of old roles remain in workflow when role is ended  -  [Wrike](https://www.wrike.com/open.htm?id=1353867080)  
#### Chores:  
* [CHORE] Business Intelligence access - users with this access need to be able to report on the whole organisation  -  [Wrike](https://www.wrike.com/open.htm?id=1339469480)  
    * Allow BI users to vew full organisation staff reporting hierarchy  
    * Allow BI users to view any department dashboard  

## Reports:  
#### Fixes:  
* [FIX] Fix some caching errors on reports when running with different parameters  
#### Chores:  
* [CHORE] Allow BI users to select any department when generating reports based on departments  -  [Wrike](https://www.wrike.com/open.htm?id=1339469480)  

---

# RELEASE aluminium-9.27  -  [Wrike](https://www.wrike.com/open.htm?id=1353840444)  

## General:  
#### Fixes:  
* [FIX] Fix rounding problem on period excess-time calculation when the difference was in the 5th decimal place  

---

# RELEASE aluminium-9.26  -  [Wrike](https://www.wrike.com/open.htm?id=1351857820)  

## Mobile API:  
#### Chores:  
* [CHORE] #API Add public holidays to API service `GET /api/v3/department/{id}/leave-calendar` that returns leave info for a department and a given period  -  [Wrike](https://www.wrike.com/open.htm?id=1351837510)  

---

# RELEASE aluminium-9.25  -  [Wrike](https://www.wrike.com/open.htm?id=1351330977)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Create specific version of report "Authority batch upload File (adj actual)" for SGSC  -  [Wrike](https://www.wrike.com/open.htm?id=1337291793)  

---

# RELEASE aluminium-9.24  -  [Wrike](https://www.wrike.com/open.htm?id=1342859276)  

## Mobile API:  
#### Chores:  
* [CHORE] Update timesheet detailed API service to filter some issues on daily issues flags  

---

# RELEASE aluminium-9.23  -  [Wrike](https://www.wrike.com/open.htm?id=1341186117)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "Predicative leave balances"  -  [Wrike](https://www.wrike.com/open.htm?id=1339537571)  

---

# RELEASE aluminium-9.22  -  [Wrike](https://www.wrike.com/open.htm?id=1336331586)  

## Integrations:  
#### Chores:  
* [CHORE] Update module that runs work-order integrations for 7.1+ to run for 7.0.40+  

---

# RELEASE aluminium-9.21  -  [Wrike](https://www.wrike.com/open.htm?id=1335496403)  

## General:  
#### Libraries:  
* [LIB] Update composer libraries  
#### Fixes:  
* [FIX] Fix issue where when skipping submission of a timesheet with non-payroll officer users the auto-allowance doesn't recalculate as expected  -  [Wrike](https://www.wrike.com/open.htm?id=1335495718)  

---

# RELEASE aluminium-9.20  -  [Wrike](https://www.wrike.com/open.htm?id=1330263519)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Add splits for users "670 Danielle Bone" and "680 Katie Harrowfield" from PPC on Synergy import file (CSV)  -  [Wrike](https://www.wrike.com/open.htm?id=1318354794)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with incorrect end date for allowances on 'info notes' under submit timesheet page  -  [Wrike](https://www.wrike.com/open.htm?id=1330248161)  

## Dev env:  
#### Chores:  
* [CHORE] Remove version from `docker-compose.yml` file as it's now marked as obsolete (Note: it was not being used for a long time already)  

---

# RELEASE aluminium-9.19  -  [Wrike](https://www.wrike.com/open.htm?id=1329434209)  

## Mobile API:  
#### Chores:  
* [CHORE] Update services for leave calendar to have a service to list days and leave counts and another for all request details in a given day  

---

# RELEASE aluminium-9.18  -  [Wrike](https://www.wrike.com/open.htm?id=1328061093)  

## Web application:  
#### Fixes:  
* [FIX] Fix navigation errors between teams timesheets page and users timesheets list page  -  [Wrike](https://www.wrike.com/open.htm?id=1326575788)  

## Mobile API:  
#### Chores:  
* [CHORE] Update URLs for department listing and department leave calendars services  

---

# RELEASE aluminium-9.17  -  [Wrike](https://www.wrike.com/open.htm?id=1327360963)  

## Mobile API:  
#### Features:  
* [FEAT] #API - Add eNPS functionality to timesheet services  -  [Wrike](https://www.wrike.com/open.htm?id=1326570596)  
* [FEAT] #API - Create new services for displaying a leave calendar  -  [Wrike](https://www.wrike.com/open.htm?id=1319154315)  
    * `/api/v3/department/list` to list all department that logged user has access to for given purpose  
    * `/api/v3/department/{id}/leave-calendar/list` to list leave requests of a single department  
    * `/api/v3/department/all/leave-calendar/list` to list leave requests of a all departments user has access to leave calendars  
#### Chores:  
* [CHORE] #API - Update daily issues and records  -  [Wrike](https://www.wrike.com/open.htm?id=1326571272)  
* [CHORE] #API - Add info for excess-time comment required issues  -  [Wrike](https://www.wrike.com/open.htm?id=1325608722)  
* [CHORE] #API - Add expected hours of work on time-card config service  -  [Wrike](https://www.wrike.com/open.htm?id=1314993053)  

---

# RELEASE aluminium-9.16  -  [Wrike](https://www.wrike.com/open.htm?id=1319195244)  

## General:  
#### Chores:  
* [CHORE] Remove skip option from final step in timesheet workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1314927721)  
* [CHORE] Adjust workflow so managers are forced to 'skip submit' before approving  -  [Wrike](https://www.wrike.com/open.htm?id=1314992305)  
* [CHORE] Change the way leave accruals are calculated when staff is not full time  -  [Wrike](https://www.wrike.com/open.htm?id=1315023693)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix flag that allows toggling primary/secondary rules for excess-time records  
* [FIX] Fix some issues with switching records back from secondary to primary  

---

# RELEASE aluminium-9.15  -  [Wrike](https://www.wrike.com/open.htm?id=1325454438)  

## Mobile API:
#### Documentation:
* [DOC] Add enum options for field `type` on excess-time schema

## General:
#### Fixes:
* [FIX] Fix issue with calculating excess-time when manually amending period excess-time and changing costing to work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1324804089)

---

# RELEASE aluminium-9.14  -  [Wrike](https://www.wrike.com/open.htm?id=1320991165)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating penalties based on rule that checks if hours are continuous when there is a break it in some cases ignores the break  -  [Wrike](https://www.wrike.com/open.htm?id=1293717003)  
* [FIX] Fix issue with calculating penalties when time-block has breaks and it ends at midnight - it duplicates the penalty hours  -  [Wrike](https://www.wrike.com/open.htm?id=1293717003)  

---

# RELEASE aluminium-9.13  -  [Wrike](https://www.wrike.com/open.htm?id=1320932480)  

## Mobile API:  
#### Features:  
* [FEAT] Create new services for excess-time on mobile API  -  [Wrike](https://www.wrike.com/open.htm?id=1314992945)  
    * `GET /api/v3/time-sheet/{id}/excess-time/summary`  
    * `GET /api/v3/time-sheet/{id}/excess-time/items`  
    * `GET /api/v3/excess-time/item/{id}`  
    * `POST /api/v3/excess-time/item/{id}/comment`  
    * `POST /api/v3/excess-time/item/{id}/primary-secondary`  

---

# RELEASE aluminium-9.12  -  [Wrike](https://www.wrike.com/open.htm?id=1319860093)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with saving allowance with start/end times, where the end time is not saving but repeating start time  
* [FIX] Fix issue with listing ad hoc allowances on config service where items that should not be used against master were showing  

---

# RELEASE aluminium-9.11  -  [Wrike](https://www.wrike.com/open.htm?id=1319187481)  

## Mobile API:  
#### Features:  
* [FEAT] Add mobile API service to delete an ad hoc allowance entry: `DELETE /api/v3/ad-hoc-allowance/entry/{id}`  -  [Wrike](https://www.wrike.com/open.htm?id=1314992932)  
* [CHORE] Update ad hoc allowance API services for managing fixed allowance with quantity instead of multiple records  -  [Wrike](https://www.wrike.com/open.htm?id=1314992932)  

## Console application:  
#### Fixes:  
* [FIX] Small fixes on commands that manage public API users  

## Reports:  
#### Fixes:  
* [FIX] Add work-order name to the daily costing report  -  [Wrike](https://www.wrike.com/open.htm?id=1314989791)  
#### Chores:  
* [CHORE] Add leave cap value to indicative leave report  -  [Wrike](https://www.wrike.com/open.htm?id=1295362018)  

---

# RELEASE aluminium-9.10  -  [Wrike](https://www.wrike.com/open.htm?id=1317461481)  

## Mobile API:  
#### Features:  
* [FEAT] Create ad hoc allowances API for timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1314992932)  

## General:  
#### Chores:  
* [Chore] Add field to allowance records to track where it was recorded (web or mobile app)  
#### Fixes:  
* [FIX] Fix issues with calculating leave hours on data-sources when summing individual days after rounding get to a different value in the end of the request (affecting integrations and timesheet summaries)  -  [Wrike](https://www.wrike.com/open.htm?id=1318355043)  

## Authority payrun integration:  
#### Fixes:  
* [FIX] Fix issue on rule that applies split on leave at half pay to balance values when sum does not match the total recorded on leave fro rounding issues  -  [Wrike](https://www.wrike.com/open.htm?id=1318355043)  

---

# RELEASE aluminium-9.09  -  [Wrike](https://www.wrike.com/open.htm?id=1317330418)  

## General:  
#### Chores:  
* [CHORE] Add flag to plant items to include on non standard plant options  -  [Wrike](https://www.wrike.com/open.htm?id=1306077838)  
* [CHORE] Split Ext ref and plant number into separate fields for plant items  -  [Wrike](https://www.wrike.com/open.htm?id=1309578987)  

---

# RELEASE aluminium-9.08  -  [Wrike](https://www.wrike.com/open.htm?id=1314066213)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when editing work-orders recorded in the timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1314048910)  
* [FIX] Fix issue with applying for leave to solve missing hours on time-card (glide day shifts)  -  [Wrike](https://www.wrike.com/open.htm?id=1312205432)  
* [FIX] Fix issue on higher duties - dates are missing when editing request in some occasions  -  [Wrike](https://www.wrike.com/open.htm?id=1309604788)  
* [FIX] Fix "Error 614: invalid Parameter" when trying to make manual excess time rule inactive  -  [Wrike](https://www.wrike.com/open.htm?id=1309610163)  

---

# RELEASE aluminium-9.07  -  [Wrike](https://www.wrike.com/open.htm?id=1312391700)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue on time-card when editing work-order - it was not keeping the selection  

---

# RELEASE aluminium-9.06  -  [Wrike](https://www.wrike.com/open.htm?id=1312205198)  

## General:  
#### Fixes:  
* [FIX] Fix issue with breaks not saving to time-blocks and resetting to schedule if the day also has leave  -  [Wrike](https://www.wrike.com/open.htm?id=1300966624)  

---

# RELEASE aluminium-9.05  -  [Wrike](https://www.wrike.com/open.htm?id=1312106719)  

## Web application:  
#### Chores:  
* [CHORE] Automatically select the work-order group in time-card when recording time and user has only one group available  

---

# RELEASE aluminium-9.04  -  [Wrike](https://www.wrike.com/open.htm?id=1311292486)  

## General:  
#### Fixes:  
* [FIX] Fix 500 random error with applying for accrued hours leave on part day  

## Merges:  
* [MERGE] Merge from [aluminium-8.27](https://www.wrike.com/open.htm?id=1311240685)  

---

# RELEASE aluminium-9.03  -  [Wrike](https://www.wrike.com/open.htm?id=1310471126)  

## General:  
#### Chores:  
* [CHORE] Create way to separate FTE adjustment from full day and part day leave requests  -  [Wrike](https://www.wrike.com/open.htm?id=1306177539)  

---

# RELEASE aluminium-9.02  -  [Wrike](https://www.wrike.com/open.htm?id=1307779129)  

## Merges:  
* [MERGE] Merge from [aluminium-8.25](https://www.wrike.com/open.htm?id=1307024632)  
* [MERGE] Merge from [aluminium-8.26](https://www.wrike.com/open.htm?id=1307773314)  

---

# RELEASE aluminium-9.01  -  [Wrike](https://www.wrike.com/open.htm?id=1306821763)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix recording time on mobile API where no group ID is being passed  

---

# RELEASE aluminium-9.00  -  [Wrike](https://www.wrike.com/open.htm?id=1306160671)  

## General:  
#### Features:  
* [FEAT] Create new means for making work-order assignments and selection  -  [Wrike](https://www.wrike.com/open.htm?id=1283141826)  

---
---
---

# RELEASE aluminium-8.27  -  [Wrike](https://www.wrike.com/open.htm?id=1311240685)  

## Web applications:  
#### Fixes:  
* [FIX] Fix issue where sometimes when amending excess time, changes don't show unless page is refreshed  -  [Wrike](https://www.wrike.com/open.htm?id=1276976193)  

## General:  
#### Fixes:  
* [FIX] Fix calculation error on RDO earnt in a timesheet when there is also unpaid excess-time  -  [Wrike](https://www.wrike.com/open.htm?id=1310353539)  

---

# RELEASE aluminium-8.26  -  [Wrike](https://www.wrike.com/open.htm?id=1307773314)  

## Mobile API:  
#### Chores:  
* [CHORE] Add issues and warnings list to the service `/api/v3/time-sheet/{id}/submit/summary` to prevent syncing issues  

---

# RELEASE aluminium-8.25  -  [Wrike](https://www.wrike.com/open.htm?id=1307024632)  

## General:  
#### Fixes:  
* [FIX] Fix error with breaks resetting after time-sheet recalculation, leave requested and other calculations  -  [Wrike](https://www.wrike.com/open.htm?id=1300966624)  

---

# RELEASE aluminium-8.24  -  [Wrike](https://www.wrike.com/open.htm?id=1301724691)  

## General:  
#### Chores:  
* [CHORE] Change leave types realisation options to add the ability to realise only on adjustments  -  [Wrike](https://www.wrike.com/open.htm?id=1306150658)  

## Integrations:  
#### Features:  
* [FEAT] Create means to integrate to Authority Cloud version (download data)  -  [Wrike](https://www.wrike.com/open.htm?id=1286596473)  

## Payroll Authority integrations:  
#### Features:  
* [FEAT] Create means to integrate to Authority Cloud version (payrun posting)  -  [Wrike](https://www.wrike.com/open.htm?id=1286596473)  

---

# RELEASE aluminium-8.23  -  [Wrike](https://www.wrike.com/open.htm?id=1304389152)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix error where plant added to children work-orders were showing also against parent project record  -  [Wrike](https://www.wrike.com/open.htm?id=1302755857)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Update Synergy import fie report to add a code split for James Lang (NAC)  -  [Wrike](https://www.wrike.com/open.htm?id=1300926836)  

---

# RELEASE aluminium-8.22  -  [Wrike](https://www.wrike.com/open.htm?id=1303538799)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with breaks not saving to time-blocks and resetting to schedule if the day also has leave  -  [Wrike](https://www.wrike.com/open.htm?id=1300966624)  

## General:  
#### Fixes:  
* [FIX] Fix issue with workflows that have invalid relationship with related model that wre stopping user terminations  

---

# RELEASE aluminium-8.21  -  [Wrike](https://www.wrike.com/open.htm?id=1302838823)  

## General:  
#### Fixes:  
* [FIX] Fix async issue with balances calculations that don't update the payrun status sometimes  
* [FIX] Fix error found on logs of an invalid return type (it wasn't affecting any functionality - only noise on the logs)  
* [FIX] Fix issue on period excess-time calculations when time-block doesn't change but only the work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1302822466)  
* [FIX] Fix error on calculating excess-time when all hours are excess-time and time has breaks, and it's split with multiple work-orders and part on master  -  [Wrike](https://www.wrike.com/open.htm?id=1302837145)  

---

# RELEASE aluminium-8.20  -  [Wrike](https://www.wrike.com/open.htm?id=1302775985)  

## Libraries:  
* [LIB] Update `browserslist` db for `caniuse-lite`  

## General:  
#### Fixes:  
* [FIX] Fix error on calculating excess-time when time has breaks, and it's split with multiple work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1301851679)  
* [FIX] Fix error on calculating leave balances in the end of a payrun where it gets stuck and doesn't finish calculating everyone's balances  -  [Wrike](https://www.wrike.com/open.htm?id=1302764920)  

## Payroll Authority integration:  
#### Fixes: 
* [FIX] Fix issue on integration when user has multiple HD requests with different pay codes and time is recorded against the same project / work-order  -  [Wrike](https://www.wrike.com/open.htm?id=1301859379)  

---

# RELEASE aluminium-8.19  -  [Wrike](https://www.wrike.com/open.htm?id=1299942519)  

## Payroll Authority integrations:  
#### Features:  
* [FEAT] Create new integration rule to offset leave hours based on conditions  -  [Wrike](https://www.wrike.com/open.htm?id=1298951057)  
* [FEAT] Create new integration rule to apply penalty as additional to leave taken based on conditions  -  [Wrike](https://www.wrike.com/open.htm?id=1299053659)  
#### Fixes:  
* [FIX] Fix rule that makes excess-time rows to become positive and negative that was miscalculating the ordinary hours hour code and preventing the integration to keep working properly  

---

# RELEASE aluminium-8.18  -  [Wrike](https://www.wrike.com/open.htm?id=1300916127)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with trying to get schedule info for users if that user has an ended shift on the current active role  -  [Wrike](https://www.wrike.com/open.htm?id=1298172567)  

---

# RELEASE aluminium-8.17  -  [Wrike](https://www.wrike.com/open.htm?id=1299915632)  

## General:  
#### Fixes:  
* [FIX] Apply fix on leave balances when payrun leave calculations is done  

## Reports:  
#### Fixes:  
* [FIX] Fix error where on reports page the "end payrun" selection label shows as "first payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=1295433305)  

## Integrations:  
#### Chores:  
* [CHORE] Update leave integrations so when leave type is set in a way that balances are realised on accrual, then all balances become available  -  [Wrike](https://www.wrike.com/open.htm?id=1295362539)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix error on service that populates time-sheet summaries that have a server error happening  

---

# RELEASE aluminium-8.16  -  [Wrike](https://www.wrike.com/open.htm?id=1297918693)  

## General:  
#### Fixes:  
* [FIX] Fix error on RDO accrued calculation and total hours to be paid when timesheet includes leave that has been FTE adjusted  -  [Wrike](https://www.wrike.com/open.htm?id=1287555923)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with `null` code on report "Costing by employee status by department"  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issues with HTML code being returned with labels on time-sheet summary info blocks  
* [FIX] Fix issues with leave requests with FTE calculations was not rounding correctly  

---

# RELEASE aluminium-8.15  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Mobile API:  
#### Features:  
* [FEAT] Create a new mobile API service called `/api/v3/time-card/{id}/scheduled` to return a daily schedule to show on time-card  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Chores:  
* [CHORE] Hide leave banks if user is not entitled to take leave of types associated on mobile API service `/api/v3/user/me/leave-banks`  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] Update mobile API service `/api/v3/leave/list/my-requests` to also include declined leave requests in the list  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-8.14  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Payroll integrations:  
#### Chores:  
* [CHORE] Update payroll integrations to pass plant hours and km as 0 (zero) if no value was added  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## General:  
#### Fixes:  
* [FIX] Apply fix on penalty for offset hours when doing period excess-time calculation, it was getting the total hours for the penalty on the whole time-block instead of only whet triggered that penalty  

## Console application:  
#### Chores:  
* [CHORE] Update command that rebuilds workflows to clear history after rebuilding the workflows  

---

# RELEASE aluminium-8.13  -  [Wrike](https://www.wrike.com/open.htm?id=1286576106)  

## Integrations:  
#### Fixes:  
* [FIX] Fix issues with work-order integration manual and automatic that is not working  -  [Wrike](https://www.wrike.com/open.htm?id=1283782354)  

---

# RELEASE aluminium-8.12  -  [Wrike](https://www.wrike.com/open.htm?id=1284490410)  

## General:  
#### Fixes:  
* [FIX] Fix error when calculating auto allowances, if allowance is calculated on the day and the user takes leave on that day  -  [Wrike](https://www.wrike.com/open.htm?id=1282360919)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with missing leave data on the work patterns by department report if time span is greater than one payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1282347810)  

---

# RELEASE aluminium-8.11  -  [Wrike](https://www.wrike.com/open.htm?id=1283729104)  

## General:  
#### Performance:  
* [PERF] Fix performance issues on listing and managing work order classification groups  

## Mobile API:  
#### Fixes:  
* [FIX] Fix API service that cancels leave requests that was not added to the list of routes  

---

# RELEASE aluminium-8.10  -  [Wrike](https://www.wrike.com/open.htm?id=1282875357)  

## Integrations:  
#### Chores:  
* [CHORE] Integrations - Create automated process to run work orders sync  -  [Wrike](https://www.wrike.com/open.htm?id=1213758180)  

---

# RELEASE aluminium-8.09  -  [Wrike](https://www.wrike.com/open.htm?id=1282800473)  

## Web applications:  
#### Fixes:  
* [FIX] Fix error when removing all adhoc allowances related to time records and the line can't be removed from timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1270282252)  

## Reports:  
#### Fixes:  
* [FIX] Fix report "Daily work costing report" to not show the CEO in all departments but only people assigned to that department (or children if child departments are included)  -  [Wrike](https://www.wrike.com/open.htm?id=1282335118)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with schedule service that was returning the ordinary hours on expected hours block  -  [Wrike](https://www.wrike.com/open.htm?id=1278733085)  
* [FIX] Fix error on schedule API service that is not returning the holidays  -  [Wrike](https://www.wrike.com/open.htm?id=1278736591)  
* [FIX] Fix error where historical roles show on dashboard API service as current  -  [Wrike](https://www.wrike.com/open.htm?id=1280555172)  
#### Chores:  
* [CHORE] Add process to recalculate issues on API service that returns timesheet issues for submit page  -  [Wrike](https://www.wrike.com/open.htm?id=1280566294)  

---

# RELEASE aluminium-8.08  -  [Wrike](https://www.wrike.com/open.htm?id=1282047296)  

## Web application:  

#### Fixes:  
* [FIX] Fix issue where mangers have the "send submission reminder to all"  and "send approval reminder to all" options on the my team timesheets page  -  [Wrike](https://www.wrike.com/open.htm?id=1256908436)  

## Reports:  
#### Features:  
* [FEAT] Create new report for costing by employee by department  -  [Wrike](https://www.wrike.com/open.htm?id=1275985490)  
* [FEAT] Build costing by employee status type by department report  -  [Wrike](https://www.wrike.com/open.htm?id=1275211998)  
* [FEAT] Create new report called "Plant items export"  -  [Wrike](https://www.wrike.com/open.htm?id=1273962337)  

## Mobile API:  
#### Features:  
* [FEAT] Create new service `/api/v3/leave/clashes` to list all clashes with leave request that is about to be made  

---

# RELEASE aluminium-8.07  -  [Wrike](https://www.wrike.com/open.htm?id=1281280204)  

## General:  
#### Fixes:  
* [FIX] Fix relationship error on schedules when making updates on public holiday hours  

---

# RELEASE aluminium-8.06  -  [Wrike](https://www.wrike.com/open.htm?id=1281272826)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with costing not correctly being calculated on report "Business Central Costing Report"  

---

# RELEASE aluminium-8.05  -  [Wrike](https://www.wrike.com/open.htm?id=1281250204)  

## General:  
#### Web application:  
* [FIX] Fix issue with recording break on time that finishes at midnight  -  [Wrike](https://www.wrike.com/open.htm?id=1274479072)  
* [FIX] Fix issue with plant items when made inactive are still available for users  -  [Wrike](https://www.wrike.com/open.htm?id=1267100734)  

---

# RELEASE aluminium-8.04  -  [Wrike](https://www.wrike.com/open.htm?id=1280901048)  

## General:  
#### Fixes:  
* [FIX] Fix public holiday calculations  

---

# RELEASE aluminium-8.03  -  [Wrike](https://www.wrike.com/open.htm?id=1279599335)  

## General:  
#### Fixes:  
* [FIX] Fix issue with auto-allowance and approving timesheet as a manager  -  [Wrike](https://www.wrike.com/open.htm?id=1279584716)  
* [FIX] Fix error when removing all adhoc allowances related to time records and the line can't be removed from timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1270282252)  

---

# RELEASE aluminium-8.02  -  [Wrike](https://www.wrike.com/open.htm?id=1279175931)  

## Mobile API:  
#### Chores:  
* [CHORE] Add flag called `isLoginFormHidden` to service `/api/v3/tenant/current`  
* [CHORE] Implement additional fields for plant items on `time-card` and `time-recorded` services  

## Merges:  
* [MERGE] Merge from [aluminium-7.33](https://www.wrike.com/open.htm?id=1269407480)  
* [MERGE] Merge from [aluminium-7.34](https://www.wrike.com/open.htm?id=1270280821)  
* [MERGE] Merge from [aluminium-7.35](https://www.wrike.com/open.htm?id=1271076078)  
* [MERGE] Merge from [aluminium-7.36](https://www.wrike.com/open.htm?id=1278431317)  
* [MERGE] Merge from [aluminium-7.37](https://www.wrike.com/open.htm?id=1278689894)  
* [MERGE] Merge from [aluminium-7.38](https://www.wrike.com/open.htm?id=1279031016)  

---

# RELEASE aluminium-8.01  -  [Wrike](https://www.wrike.com/open.htm?id=1269555041)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with plant hour validation on time-cards  

---

# RELEASE aluminium-8.00  -  [Wrike](https://www.wrike.com/open.htm?id=1269469931)  

__Base version: [aluminium-7.32](https://www.wrike.com/open.htm?id=1265365319)  

## General:  
#### Chores:  
* [CHORE] Alter Plant to allow hire cost and hour meter to be recorded  -  [Wrike](https://www.wrike.com/open.htm?id=1214620614)  
* [CHORE] Update costing type functionality on ad-hoc allowances to allow costing only against worked hours  -  [Wrike](https://www.wrike.com/open.htm?id=1200602027)  

## Web application:  
#### Chores:  
* [CHORE] Update login page - remove image scroll and replace with random quote and fact generator  -  [Wrike](https://www.wrike.com/open.htm?id=1221981436)  
* [CHORE] Alter timecard validations to allow for altering start and end times for users on work-orders without needing to delete all the work-order / allowance / plant entries  -  [Wrike](https://www.wrike.com/open.htm?id=1238144594)  
* [CHORE] Alter plant view on timesheet to include odometer reading and also unit cost  -  [Wrike](https://www.wrike.com/open.htm?id=1244374181)  
* [CHORE] Force managers to view approve page if approving not submitted timesheets - recalc non-submitted timesheets if approved  -  [Wrike](https://www.wrike.com/open.htm?id=1195363552)  
#### Fixes:  
* [FIX] When using the detailed view on the my teams timesheets page, if a issues is resolved before approving the timesheet the view changes to the summary view for the user who issue was resolved  -  [Wrike](https://www.wrike.com/open.htm?id=1255758220)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "Costing by payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=1204838003)  
* [FEAT] Create new report called "Pay run costing report"  -  [Wrike](https://www.wrike.com/open.htm?id=1199879834)  
* [FEAT] Create new report called "Financial costing journal"  -  [Wrike](https://www.wrike.com/open.htm?id=1213700745)  
* [FEAT] Create an Excel version of the leave balances table displayed in page "My teams leave details"  -  [Wrike](https://www.wrike.com/open.htm?id=1228732848)  

---
---
---

# RELEASE aluminium-7.38  -  [Wrike](https://www.wrike.com/open.htm?id=1279031016)  

## Reports:  
#### Fixes:  
* [FIX] Fix value of dimension 5 for plant items in the business central financial costing report to use ext ref ID instead of asset number  

## Web application:  
#### Fixes:  
* [FIX] Fix a few issues happening on the timecard  -  [Wrike](https://www.wrike.com/open.htm?id=1276973747)  

---

# RELEASE aluminium-7.37  -  [Wrike](https://www.wrike.com/open.htm?id=1278689894)  

## Integrations:  
#### Chores:  
* [CHORE] Amend work orders import to concatenate all descriptions to build work order name  -  [Wrike](https://www.wrike.com/open.htm?id=1276968494)  

## Reports:  
#### Fixes:  
* [FIX] Amendments to Business Central Financial costing journal  -  [Wrike](https://www.wrike.com/open.htm?id=1273948639)  

---

# RELEASE aluminium-7.36  -  [Wrike](https://www.wrike.com/open.htm?id=1278431317)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating holidays async (found on logs)  
* [FIX] Fix issue with asynchronously posting leave attachments to EDRMS (found on logs)  
* [FIX] Fix rounding on FTE calculations when applying for leave  -  [Wrike](https://www.wrike.com/open.htm?id=1276952920)  

## Notifications:  
#### Fixes:  
* [FIX] Fix error with sending excess-time notifications when record is automatically approved (found on logs)  

## Reports:  
#### Fixes:  
* [FIX] Fix error on report "Plant use by payrun" that was not working when user that used plant item does not have a current manager  -  [Wrike](https://www.wrike.com/open.htm?id=1276972968)  

---

# RELEASE aluminium-7.35  -  [Wrike](https://www.wrike.com/open.htm?id=1271076078)  

## Web application:  
#### Fixes:  
* [FIX] Fix caching on timesheet config  

---

# RELEASE aluminium-7.34  -  [Wrike](https://www.wrike.com/open.htm?id=1270280821)  

## General:  
#### Fixes:  
* [FIX] Fix issues with calculating public holidays on timesheet when users have multiple roles assigned to period  

## Command:  
#### Chores:  
* [CHORE] Clear command that updates tenant  

---

# RELEASE aluminium-7.33  -  [Wrike](https://www.wrike.com/open.htm?id=1269407480)  

## Command:  
#### Chores:  
* [CHORE] Update command to upgrade tenant to include removing unnecessary data from user profile  

---

# RELEASE aluminium-7.32  -  [Wrike](https://www.wrike.com/open.htm?id=1265365319)  

## Reports:  
#### Features:  
* [FEAT] Create a new report called "Business Central Financial costing journal"  -  [Wrike](https://www.wrike.com/open.htm?id=1239838412)  
#### Fixes:  
* [FIX] Fix daily costing report automation errors  -  [Wrike](https://www.wrike.com/open.htm?id=1258381940)  

## Mobile API:  
#### Features:  
* [FEAT] Create API service for timesheet schedule `/api/v3/schedule/time-sheet/{id}`  
#### Documentation:  
* [DOC] Create documentation for API services for timesheet schedule `/api/v3/schedule/time-sheet/{id}`  
#### Fixes:  
* [FIX] Fix timesheets listing service that was duplicating previous timesheet in the list of past records  

---

# RELEASE aluminium-7.31  -  [Wrike](https://www.wrike.com/open.htm?id=1267837913)  

## Reports:  
#### Chores:  
* [CHORE] Add new columns for pay location and pay group type into "Staff summary" report  -  [Wrike](https://www.wrike.com/open.htm?id=1267826020)  

---

# RELEASE aluminium-7.30  -  [Wrike](https://www.wrike.com/open.htm?id=1266972841)  

## General:  
#### Chores:  
* [CHORE] Check if mime-type of saved media file is not `application/octet-stream` before forcing download on file request  

---

# RELEASE aluminium-7.29  -  [Wrike](https://www.wrike.com/open.htm?id=1266858104)  

## Web application:  
#### Chores:  
* [CHORE] Add validation warning message to leave requests when request values are altered due to schedule  -  [Wrike](https://www.wrike.com/open.htm?id=1265552852)  

---

# RELEASE aluminium-7.28  -  [Wrike](https://www.wrike.com/open.htm?id=1265417077)  

## General:  
#### Fixes:  
* [FIX] Apply fix on accrued balance calculations when leave type has stand-down period - it's adding an extra payrun worth of leave when not forecasting  -  [Wrike](https://www.wrike.com/open.htm?id=1259982441)  
* [FIX] Fix rounding error when resolving missing hours error on timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1245364517)  

---

# RELEASE aluminium-7.27  -  [Wrike](https://www.wrike.com/open.htm?id=1265315248)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue where breaks are not being deducted if hours are recorded on the app and you are set up to cost time to work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1228141639)  

## General:  
#### Fixes:  
* [FIX] Fix entitled hours being incorrectly calculated for holidays  -  [Wrike](https://www.wrike.com/open.htm?id=1229619750)  

---

# RELEASE aluminium-7.26  -  [Wrike](https://www.wrike.com/open.htm?id=1263999167)  

## Reports:  
#### Hardcoded:  
* [HARDCODE] Update Port Pirie GL % Split for user Megan Cusack  -  [Wrike](https://www.wrike.com/open.htm?id=1261614654)  

## Mobile API:  
#### Chores:  
* [CHORE] Add `canCopyFromSchedule` to service `/api/v3/time-card/{id}` to allow controlling if time can be copied from schedule on that day  
* [CHORE] Add `canCopyFromPreviousDay` to service `/api/v3/time-card/{id}` to allow controlling if time can be copied from previous day on that day  
* [CHORE] Add `canCopyFromSchedule` to service `/api/v3/time-card/{id}/recorded/config` to allow controlling if time can be copied from schedule on that day  

---

# RELEASE aluminium-7.25  -  [Wrike](https://www.wrike.com/open.htm?id=1263021574)  

## Mobile API:  
#### Chores:  
* [CHORE] Add `allowRecordingAcrossMidnight` to service `/api/v3/time-card/{id}/recorded/config` to allow controlling if user can record time across midnight  
#### Features:  
* [FEAT] Add service to copy hours from schedule into API v3 called `/api/v3/time-card/{id}/copy-from-schedule`  
#### Fixes:  
* [FIX] Make attachment file URL public so it's possible to be used by the app  

---

# RELEASE aluminium-7.24  -  [Wrike](https://www.wrike.com/open.htm?id=1259242224)  

## Integrations:  
#### Fixes:  
* [FIX] Fix issue where running work-orders integrations using new module is only accessible if user is customer love  

## Reports:  
#### Hardcode:  
* [HARDCODE] Hardcode split for user "Vishnuprasad Vinodukumar" on report "Keypay batch file" for `ngsc`  -  [Wrike](https://www.wrike.com/open.htm?id=1259278531)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix service that submits timesheet to accept a `null` reason  
#### Documentation:  
* [DOC] Add missing issue description to service `GET /api/v3/time-sheet/{id}/submit/issues`  
* [DOC] Update documentation for timesheet issues to make more sense  

## Web application:  
#### Fixes:  
* [FIX] Fix error when automatically deducting a scheduled break after hours have been edited  -  [Wrike](https://www.wrike.com/open.htm?id=1255117862)  
* [FIX] Fix issue on time-card - when using the web browser on a mobile android device the duration does not calculate  -  [Wrike](https://www.wrike.com/open.htm?id=1203046226)  
#### Chores:  
* [CHORE] Amend process to manage breaks in the timecard to be the same for projects and work-orders  

---

# RELEASE aluminium-7.23  -  [Wrike](https://www.wrike.com/open.htm?id=1260595749)  

## Reports:  
#### Fixes:  
* [FIX] Fix indicative leave report that only includes direct reports even if you are payroll - when payroll run the report it should include the whole organisation  -  [Wrike](https://www.wrike.com/open.htm?id=1260148261)  

---

# RELEASE aluminium-7.22  -  [Wrike](https://www.wrike.com/open.htm?id=1259945753)  

## General:  
#### Fixes:  
* [FIX] Fix error on period excess-time calculations when hours are against work-orders only and there is mix with daily excess-time - it was costing more excess-time than possible in some cases  

---

# RELEASE aluminium-7.21  -  [Wrike](https://www.wrike.com/open.htm?id=1258901239)  

## Mobile API:  
#### Features:  
* [FEAT] Add new service `GET /api/v3/time-sheet/{id}/submit/issues` to populate summary for submit page  
#### Chores:  
* [CHORE] Remove some unnecessary data from service `GET /api/v3/time-sheet/{id}/submit/summary`  

---

# RELEASE aluminium-7.20  -  [Wrike](https://www.wrike.com/open.htm?id=1258143251)  

## Mobile API:  
#### Features:  
* [FEAT] Add new service `GET /api/v3/time-sheet/{id}/submit/summary` to populate summary for submit page  

---

# RELEASE aluminium-7.19  -  [Wrike](https://www.wrike.com/open.htm?id=1258044990)  

## Mobile API:  
#### Fixes:  
* [FIX] Add missing field `userShiftTimeType_id` on service `GET /api/v3/time-recorded/{id}`  

---

# RELEASE aluminium-7.18  -  [Wrike](https://www.wrike.com/open.htm?id=1256528329)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when automatically deducting a scheduled break after hours have been edited  -  [Wrike](https://www.wrike.com/open.htm?id=1255117862)  

## Mobile API:  
#### Features:  
* [FEAT] Create new service `GET /api/v3/time-recorded`  
#### Chores:  
* [CHORE] Amend the structure for breaks saved on service `POST /api/v3/time-recorded`  
* [CHORE] Add `isSummaryOpen` on service `GET /api/v3/time-card/{id}`  
* [CHORE] Add `intercom_identity` on services that return logged user to be used on intercom connection   
#### Documentation:  
* [DOC] Add breaks to service `POST /api/v3/time-recorded`  
#### Fixes:  
* [FIX] Fix issue where breaks are not being calculated correctly when using the mobile app API  

---

# RELEASE aluminium-7.17  -  [Wrike](https://www.wrike.com/open.htm?id=1257362720)  

## General:  
#### Fixes:  
* [FIX] Fix issue when calculating special holidays on schedules - not working in some cases (found on logs)  

## Notifications:  
#### Fixes:  
* [FIX] Fix issue with "Daily costing report" that is not being sent automatically via notification type  

---

# RELEASE aluminium-7.16  -  [Wrike](https://www.wrike.com/open.htm?id=1256529751)  

## General:  
#### Chores:  
* [CHORE] Add plant usage code field for global plant settings  -  [Wrike](https://www.wrike.com/open.htm?id=1256117067)  

## Mobile API:  
#### Chores:  
* [CHORE] Add field `recordMethod` to API service `/api/v3/time-card/{id}/config`  

---

# RELEASE aluminium-7.15  -  [Wrike](https://www.wrike.com/open.htm?id=1252666217)  

## Reports:  
#### Fixes:  
* [FIX] Fix daily cost report automation  

---

# RELEASE aluminium-7.14  -  [Wrike](https://www.wrike.com/open.htm?id=1252657531)  

## Mobile API:  
#### Chores:  
* [CHORE] Add specific service to upload new attachments to leave requests `/api/v3/leave/request/{id}/attachment`  
#### Documentation:  
* [DOC] Add documentation for service to upload new attachments to leave requests `/api/v3/leave/request/{id}/attachment`  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where the search function on the auto allowance table returns a 500 error  -  [Wrike](https://www.wrike.com/open.htm?id=1252574829)  
* [FIX] Fix issue where ended shifts didn't show if there are no active shifts into user profile  

## General:  
#### Fixes:  
* [FIX] Fix error where when ending a shift, some times the timesheets are not removed correctly (if user has adhoc allowances)  

---

# RELEASE aluminium-7.13  -  [Wrike](https://www.wrike.com/open.htm?id=1251891819)  

## Web application:  
#### Fixes:  
* [FIX] Reinstate overwritten project/activity code when assigning auto-allowances  

---

# RELEASE aluminium-7.12  -  [Wrike](https://www.wrike.com/open.htm?id=1251887349)  

## Web application:  
#### Fixes:  
* [FIX] Fix department leave calendars where managers from parent departments are showing on child department calendars  
* [FIX] Fix issue with managers managing schedules gives a 500 error some times  -  [Wrike](https://www.wrike.com/open.htm?id=1238982127)  

## Reports:  
#### Features:  
* [FEAT] Create new version of the Authority batch upload file  -  [Wrike](https://www.wrike.com/open.htm?id=1251825809)  
#### Fixes:  
* [FIX] Fix report "Plant entry by payrun" that had a blank slug what was causing errors in the backend  
#### Chores:  
* [CHORE] Amend process of assigning reports to tenants - it was bugging for some reports in some tenants  

---

# RELEASE aluminium-7.11  -  [Wrike](https://www.wrike.com/open.htm?id=1251721829)  

__First LIVE Release__  

## General:  
#### Chores:  
* [CHORE] Temporarily hide auto-allowance option to trigger based on worked hours on work-orders  

---

# RELEASE aluminium-7.10  -  [Wrike](https://www.wrike.com/open.htm?id=1251153543)  

## Reports:  
#### Fixes:  
* [FIX] Fix error on "Daily costing report" where hours recorded without any associated plant is not showing on the report  
* [FIX] Fix error on report "All leave balances" where not all direct and indirect reports are showing when running the report as a manager  

## Web application:  
#### Fixes:  
* [FIX] Fix error on timesheets listing page where direct reports don't show on the listing if they are not part of the workflow and user is set not to have sub-reports timesheets visible  -  [Wrike](https://www.wrike.com/open.htm?id=1251120170)  

---

# RELEASE aluminium-7.09  -  [Wrike](https://www.wrike.com/open.htm?id=1251141446)  

## General:  
#### Chores:  
* [CHORE] Add timeline entry to leave request when approval reminder is sent  
#### Fixes:  
* [FIX] Fix error on calculating auto-allowances based on excess-time rule if allowance is calculated on work-orders  

## Web application:  
#### Fixes:  
* [FIX] Fix error on logs for when fetching users away  
* [FIX] Add link to workflow modal on timesheet listing when approver is away on leave  
* [FIX] Fix clashes when applying for leave if user is on work-orders - it is not showing the clashes and not linking back to timecard  

## Notifications:  
#### Fixes:  
* [FIX] Fix issue with sending notification emails with CC enabled for payroll officers abd HR managers  

---

# RELEASE aluminium-7.08  -  [Wrike](https://www.wrike.com/open.htm?id=1251112919)  

## Web application:  
#### Fixes:  
* [FIX] Fix "Total hours to be paid" blocks into timesheet summary that is adding leave without pay to the total paid hours  
* [FIX] Remove button to check details for users with no main roles assigned if user doesn't have access to staff management page  
* [FIX] Remove access to projects and work-orders dashboard main page in case user doesn't have access to it  
#### Chores:  
* [CHORE] Relabel flag from "Include child departments" to "Include data from child departments" on department dashboard under "daily costing report" and "daily leave digest"  
* [CHORE] Hide project dashboards  
* [CHORE] Add button to download costing report to work-order dashboard  

## General:  
#### Fixes:  
* [FIX] Fix error 500 when trying to record time against work-orders  


---

# RELEASE aluminium-7.07  -  [Wrike](https://www.wrike.com/open.htm?id=1250313533)  

## Web application:  
#### Chores:  
* [CHORE] Amend layout of special assignment blocks inside department dashboard  

---

# RELEASE aluminium-7.06  -  [Wrike](https://www.wrike.com/open.htm?id=1250262520)  

## Web application:  
#### Fixes:  
* [FIX] Fix 500 error when trying to save new user  
* [FIX] Fix issues with duty requests not showing as info boxes on timesheets correctly after approved  
* [FIX] Fix 500 error shown when trying to submit/approve timesheet and user has a new direct manager on the period  
* [FIX] Fix issue where departments ending on the same date as the role is ending show are warnings  
* [FIX] Fix syncing issue when making actions to issues from timesheets listing page  

---

# RELEASE aluminium-7.05  -  [Wrike](https://www.wrike.com/open.htm?id=1249569118)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues where it was not possible to submit a timesheet with eNPS turned ON  

---

# RELEASE aluminium-7.04  -  [Wrike](https://www.wrike.com/open.htm?id=1249567443)  

## General:  
#### Fixes:  
* [FIX] Fix issue where users with past workflows that were not finalised (excluded, replaces, split, etc) were blocked from being terminated  
* [FIX] Fix higher duties period validation when applying on the first day of the first available period  
* [FIX] Fix higher duties validation that was getting the class by ID instead of class name what caused random 500 errors when trying to apply for duties  

---

# RELEASE aluminium-7.03  -  [Wrike](https://www.wrike.com/open.htm?id=1249549547)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where warnings and notes don't show when submitting timesheets with eNPS functionality enabled  
* [FIX] Fix 500 error when trying to access site without a logged user  

---

# RELEASE aluminium-7.02  -  [Wrike](https://www.wrike.com/open.htm?id=1249540324)  

## Merges:  
* [MERGE] Merge from [aluminium-6.14](https://www.wrike.com/open.htm?id=1249536852)  

## General:  
#### Fixes:  
* [FIX] Fix issue with duties requests period validation that are not working properly and returning errors when it is correct  

---

# RELEASE aluminium-7.01  -  [Wrike](https://www.wrike.com/open.htm?id=1249533489)  

## General:  
#### Fixes:  
* [FIX] Fix issues with "plant coordinator" system access flag - it was hiding many users from staff list also not showing the flag against profiles with that access  

## Web application:  
#### Fixes:  
* [FIX] Fix error shown when user tries to access a tenant that doesn't exist  
* [FIX] Fix issue on download reports page loading  
* [FIX] Fix spelling for "plantsheet" that were still showing as "plant timesheet" in some places  

## Merges:  
* [MERGE] Merge from [aluminium-6.13](https://www.wrike.com/open.htm?id=1247183255)  

---

# RELEASE aluminium-7.00  -  [Wrike](https://www.wrike.com/open.htm?id=1247145126)  

__Base version: [aluminium-6.12](https://www.wrike.com/open.htm?id=1247143375)__  

## General:  
#### Features:  
* [FEAT] Create "Plant coordinator" system access and change menu item name from Plant timesheets to "Plantsheets"  -  [Wrike](https://www.wrike.com/open.htm?id=1207196965)  
* [PACKAGE] Extend timesheet issues functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1188125360)  
    * [CHORE] Reinstate ModelIssue info level  -  [Wrike](https://www.wrike.com/open.htm?id=1198123398)  
    * [CHORE] Update timesheet approve page with issues, warnings and info notes  -  [Wrike](https://www.wrike.com/open.htm?id=1198123567)  
    * [CHORE] Update timesheet submit page with issues, warnings and info notes  -  [Wrike](https://www.wrike.com/open.htm?id=1198123511)  
    * [FEAT] Create new timesheet warnings / info notes  -  [Wrike](https://www.wrike.com/open.htm?id=1198930478)  
        * [FEAT] Create new timesheet warning: "Staff not assigned to a department"  -  [Wrike](https://www.wrike.com/open.htm?id=1192115172)  
        * [FEAT] Create new timesheet info note: "Staff has duties on timesheet"  -  [Wrike](https://www.wrike.com/open.htm?id=1198931998)  
        * [FEAT] Create new timesheet info note: "Pay type changed"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932082)  
        * [FEAT] Create new timesheet info note: "Staff is new in the organisation"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932273)  
        * [FEAT] Create new timesheet info note: "Staff is working on a new shift"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932439)  
        * [FEAT] Create new timesheet info note: "Staff has a new role starting"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932453)  
        * [FEAT] Create new timesheet info note: "Staff's role is ending"  -  [Wrike](https://www.wrike.com/open.htm?id=1199826619)  
        * [FEAT] Create new timesheet info note: "Staff has a new direct manager"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932633)  
        * [FEAT] Create new timesheet info note: "Staff has a new role manager"  -  [Wrike](https://www.wrike.com/open.htm?id=1200619289)  
        * [FEAT] Create new timesheet info note: "Staff is assigned to a new department"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932681)  
        * [FEAT] Create new timesheet info note: "Staff has a new master project"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932691)  
        * [FEAT] Create new timesheet info note: "Staff has a new penalty assigned"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932694)  
        * [FEAT] Create new timesheet info note: "Staff has a penalty assignment ending"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932698)  
        * [FEAT] Create new timesheet info note: "Staff has a new allowance assigned"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932713)  
        * [FEAT] Create new timesheet info note: "Staff has a allowance assignment ending"  -  [Wrike](https://www.wrike.com/open.htm?id=1198932715)  
    * [CHORE] Change issue type "pay type not assigned for whole period"  -  [Wrike](https://www.wrike.com/open.htm?id=1198928867)  
    * [CHORE] Update timesheet issues modal to also show warnings and info notes  -  [Wrike](https://www.wrike.com/open.htm?id=1188104121)  
    * [FEAT] Create new report called "Payrun advisory"  -  [Wrike](https://www.wrike.com/open.htm?id=1145203602)  
* [PACKAGE] Project dashboards rework  -  [Wrike](https://www.wrike.com/open.htm?id=1205506887)  
  * [CHORE] Remove page "Reports" > "Projects" and its sub-pages  -  [Wrike](https://www.wrike.com/open.htm?id=1215354070)  
  * [FEAT] Create work order owner  -  [Wrike](https://www.wrike.com/open.htm?id=1215357622)  
  * [FEAT] Create new page to select project / work orders dashboards  -  [Wrike](https://www.wrike.com/open.htm?id=1215357780)  
  * [FEAT] Create project dashboard page  -  [Wrike](https://www.wrike.com/open.htm?id=1215358349)  
  * [FEAT] Create work order dashboard page  -  [Wrike](https://www.wrike.com/open.htm?id=1215359329)  
* [FEAT] Create daily notification mechanism for daily costing reports  -  [Wrike](https://www.wrike.com/open.htm?id=1234638935)  
* [FEAT] Add CC functionality to workflow notifications and reminders for HR managers and payroll officers  -  [Wrike](https://www.wrike.com/open.htm?id=1213657349)  
* [FEAT] Create means to cost auto-allowances to triggering work-order instead of default master  -  [Wrike](https://www.wrike.com/open.htm?id=1231172470)  
#### Fixes:  
* [FIX] Fix Issue with requesting duties on approved timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=903224341)  
* [FIX] Fix issue where leave types that are made inactive can still be selected to take leave  -  [Wrike](https://www.wrike.com/open.htm?id=1236778223)  

## Web application:  
#### Features:  
* [FEAT] Add remind all buttons for timesheets leave requests  -  [Wrike](https://www.wrike.com/open.htm?id=1195368955)  
#### Chores:  
* [CHORE] Add the name of the project / work-order to the excess time view  -  [Wrike](https://www.wrike.com/open.htm?id=1144385998)  
* [CHORE] Add link to time-card on leave clashes modal  -  [Wrike](https://www.wrike.com/open.htm?id=1221972629)  
* [CHORE] Add clashes info for leave vs time clash when user is on work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1199879899)  
* [CHORE] Remove separation from leave request drop down list of leave types  -  [Wrike](https://www.wrike.com/open.htm?id=1233695820)  
* [CHORE] Update login screen to remove random paragraph  -  [Wrike](https://www.wrike.com/open.htm?id=1221980990)  
* [CHORE] Add ability to see if someone is away when looking at My Team > My Teams timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1233000649)  
* [CHORE] Fix issue with payment screen showing paid hours incorrectly (also showing RDO hours  as paid hours)  -  [Wrike](https://www.wrike.com/open.htm?id=1231237618)  
* [CHORE] Amend the daily leave digest  -  [Wrike](https://www.wrike.com/open.htm?id=1225871231)  
* [CHORE] Update the leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1213653593)  
#### Fixes:  
* [FIX] Add validation to modal when assigning plant items  -  [Wrike](https://www.wrike.com/open.htm?id=1200599728)  

## Reports:  
#### Features:  
* [FEAT] Redo the "Download reports" page user experience  -  [Wrike](https://www.wrike.com/open.htm?id=1213657257)  
#### Chores:  
* [CHORE] Add descriptions to existing reports  
* [CHORE] Add comments into "All excess-time by payrun" report  -  [Wrike](https://www.wrike.com/open.htm?id=1236015481)  
* [CHORE] Provide managers the ability to download indicative leave report  -  [Wrike](https://www.wrike.com/open.htm?id=1215285436)  
* [CHORE] Amend daily costing report to include comments  -  [Wrike](https://www.wrike.com/open.htm?id=1234608831)  
* [CHORE] Provide managers the ability to download leave balances report  -  [Wrike](https://www.wrike.com/open.htm?id=1225859829)  

## Integrations:  
#### Fixes:  
* [FIX] Fix issue with filtering work-orders by modified date on work-order integrations  
* [FIX] Fix issue where tasks some times will have a blank cost code when pulled from Authority on new work-order integrations  
#### Chores:  
* [CHORE] Integrations - add work order status field for mapping active vs inactive status  -  [Wrike](https://www.wrike.com/open.htm?id=1237472032)  
* [CHORE] Integrations - Create automated process to run work orders sync  -  [Wrike](https://www.wrike.com/open.htm?id=1213758180)  

---
---
---

# RELEASE aluminium-6.14  -  [Wrike](https://www.wrike.com/open.htm?id=1249536852)  

## General:  
#### Fixes:  
* [FIX] Fix syncing issue when calculating special holidays on schedule when copy forward is triggered  
* [FIX] Adjust leave validations to allow for start and end time match on flexible shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1244374355)  
* [FIX] Fix issue where direct manager cannot submit a leave request on behalf if they are not within the leave request workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1244558973)  

## Mobile API:  
#### Fixes:  
* [FIX] API v2 - Fix issue with showing photos if the ID is not an integer value  

---

# RELEASE aluminium-6.13  -  [Wrike](https://www.wrike.com/open.htm?id=1247183255)  

## General:  
#### Fixes:  
* [FIX] Fix issue when calculating FTE ratio for user where it's multiplying the ratio if user has multiple shifts on current period  
* [FIX] Fix error when trying to terminate staff that had reports with timesheets removed by changes on shift assignments  

---

# RELEASE aluminium-6.12  -  [Wrike](https://www.wrike.com/open.htm?id=1247143375)  

## Mobile API:  
#### Documentation:  
* [DOC] Remove parameter `filter` from service `GET .../leave/list/my-requests` on swagger  

---

# RELEASE aluminium-6.11  -  [Wrike](https://www.wrike.com/open.htm?id=1246315745)  

## Mobile API:  
#### Chores:  
* [CHORE] Update API service `GET ../v3/leave/list/my-requests` to make it more efficient  
#### Documentation:  
* [DOC] Update documentation for API service `POST ../v3/leave/request/cancel`  
* [DOC] Update documentation for API service `GET ../v3/leave/list/my-requests`  
* [DOC] Update documentation for API service `POST ../v3/leave/check-request`  

---

# RELEASE aluminium-6.10  -  [Wrike](https://www.wrike.com/open.htm?id=1245370082)  

## Mobile API:  
#### Chores:  
* [CHORE] Update API service `GET ../v3/time-card/{id}` to make it simpler and faster by removing unnecessary data  
* [CHORE] Remove API services that are not necessary  
* [CHORE] Update API service `GET ../v3/time-card/{id}/recorded` to make the results match the new app's screens  
* [CHORE] Update API service `GET ../v3/time-card/{id}/issues` to make the results match the new app's screens  
* [CHORE] Update API service `DELETE ../v3/time-recorded` to make it more efficient for the mobile app  
#### Documentation:  
* [DOC] Add documentation for API service `GET ../v3/time-card/{id}`  
* [DOC] Remove documentation for API services that were removed  
* [DOC] Add documentation for API service `GET ../v3/time-card/{id}/recorded`  
* [DOC] Add documentation for API service `GET ../v3/time-card/{id}/issues`  
* [DOC] Add documentation for API service `GET ../v3/time-card/{id}/issues`  
* [DOC] Add documentation for API service `POST ../v3/time-recorded`  
* [DOC] Add documentation for API service `DELETE ../v3/time-recorded`  

---

# RELEASE aluminium-6.09  -  [Wrike](https://www.wrike.com/open.htm?id=1244444204)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with historical roles being returned for profile information  
#### Chores:  
* [CHORE] Update service `.../time-card/{id}/totals` to return data on the format needed by the new mobile app  

---

# RELEASE aluminium-6.08  -  [Wrike](https://www.wrike.com/open.htm?id=1243691886)  

## Mobile API:  
#### Features:  
* [FEAT] Create API service to list all timesheets for current user  
#### Chores:  
* [CHORE] Update API services for managing file uploads: `.../media-file/...` on version 3  
* [CHORE] Update time-sheet main API service:  
    * Clean return, so it returns only what is needed for the mobile app  
    * Add flag `hasHoliday` per day  
    * Add navigation data for moving to previous and next timesheets  
* [CHORE] Amend service `../leave/list-types`  
* [CHORE] Amend service `../leave/check-type`  
#### Documentation:  
* [DOC] Create documentation on swagger for API services `.../media-file/...` on version 3  
* [DOC] Update documentation for service `../leave/list-types`  
* [DOC] Update documentation for service `../leave/check-type`  

## General:  
#### Chores:  
* [CHORE] Improve error handling for records not found  

---

# RELEASE aluminium-6.07  -  [Wrike](https://www.wrike.com/open.htm?id=1242762505)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue with service `time-sheet/{id}` where a 500 error is being returned  

---

# RELEASE aluminium-6.06  -  [Wrike](https://www.wrike.com/open.htm?id=1242142516)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix API for recording time - `null` values for works, allowances and plant items were not accepted  
#### Chores:  
* [CHORE] Update API service `time-sheet/{id}` to return only necessary data for new version of the app  
* [CHORE] Remove API service `time-sheet/{id}/config` because it was redundant in the new version  

---

# RELEASE aluminium-6.05  -  [Wrike](https://www.wrike.com/open.htm?id=1239837471)  

## Authority payroll integrations:  
#### Chores:  
* [CHORE] Create integration rule to Authority payroll integrations to set that RDO hours are not pushed through  -  [Wrike](https://www.wrike.com/open.htm?id=1239836937)  

---

# RELEASE aluminium-6.04  -  [Wrike](https://www.wrike.com/open.htm?id=1238283200)  

## General:  
#### Fixes:  
* [FIX] Fix issue where deleting department from work-order classification groups is not possible and an error 500 happens  -  [Wrike](https://www.wrike.com/open.htm?id=1233634038)  
* [FIX] Fix error when calculating secondary excess time when, the second level of the rule is triggered straight away  -  [Wrike](https://www.wrike.com/open.htm?id=1228126111)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where it is not possible to save entitled special holiday hours if no changes are made to scheduled time  
* [FIX] Fix issue with applying for leave to solve missing hours on time-card (glide day shifts)  -  [Wrike](https://www.wrike.com/open.htm?id=1220951062)  

## Mobile APIs:  
#### Fixes:  
* [FIX] Fix issue where breaks are not being deducted if hours are recorded on the app and you are set up to cost time to work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1228141639)  

---

# RELEASE aluminium-6.03  -  [Wrike](https://www.wrike.com/open.htm?id=1237504981)  

## Web application:  
#### Fixes:  
* [FIX] Fix "undefined" shown on special holidays form when "is not" flag is ON for a selected condition  
#### Chores:  
* [CHORE] Create button to reload assignments to a holiday under special holiday session  

## General:  
#### Fixes:  
* [FIX] Fix error when multiple special holidays are added to users when they shouldn't have access to them at all  
#### Chores:  
* [CHORE] Update process to recalculate timesheet to also add/update/remove special holidays from related schedules  

---

# RELEASE aluminium-6.02  -  [Wrike](https://www.wrike.com/open.htm?id=1236726764)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "Plant entry by payrun" copy  -  [Wrike](https://www.wrike.com/open.htm?id=1199876274)  
* [FEAT] Create new report called "Paid excess time by department over time"  -  [Wrike](https://www.wrike.com/open.htm?id=1193695286)  
* [FEAT] Create new report called "Paid excess time over time"  -  [Wrike](https://www.wrike.com/open.htm?id=1193695473)  

---

# RELEASE aluminium-6.01  -  [Wrike](https://www.wrike.com/open.htm?id=1236705406)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating special holiday hours x leave when they clash  
* [FIX] Fix sum calculation for holidays in a glide period into time-card  
#### Features  
* [FEAT] Add option to select pay locations as conditions to assign special holidays  
* [FEAT] Add option to select pay group types as conditions to assign special holidays  

---

# RELEASE aluminium-6.00  -  [Wrike](https://www.wrike.com/open.htm?id=1228789025)  

__Base version: [aluminium-5.22](https://www.wrike.com/open.htm?id=1233699689)__  

## General:  
#### Features:  
* [FEAT] Add reminder to submit timesheet or to approve timesheet event to timesheet workflow timeline  -  [Wrike](https://www.wrike.com/open.htm?id=1174879952)  
* [FEAT] Create new special public holiday function  -  [Wrike](https://www.wrike.com/open.htm?id=1231216082)  

## Web application:  
#### Chores:  
* [CHORE] Alter label of accrued hour ref code  -  [Wrike](https://www.wrike.com/open.htm?id=1193632545)  
* [CHORE] Alter the order of duty requests on the Staff management > Timesheet options page  -  [Wrike](https://www.wrike.com/open.htm?id=1195361095)  

## Integrations:  
#### Chores:  
* [CHORE] Integrations - LOGS - Remove rubbish from dataset  -  [Wrike](https://www.wrike.com/open.htm?id=1213757549)  
* [CHORE] Make log messages shorter to save database space on external systems integrations  -  [Wrike](https://www.wrike.com/open.htm?id=1214614774)  
* [CHORE] Integrations - Update process to run work orders syncing  -  [Wrike](https://www.wrike.com/open.htm?id=1213758227)  

## Mobile API:  
#### Chores:  
* [CHORE] Add ability to end record on the next day when saving time  

## Console application:  
#### Chores:  
* [CHORE] Create command to clean finished integration unnecessary data  

## Cron jobs:  
#### Chores:  
* [CHORE] Add command to clean finished integration unnecessary data to run automatically every night  

---
---
---

# RELEASE aluminium-5.22  -  [Wrike](https://www.wrike.com/open.htm?id=1233699689)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Remove splits for user "10165 - Matthew Peter Bourke" (NGSC) on keypay batch files  -  [Wrike](https://www.wrike.com/open.htm?id=1233690314)  

## Mobile API:  
#### Features:  
* [FEAT] Create a new API service for user profile info to be used to populate user page on mobile app  

---

# RELEASE aluminium-5.21  -  [Wrike](https://www.wrike.com/open.htm?id=1233691388)  

## Reports:  
#### Features:  
* [FEAT] Create SGSC keypay report  -  [Wrike](https://www.wrike.com/open.htm?id=1230437770)  
#### Chores:  
* [CHORE] Rename "Keypay batch file" to "NGSC Keypay batch file"  

## General:  
#### Fixes:  
* [FIX] Fix calculation error on time-sheet summaries where total hours adjusted and total paid hours adjusted were not deducting the values correctly from rdo earnt, accrued hours and unpaid excess-time  

---

# RELEASE aluminium-5.20  -  [Wrike](https://www.wrike.com/open.htm?id=1232117237)  

## EDRMS integration:  
#### Fixes:  
* [FIX] Fix on pagination on command that re-posts the failed files that was skipping a page every page, forcing it to run multiple times  

## Mobile APIs:  
#### Features:  
* [FEAT] Create new service on API v3 to refresh the token for logged user  -  [Wrike](https://www.wrike.com/open.htm?id=1232097432)  
* [FEAT] Create new service to get assigned role information by ID on API v3  
* [FEAT] Create new service to get list of work-orders by role ID on API v3  -  [Wrike](https://www.wrike.com/open.htm?id=1232113840)  
#### Chores:  
* [CHORE] Improve error codes on API v3 for better handling from mobile app  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
    * Error __891__ when tenant URL is not found  
    * Error __892__ when tenant is not configured yet  
    * Error __895__ when tenant is under maintenance  
    * Error __901__ when wrong credentials are passed on login  
    * Error __904__ when invalid device hash is passed  
    * Error __921__ when user login fails on SSO - user not logged in  
    * Error __922__ when user login fails on SSO - user info is missing from provider  
    * Error __923__ when user login fails on SSO - user email not found in elementTIME  
* [CHORE] Remove work-orders from API v3 response for time-card config  
* [CHORE] Remove work-orders from API v3 response for time-sheet config  

---

# RELEASE aluminium-5.19  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## EDRMS integration:  
#### Features:  
* [FEAT] Create console command to re-post all files that failed to integrate to EDRMS  

---

# RELEASE aluminium-5.18  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Fixes:  
* [FIX] Fix error with generating "User leave requests over time" report if there are leave requests that were auto-approved in the selected period  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-5.17  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when payroll officers open the teams timesheets page and there are a big number of outstanding timesheets  

---

# RELEASE aluminium-5.16  -  [Wrike](https://www.wrike.com/open.htm?id=1225505061)  

## Mobile app API:  
#### Fixes:  
* [FIX] Fix error with mobile app when opening timesheet or timer pages - a relationship was removed on last performance fix  

---

# RELEASE aluminium-5.15  -  [Wrike](https://www.wrike.com/open.htm?id=1225462586)  

## Mobile app API:  
#### Fixes:  
* [FIX] Fix error with mobile app when opening timesheet or timer pages - a relationship was removed on last performance fix  

---

# RELEASE aluminium-5.14  -  [Wrike](https://www.wrike.com/open.htm?id=1223351885)  

## Web application:  
#### Performance:  
* [PERF] Users with work orders - it is super slow on the web tenant  -  [Wrike](https://www.wrike.com/open.htm?id=1198026595)  

---

# RELEASE aluminium-5.13  -  [Wrike](https://www.wrike.com/open.htm?id=1221088576)  

## General:  
#### Fixes:  
* [FIX] Fix rounding error on calculating period excess-time with leave where FTE calculations are applied, daily excess-time is triggered but no period excess-time should trigger  

---

# RELEASE aluminium-5.12  -  [Wrike](https://www.wrike.com/open.htm?id=1220980160)  

## General:  
#### Fixes:  
* [FIX] Fix auto-allowance calculations for excess-time type when more than one instance of excess-time is triggered on the same time-block  

---

# RELEASE aluminium-5.11  -  [Wrike](https://www.wrike.com/open.htm?id=1214598991)  

## Importing tool:  
#### Fixes:  
* [FIX] Fix issue with importing CSV files where BOM is present  -  [Wrike](https://www.wrike.com/open.htm?id=1214589460)  

---

# RELEASE aluminium-5.10  -  [Wrike](https://www.wrike.com/open.htm?id=1214564017)  

## Reports:  
#### Fixes:  
* [FIX] Fix error on date comparison that was stopping report "Work patterns by department" from working  

---

# RELEASE aluminium-5.09  -  [Wrike](https://www.wrike.com/open.htm?id=1212907975)  

## Reports / integrations data-sources:  
#### Fixes:  
* [FIX] Fix issue with ambiguous table alias on adhoc allowances rows for details payrun data-source  

---

# RELEASE aluminium-5.08  -  [Wrike](https://www.wrike.com/open.htm?id=1207251592)  

## Reports:  
#### Features:  
* [FEAT] Create costing reports for projects and work-orders  -  [Wrike](https://www.wrike.com/open.htm?id=1201502777)  
    * Create new report "Costing by project over time"  
    * Create new report "Costing by work-order over time"  

---

# RELEASE aluminium-5.07  -  [Wrike](https://www.wrike.com/open.htm?id=1207170353)  

## Public APIs:  
#### Chores:  
* [CHORE] Increase the rate limit for public API to 600 requests per minute  

---

# RELEASE aluminium-5.06  -  [Wrike](https://www.wrike.com/open.htm?id=1205583794)  

## Public APIs:  
#### Chores:  
* [CHORE] Increase the rate limit for API requests from 60 per minute to 300 per minute  
#### Fixes:  
* [FIX] Fix issue where pay types from deleted bands were showing in the list of pay types and it caused 500 error on API  

---

# RELEASE aluminium-5.05  -  [Wrike](https://www.wrike.com/open.htm?id=1205581423)  

## Public APIs:  
#### Fixes:  
* [FIX] Fix error on returning data from API when parameters are not overwritten by route  

---

# RELEASE aluminium-5.04  -  [Wrike](https://www.wrike.com/open.htm?id=1205473260)  

__First LIVE release__  

## Web application:  
#### Fixes:  
* [FIX] Fix spelling of clash error message for leave and duty requests  -  [Wrike](https://www.wrike.com/open.htm?id=1204002485)  
* [FIX] Fix issue on time-card - when using the web browser on a mobile android device the duration does not calculate  -  [Wrike](https://www.wrike.com/open.htm?id=1203046226)  

## General:  
#### Fixes:  
* [FIX] Fix error when copying from previous sheet the breaks don't copy - when on work orders  -  [Wrike](https://www.wrike.com/open.htm?id=1184919169)  
#### Logs:  
* [LOG] Remove clash warning exception from logs - not relevant for error log  

---

# RELEASE aluminium-5.03  -  [Wrike](https://www.wrike.com/open.htm?id=1203996656)  

## Web applications:  
#### Fixes:  
* [FIX] Fix issue with duty request clashes conditions - it doesn't work in all cases  -  [Wrike](https://www.wrike.com/open.htm?id=1202978789)  

## General:  
#### Performance:  
* [PERF] Cache some workflow type used info to reduce number of queries on workflow tables when loading records to be approved  
* [PERF] Eager load some data to show on higher duty requests list to make the page faster  
* [PERF] Eager load some data to show on excess-time list to make the page faster  

## Reports:  
#### Chores:  
* [CHORE] Filter payruns available on report "Leave accruals by payrun" to filter out payruns where leave balances are still not calculated  
#### Fixes:  
* [FIX] Fix issue with generating leave balances report by department  -  [Wrike](https://www.wrike.com/open.htm?id=1203994281)  
* [FIX] Fix issue with generating work patterns by department report  

---

# RELEASE aluminium-5.02  -  [Wrike](https://www.wrike.com/open.htm?id=1202980999)  

## Web applications:  
#### Fixes:  
* [FIX] Fix issue with duty request clashes conditions - it doesn't work in all cases  -  [Wrike](https://www.wrike.com/open.htm?id=1202978789)  
* [FIX] Fix validation issue when editing higher duty - it is not allowing to edit if there is an overlap with original request  -  [Wrike](https://www.wrike.com/open.htm?id=1202980666)  

---

# RELEASE aluminium-5.01  -  [Wrike](https://www.wrike.com/open.htm?id=1202970361)  

## Reports:  
#### Fixes:  
* [FIX] Fix issues where some reports were not available for tenants if the report wasn't listed in the reports page  
* [FIX] Fix issue where you can't download reports just after making them available  
* [FIX] Fix issues on reports download page where when enabling / disabling items it breaks the page  

## Web application:  
#### Fixes:  
* [FIX] Fix error on plant item form when no suggested pay type is selected the form hangs without saving  -  [Wrike](https://www.wrike.com/open.htm?id=1202948380)  
* [FIX] Fix issues when removing items from time-cards - it hangs without deleting  -  [Wrike](https://www.wrike.com/open.htm?id=1202956590)  
* [FIX] Fix layout issue on timesheet list page when checklist report is not available  
* [FIX] Remove from the list of users the user going away on leave - when applying from higher duties after a leave is approved  -  [Wrike](https://www.wrike.com/open.htm?id=1202970061)  

## General:  
#### Fixes:  
* [FIX] Fix issue where action button doesn't show on new plant use warning  -  [Wrike](https://www.wrike.com/open.htm?id=1202914552)  
#### Libraries:  
* [LIB] Minor upgrade composer libraries  

## Merges:  
* [MERGE] Merge from [aluminium-4.32](https://www.wrike.com/open.htm?id=1202908174)  

---

# RELEASE aluminium-5.00  -  [Wrike](https://www.wrike.com/open.htm?id=1202045530)  

__Base version: aluminium-4.31__  

## General:  
#### Features:  
* [FEAT] Create warning for use of plant items  -  [Wrike](https://www.wrike.com/open.htm?id=1171237695)  

## Web application:  
#### Features:  
* [FEAT] Create mechanism for hiding the display of native login fields on log in page by tenant  -  [Wrike](https://www.wrike.com/open.htm?id=1171238967)  
* [FEAT] Add option into plant timesheets to view plant use by user / role / cost  -  [Wrike](https://www.wrike.com/open.htm?id=1150941897)  
* [FEAT] Create new timesheet view called "Plant summary view"  -  [Wrike](https://www.wrike.com/open.htm?id=1150941440)  
* [FEAT] Add ability to prompt leave approvers to apply for higher duties on behalf of staff taking leave  -  [Wrike](https://www.wrike.com/open.htm?id=899826254)  
#### Chores:  
* [CHORE] Alter existing alert modal content if a user attempts to submit a leave request that clashes with an existing leave request  -  [Wrike](https://www.wrike.com/open.htm?id=1174189848)  
* [CHORE] Create alert if there is a clash with duties requests  -  [Wrike](https://www.wrike.com/open.htm?id=903224023)  
* [CHORE] Show warning if leave request is cancelled and there is a higher duty request related to it  -  [Wrike](https://www.wrike.com/open.htm?id=1187975641)  
#### Fixes:  
* [FIX] Fix error when creating an RDO type - flag to show balances for managers  -  [Wrike](https://www.wrike.com/open.htm?id=1152267589)  
* [FIX] Fix error where if entering a start time and then duration you cannot cost against plant directly  -  [Wrike](https://www.wrike.com/open.htm?id=1174743198)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "Plant use by payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=932988352)  
* [FEAT] Create report "Leave accrual by payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=1163797449)  
* [FEAT] Create report "All leave balances"  -  [Wrike](https://www.wrike.com/open.htm?id=1174969262)  
#### Chores:  
* [CHORE] Add user status column to the "Indicative leave report"  -  [Wrike](https://www.wrike.com/open.htm?id=1174783172)  
* [CHORE] Add validation on reports page to prevent users to select start payruns that are after the end payrun  -  [Wrike](https://www.wrike.com/open.htm?id=1174741105)  
* [CHORE] Alter selection of report payruns to be the most recent first  -  [Wrike](https://www.wrike.com/open.htm?id=1193737142)  
#### Fixes:  
* [FIX] Fix report "Leave requests by payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=1163808300)  

---
---
---

# RELEASE aluminium-4.32  -  [Wrike](https://www.wrike.com/open.htm?id=1202908174)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where "Complete missing hours" on timesheet is not working for user on a weekly glide  -  [Wrike](https://www.wrike.com/open.htm?id=1168953493)  

## General:  
#### Fixes:  
* [FIX] Fix issue where RDO is incorrectly being accrued while the user is on leave of a type where it's set not to accrue RDO  -  [Wrike](https://www.wrike.com/open.htm?id=1172327673)  

---

# RELEASE aluminium-4.31  -  [Wrike](https://www.wrike.com/open.htm?id=1201331528)  

## General:  
#### Fixes:  
* [FIX] Fix sync error when editing leave requests - returning a 500 error to user  -  [Wrike](https://www.wrike.com/open.htm?id=1201331437)  

---

# RELEASE aluminium-4.30  -  [Wrike](https://www.wrike.com/open.htm?id=1200600045)  

## Console application:  
#### Fixes:  
* [FIX] Fix commands so they accept tenant slugs with dashes  

## General:  
#### Fixes:  
* [FIX] Fix issues with calculating balances for when applying for leave in a past open period if period has already leave of that type and pro-rata is available to be taken  -  [Wrike](https://www.wrike.com/open.htm?id=1200578794)  

---

# RELEASE aluminium-4.29  -  [Wrike](https://www.wrike.com/open.htm?id=1199740899)  

## General:  
#### Logs:  
* [LOG] Remove some warnings from the error log - it's adding too much noise  
* [LOG] Organise error log classes, so it's easier to spot them  

## General:  
#### Fixes:  
* [FIX] Fix issue where auto-allowance gets stuck when timesheet is submitted in some cases  -  [Wrike](https://www.wrike.com/open.htm?id=1199729556)  

---

# RELEASE aluminium-4.28  -  [Wrike](https://www.wrike.com/open.htm?id=1198053482)  

## Notifications:  
#### Performance:  
* [PERF] Increase memory available and timeout limit for notification trigger queue  
#### Fixes:  
* [FIX] Fix issue with some reminders that are not releasing from the queues  
* [FIX] Fix issue where some notifications are being triggered but not sent  -  [Wrike](https://www.wrike.com/open.htm?id=1196508066)  

## Payroll integrations:  
#### Features:  
* [FEAT] Create new integration rule for leave taken at % rate (half pay or double pay)  -  [Wrike](https://www.wrike.com/open.htm?id=1193694887)  

---

# RELEASE aluminium-4.27  -  [Wrike](https://www.wrike.com/open.htm?id=1197315410)  

## Notifications:  
#### Fixes:  
* [FIX] Fix issues with performance on triggering schedulable notifications - it times out and some notifications are not delivered, such as daily digest  

---

# RELEASE aluminium-4.26  -  [Wrike](https://www.wrike.com/open.htm?id=1196732181)  

## Logs:  
* [LOG] Remove some noise from error logs  

---

# RELEASE aluminium-4.25  -  [Wrike](https://www.wrike.com/open.htm?id=1196713150)  

## Reports:  
#### Fixes:  
* [FIX] Fix report "All excess-time by payrun" where it was not running when old excess-time groups were used (without association with excess-time rule table)  
* [FIX] Remove cache for reports listing page  

---

# RELEASE aluminium-4.24  -  [Wrike](https://www.wrike.com/open.htm?id=1196530487)  

## General:  
#### Fixes:  
* [FIX] Fix method signature on notification classes - it stopped the deployment of [aluminium-4.23](https://www.wrike.com/open.htm?id=1196522408)  

---

# RELEASE aluminium-4.23  -  [Wrike](https://www.wrike.com/open.htm?id=1196522408)  

## Notifications:  
#### Fixes:  
* [FIX] Fix issues with some "schedulable" notifications that are not sent correctly in all cases  -  [Wrike](https://www.wrike.com/open.htm?id=1196484924)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue when creating an allowance with the ability to split across allowance codes you cannot save the record  -  [Wrike](https://www.wrike.com/open.htm?id=1196146116)  

---

# RELEASE aluminium-4.22  -  [Wrike](https://www.wrike.com/open.htm?id=1196050616)  

## General:  
#### Performance:  
* [PERF] Fix performance issues on loading leave requests for timesheets - move load from database joins to application  
* [PERF] Fix performance issues on checking if a scheduled day has leave requests - move load from database joins to application  
* [PERF] Fix performance issues on approving and cancelling leave requests  
* [PERF] Fix performance issues on timesheet approval page  

---

# RELEASE aluminium-4.21  -  [Wrike](https://www.wrike.com/open.htm?id=1195270632)  

## Reports:  
#### Features:  
* [FEAT] Create new report called "All excess time by payrun"  -  [Wrike](https://www.wrike.com/open.htm?id=1193695370)  
#### Chores:  
* [CHORE] Remove invalid excess time report options  -  [Wrike](https://www.wrike.com/open.htm?id=1193717479)  

## Authority payroll integrations:  
#### Chores:  
* [CHORE] Update integration rule for accrued hours that splits into 2 lines to allow changing the code on positive line  -  [Wrike](https://www.wrike.com/open.htm?id=1193629371)  

---

# RELEASE aluminium-4.20  -  [Wrike](https://www.wrike.com/open.htm?id=1194465438)  

## Mobile APIs:  
#### Fixes:  
* [FIX] Fix on query that loads data for scheduled hours to show on mobile app  -  [Wrike](https://www.wrike.com/open.htm?id=1193684621)  

---

# RELEASE aluminium-4.19  -  [Wrike](https://www.wrike.com/open.htm?id=1194425379)  

## General:  
#### Performance:  
* [PERF] Update higher duties checks where the queries were adding a massive load to the database  

---

# RELEASE aluminium-4.18  -  [Wrike](https://www.wrike.com/open.htm?id=1194412767)  

## General:  
#### Features:  
* [FEAT] Add cap per instance of 1 flag to auto allowances  -  [Wrike](https://www.wrike.com/open.htm?id=1193617260)  

## Console application:  
#### Fixes:  
* [FIX] Fix command that optimises the database tables that broke because of changes on the mysql structure (on logs)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues with saving filters on timesheet listing page  
* [FIX] Fix issues with saving filters on staff management page  

---

# RELEASE aluminium-4.17  -  [Wrike](https://www.wrike.com/open.htm?id=1194379449)  

## Web application:  
#### Fixes:  
* [FIX] Fix department page that is broken  -  [Wrike](https://www.wrike.com/open.htm?id=1193717724)  

---

# RELEASE aluminium-4.16  -  [Wrike](https://www.wrike.com/open.htm?id=1193703457)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with validating payrun dates - it's affecting eNPS and some other places  -  [Wrike](https://www.wrike.com/open.htm?id=1193619467)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Amend percentages - Port Pirie - GL % split  -  [Wrike](https://www.wrike.com/open.htm?id=1185585481)  

## Mobile API:  
#### Documentation:  
* [DOC] Fix swagger documentation for timesheet services where the value of `{id}` was not accepting a string such as `"current"`  

---

# RELEASE aluminium-4.15  -  [Wrike](https://www.wrike.com/open.htm?id=1193053040)  

## General:  
* [LIB] Upgrade code to match mysql version `8.0`  -  [Wrike](https://www.wrike.com/open.htm?id=1177145155)  
* [LIB] Update minor versions of composer libraries  

---

# RELEASE aluminium-4.14  -  [Wrike](https://www.wrike.com/open.htm?id=1192856143)  

## Mobile API:  
#### Fixes:  
* [FIX] Fix issue where children plant items were required when they shouldn't be  
* [FIX] Fix error messages not correct for `testapi` site  

## Notifications:  
#### Fixes:  
* [FIX] Fix link for all excess-time notifications - it takes the user to the wrong URL  -  [Wrike](https://www.wrike.com/open.htm?id=1192840132)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Port Pirie - GL % split  -  [Wrike](https://www.wrike.com/open.htm?id=1185585481)  

---

# RELEASE aluminium-4.13  -  [Wrike](https://www.wrike.com/open.htm?id=1192064203)  

## Notifications:  
#### Fixes:  
* [FIX] Fix issue found in the logs where the notification related record is not found when being sent asynchronously  
* [FIX] Fix issue found on log for when saving notification records and it fails clearing the cache  

## Integrations:  
#### Fixes:  
* [FIX] Add scope to integration run items to add unit counts by status to be used on historical data table as it was causing memory leak for massive tables  

---

# RELEASE aluminium-4.12  -  [Wrike](https://www.wrike.com/open.htm?id=1189420457)  

## Notifications:  
#### Fixes:  
* [FIX] Fix notification type details cache not refreshing after saving  -  [Wrike](https://www.wrike.com/open.htm?id=1189351860)  
* [FIX] Fix issues with leave request reason in the notifications content messages  
* [FIX] Fix issue with caching notification / reminder status after turning it ON or OFF  -  [Wrike](https://www.wrike.com/open.htm?id=1187112639)  

## General:  
#### Fixes:  
* [FIX] Fix issue with auto allowances calculations when recording to midnight and allowance rules are based in non-scheduled hours condition  -  [Wrike](https://www.wrike.com/open.htm?id=1186448527)  

## Mobile APIs:  
#### Fixes:  
* [FIX] Add value of `requiresTime` that was missing from allowance types available to be used when recording time  

---

# RELEASE aluminium-4.11  -  [Wrike](https://www.wrike.com/open.htm?id=1189311489)  

## Public APIs:  
#### Features:  
* [FEAT] Create a new API for external systems  -  [Wrike](https://www.wrike.com/open.htm?id=1176230635)  
* [FEAT] Create a new public API service to get users from a tenant  -  [Wrike](https://www.wrike.com/open.htm?id=1187893023)  
* [FEAT] Create a new public API service to get a list of pay types from a tenant  -  [Wrike](https://www.wrike.com/open.htm?id=1187893101)  
* [FEAT] Create a new public API service to get user's workload hours  -  [Wrike](https://www.wrike.com/open.htm?id=1187893172)  

## Console application:  
#### Chores:  
* [CHORE] Update command that automatically creates timesheets to also visit past open payrun periods  

## General:  
#### Fixes:  
* [FIX] Fix issue with broadcast events not triggering correctly - affecting most of async processes  -  [Wrike](https://www.wrike.com/open.htm?id=1188638943)  

---

# RELEASE aluminium-4.10  -  [Wrike](https://www.wrike.com/open.htm?id=1187919537)  

## Notifications:  
#### Fixes:  
* [FIX] Fix links into all excess-time notifications that were pointed to pages that are not valid in the system  

---

# RELEASE aluminium-4.09  -  [Wrike](https://www.wrike.com/open.htm?id=1187888395)  

## Import tools:  
#### Features:  
* [FEAT] Create way to extract modified workorders with status changed  -  [Feat](https://www.wrike.com/open.htm?id=1171317031)  

## Notifications:  
#### Features:  
* [FEAT] Create new excess time notifications and triggers by types and rules  -  [Wrike](https://www.wrike.com/open.htm?id=1145204119)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with leave requested where an invalid value for time was being recorded (affecting notification for leave requested)  

## Logs:  
* [LOG] Add debug tracing to async notifications to check why excess-time notification is not being delivered for users  

---

# RELEASE aluminium-4.08  -  [Wrike](https://www.wrike.com/open.htm?id=1184958637)  

## Web application:  
#### Fixes:  
* [FIX] Fix error on breaks calculation when adjusting end time by duration where it deducts break even before it reaches the minimum hours to automatically deduct  -  [Wrike](https://www.wrike.com/open.htm?id=1183444077)  
* [FIX] Fix issue on total available balance when taking leave  -  [Wrike](https://www.wrike.com/open.htm?id=1182837612)  
* [FIX] Fix issue on leave balance alert notifications where balances were mixing (RDO and leave)  -  [Wrike](https://www.wrike.com/open.htm?id=1172289067)  

---

# RELEASE aluminium-4.07  -  [Wrike](https://www.wrike.com/open.htm?id=1182827793)  

## Web application:  
#### Fixes:  
* [FIX] Fix error where you cant save recorded hours if you are only required to add a duration  -  [Wrike](https://www.wrike.com/open.htm?id=1182804314)  

---

# RELEASE aluminium-4.06  -  [Wrike](https://www.wrike.com/open.htm?id=1177717533)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue when copying from schedule - period excess time not calculating in some cases  -  [Wrike](https://www.wrike.com/open.htm?id=1177638556)  
* [FIX] Fix issue when copying from previous day - it doesn't copy the breaks if on work orders  -  [Wrike](https://www.wrike.com/open.htm?id=1177717306)  

---

# RELEASE aluminium-4.05  -  [Wrike](https://www.wrike.com/open.htm?id=1176887347)  

## Web application:  
#### Fixes:  
* [FIX] Fix error on submit / approve pages where some users see a 500 error if they have penalties  -  [Wrike](https://www.wrike.com/open.htm?id=1176873974)  
* [FIX] Fix issue where when saving a work record with the option to align schedules activated it breaks the screen  -  [Wrike](https://www.wrike.com/open.htm?id=1176880586)  
* [FIX] Fix error on total hours recorded display when there is leave approved in that timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1176881505)  

## Libraries:  
* [LIB] Update `browserslist` as suggested by the library itself when used on the build process  

## Development environment:  
* [DEV] Create light version of `pm2-dev.json` file called `pm2-dev-light.json` to be used when developing functionality that doesn't require much of environment resource (mem and CPU)  

---

# RELEASE aluminium-4.04  -  [Wrike](https://www.wrike.com/open.htm?id=1176231799)  

## General:  
#### Fixes:  
* [FIX] Fix error where only leave types are showing as options to apply for leave (RDO and accrued hours disappeared)  -  [Wrike](https://www.wrike.com/open.htm?id=1176224418)  

---

# RELEASE aluminium-4.03  -  [Wrike](https://www.wrike.com/open.htm?id=1175481785)  

## API:  
#### Fixes:  
* [FIX] Fix error on API that populates timesheet that broke with a change made to add unapproved leave to total value calculation functionality  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with breaks on projects only - it says it deducts even worked hours don't meet the automatic break deduction criteria  -  [Wrike](https://www.wrike.com/open.htm?id=1175443043)  
* [FIX] Remove help text on add break button  -  [Wrike](https://www.wrike.com/open.htm?id=1175442356)  
* [FIX] Fix issue with automatic breaks not working in some cases  -  [Wrike](https://www.wrike.com/open.htm?id=1175442932)  

---

# RELEASE aluminium-4.02  -  [Wrike](https://www.wrike.com/open.htm?id=1175402938)  

## General:  
#### Performance:  
* [PERF] Remove some deprecated and unused code to improve performance  
#### Fixes:  
* [FIX] Fix value calculation that was still using a deleted field  

---

# RELEASE aluminium-4.01  -  [Wrike](https://www.wrike.com/open.htm?id=1174753400)  

## Shell application:  
#### Chore:  
* [CHORE] Update `update` command to change the message for reset password email for tenants  -  [Wrike](https://www.wrike.com/open.htm?id=1174714799)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating total daily hours after recorded hours are replaced with leave  -  [Wrike](https://www.wrike.com/open.htm?id=1174715817)  
* [FIX] Fix issue where when hours recorded has no ordinary hours but only excess-time the minimum ordinary hours issue was still triggering  -  [Wrike](https://www.wrike.com/open.htm?id=1174753088)  

## Merges:  
* [MERGE] Merge from [aluminium-3.26](https://www.wrike.com/open.htm?id=1173211011)  

---

# RELEASE aluminium-4.00  -  [Wrike](https://www.wrike.com/open.htm?id=1173203045)  

## General:  
#### Features:  
* [FEAT] Create trigger to penalty condition builder for plant items used  -  [Wrike](https://www.wrike.com/open.htm?id=1152989317)  
* [FEAT] Add trigger for penalties based on excess time  -  [Wrike](https://www.wrike.com/open.htm?id=1150816816)  
    * Based on specific excess-time rule  
    * Based on excess-time type  
* [FEAT] Create new penalty condition to set maximum hours  -  [Wrike](https://www.wrike.com/open.htm?id=1155816039)  
* [FEAT] Create new auto allowance condition to apply on excess-time hours adjacent (or not) to ordinary hours  -  [Wrike](https://www.wrike.com/open.htm?id=1076327440)  
* [FEAT] Minimum hours warning setting  -  [Wrike](https://www.wrike.com/open.htm?id=1150815369)  
#### Fixes:  
* [FIX] Fix error on auto-allowance based on excess-time rules and types where if excess-time was amended to a type that doesn't trigger allowances, the allowances are not being removed  -  [Wrike](https://www.wrike.com/open.htm?id=1166199562)  
* [FIX] Fix error on auto-allowance based on excess-time types when excess-time rule had split types, it is not triggering if the sub-item matches the trigger  -  [Wrike](https://www.wrike.com/open.htm?id=1166199689)  
* [FIX] Fix penalty overwritten rate type when assigned - it's not being used correctly on all places  -  [Wrike](https://www.wrike.com/open.htm?id=1166212273)  

## Web application:  
#### Features:  
* [FEAT] Ability to view, edit, delete and add breaks into time cards  -  [Wrike](https://www.wrike.com/open.htm?id=1146736117)  
* [FEAT] Create the ability to add / manage custom widgets to the users and managers dashboards  -  [Wrike](https://www.wrike.com/open.htm?id=1148825148)  
#### Fixes:  
* [FIX] Update content of reset password messages  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Reinstate link in excess time panel block to open relevant time card  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Alter the functionality of my timesheet page and my team > my teams timesheets so pages behave independently  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Fix link to "Manage shifts" page from main "Settings" page  
#### Chores:  
* [CHORE] Hide leave types from bank when it is inactive  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] Show unapproved leave hours as part of total hours recorded total  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] Add higher duties label to the new default view of timesheets on the my teams > my teams timesheet page  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Reports:  
#### Features:  
* [FEAT] Create two new report versions for big councils  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
#### Fixes:  
* [FIX] Fix issue with reports page generate report buttons being available without options selected if you refresh the page  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Fix report where option exists to run for all staff - through not selecting a department but instead when not selecting a department the report just does not generate  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [FIX] Fix ordinary hours value on payment view when there is amended excess-time - the excess-time is being double deducted from hours recorded  -  [Wrike](https://www.wrike.com/open.htm?id=1166203233)  
#### Chores:  
* [CHORE] Replace dollar value of allowances with quantity of allowance on detailed checklist download report  -  [Wrike](https://www.wrike.com/open.htm?id=1138169241)  

---
---
---

# RELEASE aluminium-3.26  -  [Wrike](https://www.wrike.com/open.htm?id=1173211011)  

## General:  
#### Fixes:  
* [FIX] Fix work order relationship issue with deleted departments  

---

# RELEASE aluminium-3.25  -  [Wrike](https://www.wrike.com/open.htm?id=1173190129)  

## Web application:  
#### Performance:  
* [PERF] Fix performance issues on loading time-card panel when user has a lot of work orders available  -  [Wrike](https://www.wrike.com/open.htm?id=1173187863)  

---

# RELEASE aluminium-3.24  -  [Wrike](https://www.wrike.com/open.htm?id=1172371335)  

## API:  
#### Fixes:  
* [FIX] Fix error with user image on mobile apps API  

## Web application:  
#### Fixes:  
* [FIX] Work order name masking does not apply to non standard work orders  -  [Wrike](https://www.wrike.com/open.htm?id=1162001614)  

## Reports:  
#### Features:  
* [FEAT] Create new report "Total costing report by day for all users"  -  [Wrike](https://www.wrike.com/open.htm?id=1162000155)  

---

# RELEASE aluminium-3.23  -  [Wrike](https://www.wrike.com/open.htm?id=1171327936)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating allowances triggered by excess-time type  -  [Wrike](https://www.wrike.com/open.htm?id=1161869384)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where when adding a comment against an excess-time from an issue, it doesn't automatically update the submit / approve page  -  [Wrike](https://www.wrike.com/open.htm?id=1168971957)  
* [FIX] Fix error when access the other task page from the managers dashboard it opens the old page  -  [Wrike](https://www.wrike.com/open.htm?id=1167655375)  

---

# RELEASE aluminium-3.22  -  [Wrike](https://www.wrike.com/open.htm?id=1167738990)  

## API:  
#### Chores:  
* [CHORE] Add custom contents to plant items and allowance types to be used on time recorder  

---

# RELEASE aluminium-3.21  -  [Wrike](https://www.wrike.com/open.htm?id=1165597184)  

## API:  
#### Fixes:  
* [FIX] Fix issue with recording time using API v3  

---

# RELEASE aluminium-3.20  -  [Wrike](https://www.wrike.com/open.htm?id=1163605928)  

## API:  
#### Features:  
* [FEAT] API - Create new api services to show leave balances and forecast on the app  -  [Wrike](https://www.wrike.com/open.htm?id=1153468884)  

---

# RELEASE aluminium-3.19  -  [Wrike](https://www.wrike.com/open.htm?id=1163538590)  

## Web application:  
#### Fixes:  
* [FIX] Fix massive performance issue on excess-time list when approved flag was set as ON, on other requests page  
* [FIX] Fix memory leak issue when using excess-time workflows that uses timesheet approvers  

---

# RELEASE aluminium-3.18  -  [Wrike](https://www.wrike.com/open.htm?id=1163468486)  

## Web application:  
#### Chores:  
* [CHORE] Amend my teams other requests page with request type split by tab and filters  -  [Wrike](https://www.wrike.com/open.htm?id=1153078192)  
#### Fixes:  
* [FIX] Fix URL params for specific content into modals - time-cards, plant-cards, leave requests, scheduled days and duty requests  -  [Wrike](https://www.wrike.com/open.htm?id=1161037304)  
* [FIX] Fix "My other requests" page as it only shows an error and no longer works  -  [Wrike](https://www.wrike.com/open.htm?id=1161947268)  

## API:  
#### Features:  
* [FEAT] API - Create API v3 for new app - so changes on API won't affect current app  -  [Wrike](https://www.wrike.com/open.htm?id=1154224661)  
#### Fixes:  
* [FIX] API - Fix multiple roles on dashboard service  -  [Wrike](https://www.wrike.com/open.htm?id=1156930826)  
* [FIX] API - Fix missing response on documentation for API v2  
* [FIX] API - Fix swagger documentation for all `time-card` services where the day ID was not accepting `"current"` as value  
#### Chores:  
* [CHORE] API - Add timezone info to tenant service  -  [Wrike](https://www.wrike.com/open.htm?id=1156933407)  
* [CHORE] API - Add documentation for service to save time record  -  [Wrike](https://www.wrike.com/open.htm?id=1156935775)  
* [CHORE] API - If time is recorded via real-time recorder then breaks should not be deducted automatically  -  [Wrike](https://www.wrike.com/open.htm?id=1156981137)  
* [CHORE] API - Add missing information needed to populate apps dashboard  -  [Wrike](https://www.wrike.com/open.htm?id=1153468211)  
* [CHORE] API - Add missing info needed to populate leave page in the app  -  [Wrike](https://www.wrike.com/open.htm?id=1153468760)  
* [CHORE] API - Add scheduled hours info on api to be shown beside role name on the app when recording time  -  [Wrike](https://www.wrike.com/open.htm?id=1153467138)  
* [CHORE] API - Remove project activities from `time-card/config` service from users with work orders  
* [CHORE] API - Add missing plant and allowances info into config service  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  
* [CHORE] API - Fix error message when user logged is locked  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-3.17  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Payroll integrations:  
#### Chores:  
* [CHORE] Update integration rule that passes accrued hours with negative row to allow overriding cost account on negative lines  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-3.16  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with recording plant when hours is set as OFF but required hours flag is ON  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-3.15  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when filtering projects when project code mask doesn't have both project code and activity code in it  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-3.14  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues where terminated employees are still being displayed in the staff hierarchy in some cases  -  [Wrike](https://www.wrike.com/open.htm?id=**********)  

---

# RELEASE aluminium-3.13  -  [Wrike](https://www.wrike.com/open.htm?id=1160325755)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where duties list is missing under Staff management > Timesheet Options > General > Duties  -  [Wrike](https://www.wrike.com/open.htm?id=1158721421)  
* [FIX] Fix issue where making a work order activity inactive doesn't stop the ability to record time against it  -  [Wrike](https://www.wrike.com/open.htm?id=1160302001)  
* [FIX] Fix issue on work orders activity form for when adding new activity, adding it to a group doesn't work as expected  -  [Wrike](https://www.wrike.com/open.htm?id=1160302636)  
* [FIX] Fix issue where making changes to activity groups it doesn't affected the associated work orders automatically  -  [Wrike](https://www.wrike.com/open.htm?id=1160302558)  
* [FIX] Force day record ID to be updated in case the schedule and time recorded don't match when saving new time record  -  [Wrike](https://www.wrike.com/open.htm?id=1160322539)  

---

# RELEASE aluminium-3.12  -  [Wrike](https://www.wrike.com/open.htm?id=1158888531)  

## General:  
#### Fixes:  
* [FIX] Fix excess-time calculation error for when calculating excess-time only - the number of hours applied were wrong when triggering levels that are not the first and not the last  

---

# RELEASE aluminium-3.11  -  [Wrike](https://www.wrike.com/open.htm?id=1158850380)  

## General:  
#### Fixes:  
* [FIX] Fix excess-time calculation error for when calculating excess-time only - it was skipping the first levels  
* [FIX] Fix excess-time calculation error for duration only on glide shifts with zero scheduled hours on period  

---

# RELEASE aluminium-3.10  -  [Wrike](https://www.wrike.com/open.htm?id=1157966958)  

## General:  
#### Fixes:  
* [FIX] Fix issue with leave balances calculations for when there are too many terminated users in the system  

---

# RELEASE aluminium-3.09  -  [Wrike](https://www.wrike.com/open.htm?id=1155790013)  

## General:  
#### Fixes:  
* [FIX] Fix excess-time calculations error for duration only glide period shifts  -  [Wrike](https://www.wrike.com/open.htm?id=1155789918)  

---

# RELEASE aluminium-3.08  -  [Wrike](https://www.wrike.com/open.htm?id=1154983501)  

## Reports / integrations data-sources:  
#### Chores:  
* [CHORE] Add ability to deduct RDO earnt hours from rows on data-source `PayRunDetails`  

## Reports:  
#### Chores:  
* [CHORE] changes to key pay extract report  -  [Wrike](https://www.wrike.com/open.htm?id=1154910656)  
    * Pass actual hours with minimum applied instead of adjusted hours for excess-time  
    * Remove any number roundings - pass all values as absolute  
    * Remove all accrued RDO from the ordinary hours  
#### Hardcode:  
* [HARDCODE] Hardcode splits for keypay import file for NSGC  -  [Wrike](https://www.wrike.com/open.htm?id=1154925731)  

---

# RELEASE aluminium-3.07  -  [Wrike](https://www.wrike.com/open.htm?id=1153435576)  

## Payroll integrations:  
#### Chores:  
* [CHORE] Add more context to API call error when it happens when integrating with Authority  

---

# RELEASE aluminium-3.06  -  [Wrike](https://www.wrike.com/open.htm?id=1153433257)  

## Reports:  
#### Fixes:  
* [FIX] Fix caching error for getting report individually - affecting only detailed check file when running from timesheets list page  

---

# RELEASE aluminium-3.05  -  [Wrike](https://www.wrike.com/open.htm?id=1153427226)  

## Notifications:  
#### Fixes:  
* [FIX] Fix caching error on notification types  

---

# RELEASE aluminium-3.04  -  [Wrike](https://www.wrike.com/open.htm?id=1152948976)  

## Web applications:  
#### Fixes:  
* [FIX] Fix issue with duplicated users showing on the balances table - under my teams leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1152934821)  
* [FIX] Fix issue where sub-reports dashboards were not showing for managers (even the item showed on the drop-down)  -  [Wrike](https://www.wrike.com/open.htm?id=1152938348)  
* [FIX] Fix FTE count by department table that shows data for FTE count by pay band  -  [Wrike](https://www.wrike.com/open.htm?id=1152942074)  
* [FIX] Fix new notification email being sent without any data  -  [Wrike](https://www.wrike.com/open.htm?id=1152948070)  
* [FIX] Fix layout issues on page "Department calendars"  

---

# RELEASE aluminium-3.03  -  [Wrike](https://www.wrike.com/open.htm?id=1152320530)  

## Web application:  
#### Fixes:  
* [FIX] Fix time-card when work orders are added after allowances have been added to master project  -  [Wrike](https://www.wrike.com/open.htm?id=1152317507)  



# RELEASE aluminium-3.02  -  [Wrike](https://www.wrike.com/open.htm?id=1152310859)  

## Web application:  
#### Fixes:  
* [FIX] Fix time-out error on "Other tasks" page  -  [Wrike](https://www.wrike.com/open.htm?id=1151387862)  

---

# RELEASE aluminium-3.01  -  [Wrike](https://www.wrike.com/open.htm?id=1152270327)  

## Web application:  
#### Fixes:  
* [FIX] Fix error where payrun processing page is broken  -  [Wrike](https://www.wrike.com/open.htm?id=1151437600)  

---

# RELEASE aluminium-3.00  -  [Wrike](https://www.wrike.com/open.htm?id=1151366593)  
__Base version: aluminium-2.14.1__  

## Web application:  
#### Features:  
* [FEAT] Add simpler view to timesheets listing page  -  [Wrike](https://www.wrike.com/open.htm?id=1136146780)  
* [FEAT] Add ability to shift eNPS to other payroll to see data at point of time  -  [Wrike](https://www.wrike.com/open.htm?id=1124491530)  
* [FEAT] Add list of timesheets to users summary page  -  [Wrike](https://www.wrike.com/open.htm?id=1136083546)  
* [FEAT] Add excess-time requests to be part of other tasks page for approvers  -  [Wrike](https://www.wrike.com/open.htm?id=1111304119)  
#### Chores:  
* [CHORE] Amend rounding issue when displaying issues  -  [Wrike](https://www.wrike.com/open.htm?id=1136076490)  
* [CHORE] Add committed balance into the my teams leave details dashboard view  -  [Wrike](https://www.wrike.com/open.htm?id=1130975588)  
* [CHORE] Show leave transaction history for terminated employees  -  [Wrike](https://www.wrike.com/open.htm?id=1136061942)  
* [CHORE] Ensure higher duties requests inherit the settings of the user acted as  -  [Wrike](https://www.wrike.com/open.htm?id=1126991071)  
* [CHORE] Where if staff record plant use against master project / work order prior to allocating time to sub work orders the plant use remains against the master  -  [Wrike](https://www.wrike.com/open.htm?id=1135985526)  
* [CHORE] Hide people without leave from balances table on Leave details page  -  [Wrike](https://www.wrike.com/open.htm?id=1071210192)  
* [CHORE] Provide more control over what managers have access to what  -  [Wrike](https://www.wrike.com/open.htm?id=1126896129)  
* [CHORE] Alter payroll dashboard layout to improve performance  -  [Wrike](https://www.wrike.com/open.htm?id=1136146908)  
* [CHORE] Alter personal dashboard - time worked graph  -  [Wrike](https://www.wrike.com/open.htm?id=1136146985)  
* [CHORE] Add the ability to view the role name linked to a shift with the shift name on the timesheet schedule view  -  [Wrike](https://www.wrike.com/open.htm?id=1144455192)  
* [CHORE] Add HR manager to system access filter on Staff management  -  [Wrike](https://www.wrike.com/open.htm?id=1140481195)  
* [CHORE] Create information modal when using copy from previous day functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1142750254)  
* [CHORE] Alter content on warning modal when copying from previous timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1142749244)  
#### Fixes:  
* [FIX] Fix copy from previous period error when time types are no longer valid  -  [Wrike](https://www.wrike.com/open.htm?id=1116931168)  
* [FIX] Fix end time calculation by changing the duration when creating a shift  -  [Wrike](https://www.wrike.com/open.htm?id=1149829954)  

## Notifications / Reminders:  
#### Features:  
* [FEAT] Create new who is away daily digest  -  [Wrike](https://www.wrike.com/open.htm?id=1116280159)  

## Reports:  
#### Chores:  
* [CHORE] Show sub-departments on filter for daily cost report to department managers  -  [Wrike](https://www.wrike.com/open.htm?id=1145946147)  
* [CHORE] Change eNPS responses report label  -  [Wrike](https://www.wrike.com/open.htm?id=1136833695)  

## General:  
#### Fixes:  
* [FIX] Fix issue eNPS feedback being sent without content  -  [Wrike](https://www.wrike.com/open.htm?id=1140606772)  
#### Performance:  
* [PERF] Update system to use the reader connection beside the writer as much as possible  -  [Wrike](https://www.wrike.com/open.htm?id=1137505235)  
* [PERF] Update the way we store tenant database connection to improve performance  -  [Wrike](https://www.wrike.com/open.htm?id=1140038799)  
* [PERF] Cache tenant basic information and configuration to be shared among requests for a while  -  [Wrike](https://www.wrike.com/open.htm?id=1140038757)  

---
---
---

# RELEASE aluminium-2.14.1  -  [Wrike](https://www.wrike.com/open.htm?id=1149042156)  

## API:  
#### Chores:  
* [CHORE] Continue organising API v3  
* [CHORE] Change Locked HTTP code from `201` to `200`  -  [Wrike](https://www.wrike.com/open.htm?id=1149042477)  

---

# RELEASE aluminium-2.14  -  [Wrike](https://www.wrike.com/open.htm?id=1146743411)  

## Libraries:  
#### Fixes:  
* [FIX] Fix bower library that was renamed (thank you bower!)  

## General:  
#### Fixes:  
* [FIX] Fix issue found in the log that happens on backend with user views old timesheets and they didn't have a pay-type associated with an hour recorded  

---

# RELEASE aluminium-2.13  -  [Wrike](https://www.wrike.com/open.htm?id=1146739504)  

## Reports:  
#### Features:  
* [FEAT] Create new csv report: Keypay batch file  -  [Wrike](https://www.wrike.com/open.htm?id=1146099860)  
#### Chores:  
* [CHORE] Update reports so new reports are set as inactive by default  

## General:  
#### Fixes:  
* [FIX] Fix issue with timesheet calculations when leave is approved after timesheet submission by an approver that is not part of timesheet workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1146726834)  

---

# RELEASE aluminium-2.12  -  [Wrike](https://www.wrike.com/open.htm?id=1144392237)  

## Web application:  
#### Fixes:  
* [FIX] Fix issues with "Complete missing hours" button  -  [Wrike](https://www.wrike.com/open.htm?id=1144391996)  

---

# RELEASE aluminium-2.11  -  [Wrike](https://www.wrike.com/open.htm?id=1143581747)  

## Web application:  
#### Fixes:  
* [FIX] Fix error on trying to solve issues on timesheet approval page  -  [Wrike](https://www.wrike.com/open.htm?id=1143565938)  

---

# RELEASE aluminium-2.10  -  [Wrike](https://www.wrike.com/open.htm?id=1143564534)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when costing excess time against multiple work orders if the copy from previous day functionality is used  -  [Wrike](https://www.wrike.com/open.htm?id=1106401236)  

## API:  
#### Chores:  
* [CHORE] Add tenant theme info to service `GET .../tenant/current`  -  [Wrike](https://www.wrike.com/open.htm?id=1143519688)  
#### Fixes:  
* [FIX] Fix swagger documentation for API service `POST .../auth/login`  -  [Wrike](https://www.wrike.com/open.htm?id=1143520113)  
* [FIX] Fix errors on API service to logout user  -  [Wrike](https://www.wrike.com/open.htm?id=1141912757)  

---

# RELEASE aluminium-2.09  -  [Wrike](https://www.wrike.com/open.htm?id=1142725035)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where timecard form breaks when clicking on "Save and continue"  -  [Wrike](https://www.wrike.com/open.htm?id=1142680937)  
* [FIX] Fix issue where randomly the recording panel doesn't open automatically on timecards  -  [Wrike](https://www.wrike.com/open.htm?id=1142724371)  
#### Chores:  
* [CHORE] Allow users that are not in the excess-time workflow to request amendments if they are on timesheet workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1141234498)  

## Deploy:  
#### Fix:  
* [FIX] Fix issues with color when deploying to environments where hosts count is zero (currently stopped)  

---

# RELEASE aluminium-2.08  -  [Wrike](https://www.wrike.com/open.htm?id=1141898174)  

## API:  
#### Chores:  
* [CHORE] Add user info to return of service `.../auth/session/take`  -  [Wrike](https://www.wrike.com/open.htm?id=1141897912)  
* [CHORE] Update documentation for service `.../auth/session/take` to add missing authorisation  -  [Wrike](https://www.wrike.com/open.htm?id=1141897957)  

---

# RELEASE aluminium-2.07  -  [Wrike](https://www.wrike.com/open.htm?id=1141208264)  

## Web application:  
#### Fixes:  
* [FIX] Check status of workflow to ensure approve button is only showing when valid for user  -  [Wrike](https://www.wrike.com/open.htm?id=1137715990)  
* [FIX] Fix issue where managers could still make changes to a timesheet after they had already approved it  -  [Wrike](https://www.wrike.com/open.htm?id=1140420848)  
* [FIX] Fix issue on approval screen when issues list doesn't update correctly after changes to the timesheet  -  [Wrike](https://www.wrike.com/open.htm?id=1137552670)  
* [FIX] Fix error on time-card with navigating among days by the buttons on the time-card header (previous / next)  -  [Wrike](https://www.wrike.com/open.htm?id=1140464551)  
* [FIX] Fix issue with recording panel on time-card not loading automatically all times  -  [Wrike](https://www.wrike.com/open.htm?id=1140464492)  
* [FIX] Excess time once fully approved cannot be amended or switched to the secondary rule  -  [Wrike](https://www.wrike.com/open.htm?id=1128150766)  

## Reports:  
#### Fixes:  
* [FIX] Fix report filters for detailed check report into my teams timesheets  -  [Wrike](https://www.wrike.com/open.htm?id=1138268820)  

---

# RELEASE aluminium-2.06  -  [Wrike](https://www.wrike.com/open.htm?id=1138238899)  

## Reports / payroll integrations / data-sources:  
#### Chores:  
* [CHORE] Alter synergy upload file to add higher duty pay code for accrued hours  -  [Wrike](https://www.wrike.com/open.htm?id=1136100321)  
* [CHORE] Pass higher duty pay codes to accrued hours lines on Authority integrations  -  [Wrike](https://www.wrike.com/open.htm?id=989334289)  
#### Fixes:  
* [FIX] Fix issue with detailed check report where only one timesheet is being returned  -   [Wrike](https://www.wrike.com/open.htm?id=1138233572)  

---

# RELEASE aluminium-2.05  -  [Wrike](https://www.wrike.com/open.htm?id=1138207144)  

## Web applications:  
#### Fixes:  
* [FIX] Fix "Login with Microsoft" button for Azure integration when on smaller screens  -  [Wrike](https://www.wrike.com/open.htm?id=1137508613)  
* [FIX] Fix issues with recording panel into timecard  -  [Wrike](https://www.wrike.com/open.htm?id=1138175080)  

---

# RELEASE aluminium-2.04  -  [Wrike](https://www.wrike.com/open.htm?id=1137484702)  

## General:  
#### Fixes:  
* [FIX] Fix issue on the process caching data where the logged user was being used to create the key and it was causing errors in some places - including the mobile app API  

---

# RELEASE aluminium-2.03  -  [Wrike](https://www.wrike.com/open.htm?id=1137444088)  

## Payroll integrations:  
#### Chores:  
* [CHORE] Add more context to errors that happen when pushing timesheets to be easier traced  

---

# RELEASE aluminium-2.02  -  [Wrike](https://www.wrike.com/open.htm?id=1136840898)  

## General:  
#### Fixes:  
* [FIX] Fix transaction / sync error when generating report records when a deploy happens  

## Reports:  
#### Fixes:  
* [FIX] Fix daily cost report to show costing against work order and plant separately from master project without duplicating  -  [Wrike](https://www.wrike.com/open.htm?id=1136795047)  

## Merges:  
* [MERGE] Merge from [aluminium-1.24](https://www.wrike.com/open.htm?id=1136753137)  

---

# RELEASE aluminium-2.01  -  [Wrike](https://www.wrike.com/open.htm?id=1130869361)  

## Integrations:  
#### Chores:  
* [CHORE] Alter the management of penalties via integration to use flags in the settings table  -  [Wrike](https://www.wrike.com/open.htm?id=1117876945)  

---

# RELEASE aluminium-2.00  -  [Wrike](https://www.wrike.com/open.htm?id=1130847575)  
__Base version: aluminium-1.23__  

## Reports:  
#### Features:  
* [FEAT] Create the ability for users to download their own reports of hours by time type  -  [Wrike](https://www.wrike.com/open.htm?id=1094193609)  
* [FEAT] Create new daily work costing report  -  [Wrike](https://www.wrike.com/open.htm?id=1116280099)  
#### Chores:  
* [CHORE] Update how system checks if user has access to certain reports to use system access flags  
* [CHORE] Add to summary report if user is included / excluded from eNPS functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1116288382)  
#### Fixes:  
* [FIX] Fix department column of report "Hours by time type by users over time"  -  [Wrike](https://www.wrike.com/open.htm?id=1105883780)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where eNPS was showing to only one user per payrun - from the second user, it was not prompting them to respond to it  -  [Wrike](https://www.wrike.com/open.htm?id=1128152194)  
#### Chores:  
* [CHORE] Show FTE adjusted hours as well as actual to leave request when making the request  -  [Wrike](https://www.wrike.com/open.htm?id=1096557823)  
* [CHORE] Update eNPS dashboards  -  [Wrike](https://www.wrike.com/open.htm?id=1117003705)  
* [CHORE] Hide payment values from penalty, allowance and excess time pages  -  [Wrike](https://www.wrike.com/open.htm?id=1118682222)  
* [CHORE] Update clashes warning flag when applying for leave  -  [Wrike](https://www.wrike.com/open.htm?id=1096513748)  
* [CHORE] Alter schedules to also show leave requests if waiting for approval  -  [Wrike](https://www.wrike.com/open.htm?id=1125917963)  
#### Features:  
* [FEAT] Create eNPS notification for comments at time of submission  -  [Wrike](https://www.wrike.com/open.htm?id=1118662618)  
#### Performance improvements:  
* [PERF] Improve performance in the time-card by separating some services that are not necessary in all cases  -  [Wrike](https://www.wrike.com/open.htm?id=1127400707)  

## General:  
#### Fixes:  
* [FIX] Fix basic FTE calculation for leave requests  
#### Libs:  
* [LIB] Framework updates  -  [Wrike](https://www.wrike.com/open.htm?id=1078063324)  

## Integrations:  
#### Chores:  
* [CHORE] Add field allowing users to set work order status codes to ignore during integration syncing  -  [Wrike](https://www.wrike.com/open.htm?id=1088827748)  

---
---
---

# RELEASE aluminium-1.24  -  [Wrike](https://www.wrike.com/open.htm?id=1136753137)  

## Web application:  
#### Fixes:  
* [FIX] Fix error 500: unknown when copying timesheet from schedule  -  [Wrike](https://www.wrike.com/open.htm?id=1130829696)  
* [FIX] Alter workflow approval for excess time so Payroll can alter excess time even if it has been approved and they are not on the workflow  -  [Wrike](https://www.wrike.com/open.htm?id=1129989319)  
* [FIX] Fix excess-time error on the payment view  -  [Wrike](https://www.wrike.com/open.htm?id=1128435872)  
* [FIX] Fix penalty rates calculation issues  -  [Wrike](https://www.wrike.com/open.htm?id=1136080596)  
## General:  
#### Fixes:  
* [FIX] Fix error 9090: please check excess time calculations - when user has offset penalties and period excess-time  -  [Wrike](https://www.wrike.com/open.htm?id=1136712202)  

---

# RELEASE aluminium-1.23  -  [Wrike](https://www.wrike.com/open.htm?id=1130030359)  

## Web application:  
#### Chores:  
* [CHORE] Remove irrelevant filters from "My timesheets" page  -  [Wrike](https://www.wrike.com/open.htm?id=1130030291)  

---

# RELEASE aluminium-1.22  -  [Wrike](https://www.wrike.com/open.htm?id=1130019027)  

## Web application:  
#### Fixes:  
* [FIX] 500 error when accessing the page "My Timehseets"  -  [Wrike](https://www.wrike.com/open.htm?id=1129266399)  

---

# RELEASE aluminium-1.21  -  [Wrike](https://www.wrike.com/open.htm?id=1128471871)  

## Reports/integrations data-sources:  
#### Fixes:  
* [FIX] Fix error with excess-time recorded in the payrun data-source that populate entry reports and Authority integrations  -  [Wrike](https://www.wrike.com/open.htm?id=1128468303)  

---

# RELEASE aluminium-1.20  -  [Wrike](https://www.wrike.com/open.htm?id=1128083399)  

## Reports:  
#### Hardcode:  
* [HARDCODE] Update splits for PPC Synergy CSV file  -  [Wrike](https://www.wrike.com/open.htm?id=1128083253)  

---

# RELEASE aluminium-1.19  -  [Wrike](https://www.wrike.com/open.htm?id=1127377080)  

## General:  
#### Fixes:  
* [FIX] Fix issue with auto-completing timesheets that was not working in all cases  -  [Wrike](https://www.wrike.com/open.htm?id=1127355927)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where recording time with allowance doesn't  work in some cases  -  [Wrike](https://www.wrike.com/open.htm?id=1127356412)  
* [FIX] Fix 500 error on payment view when staff records excess-time to an old excess-time group format  -  [Wrike](https://www.wrike.com/open.htm?id=1127345500)  

---

# RELEASE aluminium-1.18  -  [Wrike](https://www.wrike.com/open.htm?id=1126818496)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue where fixed allowances can't be recorded into time-cards anymore as the field validation broke  

---

# RELEASE aluminium-1.17  -  [Wrike](https://www.wrike.com/open.htm?id=1126712721)  

## Web application:  
#### Fixes:  
* [FIX] Fix payment view that returns an error when there is any leave request from RDO types or accrued hours (TIL, TOIL, Flexitime, etc)  -  [Wrike](https://www.wrike.com/open.htm?id=1126712835)  

---

# RELEASE aluminium-1.16  -  [Wrike](https://www.wrike.com/open.htm?id=1126704262)  

## Notifications:  
#### Fixes:  
* [FIX] Fix transaction error found on logs where some deadlocks are found when the call-back happens from elementSUP  -  [Wrike](https://www.wrike.com/open.htm?id=1126695144)  

## Web application:  
#### Fixes:  
* [FIX] Fix error when recording time with allowances, it allows the user to have a blank allowance, and it just gives a 500 error  -  [Wrike](https://www.wrike.com/open.htm?id=1126701364)  

---

# RELEASE aluminium-1.15  -  [Wrike](https://www.wrike.com/open.htm?id=1125920538)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with applying for leave by duration where the duration always rounds down to the hour  -  [Wrike](https://www.wrike.com/open.htm?id=1125917839)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue where work patterns report won't work for periods where there are public holidays  -  [Wrike](https://www.wrike.com/open.htm?id=1125911944)  
* [FIX] Fix error when reports won't generate if they have more than 702 columns (three letter column names)  -  [Wrike](https://www.wrike.com/open.htm?id=1125912370)  

---

# RELEASE aluminium-1.14  -  [Wrike](https://www.wrike.com/open.htm?id=1125124288)  

## General:  
#### Fixes:  
* [FIX] Fix issue with when excess-time group is created, assigned, used, unassigned and deleted all in the same payrun period, it breaks the excess-time calculated  -  [Wrike](https://www.wrike.com/open.htm?id=1124532867)  

---

# RELEASE aluminium-1.13  -  [Wrike](https://www.wrike.com/open.htm?id=1124498862)  

## Web application:  
#### Fixes:  
* [FIX] Fix error with scheduled hours calculation when recalculating timesheets  

---

# RELEASE aluminium-1.12  -  [Wrike](https://www.wrike.com/open.htm?id=1119242411)  

## General:  
#### Fixes:  
* [FIX] Fix little issue that shows on log on model `TimeSheetExcessTimeItem` when saving a record  
* [FIX] Fix issue where when time is recorded via auto-complete, it generates excess-time, the excess-time instance is submitted by "Customer Love", not the user themselves  
* [FIX] Fix error on period excess-time calculations when cancelling leave  

## Reports:  
#### Chores:  
* [CHORE] Tidy up checklist report total values  -  [Wrike](https://www.wrike.com/open.htm?id=1116247902)  

---

# RELEASE aluminium-1.11  -  [Wrike](https://www.wrike.com/open.htm?id=1118479874)  

__First LIVE release__  

## General:  
#### Chores:  
* [CHORE] Add condition for excess-time workflows to trigger it by excess-time group  -  [Wrike](https://www.wrike.com/open.htm?id=1118471192)  

## Web application:  
#### Fixes:  
* [FIX] Fix error shown on logs for when customer love user opens the personal dashboard and there are no valid pay type associated  

---

# RELEASE aluminium-1.10  -  [Wrike](https://www.wrike.com/open.htm?id=1118467437)  

## General:  
#### Deprecated:  
* [DEPRECATED] Deprecate `externalId` property/field from model/table `ExcessTimeRule`  

## Web application:  
#### Fixes:  
* [FIX] Please change the label to for general comments - currently incorrect  -  [Wrike](https://www.wrike.com/open.htm?id=1116295232)  
* [FIX] Fix total value blocks on time-cards that shows incorrect values for some users  -  [Wrike](https://www.wrike.com/open.htm?id=1118459140)  
* [FIX] Fix issue where when changes are made to a submitted timesheet a line is not being created in the workflow timeline  -  [Wrike](https://www.wrike.com/open.htm?id=1118461830)  
* [FIX] Fix issue where when recording excess-time across midnight, it doesn't create an excess-time instance on the next day automatically  -  [Wrike](https://www.wrike.com/open.htm?id=1118464507)  
* [FIX] Fix issue on excess-time workflows where records are automatically approved if recorded by payroll officer  -  [Wrike](https://www.wrike.com/open.htm?id=1118466727)  

## API:  
#### Chores:  
* [CHORE] Add logo to API service that returns the data from current tenant  -  [Wrike](https://www.wrike.com/open.htm?id=1117956768)  
#### Documentation:  
* [DOC] Add documentation for tenant API service  -  [Wrike](https://www.wrike.com/open.htm?id=1116963929)  

---

# RELEASE aluminium-1.09  -  [Wrike](https://www.wrike.com/open.htm?id=1117739199)  

## Web application:  
#### Chores:  
* [CHORE] Remove old ext ref ID code from excess-time rules as they are now part of the split items  -  [Wrike](https://www.wrike.com/open.htm?id=1116288165)  

## General:  
#### Fixes:  
* [FIX] Fix issue with ext ref ID not being passed to split items when upgrading instances to aluminium  -  [Wrike](https://www.wrike.com/open.htm?id=1116288007)  
#### Chores:  
* [CHORE] Add option to excess-time workflows approval options to allow "One approver from timesheet workflow" to be added  -  [Wrike](https://www.wrike.com/open.htm?id=1117737846)  

---

# RELEASE aluminium-1.08  -  [Wrike](https://www.wrike.com/open.htm?id=1116286735)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with filters for detailed checklist report  -  [Wrike](https://www.wrike.com/open.htm?id=1116274314)  

---

# RELEASE aluminium-1.07  -  [Wrike](https://www.wrike.com/open.htm?id=1116275930)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with tabbing across fields on time-card for 12h clock  -  [Wrike](https://www.wrike.com/open.htm?id=1111396694)  
* [FIX] Fix issue with rebuilding excess-time workflows where requests became un-submitted  -  [Wrike](https://www.wrike.com/open.htm?id=1111396862)  
* [FIX] Fix issue with socket when workflow is replaced - it was causing a big load as it wasn't clearing the queue  -  [Wrike](https://www.wrike.com/open.htm?id=1111396913)  
* [FIX] Fix issue with excess-time workflows for when role managers are responsible for approval it didn't work  -  [Wrike](https://www.wrike.com/open.htm?id=1112123959)  
* [FIX] Fix help text to match the label when enabling user for eNPS functionality  -  [Wrike](https://www.wrike.com/open.htm?id=1111423130)  
* [FIX] Fix flags that checks if user has access to eNPS and dashboards individually  -  [Wrike](https://www.wrike.com/open.htm?id=1116238740)  
* [FIX] Fix issue where when updating a settings value the change was not taking effect until next page refresh  
#### Chores:  
* [CHORE] Amend timesheet workflows to check sub-workflows based on user responsibilities  -  [Wrike](https://www.wrike.com/open.htm?id=1111397131)  
#### Features:  
* [FEAT] Add Workflow condition type for excess-time to be triggered in case excess-time is recorded across midnight  -  [Wrike](https://www.wrike.com/open.htm?id=1111396959)  

## Merges:  
* [MERGE] Merge from magnesium-10.16  -  [Wrike](https://www.wrike.com/open.htm?id=1116221593)  

---

# RELEASE aluminium-1.06  -  [Wrike](https://www.wrike.com/open.htm?id=1108909349)  

## Merges:  
* [MERGE] Merge from magnesium-10.13  -  [Wrike](https://www.wrike.com/open.htm?id=1103062997)  
* [MERGE] Merge from magnesium-10.14  -  [Wrike](https://www.wrike.com/open.htm?id=1105847809)  
* [MERGE] Merge from magnesium-10.15  -  [Wrike](https://www.wrike.com/open.htm?id=1105851319)  

## General:  
#### Fixes:  
* [FIX] Fix type error with validating workflow details (error 500 when trying to save workflow)  

---

# RELEASE aluminium-1.05  -  [Wrike](https://www.wrike.com/open.htm?id=1103837717)  

## General:  
#### Fixes:  
* [FIX] Fix issue with calculating excess-time with old groups where it was not using setup rules so it has no splits  
* [FIX] Fix issue with workflow not setting correctly for timesheets as they were mixing with excess-time records with the same ID (this was affecting lists, workflows and integrations)  

---

# RELEASE aluminium-1.04  -  [Wrike](https://www.wrike.com/open.htm?id=1102271521)  

## Libraries:  
#### Fixes:  
* [FIX] Force version for library to be used `ui-scroll` as it was updated and the changes broke the deployment  

## Web application:  
#### Fixes:  
* [FIX] When assigning a role manager it allows you to select and assign a terminated user  -  [Wrike](https://www.wrike.com/open.htm?id=1098879339)  

---

# RELEASE aluminium-1.03  -  [Wrike](https://www.wrike.com/open.htm?id=1102223170)  

## General:  
#### Features:  
* [PACKAGE] Create new eNPS function  -  [Wrike](https://www.wrike.com/open.htm?id=1013856950)  
    * [FEAT] Create new eNPS dashboard page  -  [Wrike](https://www.wrike.com/open.htm?id=1063171239)  

## Reports:  
#### Fixes:  
* [FIX] Fix issue with not being able to select departments when selecting work patterns by department report  -  [Wrike](https://www.wrike.com/open.htm?id=1100560089)  

## Web application:  
#### Fixes:  
* [FIX] Leave transaction modal where transactions are showing from oldest to newest - should be newest to oldest by default  -  [Wrike](https://www.wrike.com/open.htm?id=1100570112)  
#### Chores:  
* [CHORE] Indicate if a user has requested to have their timesheet returned on the my team > my teams timesheets page  -  [Wrike](https://www.wrike.com/open.htm?id=1096560400)  

---

# RELEASE aluminium-1.02  -  [Wrike](https://www.wrike.com/open.htm?id=1098237431)  

## Web application:  
#### Fixes:  
* [FIX] Fix issue with resubmitting a timesheet that has been recalled and eNPS score had already been given before  -  [Wrike](https://www.wrike.com/open.htm?id=1098232625)  
* [FIX] Fix error when calculating hours into time-card on Android devices  -  [Wrike](https://www.wrike.com/open.htm?id=1073159902)  

## Merges:  
* [MERGE] Merge from magnesium-10.12  -  [Wrike](https://www.wrike.com/open.htm?id=1098222690)  

---

# RELEASE aluminium-1.01  -  [Wrike](https://www.wrike.com/open.htm?id=1097405864)  

## General:  
#### Chores:  
* [CHORE] Update default workflow for excess-time to be any role manager or payroll officer  

## Reports:  
#### Chores:  
* [CHORE] Changes to detailed checklist report  -  [Wrike](https://www.wrike.com/open.htm?id=922590284)  

---

# RELEASE aluminium-1.00  -  [Wrike](https://www.wrike.com/open.htm?id=1096524243)  

## General:  
#### Features:  
* [PACKAGE] Create new eNPS function  -  [Wrike](https://www.wrike.com/open.htm?id=1013856950)  
    * [FEAT] Create eNPS opt in and display dashboard options  -  [Wrike](https://www.wrike.com/open.htm?id=1017489307)  
    * [CHORE] Alter layout for the timesheet submission check page  -  [Wrike](https://www.wrike.com/open.htm?id=1060574591)  
    * [FEAT] Create rank eNPS and related functionality on timesheet submission page  -  [Wrike](https://www.wrike.com/open.htm?id=1017489459)  
    * [FEAT] Create new HR contact email field  -  [Wrike](https://www.wrike.com/open.htm?id=1060622397)  
    * [FEAT] Create a HR manager system access  -  [Wrike](https://www.wrike.com/open.htm?id=1067301589)  

## Web application:  
#### Features:  
* [FEAT] Add condition type to timesheet workflows to time recorded against work orders  -  [Wrike](https://www.wrike.com/open.htm?id=1090396328)  
* [FEAT] Add workflow for managing approval / notification of excess time  -  [Wrike](https://www.wrike.com/open.htm?id=1050768331)  
* [FEAT] Ability to provide staff access to all plant items as non standard plant items  -  [Wrike](https://www.wrike.com/open.htm?id=1065438427)  
#### Chores:  
* [CHORE] Add additional filters to the my teams timesheets page for payroll officers  -  [Wrike](https://www.wrike.com/open.htm?id=1072300656)  
* [CHORE] Add employee number to users personal dashboards  -  [Wrike](https://www.wrike.com/open.htm?id=1068074702)  
* [CHORE] Add project code to dropdown field into workflow builder for rule that needs projects selected  -  [Wrike](https://www.wrike.com/open.htm?id=1071234285)  
* [CHORE] Alter view schedule details modal size to be justified / full width when open  -  [Wrike](https://www.wrike.com/open.htm?id=1088679562)  
* [CHORE] Leave with the LWOP flag as true should not show as having a paid value in the payment screen  -  [Wrike](https://www.wrike.com/open.htm?id=1029573052)  
* [CHORE] Allow splitting excess time rules into multiple types  -  [Wrike](https://www.wrike.com/open.htm?id=1009999877)  
#### Fixes:  
* [FIX] Fix issue editing leave request in schedule view  -  [Wrike](https://www.wrike.com/open.htm?id=1032755224)  

## APIs:  
#### Features:  
* [FEAT] Install and configure Swagger into elementTIME APIs  -  [Wrike](https://www.wrike.com/open.htm?id=671764220)  

---
---
---

# aluminium  -  [Wrike](https://www.wrike.com/open.htm?id=913283848)  
