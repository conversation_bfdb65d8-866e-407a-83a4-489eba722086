<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\_System\BroadcastEvents;

use Element\Core\Support\Domains\Event;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk\KioskDeviceRequestBasicResource;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;
use Element\ElementTime\Support\Facades\CurrentTenant;
use Illuminate\Broadcasting\Channel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;

class KioskDeviceRequestChangedStatusBroadcastEvent extends Event implements ShouldBroadcast
{
    use SerializesModels;

    public int $request_id;
    public KioskDeviceRequestBasicResource $data;

    public int $tenant_id;
    public string $tenant_slug;

    /** @throws \Throwable */
    public function __construct(KioskDeviceRequest $kioskDeviceRequest)
    {
        $this->request_id = $kioskDeviceRequest->id ?? 1;
        $this->data = KioskDeviceRequestBasicResource::make($kioskDeviceRequest);

        $this->tenant_id = CurrentTenant::getTenant()->id;
        $this->tenant_slug = CurrentTenant::getTenant()->slug;

        parent::__construct();
    }

    public function broadcastOn(): Channel|string|array
    {
        return ['time.tenant.' . $this->tenant_id . '.kiosk.device.request.' . $this->request_id];
    }
}
