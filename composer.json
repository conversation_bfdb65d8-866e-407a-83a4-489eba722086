{"name": "adroitcreations/elementtime", "description": "elementTIME – the smart and easy online timesheet and leave management solution", "keywords": ["elementtime", "time", "adroit creations", "new zealand", "government", "time sheet", "pay run"], "license": "proprietary", "type": "project", "homepage": "https://www.elementtime.com", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Product Lead"}], "support": {"email": "<EMAIL>"}, "require": {"php": "^8.1", "adroitcreations/elementcentresdk": "^2.0", "adroitcreations/elementsupsdk": "^3.0", "barryvdh/laravel-snappy": "^1.0", "darkaonline/l5-swagger": "^8.5", "doctrine/dbal": "^3.6", "firebase/php-jwt": "^6.4", "fntneves/laravel-transactional-events": "^2.2", "guzzlehttp/guzzle": "^7.5", "itsmill3rtime/cwh": "^1.3", "jenssegers/agent": "^2.6", "laravel/framework": "^10.9", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "laravolt/avatar": "^5.0", "league/flysystem-aws-s3-v3": "^3.13", "leocello/sweet-enum": "^0.4.0", "php-open-source-saver/jwt-auth": "^2.1", "phpoffice/phpspreadsheet": "^1.28", "predis/predis": "^2.1", "pusher/pusher-php-server": "^7.2", "spatie/laravel-ignition": "^2.1", "watson/validating": "^8.0", "webpatser/laravel-uuid": "^4.0"}, "require-dev": {"fakerphp/faker": "^1.21", "filp/whoops": "^2.15", "itsgoingd/clockwork": "^5.1", "laravel/dusk": "^7.7", "mockery/mockery": "^1.5", "nunomaduro/collision": "^7.5", "pestphp/pest": "^2.26", "pestphp/pest-plugin-faker": "^2.0", "pestphp/pest-plugin-laravel": "^2.2", "phpunit/phpunit": "^10.1", "squizlabs/php_codesniffer": "^3.7"}, "repositories": [{"type": "vcs", "url": "*****************:adroitcreations/elementsupsdk.git"}, {"type": "vcs", "url": "*****************:adroitcreations/elementcentresdk.git"}], "autoload": {"psr-4": {"Element\\": "app/", "ElementTimePhpCs\\": ".phpcs/ElementTimePhpCs"}, "files": ["app/ElementTime/Support/Helpers/helpers.php", "app/Core/Support/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "process-timeout": 900, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}