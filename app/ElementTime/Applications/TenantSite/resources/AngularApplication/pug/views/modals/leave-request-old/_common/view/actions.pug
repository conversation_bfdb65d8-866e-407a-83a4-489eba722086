div(ng-if='$ctrl.leave.view === "request" || $ctrl.leave.view === "approving"')
  .row.visible-md.visible-lg
    .col-xs-12.text-right
      button.btn.btn-default.pull-left(type='button' ng-click='$ctrl.leave.attachments.open()' ng-if='!!req.canAddAttachment')
        //i.fa.fa-plus.mr-sm
        | Attach new file
      button.btn.btn-info.ml(type='button' ng-click='$ctrl.manageWorkflow()')
        //i.fa.fa-window-maximize.mr-sm
        | View workflow
      button.btn.btn-warning.ml(type='button' ng-click='$ctrl.leave.editing.open()' ng-if='!!req.canReplace')
        //i.fa.fa-pencil.mr-sm
        | Edit request
      button.btn.btn-danger.ml(type='button' ng-click='$ctrl.leave.cancelling.open()' ng-if='!!req.canCancel')
        //i.fa.fa-ban.mr-sm
        | Cancel request
      button.btn.btn-leave.ml(ng-click='$ctrl.downloadRequest()' ng-if='!!$ctrl.leave.data.pdfReportGenerateUri' ng-disabled='!!$ctrl.isDownloadingReport')
        //i.fa.fa-download.mr-sm
        | Download request
      button.btn.btn-default.ml(type='button' ng-click='$ctrl.close()')
        | Close

  .row.visible-xs.visible-sm
    .col-xs-12.col-sm-6
      button.btn.btn-default.btn-block.mt(type='button' ng-click='$ctrl.leave.attachments.open()' ng-if='!!req.canAddAttachment')
        i.fa.fa-plus.mr-sm
        | Attach new file
    .col-xs-12.col-sm-6
      button.btn.btn-warning.btn-block.mt(type='button' ng-click='$ctrl.leave.editing.open()' ng-if='!!req.canReplace')
        i.fa.fa-pencil.mr-sm
        | Edit request
    .col-xs-12.col-sm-6
      button.btn.btn-danger.btn-block.mt(type='button' ng-click='$ctrl.leave.cancelling.open()' ng-if='!!req.canCancel')
        i.fa.fa-ban.mr-sm
        | Cancel request
    .col-xs-12.col-sm-6
      button.btn.btn-info.btn-block.mt(type='button' ng-click='$ctrl.manageWorkflow()')
        i.fa.fa-window-maximize.mr-sm
        | View workflow
    .col-xs-12.col-sm-6
      button.btn.btn-leave.btn-block.mt(ng-click='$ctrl.downloadRequest()' ng-if='!!$ctrl.leave.data.pdfReportGenerateUri' ng-disabled='!!$ctrl.isDownloadingReport')
        i.fa.fa-download.mr-sm
        | Download request
    .col-xs-12.col-sm-6
      button.btn.btn-default.btn-block.mt(type='button' ng-click='$ctrl.close()')
        | Close

// Edit
div(ng-if='$ctrl.leave.view === "edit"' ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/actions/edit.html'" ng-init='$f = $ctrl.leave.editing')

// Cancel
div(ng-if='$ctrl.leave.view === "cancel"' ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/actions/cancel.html'" ng-init='$f = $ctrl.leave.cancelling')

// Attach new file
div(ng-if='$ctrl.leave.view === "attach-file"' ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/view/actions/add-attachment.html'" ng-init='$f = $ctrl.leave.attachments')
