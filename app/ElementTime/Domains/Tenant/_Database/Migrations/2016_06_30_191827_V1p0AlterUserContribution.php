<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('User', function (Blueprint $table) {
            // Contribution Details
            $table->boolean('isEligibleForSuperPayments')->nullable();
        });

        Schema::create('User_Contribution', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('contribution_id')->unsigned()->nullable()->default(null);
            $table->string('specialReference')->nullable()->default(null);

            $table->foreign('user_id', 'user_contribution_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('contribution_id', 'user_contribution_c_fn')->references('id')->on('Contribution')->onUpdate('cascade')->onDelete('cascade');

            // Employee Rates
            $table->longText('employeeRates')->nullable(); // JSON format:
            //  [
            //      {
            //          "contributionRate": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "contributionRate": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]

            // Employer Rates
            $table->longText('employerRates')->nullable(); // JSON format:
            //  [
            //      {
            //          "contributionRate": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "contributionRate": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]

            // Employee Rates through salary sacrifice
            $table->longText('employeeRatesThroughSacrifice')->nullable(); // JSON format:
            //  [
            //      {
            //          "contributionRate": 2.5,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2016-01-01",
            //          "endDate": "2016-12-31"
            //      },
            //      {
            //          "contributionRate": 2,
            //          "rateType": "P", // Values: (P)ercentage | (F)ixed
            //          "startDate": "2017-01-01",
            //          "endDate": "2017-12-31"
            //      }
            //  ]
        });
    }

    public function down(): void
    {
        Schema::table('User', function (Blueprint $table) {
            $table->dropColumn([
                'isEligibleForSuperPayments',
                'optOut',
            ]);
        });
        Schema::drop('User_Contribution');
    }
};
