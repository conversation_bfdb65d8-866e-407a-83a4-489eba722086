<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\UserLeaveRequests;

use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\LeaveRequestsByPeriod\LeaveRequestsByPeriodDataSource;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\LeaveRequestsByPeriod\LeaveRequestsByPeriodRow;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRepository;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\CancelledType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\DeclinedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Facades\CurrentUser;
use Illuminate\Support\Collection;

class UserLeaveRequestsExcelReport extends \Element\ElementTime\Support\Reports\TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters{
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const TIME = '~0:30sec';
    const REPORT_SLUG = 'user-leave-requests-over-time';
    const TYPE_SLUG = 'user-leave-requests-over-time.xls';

    public $c = UserLeaveRequestsReportConfig::class;

    protected User $user;

    // region --\\   Overwritten methods   //--

    protected function setData(array $info)
    {
        $this->_iSetData($info);
        $this->user = static::getUser($info['idUser']);
    }

    protected function buildReportData()
    {
        $data = $this->collectReportData();

        $this->setSheetDefaultSettings($sheet);
        $this->setSheetTitle($sheet, 'User leave requests over time');
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    public function getReplacedString(string $string): string
    {
        $string = parent::getReplacedString($string);
        $string = $this->_iGetReplacedString($string);

        if (isset($this->user)) {
            $string = Str::replace('{{user.id}}', $this->user->id, $string);
            $string = Str::replace('{{user.fullName}}', $this->user->fullName, $string);
        }

        return $string;
    }

    // endregion --\\   Overwritten methods   //--

    // region --\\   Data fetching methods   //--

    /**
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     */
    private static function getUser(int $id): User
    {
        /** @var User $user */
        $user = UserRepository::getOneOrFail([
            'constraints' => [
                ['User.id', '=', $id],
            ],
            'returnType' => 'model',
        ]);

        return $user;
    }

    protected function collectReportData(): Collection
    {
        $startDate = $this->getStartDate();
        $endDate = $this->getEndDate();

        $leaves = LeaveRequestsByPeriodDataSource::new($startDate, $endDate)
            ->fromUsers([$this->user->id])
            ->withStatuses([
                SubmittedType::ID,
                PartiallyApprovedType::ID,
                ApprovedType::ID,
                DeclinedType::ID,
                CancelledType::ID,
            ])
            ->withAll();

        return $leaves->get();
    }

    // endregion --\\   Data fetching methods   //--

    // region --\\   Sheet manipulation methods   //--

    protected function fetchSheetTotals(\stdClass $sheet = null): void
    {
        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Start pay-run number', 'text', 'center', 2),
            $this->createColumn('End pay-run number', 'text', 'center', 2),
            $this->createColumn('Employee number', 'text', 'center', 2),
            $this->createColumn('Employee name', 'text', 'center', 3),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                '#' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear,
                '#' . $this->payRunEnd->number . ' ' . $this->payRunEnd->fiscalYear,
                $this->user->externalId,
                $this->user->fullName,
            ],
        ]];

        $sheet->data[] = $data;
    }

    protected function fetchSheetData(\stdClass $sheet, Collection $data): void
    {
        $table = new \stdClass();
        $table->type = 'data-table';
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($data);

        $sheet->data[] = $table;
    }

    protected function getSheetDataHeader(): array
    {
        $ret = [
            $this->createColumn('Leave type', 'text', 'center'),
            $this->createColumn('Start date', 'text', 'center'),
            $this->createColumn('End date', 'text', 'center'),
            $this->createColumn('Total hours requested', 'float', 'right'),
            $this->createColumn('Total hours adjusted', 'float', 'right'),
            $this->createColumn('Comments', 'text', 'center'),
            $this->createColumn('Status', 'text', 'center'),
            $this->createColumn('Workflow', 'text', 'center'),
            $this->createColumn('Attachment uploaded', 'text', 'center'),
        ];

        return $ret;
    }

    /**
     * @param Collection|LeaveRequestsByPeriodRow[] $data
     */
    protected function getSheetDataData(Collection|array $data): array
    {
        $tableData = [];

        $i = 1;
        $user = null;
        $reportUserRoleIds = [];
        if (CurrentUser::isLogged()) {
            $user = CurrentUser::getUser();
        }

        if (!is_null($user) && !$this->doesUserHaveFullAccess($user) && $user->access->isRoleManager(true)) {
            $activeUserRoleReports = $user->repository->getActiveUserRoleReports();
            foreach ($activeUserRoleReports as $activeUserRoleReport) {
                $reportUserRoleIds[] = $activeUserRoleReport->userRole_id;
            }
        }

        foreach ($data as $datum) {
            if (count($reportUserRoleIds) > 0 && !in_array($datum->userRole_id, $reportUserRoleIds)) {
                continue;
            }

            $rowConfig = [
                'color' => $i % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => $this->getItemData($datum),
            ];

            $i++;
        }

        return $tableData;
    }

    private function getItemData(LeaveRequestsByPeriodRow $leaveRequestByPeriodRow): array
    {
        $row = [
            $leaveRequestByPeriodRow->name . ' '. $leaveRequestByPeriodRow->externalId,
            $this->parseDate($leaveRequestByPeriodRow->startDate, $this->dateFormat),
            $this->parseDate($leaveRequestByPeriodRow->endDate, $this->dateFormat),
            $this->parseFloat($leaveRequestByPeriodRow->actualTotalHours),
            $this->parseFloat($leaveRequestByPeriodRow->calculatedTotalHours),
            $leaveRequestByPeriodRow->reason,
            $leaveRequestByPeriodRow->status,
            $leaveRequestByPeriodRow->workflowDeciderName,
            $leaveRequestByPeriodRow->hasAttachments ? 'Yes' : 'No',
        ];

        return $row;
    }

    // endregion --\\   Sheet manipulation methods   //--
}
