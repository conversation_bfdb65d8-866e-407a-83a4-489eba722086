<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region PayRunType
        Schema::table('PayRunType', function (Blueprint $table) {
            $table->boolean('allowsCopyFromSchedule')->default(false);
            $table->boolean('allowsAutoCompleteFromSchedule')->default(false);
        });
        //endregion

        //region UserProject
        Schema::table('UserProject', function (Blueprint $table) {
            $table->boolean('isMasterProject')->default(false);
        });
        //endregion

        //region User
        Schema::table('User', function (Blueprint $table) {
            $table->boolean('allowsCopyFromSchedule')->default(false);
            $table->boolean('autoCompletesFromSchedule')->default(false);
        });
        //endregion
    }

    public function down(): void
    {
        //region User
        if (Schema::hasColumn('User', 'autoCompletesFromSchedule')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('autoCompletesFromSchedule');
            });
        }

        if (Schema::hasColumn('User', 'allowsCopyFromSchedule')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('allowsCopyFromSchedule');
            });
        }
        //endregion

        //region UserProject
        if (Schema::hasColumn('UserProject', 'isMasterProject')) {
            Schema::table('UserProject', function (Blueprint $table) {
                $table->dropColumn('isMasterProject');
            });
        }
        //endregion

        //region PayRunType
        if (Schema::hasColumn('PayRunType', 'allowsAutoCompleteFromSchedule')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('allowsAutoCompleteFromSchedule');
            });
        }

        if (Schema::hasColumn('PayRunType', 'allowsCopyFromSchedule')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('allowsCopyFromSchedule');
            });
        }
        //endregion
    }
};
