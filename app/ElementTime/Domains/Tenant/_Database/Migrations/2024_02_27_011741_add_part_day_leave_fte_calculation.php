<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('doesApplyFteAdjustmentsOnFullDayLeave')->default(false)->after('doApplyFteCalculation');
            $table->boolean('doesApplyFteAdjustmentsOnPartDayLeave')->default(false)->after('doesApplyFteAdjustmentsOnFullDayLeave');
        });

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->boolean('doesApplyFteAdjustmentsOnFullDayLeave')->default(false)->after('doApplyFteCalculation');
            $table->boolean('doesApplyFteAdjustmentsOnPartDayLeave')->default(false)->after('doesApplyFteAdjustmentsOnFullDayLeave');
        });

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('excessTimeLeaveDoesApplyFteAdjustmentsOnFullDayLeave')->default(false)->after('doesApplyFteCalculationOnTakingExcessTimeLeave');
            $table->boolean('excessTimeLeaveDoesApplyFteAdjustmentsOnPartDayLeave')->default(false)->after('excessTimeLeaveDoesApplyFteAdjustmentsOnFullDayLeave');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('LeaveType', 'doesApplyFteAdjustmentsOnFullDayLeave')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doesApplyFteAdjustmentsOnFullDayLeave');
            });
        }

        if (Schema::hasColumn('LeaveType', 'doesApplyFteAdjustmentsOnPartDayLeave')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doesApplyFteAdjustmentsOnPartDayLeave');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'doesApplyFteAdjustmentsOnFullDayLeave')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doesApplyFteAdjustmentsOnFullDayLeave');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'doesApplyFteAdjustmentsOnPartDayLeave')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('doesApplyFteAdjustmentsOnPartDayLeave');
            });
        }

        if (Schema::hasColumn('Settings', 'excessTimeLeaveDoesApplyFteAdjustmentsOnFullDayLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveDoesApplyFteAdjustmentsOnFullDayLeave');
            });
        }

        if (Schema::hasColumn('Settings', 'excessTimeLeaveDoesApplyFteAdjustmentsOnPartDayLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveDoesApplyFteAdjustmentsOnPartDayLeave');
            });
        }
    }
};
