<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Support\Settings\Tenant;

use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet\GetTimeSheetForUser;
use Element\ElementTime\Domains\Admin\General\Models\Admin_Tenant;
use Element\ElementTime\Domains\Tenant\TimeSheets\Resources\TimeSheet\TimeSheetBasicResource;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Illuminate\Support\Arr;

class TenantUserSettings extends BaseTenantSettings
{
    protected ?User $user;

    protected bool $fetchTimeSheet = true;

    public function __construct(Admin_Tenant $tenant, User $user, bool $fetchTimeSheet = true)
    {
        $this->user = $user;
        $this->fetchTimeSheet = $fetchTimeSheet;

        parent::__construct($tenant);
    }

    public function populate(): void
    {
        parent::populate();

        Arr::set($this->values, 'isSuperUser', $this->user->access->isSuperUser());
        Arr::set($this->values, 'fullName', $this->user->fullName);
//        Arr::set($this->values, 'systemAccess', $this->user->currentSystemAccess);
        Arr::set($this->values, 'accessFlags', $this->user->access->getSystemAccesses(true));
        Arr::set($this->values, 'isOnHigherDuties', $this->user->isOnHigherDuties);
        Arr::set($this->values, 'canDoHigherDuties', $this->user->canDoHigherDuties);
        Arr::set($this->values, 'canApplyOwnDutyRequest', $this->user->canApplyOwnDutyRequest);
        Arr::set($this->values, 'preferredLanguage', $this->user->preferredLanguage);
        Arr::set($this->values, 'onPageHelp', $this->user->onPageHelp);
        Arr::set($this->values, 'isEligibleForAllowances', $this->user->isEligibleForAllowances);
        Arr::set($this->values, 'isEligibleForPenalties', $this->user->repository->getActiveUserPenaltyTypes()->count() > 0);
        Arr::set($this->values, 'hasLeaveOptions', $this->user->hasLeaveOptions);
        Arr::set($this->values, 'allowsCopyFromSchedule', $this->user->allowsCopyFromSchedule);
        Arr::set($this->values, 'canFileExpenses', $this->user->canFileExpenses);
        Arr::set($this->values, 'lastLogin', $this->user->lastLogin);
        Arr::set($this->values, 'doesShowReportWorkForceData', $this->user->doesShowReportWorkForceData);
        Arr::set($this->values, 'doesShowReportBirthday', $this->user->doesShowReportBirthday);
        if (!is_null($this->user) && !is_null($this->user->id)) {
            Arr::set($this->values, 'settings', $this->user->settings->toArray());
        }
        Arr::set($this->values, 'isBusinessIntelligenceMember', $this->user->isBusinessIntelligenceMember);
        if ($this->fetchTimeSheet) {
            try {
                $currentTimeSheet = GetTimeSheetForUser::make()->handle($this->user, relations: TimeSheetBasicResource::getRelations());

                Arr::set($this->values, 'currentTimeSheet', new TimeSheetBasicResource($currentTimeSheet));
                Arr::set($this->values, 'hasTimer', $currentTimeSheet->hasTimer);
            } catch (\Throwable) {
                Arr::set($this->values, 'currentTimeSheet', null);
                Arr::set($this->values, 'hasTimer', false);
            }
//            Arr::set($this->values, 'hasTimerNextPayRun', $this->user->usesTimer());
        }
        //Arr::set($this->values , 'isOnLeave' , $this->leaveRepository->isUserOnLeave($this->user));
        Arr::set($this->values, 'userMenu', [
            'hasSettings' => true,
            'hasDetails' => true,
            'hasTimeline' => true,
            'hasSummary' => false,
            'hasSchedule' => true,
            'hasNotifications' => true,
            'hasSetup' => true,
            'hasLogout' => true,
        ]);
    }
}
