<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendLeaveRequestRelatedBroadcastEvents\SendLeaveRequestRelatedBroadcastEventsDurationOnly;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Illuminate\Database\Eloquent\Collection;

class PlaceLeaveRequestFromPreAvailabilityDurationOnly extends BasePlaceLeaveRequestFromPreAvailability
{
    protected function setLeaveData(): void
    {
        $this->leave->startDate = $this->preAvailability->startDate;
        $this->leave->endDate = $this->preAvailability->endDate;
    }

    protected function generateDays(): void
    {
        $leaveRequestDays = Collection::make([]);

        foreach ($this->preAvailability->days as $day) {
            $leaveDay = new LeaveRequestDay;
            $leaveDay->user_id = $this->userLeaveBankType->bank->user_id;

            $leaveDay->date = $day->date->copy();
            $leaveDay->duration = $day->duration->copy();
            $leaveDay->durationAdjusted = $day->durationAdjusted->copy();

            $leaveRequestDays->push($leaveDay);
        }

        $this->leave->days()->saveMany($leaveRequestDays);
    }

    protected function sendBroadcastEvents(): void
    {
        SendLeaveRequestRelatedBroadcastEventsDurationOnly::dispatch($this->leave)->afterResponse();
    }
}
