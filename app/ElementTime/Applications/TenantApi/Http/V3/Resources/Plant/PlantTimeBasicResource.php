<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V3\Resources\Plant;

use Element\ElementTime\Domains\Tenant\Plant\Models\PlantSheetDayTime;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'PlantTimeBasicResource',
    title: 'Recorded Standalone Plant',
    required: ['id', 'plantItem_id', 'rel_id', 'rel_type', 'name', 'formattedCode', 'description', 'hours', 'hoursStart', 'hoursEnd', 'secondaryHours', 'secondaryHoursStart', 'secondaryHoursEnd', 'mileage', 'mileageStart', 'mileageEnd', 'cartage', 'costRate'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 23),
        new OA\Property(property: 'plantItem_id', type: 'integer', example: 3),
        new OA\Property(property: 'rel_id', type: 'integer', example: 3),
        new OA\Property(property: 'rel_type', type: 'string', example: 'Element\\ElementTime\\Domains\\Tenant\\Plant\\Models\\UserRolePlantItem'),
        new OA\Property(property: 'name', type: 'string', example: 'Volvo - 565'),
        new OA\Property(property: 'formattedCode', type: 'string', example: '10111.0240.351'),
        new OA\Property(property: 'description', type: 'string', example: 'This is a description', nullable: true),
        new OA\Property(property: 'hours', type: 'float', example: 7.6, nullable: true),
        new OA\Property(property: 'hoursStart', type: 'string', format: 'time', example: null, nullable: true),
        new OA\Property(property: 'hoursEnd', type: 'string', format: 'time', example: null, nullable: true),
        new OA\Property(property: 'secondaryHours', type: 'float', example: null, nullable: true),
        new OA\Property(property: 'secondaryHoursStart', type: 'string', format: 'time', example: null, nullable: true),
        new OA\Property(property: 'secondaryHoursEnd', type: 'string', format: 'time', example: null, nullable: true),
        new OA\Property(property: 'mileage', type: 'float', example: 12),
        new OA\Property(property: 'mileageStart', type: 'float', example: null, nullable: true),
        new OA\Property(property: 'mileageEnd', type: 'float', example: null, nullable: true),
        new OA\Property(property: 'cartage', type: 'float', example: null, nullable: true),
        new OA\Property(property: 'costRate', type: 'float', example: null, nullable: true),
    ],
)]

class PlantTimeBasicResource extends Resource
{
    public static $relations = [
        'plantSheet.plantItem.plantClass',
        'timeSheetDay',
        'timeSheet.payRunItem.user.userRoles.userRoleMasters',
        'timeSheet.payRunItem.user.userRoles.userRoleProjects.project',
    ];

    public function toArray(Request $request): array
    {
        /** @var PlantSheetDayTime $r */
        $r = $this->getModel();

        $plantItem = $r->plantItem;

        return  [
            'id' => $r->id,
            'plantItem_id' => $plantItem->id,
            'rel_id' => $r->rel_id,
            'rel_type' => $r->rel_type,

            'name' => $plantItem->title,
            'formattedCode' => $r->formattedCode,
            'description' => $r->description,

            'hours' => $r->hours,
            'hoursStart' => $r->hoursStart,
            'hoursEnd' => $r->hoursEnd,
            'secondaryHours' => $r->secondaryHours,
            'secondaryHoursStart' => $r->secondaryHoursStart,
            'secondaryHoursEnd' => $r->secondaryHoursEnd,
            'mileage' => $r->mileage,
            'mileageStart' => $r->mileageStart,
            'mileageEnd' => $r->mileageEnd,
            'cartage' => $r->cartage,
            'costRate' => $r->costRate,
        ];
    }
}
