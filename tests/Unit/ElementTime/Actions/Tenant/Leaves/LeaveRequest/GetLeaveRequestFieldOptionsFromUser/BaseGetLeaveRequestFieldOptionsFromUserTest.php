<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\Core\Exceptions\NotAssignedException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\BaseGetOptionsForLeaveRequest;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\OrdinaryUserFlag;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Support\Facades\TenantSystemSettings;

describe('Base get leave request field options from user', function () {
    it('throws exception if user not allowed to take leave', function () {
        $actor = SuperUserFlag::getOne();
        $user = User::q()->order('RAND()')->first();

        $user->employeeType->doesHideLeave = true;
        $user->clearSave();

        $actionClass = new class extends BaseGetOptionsForLeaveRequest {
            public function handle(User $user, User $actor): void
            {
                $this->validateAccess($user, $actor);
            }
        };

        $actionClass::make()->handle($user, $actor);
    })->throws(NotAssignedException::class);

    it('throws exception if actor user does not have permission to load leave option for user', function () {
        $timeSheet = TimeSheet::q()
            ->constraints([
                ['Workflow.status', '=', NewType::ID],
                ['PayRun.id', '=', TenantSystemSettings::getPayRunByDate()->id],
            ])
            ->first();
        $user = $timeSheet->user;
        $user->employeeType->doesHideLeave = false;
        $user->clearSaveOrFail();

        $actor = User::q()->constraints(
            [
                ...OrdinaryUserFlag::getConstraints(),
                ['User.id', '<>', $user->id]
            ])
            ->order('RAND()')->first();

        $actionClass = new class extends BaseGetOptionsForLeaveRequest {
            public function handle(User $user, User $actor): void
            {
                $this->validateAccess($user, $actor);
            }
        };

        $actionClass::make()->handle($user, $actor);
    })->throws(UnauthorizedActionException::class);

});
