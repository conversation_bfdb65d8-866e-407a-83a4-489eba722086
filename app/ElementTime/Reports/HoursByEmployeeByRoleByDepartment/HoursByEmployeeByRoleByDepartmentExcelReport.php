<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\HoursByEmployeeByRoleByDepartment;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItem;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRolePayType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;
use stdClass;

class HoursByEmployeeByRoleByDepartmentExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
    ];

    const TIME = '1:30 min';
    const REPORT_SLUG = 'hours-by-employee-by-role-by-department';
    const TYPE_SLUG = 'hours-by-employee-by-role-by-department.xls';

    /** @var string|HoursByEmployeeByRoleByDepartmentReportConfig */
    public $c = HoursByEmployeeByRoleByDepartmentReportConfig::class;

    /** @var Department|null */
    public Department|null $department = null;

    // region --\\   Overwritten methods   //--

    protected function setData(array $info): void
    {
        $this->_iSetData($info);
        $this->department = Department::q(
            constraints: [
                ['Department.id', '=', $info['idDepartment']],
            ],
        )->firstOrFail();
    }

    public function getReplacedString(string $string): string
    {
        $string = $this->_iGetReplacedString($string);

        if (isset($this->department) && $this->department instanceof Department) {
            $string = Str::replace('{{department.id}}', $this->department->id, $string);
            $string = Str::replace('{{department.name}}', $this->department->name, $string);
        }

        return $string;
    }

    // endregion --\\   Overwritten methods   //--

    protected function collectReportData(): array|Collection
    {
        $ret = [];

        $userRoles = UserRole::q(
            constraints: [
                ['UserRole.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.department_id', '=', $this->department->id],
                [
                    [
                        ['IS NULL', 'UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                        ['UserRoleDepartment.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRoleDepartment.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
            ],
            joins: [
                ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserRole.id'],
            ],
            order: 'CAST(User.externalId AS SIGNED), UserRole.startDate',
        )->many();
        $userRoles->load([
            'user.userManagers.manager:id,nameFirst,nameMiddle,nameLast',
            'user.userManagers:id,user_id,manager_id,startDate,endDate',
            'user.employeeType:id,name',
            'user:id,externalId,nameFirst,nameMiddle,nameLast,employeeType_id',
            'userRoleManagers.manager:id,nameFirst,nameMiddle,nameLast',
            'userRoleManagers:id,userRole_id,manager_id,startDate,endDate',
            'userRoleDepartments.department:id,name',
            'userRoleDepartments:id,userRole_id,department_id,startDate,endDate',
            'role:id,name',
        ]);

        foreach ($userRoles as $userRole) {
            if (!$this->isUserRoleRelevant($userRole)) {
                continue;
            }

            try {
                $userRoleManager = $userRole->repository->getManager($this->payRunEnd->endDate);
            } catch (InvalidArgumentException $e) {
                report($e);
                $userRoleManager = null;
            }

            $department = '';
            foreach ($userRole->userRoleDepartments as $userRoleDepartment) {
                if (!$userRoleDepartment->availability($this->payRunStart->startDate, $this->payRunEnd->endDate)->isCurrent(true)) {
                    continue;
                }

                if (strlen($department) > 0) {
                    $department .= ', ';
                }

                $department .= $userRoleDepartment->department->name;
            }

            foreach ($userRole->userRolePayTypes as $userRolePayType) {
                if (!$userRolePayType->availability($this->payRunStart->startDate, $this->payRunEnd->endDate)->isCurrent(true)) {
                    continue;
                }

                $rowId = static::buildRowId($userRole, $userRolePayType);

                $obj = new stdClass();
                $obj->totalHoursWorked = 0;
                $obj->totalHoursLeave = 0;
                $obj->totalHoursPublicHoliday = 0;
                $obj->totalHoursPaidExcessTime = 0;
                $obj->totalHoursAccruedExcessTime = 0;
                $obj->totalHoursAdditionalExcessTime = 0;
                $obj->totalHoursUnpaidExcessTime = 0;
                $obj->totalHours = 0;

                $obj->actual = 0;
                $obj->adjusted = 0;

                $ret[$rowId] = [
                    'user' => [
                        'id' => $userRole->user->id,
                        'externalId' => $userRole->repository->getExternalId(),
                        'fullName' => $userRole->user->fullName,
                        'employeeType' => $userRole->user->employeeType->name,
                        'role' => $userRole->role->name,
                        'userRole_status' => $userRole->isActiveOnTime($this->payRunEnd->endDate) ? 'Active' : 'Inactive',
                        'roleManagerName' => !is_null($userRoleManager)
                            ? $userRoleManager->fullName
                            : '',
                        'department' => $department,
                        'payBand_name' => !is_null($userRolePayType->payType) ? $userRolePayType->payType->payBand->name : 'Custom',
                        'payType_externalId' => $userRolePayType->externalId,
                        'payType_hourlyAmount' => $userRolePayType->getHourlyAmount(),
                    ],
                    'hours' => $obj,
                ];
            }

            unset($userRole->user, $userRole->role, $userRole->userRoleMasters, $userRole->userRoleDepartments, $userRole->userRolePayTypes);
        }
        $this->loadReportDataFromLeaveRequestDays($ret);
        $this->loadReportDataFromTimeSheetExcessTimes($ret);
        $this->loadReportDataFromTimeSheetDayTimes($ret);

        return $ret;
    }

    private function loadReportDataFromLeaveRequestDays(array $data): void
    {
        $leaveRequestDays = LeaveRequestDay::q(
            constraints: [
                ['BETWEEN', 'LeaveRequestDay.date', [$this->payRunStart->startDate, $this->payRunEnd->endDate]],
                ['UserRole.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.department_id', '=', $this->department->id],
                [
                    [
                        ['IS NULL', 'UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                        ['UserRoleDepartment.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRoleDepartment.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
                ['Workflow.status', '=', ApprovedType::ID],
            ],
            joins: [
                ['UserRole', 'UserRole.id', '=', 'UserRoleSchedule.userRole_id'],
                ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserRole.id'],
            ],
        )->many();

        $leaveRequestDays->load([
            'leaveRequest.userRoleSchedule.userRole.userRolePayTypes.payType:id,rateType,amount',
            'leaveRequest.userRoleSchedule.userRole.userRolePayTypes:id,payType_id,userRole_id,startDate,endDate,customRateType,customAmount',
            'leaveRequest.userRoleSchedule.userRole:id,startDate,endDate',
        ]);

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $userRolePayType = static::getUserRolePayType($leaveRequestDay->leaveRequest->userRoleSchedule->userRole, $leaveRequestDay->date);

            if (is_null($userRolePayType)) {
                continue;
            }

            $rowId = static::buildRowId($leaveRequestDay->leaveRequest->userRoleSchedule->userRole, $userRolePayType);

            if (isset($data[$rowId]['hours'])) {
                $data[$rowId]['hours']->totalHoursLeave += $leaveRequestDay->duration->getTotalHours();
                $data[$rowId]['hours']->totalHours += $leaveRequestDay->duration->getTotalHours();
            }
        }
    }

    private function loadReportDataFromTimeSheetExcessTimes(array $data): void
    {
        $timeSheetExcessTimes = TimeSheetExcessTime::q(
            constraints: [
                ['BETWEEN', 'TimeSheetDayGlide.date', [$this->payRunStart->startDate, $this->payRunEnd->endDate]],
                ['Workflow.status', '=', ApprovedType::ID],
                ['UserRole.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.department_id', '=', $this->department->id],
                [
                    [
                        ['IS NULL', 'UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                        ['UserRoleDepartment.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRoleDepartment.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
            ],
            joins: [
                ['TimeSheetExcessTimeItem', 'TimeSheetExcessTime.id', '=', 'TimeSheetExcessTimeItem.timeSheetExcessTime_id', 'left'],
                ['Workflow', [
                    ['Workflow.rel_type', 'LIKE', '"%' . TimeSheetExcessTimeItem::class . '"'],
                    ['Workflow.rel_id', '=', 'TimeSheetExcessTimeItem.id'],
                ], 'inner'],
                /// TODO: Get to department somehow
            ],
            select: [
                'TimeSheetExcessTime.id',
                'TimeSheetExcessTime.timeSheetDay_id',
            ],
        )->many();
        $timeSheetExcessTimes->load([
            'timeSheetDay:id,date,isGlide,glidePeriodStart',
            'items:timeSheetExcessTime_id,originalTimeSheetExcessTimeItem_id,minutesPaid,minutesAccrued,minutesAdditional,minutesUnPaid',
        ]);

        foreach ($timeSheetExcessTimes as $timeSheetExcessTime) {
            if (!$timeSheetExcessTime->timeSheetDay->isFirstGlideDay) {
                continue;
            }

            $userRolePayType = static::getUserRolePayType(); // TODO: pffff - this report will have to be completely redone!!!

            if (is_null($userRolePayType)) {
                continue;
            }

            $rowId = static::buildRowId($timeSheetExcessTime->timeSheetPeriod?->scheduledPayRunPeriod->scheduledPayRun->userRoleSchedule->userRole, $userRolePayType); // TODO: This won't work in daily cases - redo this stuff
            $data[$rowId]['hours']->totalHoursPaidExcessTime += $timeSheetExcessTime->activeItem->totalHoursPaid;
            $data[$rowId]['hours']->totalHoursAccruedExcessTime += $timeSheetExcessTime->activeItem->totalHoursAccrued;
            $data[$rowId]['hours']->totalHoursAdditionalExcessTime += $timeSheetExcessTime->activeItem->totalHoursAdditional;
            $data[$rowId]['hours']->totalHoursUnpaidExcessTime += $timeSheetExcessTime->activeItem->totalHoursUnpaid;
        }
    }

    private function loadReportDataFromTimeSheetDayTimes(array &$data): void
    {
        // TODO: NOOOOOOO!
        $timeSheetDayTimes = TimeSheetDayTime::q(
            constraints: [
                ['BETWEEN', 'TimeSheetDay.date', [$this->payRunStart->startDate, $this->payRunEnd->endDate]],
                ['UserRole.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.startDate', '<=', $this->payRunEnd->endDate],
                ['UserRoleDepartment.department_id', '=', $this->department->id],
                [
                    [
                        ['IS NULL', 'UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
                [
                    [
                        ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                        ['UserRoleDepartment.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRoleDepartment.endDate', '>=', $this->payRunStart->startDate, 'OR'],
                    ],
                ],
            ],
            joins: [
                ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserRole.id'],
            ],
            select: [
                'TimeSheetDayTime.id',
                'TimeSheetDayTime.timeSheetDay_id',
                'TimeSheetDayTime.minutes',
                'TimeSheetDayTime.recordType',
            ],
        )->many();

        $timeSheetDayTimes->load([
            'excessTimes.items:timeSheetExcessTime_id,originalTimeSheetExcessTimeItem_id,minutesPaid,minutesAccrued,minutesAdditional,minutesUnPaid',
            'timeSheetDay:id,date',
        ]);

        foreach ($timeSheetDayTimes as $timeSheetDayTime) {
            $userRolePayType = static::getUserRolePayType($timeSheetDayTime->userRole, $timeSheetDayTime->timeSheetDay->date);
            if (is_null($userRolePayType)) {
                continue;
            }

            $rowId = static::buildRowId($timeSheetDayTime->userRole, $userRolePayType);

            if (!isset($data[$rowId])) {
                continue;
            }

            if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                $data[$rowId]['hours']->totalHoursWorked += $timeSheetDayTime->hours;
            } elseif ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                $data[$rowId]['hours']->totalHoursPublicHoliday += $timeSheetDayTime->hours;
            }
            $data[$rowId]['hours']->totalHours += $timeSheetDayTime->hours;

            foreach ($timeSheetDayTime->excessTimes as $excessTime) {
                $data[$rowId]['hours']->totalHoursPaidExcessTime += $excessTime->activeItem->totalHoursPaid;
                $data[$rowId]['hours']->totalHoursAccruedExcessTime += $excessTime->activeItem->totalHoursAccrued;
                $data[$rowId]['hours']->totalHoursAdditionalExcessTime += $excessTime->activeItem->totalHoursAdditional;
                $data[$rowId]['hours']->totalHoursUnpaidExcessTime += $excessTime->activeItem->totalHoursUnpaid;
            }
        }
    }

    private static function getUserRolePayType(UserRole $userRole, Carbon $date): UserRolePayType|null
    {
        return $userRole->getActiveUserRolePayType($date);
    }

    private static function buildRowId(UserRole $userRole, UserRolePayType $userRolePayType): string
    {
        if (!is_null($userRolePayType->payType)) {
            return '_' . $userRole->id . '_' . $userRolePayType->payType_id;
        }

        return '_' . $userRole->id . '_' . $userRolePayType->getHourlyAmount();
    }

    protected function isUserRoleRelevant(UserRole $userRole): bool
    {
        return
            $userRole->startDate->lte($this->payRunEnd->endDate)
            && (
                is_null($userRole->endDate)
                || $userRole->endDate->lt(Carbon::minValue())
                || $userRole->endDate->gte($this->payRunStart->startDate)
            );
    }

    protected function buildReportData(): void
    {
        $data = $this->collectReportData();
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetTotals($sheet, $data);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function fetchSheetTotals(stdClass|null &$sheet, array $data): void
    {
        $this->setSheetDefaultSettings($sheet);

        $table = new stdClass();
        $table->type = 'data-table';
        $table->columns = [
            $this->createColumn('Date/time', 'text', 'center', 3),
            $this->createColumn('Span of report', 'text', 'center', 3),
            $this->createColumn('Number of staff', 'integer', 'center', 3),
        ];
        $staffIds = [];

        if ($this->payRunStart->id == $this->payRunEnd->id) {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear;
        } else {
            $span = 'Payrun #' . $this->payRunStart->number . ' ' . $this->payRunStart->fiscalYear . ' to Payrun #' . $this->payRunEnd->number . ' ' . $this->payRunEnd->fiscalYear;
        }

        foreach ($data as $datum) {
            if (!in_array($datum['user']['id'], $staffIds)) {
                $staffIds[] = $datum['user']['id'];
            }
        }

        $table->data = [[
            'config' => [],
            'data' => [
                $this->parseDate(Carbon::now()),
                $span,
                count($staffIds),
            ],
        ]];

        $sheet->data[] = $table;
    }


    protected function fetchSheetData(stdClass &$sheet = null, $data = []): void
    {
        $this->setSheetDefaultSettings($sheet);
        $rows = $this->loadData($data);

        $table = new stdClass();
        $table->type = 'data-table';
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($rows);
        $sheet->data[] = $table;
    }

    public function loadData(array $data = []): array
    {
        $rows = [];

        foreach ($data as $datum) {
            $rows[] = [
                $datum['user']['externalId'],
                $datum['user']['fullName'],
                $datum['user']['employeeType'],
                $datum['user']['role'],
                $datum['user']['userRole_status'],
                $datum['user']['roleManagerName'],
                $datum['user']['department'],
                $datum['user']['payBand_name'],
                $datum['user']['payType_externalId'],
                $datum['user']['payType_hourlyAmount'],

                etime_round($datum['hours']->totalHoursWorked, 2),
                etime_round($datum['hours']->totalHoursLeave),
                etime_round($datum['hours']->totalHoursPublicHoliday),
                etime_round($datum['hours']->totalHoursPaidExcessTime),
                etime_round($datum['hours']->totalHoursAccruedExcessTime),
                etime_round($datum['hours']->totalHoursAdditionalExcessTime),
                etime_round($datum['hours']->totalHoursUnpaidExcessTime),
                etime_round($datum['hours']->totalHours),
            ];
        }

        return $rows;
    }

    protected function getSheetDataHeader(): array
    {
        $ret = [
            $this->createColumn('#', 'integer', 'center'),
            $this->createColumn('Ext ref ID'),
            $this->createColumn('User name'),
            $this->createColumn('Employee type'),
            $this->createColumn('Role name'),
            $this->createColumn('Role status'),
            $this->createColumn('Role manager'),
            $this->createColumn('Department'),
            $this->createColumn('Pay-rate name'),
            $this->createColumn('Pay-rate code'),
            $this->createFloatColumn('Pay-rate hourly amount'),
            $this->createFloatColumn('Total hours worked'),
            $this->createFloatColumn('Total hours leave'),
            $this->createFloatColumn('Total hours public holiday'),
            $this->createFloatColumn('Total hours overtime paid'),
            $this->createFloatColumn('Total hours overtime accrued'),
            $this->createFloatColumn('Total hours overtime additional hours'),
            $this->createFloatColumn('Total hours overtime unpaid'),
            $this->createFloatColumn('Total hours'),
        ];

        return $ret;
    }

    protected function getSheetDataData(array $rows = []): array
    {
        $i = 0;
        $tableData = [];

        foreach ($rows as $row) {
            $rowConfig = [
                'color' => $i % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => array_merge([$i + 1], $row),
            ];

            $i++;
        }

        return $tableData;
    }
}
