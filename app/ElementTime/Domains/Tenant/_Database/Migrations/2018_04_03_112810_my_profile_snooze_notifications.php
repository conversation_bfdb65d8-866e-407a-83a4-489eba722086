<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->string('notificationSnoozedWeekdayStart')->nullable();
            $table->string('notificationSnoozedWeekdayEnd')->nullable();
            $table->string('notificationSnoozedWeekendStart')->nullable();
            $table->string('notificationSnoozedWeekendEnd')->nullable();
            $table->boolean('notificationSnoozedWeekendFullDay')->default(false);
        });

        Schema::table('Notification', function (Blueprint $table) {
            $table->dateTime('lastSnoozed')->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('Notification', 'lastSnoozed')) {
            Schema::table('Notification', function (Blueprint $table) {
                $table->dropColumn('lastSnoozed');
            });
        }

        if (Schema::hasColumn('User', 'notificationSnoozedWeekendFullDay')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('notificationSnoozedWeekendFullDay');
            });
        }
        if (Schema::hasColumn('User', 'notificationSnoozedWeekendEnd')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('notificationSnoozedWeekendEnd');
            });
        }
        if (Schema::hasColumn('User', 'notificationSnoozedWeekendStart')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('notificationSnoozedWeekendStart');
            });
        }
        if (Schema::hasColumn('User', 'notificationSnoozedWeekdayEnd')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('notificationSnoozedWeekdayEnd');
            });
        }
        if (Schema::hasColumn('User', 'notificationSnoozedWeekdayStart')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('notificationSnoozedWeekdayStart');
            });
        }
    }
};
