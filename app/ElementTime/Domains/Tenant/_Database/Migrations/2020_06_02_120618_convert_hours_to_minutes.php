<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: UserShiftDay
        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
            $table->unsignedInteger('minutesGlidePeriod')->nullable()->default(0)->after('glidePeriodEndDate');
        });
        //endregion Table: UserShiftDay

        //region Table: UserShiftDayBreak
        Schema::table('UserShiftDayBreak', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
        });
        //endregion Table: UserShiftDayBreak

        //region Table: LeaveRequest
        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('glideDailyEndTime');
            $table->unsignedInteger('minutesGlideDaily')->nullable()->default(0)->after('minutes');
        });
        //endregion Table: LeaveRequest

        //region Table: LeaveRequestDay
        Schema::table('LeaveRequestDay', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
        });
        //endregion Table: LeaveRequestDay

        //region Table: LeaveRequestDayBreak
        Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
        });
        //endregion Table: LeaveRequestDayBreak

        //region Table: TimeSheet
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->unsignedInteger('minutesScheduled')->nullable()->default(0)->after('id');
            $table->unsignedInteger('minutesRecorded')->nullable()->default(0)->after('minutesScheduled');
            $table->unsignedInteger('minutesWorked')->nullable()->default(0)->after('minutesRecorded');
            $table->unsignedInteger('minutesLeaveTaken')->nullable()->default(0)->after('minutesWorked');
            $table->unsignedInteger('minutesRosteredTimeOffTaken')->nullable()->default(0)->after('minutesLeaveTaken');
            $table->unsignedInteger('minutesAccruedHoursTaken')->nullable()->default(0)->after('minutesRosteredTimeOffTaken');
            $table->unsignedInteger('minutesPublicHolidayTaken')->nullable()->default(0)->after('minutesAccruedHoursTaken');
            $table->unsignedInteger('minutesMissing')->nullable()->default(0)->after('minutesPublicHolidayTaken');
            $table->unsignedInteger('minutesExcessTimeAccrued')->nullable()->default(0)->after('minutesMissing');
            $table->unsignedInteger('minutesExcessTimePaid')->nullable()->default(0)->after('minutesExcessTimeAccrued');
            $table->unsignedInteger('minutesExcessTimeUnPaid')->nullable()->default(0)->after('minutesExcessTimePaid');
            $table->unsignedInteger('minutesAllowance')->nullable()->default(0)->after('minutesExcessTimeUnPaid');
        });
        //endregion Table: TimeSheet

        //region Table: TimeSheetAllowance
        Schema::table('TimeSheetAllowance', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('weekNotes');
        });
        //endregion Table: TimeSheetAllowance

        //region Table: TimeSheetAllowanceDay
        Schema::table('TimeSheetAllowanceDay', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('date');
        });
        //endregion Table: TimeSheetAllowanceDay

        //region Table: TimeSheetAllowanceDayEntry
        Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('timeSheetAllowanceDay_id');
        });
        //endregion Table: TimeSheetAllowanceDayEntry

        //region Table: TimeSheetDay
        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->unsignedInteger('minutesScheduled')->nullable()->default(0)->after('date');
            $table->unsignedInteger('minutesRecorded')->nullable()->default(0)->after('minutesScheduled');
            $table->unsignedInteger('minutesWorked')->nullable()->default(0)->after('minutesRecorded');
            $table->unsignedInteger('minutesLeaveTaken')->nullable()->default(0)->after('minutesWorked');
            $table->unsignedInteger('minutesRosteredTimeOffTaken')->nullable()->default(0)->after('minutesLeaveTaken');
            $table->unsignedInteger('minutesAccruedHoursTaken')->nullable()->default(0)->after('minutesRosteredTimeOffTaken');
            $table->unsignedInteger('minutesPublicHolidayTaken')->nullable()->default(0)->after('minutesAccruedHoursTaken');
            $table->unsignedInteger('minutesExcessTimeAccrued')->nullable()->default(0)->after('minutesPublicHolidayTaken');
            $table->unsignedInteger('minutesExcessTimePaid')->nullable()->default(0)->after('minutesExcessTimeAccrued');
            $table->unsignedInteger('minutesExcessTimeUnPaid')->nullable()->default(0)->after('minutesExcessTimePaid');
            $table->unsignedInteger('minutesAllowance')->nullable()->default(0)->after('minutesExcessTimeUnPaid');
            $table->unsignedInteger('minutesExtra')->nullable()->default(0)->after('minutesAllowance');
            $table->unsignedInteger('minutesMissing')->nullable()->default(0)->after('minutesExtra');

            $table->unsignedInteger('minutesScheduledDayAverageGlidePeriod')->nullable()->default(0)->after('glidePeriodEnd');
            $table->unsignedInteger('minutesScheduledDayMinGlidePeriod')->nullable()->default(0)->after('minutesScheduledDayAverageGlidePeriod');
            $table->unsignedInteger('minutesScheduledDayMaxGlidePeriod')->nullable()->default(0)->after('minutesScheduledDayMinGlidePeriod');
            $table->unsignedInteger('minutesScheduledGlidePeriod')->nullable()->default(0)->after('minutesScheduledDayMaxGlidePeriod');
            $table->unsignedInteger('minutesRecordedGlidePeriod')->nullable()->default(0)->after('minutesScheduledGlidePeriod');
            $table->unsignedInteger('minutesWorkedGlidePeriod')->nullable()->default(0)->after('minutesRecordedGlidePeriod');
            $table->unsignedInteger('minutesLeaveTakenGlidePeriod')->nullable()->default(0)->after('minutesWorkedGlidePeriod');
            $table->unsignedInteger('minutesRosteredTimeOffTakenGlidePeriod')->nullable()->default(0)->after('minutesLeaveTakenGlidePeriod');
            $table->unsignedInteger('minutesAccruedHoursTakenGlidePeriod')->nullable()->default(0)->after('minutesRosteredTimeOffTakenGlidePeriod');
            $table->unsignedInteger('minutesPublicHolidayTakenGlidePeriod')->nullable()->default(0)->after('minutesAccruedHoursTakenGlidePeriod');
            $table->unsignedInteger('minutesExcessTimeAccruedGlidePeriod')->nullable()->default(0)->after('minutesPublicHolidayTakenGlidePeriod');
            $table->unsignedInteger('minutesExcessTimePaidGlidePeriod')->nullable()->default(0)->after('minutesExcessTimeAccruedGlidePeriod');
            $table->unsignedInteger('minutesExcessTimeUnPaidGlidePeriod')->nullable()->default(0)->after('minutesExcessTimePaidGlidePeriod');
            $table->unsignedInteger('minutesAllowanceGlidePeriod')->nullable()->default(0)->after('minutesExcessTimeUnPaidGlidePeriod');
            $table->unsignedInteger('minutesExtraGlidePeriod')->nullable()->default(0)->after('minutesAllowanceGlidePeriod');
            $table->unsignedInteger('minutesMissingGlidePeriod')->nullable()->default(0)->after('minutesExtraGlidePeriod');
        });
        //endregion Table: TimeSheetDay

        //region Table: TimeSheetExcessTime
        Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endCalendarDate_id');
        });
        //endregion Table: TimeSheetExcessTime

        //region Table: TimeSheetExcessTimeBlock
        Schema::table('TimeSheetExcessTimeBlock', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('timeSheetExcessTime_id');
        });
        //endregion Table: TimeSheetExcessTimeBlock

        //region Table: TimeSheetExcessTimeItem
        Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
            $table->unsignedInteger('minutesAccrued')->nullable()->default(0)->after('attachmentFile_id');
            $table->unsignedInteger('minutesPaid')->nullable()->default(0)->after('minutesAccrued');
            $table->unsignedInteger('minutesUnPaid')->nullable()->default(0)->after('minutesPaid');
            $table->unsignedInteger('minutesBonusTimeInLieu')->nullable()->default(0)->after('doesHaveBonusTimeInLieu');
        });
        //endregion Table: TimeSheetExcessTimeItem

        //region Table: TimeSheetExcessTimeItemRule
        Schema::table('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('ratio');
        });
        //endregion Table: TimeSheetExcessTimeItemRule

        //region Table: TimeSheetItemDay
        Schema::table('TimeSheetItemDay', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('date');
            $table->unsignedInteger('minutesDeductedFromPublicHolidays')->nullable()->default(0)->after('minutes');
        });
        //endregion Table: TimeSheetItemDay

        //region Table: TimeSheetItemDayTime
        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
            $table->unsignedInteger('minutesDeductedFromPublicHolidays')->nullable()->default(0)->after('minutes');
        });
        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheetItemDayTimeBreak
        Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
        });
        //endregion Table: TimeSheetItemDayTimeBreak

        //region Table: TimeSheetPenaltyTrigger
        Schema::table('TimeSheetPenaltyTrigger', function (Blueprint $table) {
            $table->unsignedInteger('minutes')->nullable()->default(0)->after('endDateTime');
        });
        //endregion Table: TimeSheetPenaltyTrigger
    }

    public function down(): void
    {
        //region Table: TimeSheetPenaltyTrigger
        if (Schema::hasColumn('TimeSheetPenaltyTrigger', 'minutes')) {
            Schema::table('TimeSheetPenaltyTrigger', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetPenaltyTrigger

        //region Table: TimeSheetItemDayTimeBreak
        if (Schema::hasColumn('TimeSheetItemDayTimeBreak', 'minutes')) {
            Schema::table('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetItemDayTimeBreak

        //region Table: TimeSheetItemDayTime
        if (Schema::hasColumn('TimeSheetItemDayTime', 'minutes')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        if (Schema::hasColumn('TimeSheetItemDayTime', 'minutesDeductedFromPublicHolidays')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('minutesDeductedFromPublicHolidays');
            });
        }
        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheetItemDay
        if (Schema::hasColumn('TimeSheetItemDay', 'minutes')) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        if (Schema::hasColumn('TimeSheetItemDay', 'minutesDeductedFromPublicHolidays')) {
            Schema::table('TimeSheetItemDay', function (Blueprint $table) {
                $table->dropColumn('minutesDeductedFromPublicHolidays');
            });
        }
        //endregion Table: TimeSheetItemDay

        //region Table: TimeSheetExcessTimeItemRule
        if (Schema::hasColumn('TimeSheetExcessTimeItemRule', 'minutes')) {
            Schema::table('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetExcessTimeItemRule

        //region Table: TimeSheetExcessTimeItem
        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'minutesAccrued')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('minutesAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'minutesPaid')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('minutesPaid');
            });
        }
        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'minutesUnPaid')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('minutesUnPaid');
            });
        }
        if (Schema::hasColumn('TimeSheetExcessTimeItem', 'minutesBonusTimeInLieu')) {
            Schema::table('TimeSheetExcessTimeItem', function (Blueprint $table) {
                $table->dropColumn('minutesBonusTimeInLieu');
            });
        }
        //endregion Table: TimeSheetExcessTimeItem

        //region Table: TimeSheetExcessTimeBlock
        if (Schema::hasColumn('TimeSheetExcessTimeBlock', 'minutes')) {
            Schema::table('TimeSheetExcessTimeBlock', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetExcessTimeBlock

        //region Table: TimeSheetExcessTime
        if (Schema::hasColumn('TimeSheetExcessTime', 'minutes')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetExcessTime

        //region Table: TimeSheetDay
        if (Schema::hasColumn('TimeSheetDay', 'minutesScheduled')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesScheduled');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesRecorded')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesRecorded');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesWorked')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesWorked');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesLeaveTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesLeaveTaken');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesRosteredTimeOffTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesRosteredTimeOffTaken');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesAccruedHoursTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesAccruedHoursTaken');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesPublicHolidayTaken')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesPublicHolidayTaken');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimeAccrued')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimeUnPaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeUnPaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExtra')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExtra');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesMissing')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesMissing');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'minutesScheduledDayAverageGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesScheduledDayAverageGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesScheduledDayMinGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesScheduledDayMinGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesScheduledDayMaxGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesScheduledDayMaxGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesScheduledGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesScheduledGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesRecordedGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesRecordedGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesWorkedGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesWorkedGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesLeaveTakenGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesLeaveTakenGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesRosteredTimeOffTakenGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesRosteredTimeOffTakenGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesAccruedHoursTakenGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesAccruedHoursTakenGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesPublicHolidayTakenGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesPublicHolidayTakenGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimeAccruedGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeAccruedGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimePaidGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimePaidGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExcessTimeUnPaidGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeUnPaidGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesAllowanceGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesAllowanceGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesExtraGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesExtraGlidePeriod');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'minutesMissingGlidePeriod')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('minutesMissingGlidePeriod');
            });
        }
        //endregion Table: TimeSheetDay

        //region Table: TimeSheetAllowanceDayEntry
        if (Schema::hasColumn('TimeSheetAllowanceDayEntry', 'minutes')) {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetAllowanceDayEntry

        //region Table: TimeSheetAllowanceDay
        if (Schema::hasColumn('TimeSheetAllowanceDay', 'minutes')) {
            Schema::table('TimeSheetAllowanceDay', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetAllowanceDay

        //region Table: TimeSheetAllowance
        if (Schema::hasColumn('TimeSheetAllowance', 'minutes')) {
            Schema::table('TimeSheetAllowance', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: TimeSheetAllowance

        //region Table: TimeSheet
        if (Schema::hasColumn('TimeSheet', 'minutesScheduled')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesScheduled');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesRecorded')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesRecorded');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesWorked')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesWorked');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesLeaveTaken')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesLeaveTaken');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesRosteredTimeOffTaken')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesRosteredTimeOffTaken');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesAccruedHoursTaken')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesAccruedHoursTaken');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesPublicHolidayTaken')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesPublicHolidayTaken');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesMissing')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesMissing');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesExcessTimeAccrued')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesExcessTimePaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesExcessTimeUnPaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesExcessTimeUnPaid');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'minutesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('minutesAllowance');
            });
        }
        //endregion Table: TimeSheet

        //region Table: LeaveRequestDayBreak
        if (Schema::hasColumn('LeaveRequestDayBreak', 'minutes')) {
            Schema::table('LeaveRequestDayBreak', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: LeaveRequestDayBreak

        //region Table: LeaveRequestDay
        if (Schema::hasColumn('LeaveRequestDay', 'minutes')) {
            Schema::table('LeaveRequestDay', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: LeaveRequestDay

        //region Table: LeaveRequest
        if (Schema::hasColumn('LeaveRequest', 'minutes')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }

        if (Schema::hasColumn('LeaveRequest', 'minutesGlideDaily')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('minutesGlideDaily');
            });
        }
        //endregion Table: LeaveRequest

        //region Table: UserShiftDayBreak
        if (Schema::hasColumn('UserShiftDayBreak', 'minutes')) {
            Schema::table('UserShiftDayBreak', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }
        //endregion Table: UserShiftDayBreak

        //region Table: UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'minutes')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('minutes');
            });
        }

        if (Schema::hasColumn('UserShiftDay', 'minutesGlidePeriod')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('minutesGlidePeriod');
            });
        }
        //endregion Table: UserShiftDay
    }
};
