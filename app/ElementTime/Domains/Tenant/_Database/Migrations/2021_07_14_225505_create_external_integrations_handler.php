<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('ExternalIntegration', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('system');

            // General data
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('isActive')->default(false);
            $table->integer('order')->default(1);

            // Timestamps
            $table->timestamps();
        });

        Schema::create('ExternalIntegrationSettingsValue', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('rel_id');
            $table->foreign('rel_id', 'eisv_rel_fn')
                ->references('id')
                ->on('ExternalIntegration')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // General data
            $table->string('type');
            $table->longText('value')->nullable();

            // Actor data
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'eisv_au_fn')
                ->references('id')
                ->on('User')
                ->onUpdate('cascade')
                ->onDelete('set null');

            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index(['rel_id', 'type'], 'eisv_rel_type_ix');
        });

        Schema::create('ExternalIntegrationAdminUser', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegration_id');
            $table->foreign('externalIntegration_id', 'eiau_ei_fn')
                ->references('id')
                ->on('ExternalIntegration')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'eiau_u_fn')
                ->references('id')
                ->on('User')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->timestamps();
        });

        Schema::create('ExternalIntegrationModule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegration_id');
            $table->foreign('externalIntegration_id', 'eim_ei_fn')
                ->references('id')
                ->on('ExternalIntegration')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // General data
            $table->string('type');
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->boolean('isActive')->default(false);

            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index(['externalIntegration_id', 'type'], 'eim_ei_type_ix');
            $table->index(['externalIntegration_id', 'isActive'], 'eim_ei_isActive_ix');
        });

        Schema::create('ExternalIntegrationModuleSettingsValue', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('rel_id');
            $table->foreign('rel_id', 'eimsv_rel_fn')
                ->references('id')
                ->on('ExternalIntegrationModule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // General data
            $table->string('type');
            $table->longText('value')->nullable();

            // Actor data
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'eimsv_au_fn')
                ->references('id')
                ->on('User')
                ->onUpdate('cascade')
                ->onDelete('set null');

            // Timestamps
            $table->timestamps();

            // Indexes
            $table->index(['rel_id', 'type'], 'eimsv_rel_type_ix');
        });

        Schema::create('ExternalIntegrationModuleRun', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegrationModule_id');
            $table->foreign('externalIntegrationModule_id', 'eimr_eim_fn')
                ->references('id')
                ->on('ExternalIntegrationModule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'eimr_au_fn')
                ->references('id')
                ->on('User')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->string('type')->nullable();

            $table->string('integrable_type')->nullable();
            $table->unsignedBigInteger('integrable_id')->nullable();

            $table->string('status')->nullable();
            $table->dateTime('requestedDateTime')->nullable();
            $table->dateTime('scheduledDateTime')->nullable();
            $table->dateTime('startedDateTime')->nullable();
            $table->dateTime('finishedDateTime')->nullable();

            $table->text('description')->nullable();
            $table->text('name')->nullable();
            $table->text('comments')->nullable();
            $table->json('details')->nullable();
            $table->json('requestDetails')->nullable();

            $table->json('responseDetails')->nullable();
            $table->json('externalRecordDetails')->nullable();
            $table->json('errors')->nullable();
            $table->json('warnings')->nullable();
            $table->json('infos')->nullable();

            $table->timestamps();
        });

        Schema::create('ExternalIntegrationModuleRunItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegrationModuleRun_id');
            $table->foreign('externalIntegrationModuleRun_id', 'eimri_eimr_fn')
                ->references('id')
                ->on('ExternalIntegrationModuleRun')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->integer('itemNumber')->default(1);
            $table->string('type')->nullable();

            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();

            $table->string('status')->nullable();
            $table->dateTime('startedDateTime')->nullable();
            $table->dateTime('finishedDateTime')->nullable();

            $table->text('description')->nullable();
            $table->json('details')->nullable();
            $table->json('requestDetails')->nullable();

            $table->json('responseDetails')->nullable();
            $table->json('externalRecordDetails')->nullable();
            $table->json('errors')->nullable();
            $table->json('warnings')->nullable();
            $table->json('infos')->nullable();

            $table->timestamps();
        });

        Schema::create('ExternalIntegrationModuleRunItemUnit', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegrationModuleRunItem_id');
            $table->foreign('externalIntegrationModuleRunItem_id', 'eimriu_eimri_fn')
                ->references('id')
                ->on('ExternalIntegrationModuleRunItem')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('parent_id')->nullable();
            $table->foreign('parent_id', 'eimriu_parent_fn')
                ->references('id')
                ->on('ExternalIntegrationModuleRunItemUnit')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->integer('unitNumber')->default(1);
            $table->string('type')->nullable();

            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();

            $table->string('status')->nullable();
            $table->dateTime('startedDateTime')->nullable();
            $table->dateTime('finishedDateTime')->nullable();

            $table->text('description')->nullable();
            $table->json('details')->nullable();
            $table->json('requestDetails')->nullable();

            $table->json('responseDetails')->nullable();
            $table->json('externalRecordDetails')->nullable();
            $table->json('errors')->nullable();
            $table->json('warnings')->nullable();
            $table->json('infos')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ExternalIntegrationModuleRunItemUnit');
        Schema::dropIfExists('ExternalIntegrationModuleRunItem');
        Schema::dropIfExists('ExternalIntegrationModuleRun');
        Schema::dropIfExists('ExternalIntegrationModuleSettingsValue');
        Schema::dropIfExists('ExternalIntegrationModule');
        Schema::dropIfExists('ExternalIntegrationAdminUser');
        Schema::dropIfExists('ExternalIntegrationSettingsValue');
        Schema::dropIfExists('ExternalIntegration');
    }
};
