<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeavePeriodInstanceType\LeavePeriodInstanceType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\PayrollOfficerFlag;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeConditionType\ModelActionTypeConditionType;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelActionType;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelActionTypeCondition;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\Workflows\ModelActionGeneralFunctionality;

uses(LeaveGeneralFunctionality::class, ModelActionGeneralFunctionality::class);

describe('Leave model action', function() {
    it('Basic submission comment/attachment requirements apply to leave requests', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->doesRequireCommentsOnSubmit->toBeTrue()
            ->and($availability)->doesRequireAttachmentsOnSubmit->toBeTrue()
            ->and($availability)->doesRequireCommentsOnApprove->toBeFalse()
            ->and($availability)->doesRequireAttachmentsOnApprove->toBeFalse();
    });

    it('Basic approval comment/attachment requirements apply to leave requests', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->doesRequireCommentsOnSubmit->toBeFalse()
            ->and($availability)->doesRequireAttachmentsOnSubmit->toBeFalse()
            ->and($availability)->doesRequireCommentsOnApprove->toBeTrue()
            ->and($availability)->doesRequireAttachmentsOnApprove->toBeTrue();
    });

    it('Condition: DaysExceeded - Requirements apply to leave requests', function (int $leaveDays, int $conditionDays, bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date, isScheduled: true);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($conditionDays, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_DaysExceeded;
                $condition->options = ['days' => $conditionDays];

                $condition->saveOrFail();
            },
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date->copy()->addDays($leaveDays - 1),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment);
    })->with([
        ['leaveDays' => 1, 'conditionDays' => 3, 'isNot' => false, 'shouldRequireComment' => false],
        ['leaveDays' => 7, 'conditionDays' => 3, 'isNot' => false, 'shouldRequireComment' => true],
        ['leaveDays' => 1, 'conditionDays' => 3, 'isNot' => true, 'shouldRequireComment' => true],
        ['leaveDays' => 7, 'conditionDays' => 3, 'isNot' => true, 'shouldRequireComment' => false],
    ]);

    it('Condition: HoursExceeded - Requirements apply to leave requests', function (int $leaveDays, float $conditionHours, bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($conditionHours, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_HoursExceeded;
                $condition->options = ['hours' => $conditionHours];

                $condition->saveOrFail();
            },
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date->copy()->addDays($leaveDays - 1),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment);
    })->with([
        ['leaveDays' => 1, 'conditionHours' => 12, 'isNot' => false, 'shouldRequireComment' => false],
        ['leaveDays' => 7, 'conditionHours' => 12, 'isNot' => false, 'shouldRequireComment' => true],
        ['leaveDays' => 1, 'conditionHours' => 12, 'isNot' => true, 'shouldRequireComment' => true],
        ['leaveDays' => 7, 'conditionHours' => 12, 'isNot' => true, 'shouldRequireComment' => false],
    ]);

    it('Condition:  InstancesCountHasExceeded - Requirements apply to leave requests', function (int $leaveDays, float $conditionInstances, bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($conditionInstances, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_InstancesCountHasExceeded;
                $condition->options = ['periodType' => LeavePeriodInstanceType::CalendarYear, 'instancesCount' => $conditionInstances];

                $condition->saveOrFail();
            },
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date->copy()->addDays($leaveDays - 1),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment);
    })->with([
        ['leaveDays' => 1, 'conditionInstances' => 0, 'isNot' => false, 'shouldRequireComment' => true],
        ['leaveDays' => 1, 'conditionInstances' => 0, 'isNot' => true, 'shouldRequireComment' => false],
        // TODO: Add cases with a greater number of instances after leave functionality is finished
    ]);

    it('Condition:  StartsOnImmediatelyAfterSpecificDay - Requirements apply to leave requests', function (bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);

        $previousDay = $scheduledDayData[0]->date->copy()->subDay();

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($previousDay, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_StartsOnImmediatelyAfterSpecificDay;
                $condition->options = ['days' => [['id' => $previousDay->dayOfWeek]]];

                $condition->saveOrFail();
            },
        );

        $availability1 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[0]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        $availability2 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[1]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability1)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment)
            ->and($availability2)->doesRequireCommentsOnSubmit->toEqual(!$shouldRequireComment);

        /// TODO: Do conditions for public holidays/holidays
    })->with([
        ['isNot' => false, 'shouldRequireComment' => true],
        ['isNot' => true, 'shouldRequireComment' => false],
    ]);

    it('Condition:  StartsOnSpecificDay - Requirements apply to leave requests', function (bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);

        $startDate = $scheduledDayData[0]->date->copy();

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($startDate, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_StartsOnSpecificDay;
                $condition->options = ['days' => [['id' => $startDate->dayOfWeek]]];

                $condition->saveOrFail();
            },
        );

        $availability1 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[0]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        $availability2 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[1]->date,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability1)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment)
            ->and($availability2)->doesRequireCommentsOnSubmit->toEqual(!$shouldRequireComment);

        /// TODO: Do conditions for public holidays/holidays
    })->with([
        ['isNot' => false, 'shouldRequireComment' => true],
        ['isNot' => true, 'shouldRequireComment' => false],
    ]);

    it('Condition:  SubmittedBySpecificStaff - Requirements apply to leave requests', function (bool $isNot, bool $shouldRequireComment) {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $date = $payRun->startDate;
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $date);
        $scheduledDayData = $this->l_lg_getScheduledDaysFromScheduledPayRunPeriodDaysDefaultData($days);

        $payRollUser = PayrollOfficerFlag::getOne(['constraints' => [
            ['User.id', '<>', $user->id],
        ]]);

        $this->w_mag_createLeaveModelActionWithSubmitRequirements(
            $userLeaveBankType->type,
            function (ModelActionType $modelActionType) use ($payRollUser, $isNot) {
                $condition = new ModelActionTypeCondition();
                $condition->modelActionType_id = $modelActionType->id;
                $condition->isNot = $isNot;
                $condition->type = ModelActionTypeConditionType::Leave_SubmittedBySpecificStaff;
                $condition->options = ['users' => [['id' => $payRollUser->id]]];

                $condition->saveOrFail();
            },
        );

        $availability1 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[0]->date,
            actorUser: $payRollUser,
        );

        $availability2 = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData[1]->date,
        );

        expect($availability1)->doesRequireCommentsOnSubmit->toEqual($shouldRequireComment)
            ->and($availability2)->doesRequireCommentsOnSubmit->toEqual(!$shouldRequireComment);
    })->with([
        ['isNot' => false, 'shouldRequireComment' => true],
        ['isNot' => true, 'shouldRequireComment' => false],
    ]);
});
