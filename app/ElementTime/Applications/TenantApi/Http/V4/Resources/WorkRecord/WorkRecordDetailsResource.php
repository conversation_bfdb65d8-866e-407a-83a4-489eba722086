<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\WorkRecord;

use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance\AdHocAllowanceEntryResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Plant\PlantSheetDayTimeRecordResource;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetRecordType\TimeSheetRecordType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetWorkType\TimeSheetWorkType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkBreak;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;
use Element\ElementTime\Support\Domains\Resource;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'WorkRecordDetailsResource',
    title: 'Work Recorded Details',
    required: ['id', 'timeSheetDay_id', 'timeSheetPeriod_id', 'scheduledPayRunPeriodDay_id', 'userRoleTimeType_id', 'start', 'end', 'workDuration', 'breakDuration', 'sumDuration', 'notes', 'recordedVia', 'itemsWithBreaks'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 42),
        new OA\Property(property: 'timeSheetDay_id', type: 'integer', example: 42),
        new OA\Property(property: 'timeSheetPeriod_id', type: 'integer', example: 42),
        new OA\Property(property: 'scheduledPayRunPeriodDay_id', type: 'integer', example: 42),
        new OA\Property(property: 'userRoleTimeType_id', type: 'integer', example: 42),
        new OA\Property(property: 'start', type: 'string', example: '2025-04-11 10:30:00', nullable: true),
        new OA\Property(property: 'end', type: 'string', example: '2025-04-11 10:30:00', nullable: true),
        new OA\Property(property: 'workDuration', type: 'float', example: 2.5),
        new OA\Property(property: 'breakDuration', type: 'float', example: 2.5),
        new OA\Property(property: 'sumDuration', type: 'float', example: 2.5),
        new OA\Property(property: 'notes', type: 'string', example: 'this is a note'),
        new OA\Property(property: 'recordedVia', type: 'string', enum: RecordedVia::class, example: RecordedVia::KioskTimer),
        new OA\Property(property: 'items_and_breaks', type: 'array', items: new OA\Items(required: ['_', 'id', 'type', 'fullCostCentreLabel', 'group_type', 'group_id', 'model_type', 'model_id', 'sub_type', 'sub_id', 'start', 'end', 'duration', 'notes', 'isScheduled', 'isDurationOnly', 'plant_times', 'allowances'], properties: [
            new OA\Property(property: '_', type: 'string', enum: TimeSheetRecordType::class, example: TimeSheetRecordType::Item),
            new OA\Property(property: 'id', type: 'integer', example: 42),
            new OA\Property(property: 'type', description: 'this only returns if "_" = item', enum: TimeSheetWorkType::class, example: TimeSheetWorkType::Project),
            new OA\Property(property: 'fullCostCentreLabel', description: 'this only returns if "_" = item', type: 'string', example: 'Capital Mains - Trunk (Town to Town) Jugiong - Valve Installation/Replacement - W3253 - 914'),
            new OA\Property(property: 'group_type', description: 'this only returns if "_" = item', type: 'string', example: 'projects', nullable: true),
            new OA\Property(property: 'group_id', description: 'this only returns if "_" = item', type: 'integer', example: 4, nullable: true),
            new OA\Property(property: 'model_type', description: 'this only returns if "_" = item', type: 'string', example: 'project'),
            new OA\Property(property: 'model_id', description: 'this only returns if "_" = item', type: 'integer', example: 4),
            new OA\Property(property: 'sub_type', description: 'this only returns if "_" = item', type: 'string', example: 'activity', nullable: true),
            new OA\Property(property: 'sub_id', description: 'this only returns if "_" = item', type: 'integer', example: 4, nullable: true),
            new OA\Property(property: 'start', type: 'string', example: '2025-04-11 10:30:00', nullable: true),
            new OA\Property(property: 'end', type: 'string', example: '2025-04-11 10:30:00', nullable: true),
            new OA\Property(property: 'duration', type: 'float', example: 2.5),
            new OA\Property(property: 'isScheduled', description: 'this is only required if "_" = break', type: 'boolean', example: false, nullable: true),
            new OA\Property(property: 'isDurationOnly', description: 'this only returns if "_" = item', type: 'boolean', example: false),
            new OA\Property(property: 'notes', type: 'string', example: 'this is a note', nullable: true),
            new OA\Property(property: 'plant_times', description: 'this only returns if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/PlantSheetDayTimeRecordResource')),
            new OA\Property(property: 'allowances', description: 'this only returns if "_" = item', type: 'array', items: new OA\Items(ref: '#/components/schemas/AdHocAllowanceEntryResource')),
        ])),
    ],
)]
class WorkRecordDetailsResource extends Resource
{
    public static $relations = [
        'userRoleTimeType.userRole.role',
        'userRoleTimeType.timeType',
        'breaks',
        'items.plantSheetDayTimes.plantSheet.plantItem',
        'items.plantSheetDayTimes.rel',
        'items.allowanceEntries.timeSheetAllowanceDay.timeSheetAllowance.allowanceType',
    ];

    public function toArray(Request $request): array
    {
        /** @var TimeSheetWork $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'timeSheetDay_id' => $r->timeSheetDay_id,
            'timeSheetPeriod_id' => $r->timeSheetPeriod_id,
            'scheduledPayRunPeriodDay_id' => $r->scheduledPayRunPeriodDay_id,
            'userRoleTimeType_id' => $r->userRoleTimeType_id,
            'start' => $this->fetchDateTime($r->start),
            'end' => $this->fetchDateTime($r->end),
            'workDuration' => $r->workDuration->getTotalHours(),
            'breakDuration' => $r->breakDuration->getTotalHours(),
            'sumDuration' => $r->sumDuration->getTotalHours(),
            'notes' => $r->notes,
            'recordedVia' => $r->recordedVia->id(),
            'items_and_breaks' => $this->fetchItemsWithBreaks($r),
        ];
    }

    /** @throws \Throwable */
    protected function fetchItemsWithBreaks(TimeSheetWork $r): array
    {
        $ret = [];

        foreach ($r->itemsWithBreaks as $item) {
            $v = [];

            if($item instanceof TimeSheetWorkItem){
                $v['_'] = TimeSheetRecordType::Item->id();
                $v['id'] = $item->id;
                $v['type'] = $item->type->id();
                $v['fullCostCentreLabel'] = $item->fullCostCentreLabel;
                $v['group_type'] = $item->group_type;
                $v['group_id'] = $item->group_id;
                $v['model_type'] = $item->model_type;
                $v['model_id'] = $item->model_id;
                $v['sub_type'] = $item->sub_type;
                $v['sub_id'] = $item->sub_id;
                $v['start'] = $this->fetchDateTime($item->start);
                $v['end'] = $this->fetchDateTime($item->end);
                $v['duration'] = $item->duration->getTotalHours();
                $v['isDurationOnly'] = $item->isDurationOnly;
                $v['notes'] = $item->notes;
                $v['plant_times'] = PlantSheetDayTimeRecordResource::collection($item->plantSheetDayTimes);
                $v['allowances'] = AdHocAllowanceEntryResource::collection($item->allowanceEntries);
            } else if($item instanceof TimeSheetWorkBreak){
                $v['_'] = TimeSheetRecordType::Break->id();
                $v['id'] = $item->id;
                $v['start'] = $this->fetchDateTime($item->start);
                $v['end'] = $this->fetchDateTime($item->end);
                $v['duration'] = $item->duration->getTotalHours();
                $v['isScheduled'] = $item->isScheduled;
                $v['notes'] = $item->notes;
            }

            $ret[] = $v;
        }

        return $ret;
    }
}
