<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\TimeSheets\Repositories;

use Carbon\Carbon;
use Element\Core\Exceptions\ExcessTimeAlreadyCalculatedException;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\InvalidStatusException;
use Element\Core\Exceptions\InvalidValueException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\Core\Support\Domains\Repositories\Repository;
use Element\Core\Support\Helpers\TimeCalculations;
use Element\Core\Types\Minute;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\LeaveRequestDayRepository;
use Element\ElementTime\Domains\Tenant\PayRuns\Exceptions\FinishedPayRunException;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDayHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDayTime;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftDayTimeBreak;
use Element\ElementTime\Domains\Tenant\Schedules\Repositories\PublicHolidayRepository;
use Element\ElementTime\Domains\Tenant\Schedules\Repositories\UserShiftDayRepository;
use Element\ElementTime\Domains\Tenant\Schedules\Repositories\UserShiftDayTimeRepository;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructGlideDayTimeBlock;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledMap;
use Element\ElementTime\Domains\Tenant\Schedules\Support\GlideCopyFromScheduleMethods\ExpectedHoursOnlyMethod;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AccruedHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AdditionalHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\PaidHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\UnpaidHoursType;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeCardTotalsUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetApproveViewUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetExcessTimeViewUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetPenaltyViewUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetSubmitViewUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetSummaryViewUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\BroadcastEvents\TimeSheetUpdatedBroadcastEvent;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowance;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTimeWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeBlock;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheet\TimeSheetRepository;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructTimeBalance;
use Element\ElementTime\Domains\Tenant\TimeSheets\Structures\StructTimeRecordedBlock;
use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeSheetDayTimeRecordTypes\BaseType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeSheetDayTimeRecordTypes\HolidayType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeSheetDayTimeRecordTypes\MasterProjectType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeSheetDayTimeRecordTypes\PublicHolidayType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleProject;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserHigherDutyRepository;
use Element\ElementTime\Domains\Tenant\Workflows\Exceptions\UnauthorizedWorkflowException;
use Element\ElementTime\Domains\Tenant\Workflows\Models\ModelIssue;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueLevels\StopperLevel;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeNotApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeNotCommentedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeRuleNotAssignedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\UnderRecordedHoursDayType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\UnderRecordedHoursPeriodType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes as Statuses;
use Element\ElementTime\Support\Enums\RecordedVia;
use Element\ElementTime\Support\Exceptions\ElementTimeException;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Structures\StructTimeBlock;
use Element\ElementTime\Support\Structures\StructTimeSheetIssue;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\DB;

/**
 * @method TimeSheetDay getModel()
 */
class TimeSheetDayRepository extends Repository
{
    /** @var TimeSheetDay */
    protected static $modelClass = TimeSheetDay::class;

    //region --\\   Accessors and Mutators   //--

    //region Property: higherDuty

    protected $customHigherDuty = null;

    /**
     * @return SupportCollection|EloquentCollection|UserHigherDutyRepository
     */
    public function getHigherDutyAttribute()
    {
        if (is_null($this->customHigherDuty)) {
            $this->customHigherDuty = $this->getHigherDuty();
        }

        return $this->customHigherDuty;
    }

    //endregion higherDuty

    //region Property: canCopyFromSchedule
    protected $customCanCopyFromSchedule = null;

    public function getCanCopyFromScheduleAttribute():bool
    {
        if (is_null($this->customCanCopyFromSchedule)) {
            $this->customCanCopyFromSchedule = $this->canCopyFromSchedule();
        }

        return $this->customCanCopyFromSchedule;
    }

    //endregion Property: canCopyFromSchedule

    //region Property: hasExcessTime
    protected $customHasExcessTime = null;

    /**
     * @return bool
     * @throws InvalidValueException
     */
    public function getHasExcessTimeAttribute():bool
    {
        if (is_null($this->customHasExcessTime)) {
            $this->customHasExcessTime = $this->hasExcessTime();
        }

        return $this->customHasExcessTime;
    }

    //endregion Property: hasExcessTime

    //region Property: canEditScheduled
    protected $customCanEditScheduled = null;

    /**
     * @return bool
     * @throws UnauthorizedActionException
     */
    public function getCanEditScheduledAttribute():bool
    {
        if (is_null($this->customCanEditScheduled)) {
            $this->customCanEditScheduled = $this->canEditScheduled(false);
        }

        return $this->customCanEditScheduled;
    }

    //endregion Property: canEditScheduled

    //region Property: canAlignSchedule
    protected $customCanAlignSchedule = null;

    /**
     * @return bool
     * @throws UnauthorizedActionException
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     */
    public function getCanAlignScheduleAttribute():bool
    {
        if (is_null($this->customCanAlignSchedule)) {
            $this->customCanAlignSchedule = $this->canAlignSchedule(false);
        }

        return $this->customCanAlignSchedule;
    }

    //endregion Property: canAlignSchedule

    //region Property: glideFirstDayFromPeriod
    protected $customGlideFirstDayFromPeriod = null;

    public function getGlideFirstDayFromPeriodAttribute(): ?TimeSheetDay
    {
        if (is_null($this->customGlideFirstDayFromPeriod)) {
            $this->customGlideFirstDayFromPeriod = $this->getGlideFirstDayFromPeriod();
        }

        return $this->customGlideFirstDayFromPeriod;
    }

    //endregion Property: glideFirstDayFromPeriod

    //region Property: glideLastDayFromPeriod
    protected $customGlideLastDayFromPeriod = null;

    public function getGlideLastDayFromPeriodAttribute(): ?TimeSheetDay
    {
        if (is_null($this->customGlideLastDayFromPeriod)) {
            $this->customGlideLastDayFromPeriod = $this->getGlideLastDayFromPeriod();
        }

        return $this->customGlideLastDayFromPeriod;
    }

    //endregion Property: glideLastDayFromPeriod

    //region Property: submitIssues
    protected $customSubmitIssues = null;

    /**
     * @return ModelIssue[]|SupportCollection
     * @throws UnauthorizedActionException
     */
    public function getSubmitIssuesAttribute()
    {
        if (is_null($this->customSubmitIssues)) {
            $this->customSubmitIssues = $this->getSubmitIssues();
        }

        return $this->customSubmitIssues;
    }

    //endregion Property: submitIssues

    //region Property: approveIssues
    protected $customApproveIssues = null;

    /**
     * @return \Element\ElementTime\Support\Structures\StructTimeSheetIssue[]|\Illuminate\Support\Collection|null
     * @throws UnauthorizedActionException
     */
    public function getApproveIssuesAttribute()
    {
        if (is_null($this->customApproveIssues)) {
            $this->customApproveIssues = $this->getApproveIssues();
        }

        return $this->customApproveIssues;
    }

    //endregion Property: approveIssues

    //region Property: hasSubmitIssues

    /** @throws UnauthorizedActionException */
    public function getHasSubmitIssuesAttribute():bool
    {
        return $this->getSubmitIssuesAttribute()->count() > 0;
    }

    //endregion Property: hasSubmitIssues

    //region Property: hasApproveIssues

    /** @throws UnauthorizedActionException */
    public function getHasApproveIssuesAttribute():bool
    {
        return $this->getApproveIssuesAttribute()->count() > 0;
    }

    //endregion Property: hasApproveIssues

    //region Property: recordMethod

    protected $customRecordMethod = null;

    public function getRecordMethodAttribute()
    {
        if (is_null($this->customRecordMethod)) {
            $this->customRecordMethod = $this->getModel()->timeSheet->recordMethod;
        }

        return $this->customRecordMethod;
    }

    //endregion Property: recordMethod

    //region Property: hasPublicHoliday

    public function getHasPublicHolidayAttribute():bool
    {
        return !is_null($this->getModel()->publicHoliday_id);
    }

    //endregion Property: hasPublicHoliday

    //region Property: hoursExcessTimeAccruedApproved

    protected $customHoursExcessTimeAccruedApproved = null;

    public function getHoursExcessTimeAccruedApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeAccruedApproved)) {
            $this->customHoursExcessTimeAccruedApproved = $this->getDailyHoursExcessTime(AccruedHoursType::ID, true, false);
        }

        return $this->customHoursExcessTimeAccruedApproved;
    }

    //endregion Property: hoursExcessTimeAccruedApproved

    //region Property: hoursExcessTimeAccruedNotApproved

    protected $customHoursExcessTimeAccruedNotApproved = null;

    public function getHoursExcessTimeAccruedNotApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeAccruedNotApproved)) {
            $this->customHoursExcessTimeAccruedNotApproved = $this->getDailyHoursExcessTime(AccruedHoursType::ID, false, false);
        }

        return $this->customHoursExcessTimeAccruedNotApproved;
    }

    //endregion Property: hoursExcessTimeAccruedNotApproved

    //region Property: hoursExcessTimeAccruedAdjustedApproved

    protected $customHoursExcessTimeAccruedAdjustedApproved = null;

    public function getHoursExcessTimeAccruedAdjustedApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeAccruedAdjustedApproved)) {
            $this->customHoursExcessTimeAccruedAdjustedApproved = $this->getDailyHoursExcessTime(AccruedHoursType::ID, true, true);
        }

        return $this->customHoursExcessTimeAccruedAdjustedApproved;
    }

    //endregion Property: hoursExcessTimeAccruedAdjustedApproved

    //region Property: hoursExcessTimeAccruedAdjustedNotApproved

    protected $customHoursExcessTimeAccruedAdjustedNotApproved = null;

    public function getHoursExcessTimeAccruedAdjustedNotApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeAccruedAdjustedNotApproved)) {
            $this->customHoursExcessTimeAccruedAdjustedNotApproved = $this->getDailyHoursExcessTime(AccruedHoursType::ID, false, true);
        }

        return $this->customHoursExcessTimeAccruedAdjustedNotApproved;
    }

    //endregion Property: hoursExcessTimeAccruedAdjustedNotApproved

    //region Property: hoursExcessTimePaidApproved

    protected $customHoursExcessTimePaidApproved = null;

    public function getHoursExcessTimePaidApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimePaidApproved)) {
            $this->customHoursExcessTimePaidApproved = $this->getDailyHoursExcessTime(PaidHoursType::ID, true, false)
                + $this->getDailyHoursExcessTime(AdditionalHoursType::ID, true, false);
        }

        return $this->customHoursExcessTimePaidApproved;
    }

    //endregion Property: hoursExcessTimePaidApproved

    //region Property: hoursExcessTimePaidNotApproved

    protected $customHoursExcessTimePaidNotApproved = null;

    public function getHoursExcessTimePaidNotApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimePaidNotApproved)) {
            $this->customHoursExcessTimePaidNotApproved = $this->getDailyHoursExcessTime(PaidHoursType::ID, false, false)
                + $this->getDailyHoursExcessTime(AdditionalHoursType::ID, false, false);
        }

        return $this->customHoursExcessTimePaidNotApproved;
    }

    //endregion Property: hoursExcessTimePaidNotApproved

    //region Property: hoursExcessTimePaidAdjustedApproved

    protected $customHoursExcessTimePaidAdjustedApproved = null;

    public function getHoursExcessTimePaidAdjustedApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimePaidAdjustedApproved)) {
            $this->customHoursExcessTimePaidAdjustedApproved = $this->getDailyHoursExcessTime(PaidHoursType::ID, true, true)
                + $this->getDailyHoursExcessTime(AdditionalHoursType::ID, true, true);
        }

        return $this->customHoursExcessTimePaidAdjustedApproved;
    }

    //endregion Property: hoursExcessTimePaidAdjustedApproved

    //region Property: hoursExcessTimePaidAdjustedNotApproved

    protected $customHoursExcessTimePaidAdjustedNotApproved = null;

    public function getHoursExcessTimePaidAdjustedNotApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimePaidAdjustedNotApproved)) {
            $this->customHoursExcessTimePaidAdjustedNotApproved = $this->getDailyHoursExcessTime(PaidHoursType::ID, false, true)
                + $this->getDailyHoursExcessTime(AdditionalHoursType::ID, false, true);
        }

        return $this->customHoursExcessTimePaidAdjustedNotApproved;
    }

    //endregion Property: hoursExcessTimePaidAdjustedNotApproved

    //region Property: hoursExcessTimeUnpaidApproved

    protected $customHoursExcessTimeUnpaidApproved = null;

    public function getHoursExcessTimeUnpaidApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeUnpaidApproved)) {
            $this->customHoursExcessTimeUnpaidApproved = $this->getDailyHoursExcessTime(UnpaidHoursType::ID, true, false);
        }

        return $this->customHoursExcessTimeUnpaidApproved;
    }

    //endregion Property: hoursExcessTimeUnpaidApproved

    //region Property: hoursExcessTimeUnpaidNotApproved

    protected $customHoursExcessTimeUnpaidNotApproved = null;

    public function getHoursExcessTimeUnpaidNotApprovedAttribute()
    {
        if (is_null($this->customHoursExcessTimeUnpaidNotApproved)) {
            $this->customHoursExcessTimeUnpaidNotApproved = $this->getDailyHoursExcessTime(UnpaidHoursType::ID, false, false);
        }

        return $this->customHoursExcessTimeUnpaidNotApproved;
    }

    //endregion Property: hoursExcessTimeUnpaidNotApproved

    //region Property: glidePeriodHoursExcessTimeAccruedApproved

    protected $customGlidePeriodHoursExcessTimeAccruedApproved = null;

    public function getGlidePeriodHoursExcessTimeAccruedApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeAccruedApproved)) {
            $this->customGlidePeriodHoursExcessTimeAccruedApproved = $this->getPeriodHoursExcessTime(AccruedHoursType::ID, true, false);
        }

        return $this->customGlidePeriodHoursExcessTimeAccruedApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeAccruedApproved

    //region Property: glidePeriodHoursExcessTimeAccruedNotApproved

    protected $customGlidePeriodHoursExcessTimeAccruedNotApproved = null;

    public function getGlidePeriodHoursExcessTimeAccruedNotApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeAccruedNotApproved)) {
            $this->customGlidePeriodHoursExcessTimeAccruedNotApproved = $this->getPeriodHoursExcessTime(AccruedHoursType::ID, false, false);
        }

        return $this->customGlidePeriodHoursExcessTimeAccruedNotApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeAccruedNotApproved

    //region Property: glidePeriodHoursExcessTimeAccruedAdjustedApproved

    protected $customGlidePeriodHoursExcessTimeAccruedAdjustedApproved = null;

    public function getGlidePeriodHoursExcessTimeAccruedAdjustedApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeAccruedAdjustedApproved)) {
            $this->customGlidePeriodHoursExcessTimeAccruedAdjustedApproved = $this->getPeriodHoursExcessTime(AccruedHoursType::ID, true, true);
        }

        return $this->customGlidePeriodHoursExcessTimeAccruedAdjustedApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeAccruedAdjustedApproved

    //region Property: glidePeriodHoursExcessTimeAccruedAdjustedNotApproved

    protected $customGlidePeriodHoursExcessTimeAccruedAdjustedNotApproved = null;

    public function getGlidePeriodHoursExcessTimeAccruedAdjustedNotApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeAccruedAdjustedNotApproved)) {
            $this->customGlidePeriodHoursExcessTimeAccruedAdjustedNotApproved = $this->getPeriodHoursExcessTime(AccruedHoursType::ID, false, true);
        }

        return $this->customGlidePeriodHoursExcessTimeAccruedAdjustedNotApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeAccruedAdjustedNotApproved

    //region Property: glidePeriodHoursExcessTimePaidApproved

    protected $customGlidePeriodHoursExcessTimePaidApproved = null;

    public function getGlidePeriodHoursExcessTimePaidApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimePaidApproved)) {
            $this->customGlidePeriodHoursExcessTimePaidApproved = $this->getPeriodHoursExcessTime(PaidHoursType::ID, true, false)
                + $this->getPeriodHoursExcessTime(AdditionalHoursType::ID, true, false);
        }

        return $this->customGlidePeriodHoursExcessTimePaidApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimePaidApproved

    //region Property: glidePeriodHoursExcessTimePaidNotApproved

    protected $customGlidePeriodHoursExcessTimePaidNotApproved = null;

    public function getGlidePeriodHoursExcessTimePaidNotApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimePaidNotApproved)) {
            $this->customGlidePeriodHoursExcessTimePaidNotApproved = $this->getPeriodHoursExcessTime(PaidHoursType::ID, false, false)
                + $this->getPeriodHoursExcessTime(AdditionalHoursType::ID, false, false);
        }

        return $this->customGlidePeriodHoursExcessTimePaidNotApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimePaidNotApproved

    //region Property: glidePeriodHoursExcessTimePaidAdjustedApproved

    protected $customGlidePeriodHoursExcessTimePaidAdjustedApproved = null;

    public function getGlidePeriodHoursExcessTimePaidAdjustedApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimePaidAdjustedApproved)) {
            $this->customGlidePeriodHoursExcessTimePaidAdjustedApproved = $this->getPeriodHoursExcessTime(PaidHoursType::ID, true, true)
                + $this->getPeriodHoursExcessTime(AdditionalHoursType::ID, true, true);
        }

        return $this->customGlidePeriodHoursExcessTimePaidAdjustedApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimePaidAdjustedApproved

    //region Property: glidePeriodHoursExcessTimePaidAdjustedNotApproved

    protected $customGlidePeriodHoursExcessTimePaidAdjustedNotApproved = null;

    public function getGlidePeriodHoursExcessTimePaidAdjustedNotApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimePaidAdjustedNotApproved)) {
            $this->customGlidePeriodHoursExcessTimePaidAdjustedNotApproved = $this->getPeriodHoursExcessTime(PaidHoursType::ID, false, true)
                + $this->getPeriodHoursExcessTime(AdditionalHoursType::ID, false, true);
        }

        return $this->customGlidePeriodHoursExcessTimePaidAdjustedNotApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimePaidAdjustedNotApproved

    //region Property: glidePeriodHoursExcessTimeUnpaidApproved

    protected $customGlidePeriodHoursExcessTimeUnpaidApproved = null;

    public function getGlidePeriodHoursExcessTimeUnpaidApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeUnpaidApproved)) {
            $this->customGlidePeriodHoursExcessTimeUnpaidApproved = $this->getPeriodHoursExcessTime(UnpaidHoursType::ID, true, false);
        }

        return $this->customGlidePeriodHoursExcessTimeUnpaidApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeUnpaidApproved

    //region Property: glidePeriodHoursExcessTimeUnpaidNotApproved

    protected $customGlidePeriodHoursExcessTimeUnpaidNotApproved = null;

    public function getGlidePeriodHoursExcessTimeUnpaidNotApprovedAttribute()
    {
        if (is_null($this->customGlidePeriodHoursExcessTimeUnpaidNotApproved)) {
            $this->customGlidePeriodHoursExcessTimeUnpaidNotApproved = $this->getPeriodHoursExcessTime(UnpaidHoursType::ID, false, false);
        }

        return $this->customGlidePeriodHoursExcessTimeUnpaidNotApproved;
    }

    //endregion Property: glidePeriodHoursExcessTimeUnpaidNotApproved

    //region Property: penaltyHours

    protected $customPenaltyHours = null;

    public function getPenaltyHoursAttribute():float
    {
        if (is_null($this->customPenaltyHours)) {
            $this->customPenaltyHours = $this->getPenaltyHours();
        }

        return $this->customPenaltyHours;
    }

    //endregion Property: penaltyHours

    //region Property: requestedLeaveHours

    protected $customRequestedLeaveHours = null;

    /** @throws \InvalidArgumentException */
    public function getRequestedLeaveHoursAttribute():float
    {
        if (is_null($this->customRequestedLeaveHours)) {
            $this->customRequestedLeaveHours = $this->getLeaveHours('all');
        }

        return $this->customRequestedLeaveHours;
    }

    //endregion Property: requestedLeaveHours

    //region Property: approvedLeaveHours
    protected $customApprovedLeaveHours = null;

    /** @throws \InvalidArgumentException */
    public function getApprovedLeaveHoursAttribute():float
    {
        if (is_null($this->customApprovedLeaveHours)) {
            $this->customApprovedLeaveHours = $this->getLeaveHours('approved');
        }

        return $this->customApprovedLeaveHours;
    }

    //endregion Property: approvedLeaveHours

    //region Property: approvedLeaveRequestDays

    protected $customApprovedLeaveRequestDays = null;

    public function getApprovedLeaveRequestDaysAttribute()
    {
        if (is_null($this->customApprovedLeaveRequestDays)) {
            $this->customApprovedLeaveRequestDays = $this->getApprovedLeaveRequestDays();
        }

        return $this->customApprovedLeaveRequestDays;
    }

    //endregion Property: approvedLeaveRequestDays

    //region Property: allLeaveRequestDays

    protected $customAllLeaveRequestDays = null;

    public function getAllLeaveRequestDaysAttribute()
    {
        if (is_null($this->customAllLeaveRequestDays)) {
            $this->customAllLeaveRequestDays = $this->getAllLeaveRequestDays();
        }

        return $this->customAllLeaveRequestDays;
    }

    //endregion Property: allLeaveRequestDays

    //region Property: approvedLeaveRequestDayOnPeriods

    protected $customApprovedLeaveRequestDaysOnPeriod = null;

    /**
     * @return LeaveRequestDay[]
     * @throws InvalidArgumentException
     */
    public function getApprovedLeaveRequestDaysOnPeriodAttribute()
    {
        if (is_null($this->customApprovedLeaveRequestDaysOnPeriod)) {
            $this->customApprovedLeaveRequestDaysOnPeriod = $this->getApprovedLeaveRequestDaysOnPeriod();
        }

        return $this->customApprovedLeaveRequestDaysOnPeriod;
    }

    //endregion Property: approvedLeaveRequestDayOnPeriods

    //region Property: allLeaveRequestDayOnPeriods

    protected $customAllLeaveRequestDaysOnPeriod = null;

    /**
     * @return LeaveRequestDay[]
     * @throws InvalidArgumentException
     */
    public function getAllLeaveRequestDaysOnPeriodAttribute()
    {
        if (is_null($this->customAllLeaveRequestDaysOnPeriod)) {
            $this->customAllLeaveRequestDaysOnPeriod = $this->getAllLeaveRequestDaysOnPeriod();
        }

        return $this->customAllLeaveRequestDaysOnPeriod;
    }

    //endregion Property: allLeaveRequestDays

    //endregion --\\   Accessors and Mutators   //--

    //region --\\   General accessor methods   //--

    /** @throws \Throwable */
    public function canCopyFromSchedule(): bool
    {
        $model = $this->getModel();

        $ret =
            $model->canBeUpdated
            && $model->timeSheet->canBeUpdated(CurrentUser::getUser())
            && $model->timeSheet->payRunItem->user->allowsCopyFromSchedule
            && $model->hoursWorkedCalculation == 0
            && $model->hoursExpected > $model->hoursPublicHoliday
            && $model->requestedLeaveHours == 0;

        return $ret;
    }

    /** @throws \Throwable */
    public function canCopyFromPreviousDay(): bool
    {
        $model = $this->getModel();

        $previousDay = $model->repository->getSiblingByDate($model->date->copy()->subDay());

        if (is_null($previousDay)) {
            return false;
        }

        return $this->canCopyFromDay($previousDay);
    }

    /**
     * @throws \Throwable
     * @throws UnauthorizedWorkflowException
     */
    public function canCopyFromDay(TimeSheetDay $fromTimeSheetDay): bool
    {
        $model = $this->getModel();

        return
            $model->canBeUpdated
            && $model->timeSheet->canBeUpdated(CurrentUser::getUser())
            && $model->hoursWorkedCalculation == 0
            && $model->requestedLeaveHours == 0
            && $model->recordMethod === $fromTimeSheetDay->recordMethod
            && $fromTimeSheetDay->hoursWorkedCalculation > 0;
    }

    /** @throws InvalidValueException */
    public function hasExcessTime():bool
    {
        $model = $this->getModel();

        if ($model->isGlide && $model->glideDurationType != 'D') {
            if (!is_null($model->excessTime)) {
                return true;
            }

            foreach ($model->getManyGlideSiblings() as $sibling) {
                foreach ($sibling->timeSheetDayTimes as $timeSheetDayTime) {
                    if ($timeSheetDayTime->excessTimes()->count() > 0) {
                        return true;
                    }
                }
            }
        }

        foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->excessTimes()->count() > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return UserHigherDutyRepository|EloquentBuilder|EloquentCollection|\Illuminate\Database\Eloquent\Model|SupportCollection
     */
    public function getHigherDuty()
    {
        $model = $this->getModel();

        foreach ($model->timeSheet->payRunItem->user->userHigherDuties as $userHigherDuty) {
            if (
                ($userHigherDuty->repository->isActiveRequest())
                &&
                (
                    is_null($userHigherDuty->startDate)
                    || $userHigherDuty->startDate->lt(Carbon::minValue())
                    || $userHigherDuty->startDate->lte($model->date)
                )
                && (
                    is_null($userHigherDuty->endDate)
                    || $userHigherDuty->endDate->lt(Carbon::minValue())
                    || $userHigherDuty->endDate->gte($model->date)
                )
            ) {
                return $userHigherDuty;
            }
        }

        return null;
    }

    public function isOnHigherDuty():bool
    {
        return !is_null($this->getHigherDuty());
    }

    /** @throws UnauthorizedActionException */
    public function canEditScheduled(bool $throwsException = true):bool
    {
        $model = $this->getModel();
        $timeSheet = $model->timeSheet;
        $user = $timeSheet->payRunItem->user;

        if (!TenantSystemSettings::o()->canOverrideScheduledHours || !$user->canOverrideScheduledHours) {
            if ($throwsException) {
                throw new UnauthorizedActionException([
                    'action' => 'override scheduled hours',
                    'model' => 'timesheet',
                    'reason' => 'This option is disabled on system settings',
                ]);
            }

            return false;
        }

        $count = 0;
        foreach ($user->userShifts as $userShift) {
            if ($userShift->isActiveOnTime($model->date) && $userShift->shift->type == 'X') {
                $count++;
            }
        }

        if ($count <= 0) {
            if ($throwsException) {
                throw new UnauthorizedActionException([
                    'action' => 'override scheduled hours',
                    'model' => 'timesheet',
                    'reason' => 'Only flexible shifts can be aligned',
                ]);
            }

            return false;
        }

        if (!$timeSheet->canBeUpdated(CurrentUser::getUser())) {
            if ($throwsException) {
                throw new UnauthorizedActionException([
                    'action' => 'override scheduled hours',
                    'model' => 'timesheet',
                    'reason' => 'This timesheet is unable to be edited',
                ]);
            }

            return false;
        }

        return true;
    }

    /**
     * @throws UnauthorizedActionException
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     */
    public function canAlignSchedule(bool $throwsException = true, bool $checkMap = true):bool
    {
        $model = $this->getModel();

        if ($model->isGlide || $model->timeSheet->recordMethod != 'T') {
            if ($throwsException) {
                throw new UnauthorizedActionException([
                    'action' => 'align',
                    'model' => 'schedule',
                    'reason' => 'Not allowed for glide shifts',
                ]);
            }

            return false;
        }

        if ($checkMap) {
            $model->load('timeSheetDayTimes.breaks');

            $map = $model->repository->getBalanceTimeMap([], false, true);

            if (count($map->extra) == 0 && count($map->missing) == 0) {
                if ($throwsException) {
                    throw new UnauthorizedActionException([
                        'action' => 'align',
                        'model' => 'schedule',
                        'reason' => 'Nothing to be changed',
                    ]);
                }

                return false;
            }
        }

        return $this->canEditScheduled($throwsException);
    }

    public function getDailyHoursExcessTime($type = AccruedHoursType::ID, $approved = true, $adjusted = false)
    {
        $model = $this->getModel();

        $hours = 0;

        foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
            foreach ($timeSheetDayTime->excessTimes as $excessTime) {
                if ($approved) {
                    if (!is_null($excessTime->activeItem) && $excessTime->activeItem->repository->isApproved()) {
                        if ($type == AccruedHoursType::ID) {
                            if (!$adjusted) {
                                $hours += $excessTime->activeItem->totalHoursAccrued;
                            } else {
                                $hours += $excessTime->activeItem->totalHoursAccruedAdjusted;
                            }
                        } elseif ($type == PaidHoursType::ID) {
                            if (!$adjusted) {
                                $hours += $excessTime->activeItem->totalHoursPaid;
                            } else {
                                $hours += $excessTime->activeItem->totalHoursPaidAdjusted;
                            }
                        } elseif ($type == UnpaidHoursType::ID) {
                            $hours += $excessTime->activeItem->totalHoursUnpaid;
                        } elseif ($type == AdditionalHoursType::ID) {
                            $hours += $excessTime->activeItem->totalHoursAdditional;
                        }
                    }
                } else {
                    if (!is_null($excessTime->activeItem) && !$excessTime->activeItem->repository->isApproved()) {
                        if ($type == AccruedHoursType::ID) {
                            if (!$adjusted) {
                                $hours += $excessTime->activeItem->totalHoursAccrued;
                            } else {
                                $hours += $excessTime->activeItem->totalHoursAccruedAdjusted;
                            }
                        } elseif ($type == PaidHoursType::ID) {
                            if (!$adjusted) {
                                $hours += $excessTime->activeItem->totalHoursPaid;
                            } else {
                                $hours += $excessTime->activeItem->totalHoursPaidAdjusted;
                            }
                        } elseif ($type == UnpaidHoursType::ID) {
                            $hours += $excessTime->activeItem->totalHoursUnpaid;
                        } elseif ($type == AdditionalHoursType::ID) {
                            $hours += $excessTime->activeItem->totalHoursAdditional;
                        }
                    }
                }
            }
        }

        return $hours;
    }

    public function getPeriodHoursExcessTime($type = AccruedHoursType::ID, $approved = true, $adjusted = false)
    {
        $model = $this->getModel();

        if (!$model->isGlide || $model->glideDurationType == 'D' || !$model->glideFirstDayFromPeriod) {
            return 0;
        }

        $hours = 0;

        if (!is_null($model->excessTime)) {
            $excessTime = $model->excessTime;

            if ($approved) {
                if (!is_null($excessTime->activeItem) && $excessTime->activeItem->repository->isApproved()) {
                    if ($type == AccruedHoursType::ID) {
                        if (!$adjusted) {
                            $hours += $excessTime->activeItem->totalHoursAccrued;
                        } else {
                            $hours += $excessTime->activeItem->totalHoursAccruedAdjusted;
                        }
                    } elseif ($type == PaidHoursType::ID) {
                        if (!$adjusted) {
                            $hours += $excessTime->activeItem->totalHoursPaid;
                        } else {
                            $hours += $excessTime->activeItem->totalHoursPaidAdjusted;
                        }
                    } elseif ($type == UnpaidHoursType::ID) {
                        $hours += $excessTime->activeItem->totalHoursUnpaid;
                    } elseif ($type == AdditionalHoursType::ID) {
                        $hours += $excessTime->activeItem->totalHoursAdditional;
                    }
                }
            } else {
                if (!is_null($excessTime->activeItem) && !$excessTime->activeItem->repository->isApproved()) {
                    if ($type == AccruedHoursType::ID) {
                        if (!$adjusted) {
                            $hours += $excessTime->activeItem->totalHoursAccrued;
                        } else {
                            $hours += $excessTime->activeItem->totalHoursAccruedAdjusted;
                        }
                    } elseif ($type == PaidHoursType::ID) {
                        if (!$adjusted) {
                            $hours += $excessTime->activeItem->totalHoursPaid;
                        } else {
                            $hours += $excessTime->activeItem->totalHoursPaidAdjusted;
                        }
                    } elseif ($type == UnpaidHoursType::ID) {
                        $hours += $excessTime->activeItem->totalHoursUnpaid;
                    } elseif ($type == AdditionalHoursType::ID) {
                        $hours += $excessTime->activeItem->totalHoursAdditional;
                    }
                }
            }
        }

        return $hours;
    }

    public function getPenaltyHours():float
    {
        $model = $this->getModel();

        if (!$model->timeSheet->penaltyCalculationsIsDone || $model->timeSheet->penaltyCalculationsIsRunning) {
            return 0;
        }

        $hours = 0;

        foreach ($model->timeSheetDayTimes as $time) {
            foreach ($time->penaltyTriggers as $penaltyTrigger) {
                $hours += $penaltyTrigger->minutes->toHours();
            }
        }

        /** @var LeaveRequestDay[] $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '=', $model->date->toDateString()],
                ['IN', 'Workflow.status', [
                    Statuses\ApprovedType::ID,
                ]],
            ],
            'returnType' => 'model',
        ]);

        foreach ($leaveRequestDays as $leaveRequestDay) {
            foreach ($leaveRequestDay->penaltyTriggers as $penaltyTrigger) {
                $hours += $penaltyTrigger->minutes->toHours();
            }
        }

        return $hours;
    }

    /** @throws \InvalidArgumentException */
    public function getLeaveHours(string $type = 'all'):float
    {
        $model = $this->getModel();

        $statuses = [
            Statuses\NewType::ID,
            Statuses\SubmittedType::ID,
            Statuses\PartiallyApprovedType::ID,
            Statuses\ApprovedType::ID,
        ];

        if ($type == 'approved') {
            $statuses = [
                Statuses\ApprovedType::ID,
            ];
        }

        /** @var LeaveRequestDay[] $leaveDays */
        $leaveDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['LeaveRequestDay.date', '=', $model->date->toDateString()],
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['IN', 'Workflow.status', $statuses],
            ],
            'returnType' => 'model',
        ]);

        $hours = 0;

        foreach ($leaveDays as $leaveDay) {
            $hours += $leaveDay->actualHours;
        }

        return $hours;
    }

    //endregion --\\   General accessor methods   //--

    //region --\\   Glide accessor methods   //--

    public function getGlideFirstDayFromPeriod(): ?TimeSheetDay
    {
        $model = $this->getModel();

        if (!$this->getModel()->isGlide) {
            return null;
        }

        if ($this->getModel()->isFirstGlideDay) {
            return $this->getModel();
        }

        foreach ($model->timeSheet->timeSheetDays as $timeSheetDay) {
            if ($timeSheetDay->isFirstGlideDay && $timeSheetDay->date->isSameDay($model->glidePeriodStart)) {
                return $timeSheetDay;
            }
        }

        return null;
    }

    public function getGlideLastDayFromPeriod(): ?TimeSheetDay
    {
        $model = $this->getModel();

        if (!$this->getModel()->isGlide) {
            return null;
        }

        if ($this->getModel()->isLastGlideDay) {
            return $this->getModel();
        }

        foreach ($model->timeSheet->timeSheetDays as $timeSheetDay) {
            if ($timeSheetDay->isLastGlideDay) {
                return $timeSheetDay;
            }
        }

        return null;
    }

    //endregion --\\   Glide accessor methods   //--

    //region --\\   General calculation methods   //--

    /**
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     * @throws \Element\ElementTime\Domains\Tenant\PayRuns\Exceptions\FinishedPayRunException
     * @throws \Throwable
     */
    public function getScheduled():StructScheduledMap
    {
        return UserShiftDayRepository::getScheduledByPeriod(
            $this->getModel()->timeSheet->user,
            $this->getModel()->date,
        );
    }

    /**
     * @throws UnauthorizedActionException
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     * @throws \Element\ElementTime\Domains\Tenant\PayRuns\Exceptions\FinishedPayRunException
     * @throws \Throwable
     */
    public function copyFromSchedule(array $options = [], string $notes = null):bool
    {
        $options['via'] = $options['via'] ?? RecordedVia::CopySchedule;
        $options['saveParent'] = $options['saveParent'] ?? true;
        $options['calculatesExcessTime'] = $options['calculatesExcessTime'] ?? true;

        if (!$this->canCopyFromSchedule()) {
            throw new UnauthorizedActionException([
                'action' => 'Copy',
                'model' => 'scheduled hours into recorded',
                'reason' => '',
            ]);
        }

        $model = $this->getModel();

        $newOptions = $options;
        $newOptions['saveParent'] = false;
        $newOptions['calculations'] = 'normal';
        $newOptions['calculatesExcessTime'] = false;
        $newOptions['forceDeductBreaks'] = true;
        $newOptions['forceAvoidDeductingBreaks'] = false;

        $scheduled = $this->getScheduled();

        foreach ($scheduled->days as $day) {
            if ($day->date->eq($model->date)) {
                if (!is_null($day->expectedTimeBlocks) && count($day->expectedTimeBlocks) > 0) {
                    $timeBlocks = $day->expectedTimeBlocks;
                } else {
                    $timeBlocks = $day->timeBlocks;
                }

                foreach ($timeBlocks as $timeBlock) {
                    $newTime = new TimeSheetDayTime;
                    $newTime->timeSheetDay_id = $model->id;
                    $newTime->userShiftDay_id = $timeBlock->data->userShiftDay_id;

                    if ($model->isGlide && $model->glideDurationType != 'D' && $newTime->userShift->glideCopyFromScheduleMethodClass::isType(ExpectedHoursOnlyMethod::class) && $newTime->userShiftDay->expectedTimes->count() < 1) {
                        continue;
                    }

                    $newTime->userShiftTimeType_id = $timeBlock->data->userShiftTimeType_id;
                    $newTime->recordType = MasterProjectType::ID;
                    $newTime->type_type = UserRoleProject::class;
                    $newTime->type_id = $timeBlock->data->masterUserRoleProject_id;
                    $newTime->model_type = Project::class;
                    $newTime->model_id = $timeBlock->data->masterProject_id;
                    $newTime->startDateTime = $timeBlock->startDateTime;
                    $newTime->endDateTime = $timeBlock->endDateTime;
                    $newTime->minutes = new Minute($timeBlock->hours, 'hours');
                    $newTime->name = 'Copied from scheduled hours by ' . CurrentUser::getUser()->fullName . ' at ' . Carbon::now()->format(TenantSystemSettings::getValue('settings.dateFormat') . ' ' . TenantSystemSettings::getValue('settings.timeFormat'));
                    $newTime->notes = $notes;

                    $newTime->saveOrFail($newOptions);
                }
            }
        }

        if ($options['calculatesExcessTime']) {
            $model->load(['timeSheetDayTimes.excessTimes']);
            $model->load(['timeSheetDayTimes.breaks']);

            $model->saveOrFail([
                'forceSave' => true,
                'saveParent' => false,
                'forcePreventEvent' => true,
                'forceUpdateEvent' => false,
            ]);

            $this->updateExcessTime([
                'saveParent' => true,
                'forceUpdateEvent' => false,
                'forcePreventEvent' => true,
                'forceSave' => true,
            ], [
                TimeSheetUpdatedBroadcastEvent::class,
                TimeSheetExcessTimeViewUpdatedBroadcastEvent::class,
                TimeSheetPenaltyViewUpdatedBroadcastEvent::class,
                TimeSheetSummaryViewUpdatedBroadcastEvent::class,
                TimeSheetApproveViewUpdatedBroadcastEvent::class,
                TimeSheetSubmitViewUpdatedBroadcastEvent::class,
            ]);
        } else {
            $model->saveOrFail($options);
        }

        return true;
    }

    /**
     * @throws FinishedPayRunException
     * @throws \Throwable
     */
    public function copyFromDay(TimeSheetDay $fromDay): self
    {
        $model = $this->getModel();

        if ($model->recordMethod !== $fromDay->recordMethod) {
            throw new InvalidArgumentException('Cannot mix duration only record and start/end record');
        }

        if (!$this->canCopyFromDay($fromDay)) {
            throw new UnauthorizedActionException([
                'action' => 'copy',
                'model' => 'recorded hours',
                'reason' => 'There are clashes with current records',
            ]);
        }

        $model->getConnection()->transaction(function () use ($model, $fromDay) {
            $targetDate = $model->date->copy();

            $timeSheetDayTimeOptions = [
                'forceSave' => true,
                'saveParent' => false,
                'calculations' => 'normal',
                'calculatesExcessTime' => false,
            ];

            foreach ($fromDay->timeSheetDayTimes as $fromTimeSheetDayTime) {
                if (!$fromTimeSheetDayTime->recordTypeClass::IS_WORK) {
                    continue;
                }

                $targetUserShiftDay = UserShiftDay::getOneByDate($fromTimeSheetDayTime->userShift, $targetDate);
                $replicatedTimeSheetDayTime = $fromTimeSheetDayTime->replicateOnlyRawProperties();
                $replicatedTimeSheetDayTime->userShiftDay_id = $targetUserShiftDay->id;
                $replicatedTimeSheetDayTime->timeSheetDay_id = $model->id;

                if ($model->recordMethod === 'T') {
                    $replicatedTimeSheetDayTime->startDateTime = $fromTimeSheetDayTime->startDateTime->copy()->setDateFrom($targetDate);
                    $replicatedTimeSheetDayTime->endDateTime = $fromTimeSheetDayTime->endDateTime->copy()->setDateFrom($targetDate);
                } else {
                    $replicatedTimeSheetDayTime->minutes = $fromTimeSheetDayTime->minutes->toMinutes();
                }

                if (!$fromTimeSheetDayTime->userShiftTimeType->isActiveOnTime($targetDate)) {
                    $replicatedTimeSheetDayTime->userShiftTimeType_id = $replicatedTimeSheetDayTime->userShift->repository->getMasterUserShiftTimeType($targetDate)->id;
                    $replicatedTimeSheetDayTime->load('userShiftTimeType');
                }

                if (!$replicatedTimeSheetDayTime->repository->isValid(true, $targetDate)) {
                    continue;
                }

                $replicatedTimeSheetDayTime->saveOrFail($timeSheetDayTimeOptions);

                if ($replicatedTimeSheetDayTime->breaks->count() != $fromTimeSheetDayTime->breaks->count()) {
                    $hasBreakChanges = false;
                    foreach ($fromTimeSheetDayTime->breaks as $break) {
                        $replicatedBreak = $break->replicateOnlyRawProperties();
                        $replicatedBreak->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                        $replicatedBreak->startDateTime = $break->startDateTime->copy()->setDateFrom($targetDate);
                        $replicatedBreak->endDateTime = $break->endDateTime->copy()->setDateFrom($targetDate);
                        $replicatedBreak->minutes = $break->minutes;
                        $replicatedBreak->saveOrFail();

                        $hasBreakChanges = true;
                    }

                    if ($hasBreakChanges) {
                        $replicatedTimeSheetDayTime->load('breaks');
                        $replicatedTimeSheetDayTime->saveOrFail($timeSheetDayTimeOptions);
                    }
                }

                foreach ($fromTimeSheetDayTime->allowanceEntries as $entry) {
                    if (!is_null($entry->timeSheetDayTime_id) && is_null($entry->timeSheetDayTimeWork_id)) {
                        $dayTimeAllowanceDay = $this->getTimeSheetAllowanceDayFromType($entry->timeSheetAllowanceDay->timeSheetAllowance->allowanceType_id);

                        $dayTimeAllowanceEntry = $entry->replicateOnlyRawProperties();

                        $dayTimeAllowanceEntry->timeSheetAllowanceDay_id = $dayTimeAllowanceDay->id;
                        $dayTimeAllowanceEntry->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                        $dayTimeAllowanceEntry->startDateTime = !is_null($entry->startDateTime) ? $entry->startDateTime->copy()->setDateFrom($targetDate) : null;
                        $dayTimeAllowanceEntry->endDateTime = !is_null($entry->endDateTime) ? $entry->endDateTime->copy()->setDateFrom($targetDate) : null;

                        $dayTimeAllowanceEntry->saveOrFail();
                    }
                }

                foreach ($fromTimeSheetDayTime->plantTimes as $plantTime) {
                    if (is_null($plantTime->timeSheetDayTimeWork_id) && is_null($plantTime->parent)) {
                        $replicatedPlantTime = $plantTime->replicateOnlyRawProperties();

                        $replicatedPlantTime->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;

                        $replicatedPlantTime->saveOrFail([
                            'plantItem_id' => $plantTime->plantItem->id,
                        ]);

                        foreach ($plantTime->children as $child) {
                            $replicatedChildPlant = $child->replicateOnlyRawProperties();

                            $replicatedChildPlant->parentPlantSheetDayTime_id = $replicatedPlantTime->id;
                            $replicatedChildPlant->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;

                            $replicatedChildPlant->saveOrFail([
                                'plantItem_id' => $child->plantItem->id,
                            ]);
                        }
                    }
                }

                foreach ($fromTimeSheetDayTime->works as $work) {
                    if (!$work->activityType->isActiveOnTime($targetDate)) {
                        continue;
                    }

                    $replicatedWork = $work->replicateOnlyRawProperties();
                    $replicatedWork->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                    $replicatedWork->saveOrFail();

                    foreach ($work->allowanceEntries as $entry) {

                        if (!is_null($entry->timeSheetDayTimeWork_id)) {
                            $timeSheetAllowanceDay = $this->getTimeSheetAllowanceDayFromType($entry->timeSheetAllowanceDay->timeSheetAllowance->allowanceType_id);

                            $dayTimeAllowanceEntry = $entry->replicateOnlyRawProperties();

                            $dayTimeAllowanceEntry->timeSheetAllowanceDay_id = $timeSheetAllowanceDay->id;
                            $dayTimeAllowanceEntry->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                            $dayTimeAllowanceEntry->timeSheetDayTimeWork_id = $replicatedWork->id;
                            $dayTimeAllowanceEntry->startDateTime = !is_null($entry->startDateTime) ? $entry->startDateTime->copy()->setDateFrom($targetDate) : null;
                            $dayTimeAllowanceEntry->endDateTime = !is_null($entry->endDateTime) ? $entry->endDateTime->copy()->setDateFrom($targetDate) : null;

                            $dayTimeAllowanceEntry->saveOrFail();
                        }
                    }

                    foreach ($work->plantTimes as $plantTime) {
                        if (!is_null($plantTime->timeSheetDayTimeWork_id) && is_null($plantTime->parent)) {
                            $replicatedPlantTime = $plantTime->replicateOnlyRawProperties();

                            $replicatedPlantTime->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                            $replicatedPlantTime->timeSheetDayTimeWork_id = $replicatedWork->id;

                            $replicatedPlantTime->saveOrFail([
                                'plantItem_id' => $plantTime->plantItem->id,
                            ]);

                            foreach ($plantTime->children as $child) {
                                $replicatedChildPlant = $child->replicateOnlyRawProperties();

                                $replicatedChildPlant->parentPlantSheetDayTime_id = $replicatedPlantTime->id;
                                $replicatedChildPlant->timeSheetDayTime_id = $replicatedTimeSheetDayTime->id;
                                $replicatedChildPlant->timeSheetDayTimeWork_id = $replicatedWork->id;

                                $replicatedChildPlant->saveOrFail([
                                    'plantItem_id' => $child->plantItem->id,
                                ]);
                            }
                        }
                    }
                }
            }

            $model->load([
                'timeSheetAllowanceDays.timeSheetAllowanceDayEntries',
                'timeSheetDayTimes.excessTimes',
                'timeSheetDayTimes.breaks',
            ]);

            foreach ($model->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
                $timeSheetAllowanceDay->save();
            }

            $model->saveOrFail([
                'forceSave' => true,
                'saveParent' => true,
                'forcePreventEvent' => true,
                'forceUpdateEvent' => false,
            ]);

            $this->updateExcessTime([
                'saveParent' => true,
                'forceUpdateEvent' => false,
                'forcePreventEvent' => true,
                'forceSave' => true,
            ], [
                TimeCardTotalsUpdatedBroadcastEvent::class,
                TimeSheetUpdatedBroadcastEvent::class,
                TimeSheetExcessTimeViewUpdatedBroadcastEvent::class,
                TimeSheetPenaltyViewUpdatedBroadcastEvent::class,
                TimeSheetSummaryViewUpdatedBroadcastEvent::class,
                TimeSheetApproveViewUpdatedBroadcastEvent::class,
                TimeSheetSubmitViewUpdatedBroadcastEvent::class,
            ]);
        });

        return $this;
    }

    /**
     * @throws InvalidValueException
     * @throws UnauthorizedActionException
     * @throws \Element\Core\Exceptions\CompulsoryParametersMissingException
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     * @throws \Throwable
     */
    public function runNonGlideCalculations(bool $save = true, bool $deep = false, bool $updateScheduling = true)
    {
        $model = $this->getModel();

        if ($model->isGlide) {
            throw new InvalidValueException([
                'model' => 'timesheet day',
                'field' => 'is glide',
                'more' => 'Only non-glide timesheet days can use this method',
            ]);
        }

        $hoursWorked = 0.0;
        $hoursWorkedCalculation = 0.0;
        $hoursLeave = 0.0;
        $hoursLeaveCalculation = 0.0;
        $hoursExcessTimeAccrued = 0.0;
        $hoursExcessTimeAccruedAdjusted = 0.0;
        $hoursExcessTimePaid = 0.0;
        $hoursExcessTimePaidAdjusted = 0.0;
        $valueExcessTimePaid = 0.0;
        $hoursExcessTimeUnpaid = 0.0;
        $hoursExcessTimeUsed = 0.0;
        $hoursExcessTimeUsedCalculation = 0.0;
        $hoursRosteredTimeOff = 0.0;
        $hoursRosteredTimeOffCalculation = 0.0;
        $hoursPublicHoliday = 0.0;
        $hoursAllowance = 0.0;
        $hoursAllowanceCalculation = 0.0;
        $milesAllowance = 0.0;
        $valueAllowance = 0.0;
        $hoursMore = 0.0;
        $hoursLess = 0.0;
        $hoursTotal = 0.0;
        $hoursTotalCalculation = 0.0;
        $hoursExpected = 0.0;

        /** @var UserShiftDay[] $userShiftDays */
        $userShiftDays = UserShiftDay::getManyFromUser($model->timeSheet->user, $model->date->copy(), null, false, false, [], false, !$updateScheduling);

        foreach ($userShiftDays as $day) {
            if ($day->isPublicHoliday) {
                $hoursExpected += $day->publicHolidayHours;

                if ($day->doesWorkOnPublicHoliday) {
                    $hoursExpected += $day->hours;
                }
            } elseif ($day->hasHolidays) {
                $hoursExpected += $day->holidayMinutes->toHours();

                if ($day->doesWorkOnPublicHoliday) {
                    $hoursExpected += $day->hours;
                }
            } else {
                $hoursExpected += $day->hours;
            }
        }

        $model->load(['timeSheetDayTimes.excessTimes.items']);

        foreach ($model->timeSheetDayTimes as $t) {
            if ($deep) {
                $t->saveOrFail([
                    'calculations' => 'deep',
                    'calculatesExcessTime' => false,
                ]);
            }

            if ($t->recordTypeClass::IS_WORK) {
                $hoursWorked += $t->finalCalculatedHours;
                $hoursWorkedCalculation += $t->finalCalculatedHours;
            } elseif ($t->recordTypeClass::IS_HOLIDAY) {
                $hoursPublicHoliday += $t->finalCalculatedHours;
            }

            $hoursTotal += $t->finalCalculatedHours;
            $hoursTotalCalculation += $t->finalCalculatedHours;

            foreach ($t->excessTimes as $excessTime) {
                $item = $excessTime->activeItem;
                if (!is_null($item)) {
                    if ($item->repository->isApproved()) {
                        $hoursExcessTimeAccrued += $item->totalHoursAccrued;
                        $hoursExcessTimeAccruedAdjusted += $item->totalHoursAccruedAdjusted;
                        $hoursExcessTimePaid += $item->totalHoursPaid;
                        $hoursExcessTimePaid += $item->totalHoursAdditional;
                        $hoursExcessTimePaidAdjusted += $item->totalHoursPaidAdjusted;
                        $hoursExcessTimePaidAdjusted += $item->totalHoursAdditional;
                        $valueExcessTimePaid += $item->totalHoursPaidAdjusted * $excessTime->hourlyRate;
                        $valueExcessTimePaid += $item->totalHoursAdditional * $excessTime->hourlyRate;
                        $hoursExcessTimeUnpaid += $item->totalHoursUnpaid;
                    }
                }
            }
        }

        /** @var LeaveRequestDay[] $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '=', $model->date],
                ['Workflow.status', '=', Statuses\ApprovedType::ID],
            ],
            'relations' => [
                'leaveRequest.userLeaveBankType',
            ],
            'returnType' => 'model',
        ]);

        foreach ($leaveRequestDays as $leaveRequestDay) {
            if ($leaveRequestDay->leaveRequest->userLeaveBankType->isLeave) {
                $hoursLeave += $leaveRequestDay->calculatedHours;
                $hoursLeaveCalculation += $leaveRequestDay->actualHours;
            } elseif ($leaveRequestDay->leaveRequest->userLeaveBankType->isRosteredTimeOff) {
                $hoursRosteredTimeOff += $leaveRequestDay->calculatedHours;
                $hoursRosteredTimeOffCalculation += $leaveRequestDay->actualHours;
            } elseif ($leaveRequestDay->leaveRequest->userLeaveBankType->isAccruedHours) {
                $hoursExcessTimeUsed += $leaveRequestDay->calculatedHours;
                $hoursExcessTimeUsedCalculation += $leaveRequestDay->actualHours;
            }

            $hoursTotal += $leaveRequestDay->calculatedHours;
            $hoursTotalCalculation += $leaveRequestDay->actualHours;
        }

        $model->load(['timeSheetAllowanceDays.timeSheetAllowance']);

        foreach ($model->timeSheetAllowanceDays as $allowanceDay) {
            if (is_null($allowanceDay->timeSheetAllowance)) {
                $allowanceDay->delete();
            }

            if ($deep) {
                $allowanceDay->save([
                    'preventJobsDispatching' => true,
                    'calculations' => 'deep',
                ]);
            }

            $hoursAllowance += $allowanceDay->hours;
            $milesAllowance += $allowanceDay->miles;
            $valueAllowance += $allowanceDay->value;
        }

        if ($hoursExpected < $hoursTotal) {
            $hoursMore = $hoursTotalCalculation - $hoursExpected;
        } else {
            $hoursLess = $hoursExpected - $hoursTotalCalculation;
        }

        $model->hoursWorked = $hoursWorked;
        $model->hoursWorkedCalculation = $hoursWorkedCalculation;
        $model->hoursLeave = $hoursLeave;
        $model->hoursLeaveCalculation = $hoursLeaveCalculation;
        $model->hoursExcessTimeAccrued = $hoursExcessTimeAccrued;
        $model->hoursExcessTimeAccruedAdjusted = $hoursExcessTimeAccruedAdjusted;
        $model->hoursExcessTimePaid = $hoursExcessTimePaid;
        $model->hoursExcessTimePaidAdjusted = $hoursExcessTimePaidAdjusted;
        $model->valueExcessTimePaid = $valueExcessTimePaid;
        $model->hoursExcessTimeUnpaid = $hoursExcessTimeUnpaid;
        $model->hoursExcessTimeUsed = $hoursExcessTimeUsed;
        $model->hoursExcessTimeUsedCalculation = $hoursExcessTimeUsedCalculation;
        $model->hoursRosteredTimeOff = $hoursRosteredTimeOff;
        $model->hoursRosteredTimeOffCalculation = $hoursRosteredTimeOffCalculation;
        $model->hoursPublicHoliday = $hoursPublicHoliday;
        $model->hoursAllowance = $hoursAllowance;
        $model->hoursAllowanceCalculation = $hoursAllowanceCalculation;
        $model->milesAllowance = $milesAllowance;
        $model->valueAllowance = $valueAllowance;
        $model->hoursMore = $hoursMore;
        $model->hoursLess = $hoursLess;
        $model->hoursTotal = $hoursTotal;
        $model->hoursTotalCalculation = $hoursTotalCalculation;
        $model->hoursExpected = $hoursExpected;

        if ($save) {
            $model->saveOrFail(['calculations' => 'none']);
        }
    }

    /** @return \Illuminate\Support\Collection|StructTimeRecordedBlock[] */
    public function getTimeRecordedMap(bool $includesNotApprovedLeave = false, bool $joinsTimesWithNoNoGap = false): array|SupportCollection
    {
        $model = $this->getModel();

        $map = [];
        $i = 0;

        foreach ($model->timeSheetDayTimes as $item) {
            if (!$item->recordTypeClass::IS_WORK) {
                continue;
            }

            $mapItem = new StructTimeRecordedBlock();
            $mapItem->userShiftDay_id = $item->userShiftDay_id;
            $mapItem->timeSheetDayTime_id = $item->id;
            $mapItem->userShiftTimeType_id = $item->userShiftTimeType_id;
            $mapItem->formattedCode = $item->formattedCode;
            $mapItem->comment = $item->notes;

            if ($model->recordMethod == 'T') {
                $mapItem->startDateTime = $item->startDateTime;
                foreach ($item->breaks as $break) {
                    $mapItem->endDateTime = $break->startDateTime;
                    $mapItem->hours = $mapItem->startDateTime->diffInSeconds($mapItem->endDateTime) / 60 / 60;
                    $data = new \stdClass();
                    $data->isAlwaysOrdinarySpanOnGlide = $item->userShiftTimeType->timeType->isAlwaysOrdinarySpanOnGlide;
                    $data->isAlwaysExcessTimeOnGlide = $item->userShiftTimeType->timeType->isAlwaysExcessTimeOnGlide;
                    $mapItem->data = $data;

                    $map[$i] = $mapItem;

                    $i++;

                    $mapItem = new StructTimeRecordedBlock();
                    $mapItem->userShiftDay_id = $item->userShiftDay_id;
                    $mapItem->timeSheetDayTime_id = $item->id;
                    $mapItem->userShiftTimeType_id = $item->userShiftTimeType_id;
                    $mapItem->formattedCode = $item->formattedCode;
                    $mapItem->comment = $item->notes;
                    $mapItem->startDateTime = $break->endDateTime;
                }
                $mapItem->endDateTime = $item->endDateTime;
                $itemPeriod = TimeCalculations::getValidPeriod($mapItem->startDateTime, $mapItem->endDateTime);
                $mapItem->hours = $itemPeriod->start->diffInSeconds($itemPeriod->end) / 60 / 60;
            } else {
                $mapItem->hours = $item->finalCalculatedHours;
            }

            $data = new \stdClass();
            $data->isAlwaysOrdinarySpanOnGlide = $item->userShiftTimeType->timeType->isAlwaysOrdinarySpanOnGlide;
            $data->isAlwaysExcessTimeOnGlide = $item->userShiftTimeType->timeType->isAlwaysExcessTimeOnGlide;
            $mapItem->data = $data;

            $map[$i] = $mapItem;
            $i++;
        }

        if ($includesNotApprovedLeave) {
            $leaveRequestDays = $model->allLeaveRequestDays;
        } else {
            $leaveRequestDays = $model->approvedLeaveRequestDays;
        }

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $mapItem = new StructTimeRecordedBlock();
            $mapItem->userShiftDay_id = $leaveRequestDay->userShiftDay_id;
            $mapItem->timeSheetDayTime_id = null;
            $mapItem->userShiftTimeType_id = null;
            $mapItem->formattedCode = $leaveRequestDay->userShiftDay->userShift->userRole->repository->getMasterUserRoleProject($leaveRequestDay->date)->formattedCode;
            $mapItem->comment = $leaveRequestDay->leaveRequest->reason;

            if ($model->recordMethod == 'T') {
                $leaveRequestDayTimeBlock = $leaveRequestDay->repository->createPeriodFromTimeBlock();
                $mapItem->startDateTime = $leaveRequestDay->startDateTime;

                if ($leaveRequestDayTimeBlock->start->diffInSeconds($leaveRequestDayTimeBlock->end) / 60 / 60 != $leaveRequestDay->actualHours) {
                    foreach ($leaveRequestDay->userShiftDay->scheduledTimes as $scheduledTime) {
                        foreach ($scheduledTime->breaks as $break) {
                            if ($leaveRequestDayTimeBlock->contains($break->repository->createPeriodFromTimeBlock())) {
                                $leaveBlock = TimeCalculations::getValidPeriod($mapItem->startDateTime, $break->startDateTime);
                                $mapItem->endDateTime = $break->startDateTime;
                                $mapItem->hours = $leaveBlock->start->diffInSeconds($leaveBlock->end) / 60 / 60;

                                $data = new \stdClass();
                                $data->isAlwaysOrdinarySpanOnGlide = false;
                                $data->isAlwaysExcessTimeOnGlide = false;

                                $mapItem->data = $data;

                                $map[$i] = $mapItem;

                                $i++;

                                $mapItem = new StructTimeRecordedBlock();
                                $mapItem->userShiftDay_id = $leaveRequestDay->userShiftDay_id;
                                $mapItem->timeSheetDayTime_id = null;
                                $mapItem->userShiftTimeType_id = null;
                                $mapItem->formattedCode = $leaveRequestDay->userShiftDay->userShift->userRole->repository->getMasterUserRoleProject($leaveRequestDay->date)->formattedCode;
                                $mapItem->comment = $leaveRequestDay->leaveRequest->reason;
                                $mapItem->startDateTime = $break->endDateTime;
                            }
                        }
                    }
                }

                $mapItem->endDateTime = $leaveRequestDay->endDateTime;
                $mapItemPeriod = TimeCalculations::getValidPeriod($mapItem->startDateTime, $mapItem->endDateTime);
                $mapItem->hours = $mapItemPeriod->start->diffInSeconds($mapItemPeriod->end) / 60 / 60;
            } else {
                $mapItem->hours = $leaveRequestDay->actualHours;
            }

            $data = new \stdClass();
            $data->isAlwaysOrdinarySpanOnGlide = false;
            $data->isAlwaysExcessTimeOnGlide = false;
            $mapItem->data = $data;

            $map[$i] = $mapItem;
            $i++;
        }

        if ($model->timeSheet->recordMethod == 'T') {
            $map = array_values(Arr::sort($map, fn ($a) => $a->startDateTime->format('YmdHis')));
        }

        if ($joinsTimesWithNoNoGap) {
            for ($i = 1; $i < count($map); $i++) {
                $currentItem = $map[$i];

                /** @var StructTimeRecordedBlock $prevItem */
                $prevItem = $map[$i - 1];

                if ($model->timeSheet->recordMethod == 'T' && $prevItem->endDateTime->gte($currentItem->startDateTime) && $prevItem->userShiftDay_id == $currentItem->userShiftDay_id && $prevItem->userShiftTimeType_id == $currentItem->userShiftTimeType_id && $prevItem->formattedCode == $currentItem->formattedCode) {
                    $prevItem->endDateTime = $currentItem->endDateTime->copy();
                    $period = TimeCalculations::getValidPeriod($prevItem->startDateTime, $prevItem->endDateTime);
                    $prevItem->hours = $period->start->diffInSeconds($period->end) / 60 / 60;
                    unset($map[$i]);

                    $map = array_values($map);
                    $i--;
                } elseif ($model->timeSheet->recordMethod == 'H' && $prevItem->userShiftDay_id == $currentItem->userShiftDay_id && $prevItem->userShiftTimeType_id == $currentItem->userShiftTimeType_id && $prevItem->formattedCode == $currentItem->formattedCode) {
                    $prevItem->hours += $currentItem->hours;
                    unset($map[$i]);

                    $map = array_values($map);
                    $i--;
                }
            }
        }

        /** @var \Illuminate\Support\Collection|StructTimeRecordedBlock[] $ret */
        $ret = collect(array_values($map));

        return $ret;
    }

    protected $cachedBalanceMap = null;

    /**
     * @param UserShiftDay[]|Collection $userShiftDays
     * @return \Illuminate\Support\Collection|StructTimeBalance
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     */
    public function getBalanceTimeMap($userShiftDays = [], bool $forceUpdate = true, bool $includesNotApprovedLeave = false)
    {
        if (!$forceUpdate && !is_null($this->cachedBalanceMap)) {
            return $this->cachedBalanceMap;
        }

        $model = $this->getModel();

        $recordedMap = $this->getTimeRecordedMap($includesNotApprovedLeave);

        $scheduledMap = [];
        $extraMap = [];
        $missingMap = [];

        if (count($userShiftDays) == 0) {
            if ($model->recordMethod == 'T') {
                /** @var UserShiftDayTime[] $userShiftDayTimes */
                $userShiftDayTimes = UserShiftDayTimeRepository::getMany([
                    'constraints' => [
                        ['UserShiftDayTime.type', '=', 'A'],
                        ['UserShift.user_id', '=', $model->timeSheet->payRunItem->user_id],
                        ['UserShiftDay.date', '=', $model->date->toDateString()],
                        [
                            [
                                ['IS NULL', 'UserShift.startDate', 'OR'],
                                ['UserShift.startDate', '<=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'UserShift.endDate', 'OR'],
                                ['UserShift.endDate', '>=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'Shift.startDate', 'OR'],
                                ['Shift.startDate', '<=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'Shift.endDate', 'OR'],
                                ['Shift.endDate', '>=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                    ],
                    'joins' => [
                        ['Shift', 'Shift.id', '=', 'UserShift.shift_id'],
                    ],
                    'order' => 'UserShiftDayTime.startDateTime',
                    'relations' => [
                        'userShiftDay',
                    ],
                    'returnType' => 'model',
                ]);

                foreach ($userShiftDayTimes as $userShiftDayTime) {
                    $userShiftDays[] = $userShiftDayTime->userShiftDay;
                }
            } else {
                /** @var UserShiftDay[] $userShiftDayTimes */
                $userShiftDays = UserShiftDayRepository::getMany([
                    'constraints' => [
                        ['UserShift.user_id', '=', $model->timeSheet->payRunItem->user_id],
                        ['UserShiftDay.date', '=', $model->date->toDateString()],
                        [
                            [
                                ['IS NULL', 'UserShift.startDate', 'OR'],
                                ['UserShift.startDate', '<=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'UserShift.endDate', 'OR'],
                                ['UserShift.endDate', '>=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'Shift.startDate', 'OR'],
                                ['Shift.startDate', '<=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'Shift.endDate', 'OR'],
                                ['Shift.endDate', '>=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                    ],
                    'joins' => [
                        ['Shift', 'Shift.id', '=', 'UserShift.shift_id'],
                    ],
                    'returnType' => 'model',
                ]);
            }
        }

        foreach ($userShiftDays as $userShiftDay) {
            if (($userShiftDay->isPublicHoliday || $userShiftDay->hasHolidays) && !$userShiftDay->doesWorkOnPublicHoliday) {
                continue;
            }

            $map = $userShiftDay->getTimeMap();
            if (is_object($map) && count($map) > 0) {
                foreach ($map as $item) {
                    $scheduledMap[] = $item;
                }
            }
        }

        /** @var StructTimeBlock[]|StructGlideDayTimeBlock[]|SupportCollection $scheduledMap */
        $scheduledMap = collect($scheduledMap);

        if ($model->isGlide && $model->glideDurationType != 'D') {
            //
            // Glide period
            //
            if ($model->timeSheet->recordMethod == 'T') {
                $totalRecorded = [];
                $totalExtra = [];
                $maxScheduled = [];
                $minScheduled = [];

                for ($s = 0; $s < count($scheduledMap); $s++) {
                    if (!isset($scheduled['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $maxScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] = Minute::new($scheduledMap[$s]->maxHours, 'hours')->toHours();
                    }
                    if (!isset($minScheduled['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $minScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] = Minute::new($scheduledMap[$s]->minHours, 'hours')->toHours();
                    }
                    if (!isset($totalRecorded['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $totalRecorded['us_' . $scheduledMap[$s]->userShiftDay_id] = 0;
                    }
                    if (!isset($totalExtra['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $totalExtra['us_' . $scheduledMap[$s]->userShiftDay_id] = 0;
                    }
                }

                for ($r = 0; $r < count($recordedMap); $r++) {
                    $recordedMap[$r]->data->remainingHours = $recordedMap[$r]->hours;
                    $recordedMap[$r]->data->extraBlocks = [];
                    $recordedMapPeriod = TimeCalculations::getValidPeriod($recordedMap[$r]->startDateTime, $recordedMap[$r]->endDateTime);

                    if (!isset($totalExtra['us_' . $recordedMap[$r]->userShiftDay_id])) {
                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] = 0;
                    }

                    if (
                        $recordedMap[$r]->data->isAlwaysExcessTimeOnGlide
                        || $scheduledMap->count() == 0
                        || $scheduledMap->where('maxHours', '>', 0)->where('isOn', '=', true)->where('userShiftDay_id', '=', $recordedMap[$r]->userShiftDay_id)->count() == 0
                    ) {
                        $obj = $recordedMap[$r]->copy();

                        $extraMap[] = $obj;
                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                    } else {
                        if (!isset($totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id])) {
                            $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] = 0;
                        }
                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] += $recordedMap[$r]->hours;

                        if (!$recordedMap[$r]->data->isAlwaysOrdinarySpanOnGlide) {
                            /** @var StructTimeBlock[]|StructGlideDayTimeBlock[]|SupportCollection $relatedScheduledMap */
                            $relatedScheduledMap = array_values($scheduledMap->where('userShiftDay_id', '=', $recordedMap[$r]->userShiftDay_id)->all());

                            for ($s = 0; $s < count($relatedScheduledMap); $s++) {
                                $relatedScheduledMapPeriod = TimeCalculations::getValidPeriod($relatedScheduledMap[$s]->startDateTime, $relatedScheduledMap[$s]->endDateTime);

                                if ($recordedMapPeriod->start->lt($relatedScheduledMapPeriod->start)) {
                                    $previousRelatedSchedulePeriod = $s == 0 ? null : TimeCalculations::getValidPeriod($relatedScheduledMap[$s - 1]->startDateTime, $relatedScheduledMap[$s - 1]->endDateTime);

                                    if (is_null($previousRelatedSchedulePeriod) || $previousRelatedSchedulePeriod->end->lt($recordedMapPeriod->start)) {
                                        $obj = new StructTimeRecordedBlock();

                                        $obj->timeSheetDayTime_id = $recordedMap[$r]->timeSheetDayTime_id;
                                        $obj->userShiftTimeType_id = $recordedMap[$r]->userShiftTimeType_id;
                                        $obj->userShiftDay_id = $recordedMap[$r]->userShiftDay_id;
                                        $obj->formattedCode = $recordedMap[$r]->formattedCode;
                                        $obj->comment = $recordedMap[$r]->comment;
                                        $obj->startDateTime = is_null($previousRelatedSchedulePeriod) || $recordedMapPeriod->start->gt($previousRelatedSchedulePeriod->end) ? $recordedMapPeriod->start->copy() : $previousRelatedSchedulePeriod->end->copy();
                                        $obj->endDateTime = $recordedMapPeriod->end->lt($relatedScheduledMapPeriod->start) ? $recordedMapPeriod->end->copy() : $relatedScheduledMapPeriod->start->copy();
                                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                        $obj->data = $this->getGeneralDataFromBlock($recordedMap[$r]);

                                        $extraMap[] = $obj;
                                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] -= $obj->hours;
                                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                                    }
                                }

                                if ($recordedMapPeriod->end->gt($relatedScheduledMapPeriod->end)) {
                                    $nextRelatedSchedulePeriod = $s == count($relatedScheduledMap) - 1
                                        ? null
                                        : TimeCalculations::getValidPeriod($relatedScheduledMap[$s + 1]->startDateTime, $relatedScheduledMap[$s + 1]->endDateTime);

                                    if (is_null($nextRelatedSchedulePeriod) || $nextRelatedSchedulePeriod->start->gt($recordedMapPeriod->end)) {
                                        $obj = new StructTimeRecordedBlock();

                                        $obj->timeSheetDayTime_id = $recordedMap[$r]->timeSheetDayTime_id;
                                        $obj->userShiftTimeType_id = $recordedMap[$r]->userShiftTimeType_id;
                                        $obj->userShiftDay_id = $recordedMap[$r]->userShiftDay_id;
                                        $obj->formattedCode = $recordedMap[$r]->formattedCode;
                                        $obj->comment = $recordedMap[$r]->comment;
                                        $obj->startDateTime = $recordedMapPeriod->start->gt($relatedScheduledMapPeriod->end) ? $recordedMapPeriod->start->copy() : $relatedScheduledMapPeriod->end->copy();
                                        $obj->endDateTime = is_null($nextRelatedSchedulePeriod) || $recordedMapPeriod->end->lt($nextRelatedSchedulePeriod->start) ? $recordedMapPeriod->end->copy() : $nextRelatedSchedulePeriod->start->copy();
                                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                        $obj->data = $this->getGeneralDataFromBlock($recordedMap[$r]);

                                        $extraMap[] = $obj;
                                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] -= $obj->hours;
                                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                                    }
                                }
                            }
                        }
                    }
                }

                foreach ($totalRecorded as $k => $v) {
                    if ($v > $maxScheduled[$k]) {
                        $hours = $v - $maxScheduled[$k];
                        $userShiftDay_id = intval(substr($k, 3));

                        /** @var StructTimeRecordedBlock[]|SupportCollection $relatedRecordedMap */
                        $relatedRecordedMap = $recordedMap->where('userShiftDay_id', '=', $userShiftDay_id)->all();

                        /** @var StructTimeRecordedBlock[] $map */
                        $map = [];
                        foreach ($relatedRecordedMap as $item) {
                            if (is_null($item->timeSheetDayTime_id)) {
                                continue;
                            }

                            $obj = new StructTimeRecordedBlock();

                            $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
                            $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
                            $obj->userShiftDay_id = $item->userShiftDay_id;
                            $obj->formattedCode = $item->formattedCode;
                            $obj->comment = $item->comment;
                            $obj->startDateTime = $item->startDateTime->copy();
                            if (isset($item->data->extraBlocks) && is_array($item->data->extraBlocks) && count($item->data->extraBlocks) > 0) {
                                foreach ($item->data->extraBlocks as $extraBlock) {
                                    $obj->endDateTime = $extraBlock->startDateTime->copy();
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $extraBlock->data;

                                    if ($obj->hours > 0) {
                                        $map[] = $obj;
                                    }

                                    $obj = new StructTimeRecordedBlock();
                                    $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $item->userShiftDay_id;
                                    $obj->formattedCode = $item->formattedCode;
                                    $obj->startDateTime = $extraBlock->endDateTime->copy();
                                }
                            }
                            $obj->endDateTime = $item->endDateTime->copy();
                            $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                            $obj->data = $item->data;

                            if (!$obj->startDateTime->equalTo($obj->endDateTime) && $obj->hours > 0) {
                                $map[] = $obj;
                            }
                        }

                        $r = count($map) - 1;
                        while (etime_gt($hours, 0, 2)) {
                            if (etime_gt($map[$r]->hours, 0, 2)) {
                                $minutesDiff = Minute::instance($hours, 'hours')->toMinutes();

                                if ($hours >= $map[$r]->hours) {
                                    $hours -= $map[$r]->hours;

                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                                    $obj->formattedCode = $map[$r]->formattedCode;
                                    $obj->comment = $map[$r]->comment;
                                    $obj->startDateTime = $map[$r]->startDateTime->copy();
                                    $obj->endDateTime = $map[$r]->endDateTime->copy();
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                                    $extraMap[] = $obj;
                                } else {
                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                                    $obj->formattedCode = $map[$r]->formattedCode;
                                    $obj->comment = $map[$r]->comment;
                                    $obj->endDateTime = $map[$r]->endDateTime->copy();
                                    if ($obj->endDateTime->isStartOfDay()) {
                                        $obj->startDateTime = $obj->endDateTime->copy()->addDay()->subMinutes($minutesDiff)->copy();
                                    } else {
                                        $obj->startDateTime = $obj->endDateTime->copy()->subMinutes($minutesDiff)->copy();
                                    }
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                                    $extraMap[] = $obj;

                                    $hours = 0;
                                }
                            }

                            $r--;

                            if ($r < 0) {
                                $this->reportLeaveExcessTime($hours, $map);

                                break;
                            }
                        }
                    }
                }

                foreach ($minScheduled as $k => $v) {
                    if (($v > 0 && !isset($totalRecorded[$k])) || $v > ($totalRecorded[$k] + $totalExtra[$k])) {
                        $hours = $v - (isset($totalRecorded[$k]) ? $totalRecorded[$k] + $totalExtra[$k] : 0);
                        $userShiftDay_id = intval(substr($k, 3));

                        $obj = new StructTimeBlock();

                        $obj->userShiftDay_id = $userShiftDay_id;
                        $obj->hours = $hours;

                        $missingMap[] = $obj;
                    }
                }
            } else {
                $totalScheduled = [];
                foreach ($scheduledMap as $scheduledItem) {
                    if (!isset($totalScheduled['us_' . $scheduledItem->userShiftDay_id])) {
                        $totalScheduled['us_' . $scheduledItem->userShiftDay_id] = $scheduledItem;
                    } else {
                        $totalScheduled['us_' . $scheduledItem->userShiftDay_id]->maxHours += $scheduledItem->maxHours;
                    }
                }

                $totalRecorded = [];
                foreach ($recordedMap as $recordedItem) {
                    if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id])) {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id] = [];
                    }
                    if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id])) {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id] = $recordedItem->copy();
                    } else {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id]->hours += $recordedItem->hours;
                    }
                }

                foreach ($totalScheduled as $sK => $sV) {
                    if (isset($totalRecorded[$sK])) {
                        $maxExpected = $sV->maxHours;
                        $minExpected = $sV->minHours;
                        $hoursRecorded = 0;
                        foreach ($totalRecorded[$sK] as /*$rK =>*/ $rV) {
                            if ($rV->data->isAlwaysExcessTimeOnGlide) {
                                $obj = new StructTimeRecordedBlock();

                                $obj->timeSheetDayTime_id = $rV->timeSheetDayTime_id;
                                $obj->userShiftTimeType_id = $rV->userShiftTimeType_id;
                                $obj->userShiftDay_id = $rV->userShiftDay_id;
                                $obj->formattedCode = $rV->formattedCode;
                                $obj->comment = $rV->comment;
                                $obj->hours = $rV->hours;
                                $obj->data = $this->getGeneralDataFromBlock($rV);

                                $extraMap[] = $obj;
                            } else {
                                if (etime_lte($rV->hours, $maxExpected, 2)) {
                                    $maxExpected -= etime_round($rV->hours, 2);
                                    if (etime_round($maxExpected, 2) <= 0) {
                                        $maxExpected = 0;
                                    }
                                } else {
                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $rV->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $rV->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $rV->userShiftDay_id;
                                    $obj->formattedCode = $rV->formattedCode;
                                    $obj->comment = $rV->comment;
                                    $obj->hours = $rV->hours - $maxExpected;
                                    $obj->data = $this->getGeneralDataFromBlock($rV);

                                    $extraMap[] = $obj;

                                    $maxExpected = 0;
                                }

                                $hoursRecorded += $rV->hours;
                            }
                        }

                        if (etime_gt($minExpected, $hoursRecorded, 2)) {
                            $obj = new StructTimeBlock();

                            $obj->userShiftDay_id = $sV->userShiftDay_id;
                            $obj->hours = $minExpected - $hoursRecorded;
                            $obj->data = $this->getGeneralDataFromBlock($sV);

                            $missingMap[] = $obj;
                        }
                    } else {
                        $obj = new StructTimeBlock();

                        $obj->userShiftDay_id = $sV->userShiftDay_id;
                        $obj->hours = $sV->minHours;
                        $obj->data = $this->getGeneralDataFromBlock($sV);

                        $missingMap[] = $obj;
                    }
                }
            }
        } else {
            //
            // Non-glide or Glide Day
            //
            if ($model->timeSheet->recordMethod == 'T') {
                $totalRecorded = [];
                $totalExtra = [];
                $maxScheduled = [];
                $minScheduled = [];

                for ($s = 0; $s < count($scheduledMap); $s++) {
                    if ($scheduledMap[$s]->data->doesHaveMaximumDailyHours) {
                        $maxScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] = $scheduledMap[$s]->data->maximumDailyHours;
                    } else {
                        if (!isset($maxScheduled['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                            $maxScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] = $scheduledMap[$s]->hours;
                        } else {
                            $maxScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] += $scheduledMap[$s]->hours;
                        }
                    }
                    if (!isset($minScheduled['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $minScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] = $scheduledMap[$s]->hours;
                    } else {
                        $minScheduled['us_' . $scheduledMap[$s]->userShiftDay_id] += $scheduledMap[$s]->hours;
                    }
                    if (!isset($totalRecorded['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $totalRecorded['us_' . $scheduledMap[$s]->userShiftDay_id] = 0;
                    } else {
                        $totalRecorded['us_' . $scheduledMap[$s]->userShiftDay_id] += 0;
                    }
                    if (!isset($totalExtra['us_' . $scheduledMap[$s]->userShiftDay_id])) {
                        $totalExtra['us_' . $scheduledMap[$s]->userShiftDay_id] = 0;
                    } else {
                        $totalExtra['us_' . $scheduledMap[$s]->userShiftDay_id] += 0;
                    }
                }

                for ($r = 0; $r < count($recordedMap); $r++) {
                    $recordedMap[$r]->data->remainingHours = $recordedMap[$r]->hours;
                    $recordedMap[$r]->data->extraBlocks = [];
                    $recordedMapPeriod = TimeCalculations::getValidPeriod($recordedMap[$r]->startDateTime, $recordedMap[$r]->endDateTime);

                    if (!isset($totalExtra['us_' . $recordedMap[$r]->userShiftDay_id])) {
                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] = 0;
                    }

                    if (
                        (
                            $model->isGlide
                            && $recordedMap[$r]->data->isAlwaysExcessTimeOnGlide
                        )
                        || $scheduledMap->count() == 0
                        || $scheduledMap->where('hours', '>', 0)->where('userShiftDay_id', '=', $recordedMap[$r]->userShiftDay_id)->count() == 0
                    ) {
                        $obj = $recordedMap[$r]->copy();

                        $extraMap[] = $obj;
                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                    } else {
                        if (!isset($totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id])) {
                            $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] = 0;
                        }
                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] += $recordedMap[$r]->hours;

                        if (!$model->isGlide || !$recordedMap[$r]->data->isAlwaysOrdinarySpanOnGlide) {
                            /** @var StructTimeBlock[]|SupportCollection $relatedScheduledMap */
                            $relatedScheduledMap = $scheduledMap->where('userShiftDay_id', '=', $recordedMap[$r]->userShiftDay_id)->values();

                            for ($s = 0; $s < count($relatedScheduledMap); $s++) {
                                $relatedScheduledMapPeriod = TimeCalculations::getValidPeriod($relatedScheduledMap[$s]->startDateTime, $relatedScheduledMap[$s]->endDateTime);

                                if ($recordedMapPeriod->start->lt($relatedScheduledMapPeriod->start)) {
                                    $previousRelatedSchedulePeriod = $s == 0 ? null : TimeCalculations::getValidPeriod($relatedScheduledMap[$s - 1]->startDateTime, $relatedScheduledMap[$s - 1]->endDateTime);

                                    if (is_null($previousRelatedSchedulePeriod) || $previousRelatedSchedulePeriod->end->lt($recordedMapPeriod->start)) {
                                        $obj = new StructTimeRecordedBlock();

                                        $obj->timeSheetDayTime_id = $recordedMap[$r]->timeSheetDayTime_id;
                                        $obj->userShiftTimeType_id = $recordedMap[$r]->userShiftTimeType_id;
                                        $obj->userShiftDay_id = $recordedMap[$r]->userShiftDay_id;
                                        $obj->formattedCode = $recordedMap[$r]->formattedCode;
                                        $obj->comment = $recordedMap[$r]->comment;
                                        $obj->startDateTime = is_null($previousRelatedSchedulePeriod) || $recordedMapPeriod->start->gt($previousRelatedSchedulePeriod->end) ? $recordedMapPeriod->start->copy() : $previousRelatedSchedulePeriod->end->copy();
                                        $obj->endDateTime = $recordedMapPeriod->end->lt($relatedScheduledMapPeriod->start) ? $recordedMapPeriod->end->copy() : $relatedScheduledMapPeriod->start->copy();
                                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                        $obj->data = $this->getGeneralDataFromBlock($recordedMap[$r]);

                                        $extraMap[] = $obj;
                                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] -= $obj->hours;
                                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                                    }
                                }

                                if ($recordedMapPeriod->end->gt($relatedScheduledMapPeriod->end)) {
                                    $nextRelatedSchedulePeriod = $s == count($relatedScheduledMap) - 1
                                        ? null
                                        : TimeCalculations::getValidPeriod($relatedScheduledMap[$s + 1]->startDateTime, $relatedScheduledMap[$s + 1]->endDateTime);

                                    if (is_null($nextRelatedSchedulePeriod) || $nextRelatedSchedulePeriod->start->gt($recordedMapPeriod->end)) {
                                        $obj = new StructTimeRecordedBlock();

                                        $obj->timeSheetDayTime_id = $recordedMap[$r]->timeSheetDayTime_id;
                                        $obj->userShiftTimeType_id = $recordedMap[$r]->userShiftTimeType_id;
                                        $obj->userShiftDay_id = $recordedMap[$r]->userShiftDay_id;
                                        $obj->formattedCode = $recordedMap[$r]->formattedCode;
                                        $obj->comment = $recordedMap[$r]->comment;
                                        $obj->startDateTime = $recordedMapPeriod->start->gt($relatedScheduledMapPeriod->end) ? $recordedMapPeriod->start->copy() : $relatedScheduledMapPeriod->end->copy();
                                        $obj->endDateTime = is_null($nextRelatedSchedulePeriod) || $recordedMapPeriod->end->lt($nextRelatedSchedulePeriod->start) ? $recordedMapPeriod->end->copy() : $nextRelatedSchedulePeriod->start->copy();
                                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                        $obj->data = $this->getGeneralDataFromBlock($recordedMap[$r]);

                                        $extraMap[] = $obj;
                                        $recordedMap[$r]->data->extraBlocks[] = $obj;
                                        $totalRecorded['us_' . $recordedMap[$r]->userShiftDay_id] -= $obj->hours;
                                        $totalExtra['us_' . $recordedMap[$r]->userShiftDay_id] += $obj->hours;
                                        $recordedMap[$r]->data->remainingHours -= $obj->hours;
                                    }
                                }
                            }
                        }
                    }
                }

                foreach ($totalRecorded as $k => $v) {
                    if (etime_gt($v, $maxScheduled[$k], 2)) {
                        $hours = $v - $maxScheduled[$k];
                        $userShiftDay_id = intval(substr($k, 3));

                        /** @var StructTimeRecordedBlock[]|SupportCollection $relatedRecordedMap */
                        $relatedRecordedMap = $recordedMap->where('userShiftDay_id', '=', $userShiftDay_id)->all();

                        /** @var StructTimeRecordedBlock[] $map */
                        $map = [];
                        foreach ($relatedRecordedMap as $item) {
                            if (is_null($item->timeSheetDayTime_id)) {
                                continue;
                            }

                            $obj = new StructTimeRecordedBlock();

                            $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
                            $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
                            $obj->userShiftDay_id = $item->userShiftDay_id;
                            $obj->formattedCode = $item->formattedCode;
                            $obj->comment = $item->comment;
                            $obj->startDateTime = $item->startDateTime->copy();
                            if (isset($item->data->extraBlocks) && is_array($item->data->extraBlocks) && count($item->data->extraBlocks) > 0) {
                                foreach ($item->data->extraBlocks as $extraBlock) {
                                    $obj->endDateTime = $extraBlock->startDateTime->copy();
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $extraBlock->data;

                                    if ($obj->hours > 0) {
                                        $map[] = $obj;
                                    }

                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $item->userShiftDay_id;
                                    $obj->formattedCode = $item->formattedCode;
                                    $obj->startDateTime = $extraBlock->endDateTime->copy();
                                }
                            }
                            $obj->endDateTime = $item->endDateTime->copy();
                            $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                            $obj->data = $item->data;

                            if (!$obj->startDateTime->equalTo($obj->endDateTime) && $obj->hours > 0) {
                                $map[] = $obj;
                            }
                        }

                        $r = count($map) - 1;
                        while (etime_gt($hours, 0, 2)) {
                            if ($r < 0) {
                                $this->reportLeaveExcessTime($hours, $map);

                                break;
                            }

                            $minutesDiff = Minute::instance($hours, 'hours')->toMinutes();

                            if (etime_gt($map[$r]->hours, 0, 2)) {
                                if ($hours >= $map[$r]->hours) {
                                    $hours -= $map[$r]->hours;

                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                                    $obj->formattedCode = $map[$r]->formattedCode;
                                    $obj->comment = $map[$r]->comment;
                                    $obj->startDateTime = $map[$r]->startDateTime->copy();
                                    $obj->endDateTime = $map[$r]->endDateTime->copy();
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                                    $extraMap[] = $obj;
                                } else {
                                    $obj = new StructTimeRecordedBlock();


                                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                                    $obj->formattedCode = $map[$r]->formattedCode;
                                    $obj->comment = $map[$r]->comment;
                                    $obj->endDateTime = $map[$r]->endDateTime->copy();
                                    if ($obj->endDateTime->isStartOfDay()) {
                                        $obj->startDateTime = $obj->endDateTime->copy()->addDay()->subMinutes($minutesDiff)->copy();
                                    } else {
                                        $obj->startDateTime = $obj->endDateTime->copy()->subMinutes($minutesDiff)->copy();
                                    }
                                    $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                                    $extraMap[] = $obj;

                                    $hours = 0;
                                }
                            }

                            $r--;
                        }
                    }
                }

                if ($model->isGlide) {
                    $totalScheduled = [];
                    foreach ($scheduledMap as $scheduledItem) {
                        if (!isset($totalScheduled['us_' . $scheduledItem->userShiftDay_id])) {
                            $totalScheduled['us_' . $scheduledItem->userShiftDay_id] = $scheduledItem;
                        } else {
                            $totalScheduled['us_' . $scheduledItem->userShiftDay_id]->hours += $scheduledItem->hours;
                        }
                    }

                    $totalRecorded = [];
                    foreach ($recordedMap as $recordedItem) {
                        if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id])) {
                            $totalRecorded['us_' . $recordedItem->userShiftDay_id] = [];
                        }
                        if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id])) {
                            $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id] = $recordedItem->copy();
                        } else {
                            $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id]->hours += $recordedItem->hours;
                        }
                    }

                    foreach ($totalScheduled as $sK => $sV) {
                        if (isset($totalRecorded[$sK])) {
                            $totalExpected = $sV->hours;
                            foreach ($totalRecorded[$sK] as /*$rK =>*/ $rV) {
                                $totalExpected -= $rV->hours;
                            }

                            if ($totalExpected > 0) {
                                $obj = new StructTimeBlock();

                                $obj->userShiftDay_id = $sV->userShiftDay_id;
                                $obj->hours = $totalExpected;
                                $obj->data = $this->getGeneralDataFromBlock($sV);

                                $missingMap[] = $obj;
                            }
                        }
                    }
                } else {
                    for ($s = 0; $s < count($scheduledMap); $s++) {
                        if ($scheduledMap[$s]->hours > 0) {
                            if ($recordedMap->count() == 0 || $recordedMap->where('userShiftDay_id', '=', $scheduledMap[$s]->userShiftDay_id)->count() == 0) {
                                $missingMap[] = $scheduledMap[$s];
                            } else {
                                /** @var StructTimeRecordedBlock[]|SupportCollection $relatedRecordedMap */
                                $relatedRecordedMap = array_values($recordedMap->where('userShiftDay_id', '=', $scheduledMap[$s]->userShiftDay_id)->all());

                                for ($r = 0; $r < count($relatedRecordedMap); $r++) {
                                    if ($scheduledMap[$s]->startDateTime->lt($relatedRecordedMap[$r]->startDateTime)) {
                                        if ($r == 0 || $relatedRecordedMap[$r - 1]->endDateTime->lt($scheduledMap[$s]->endDateTime)) {
                                            $obj = new StructTimeBlock();

                                            $obj->userShiftDay_id = $scheduledMap[$s]->userShiftDay_id;

                                            if ($r == 0) {
                                                $obj->startDateTime = $scheduledMap[$s]->startDateTime->copy();
                                            } else {
                                                $obj->startDateTime = $relatedRecordedMap[$r - 1]->endDateTime->copy();
                                            }

                                            if ($scheduledMap[$s]->endDateTime->lt($relatedRecordedMap[$r]->startDateTime)) {
                                                $obj->endDateTime = $scheduledMap[$s]->endDateTime->copy();
                                            } else {
                                                $obj->endDateTime = $relatedRecordedMap[$r]->startDateTime->copy();
                                            }

                                            $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                            $obj->data = $this->getGeneralDataFromBlock($scheduledMap[$s]);

                                            if ($obj->hours > 0) {
                                                $missingMap[] = $obj;
                                            }
                                        }
                                    }

                                    if ($scheduledMap[$s]->endDateTime->gt($relatedRecordedMap[$r]->endDateTime) && $r == count($relatedRecordedMap) - 1) {
                                        $obj = new StructTimeBlock();

                                        $obj->userShiftDay_id = $scheduledMap[$s]->userShiftDay_id;

                                        if ($scheduledMap[$s]->startDateTime->gt($relatedRecordedMap[$r]->endDateTime)) {
                                            $obj->startDateTime = $scheduledMap[$s]->startDateTime->copy();
                                        } else {
                                            $obj->startDateTime = $relatedRecordedMap[$r]->endDateTime->copy();
                                        }
                                        $obj->endDateTime = $scheduledMap[$s]->endDateTime->copy();

                                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                                        $obj->data = $this->getGeneralDataFromBlock($scheduledMap[$s]);

                                        if ($obj->hours > 0) {
                                            $missingMap[] = $obj;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                $totalScheduled = [];
                foreach ($scheduledMap as $scheduledItem) {
                    if (!isset($totalScheduled['us_' . $scheduledItem->userShiftDay_id])) {
                        $totalScheduled['us_' . $scheduledItem->userShiftDay_id] = $scheduledItem;
                    } else {
                        $totalScheduled['us_' . $scheduledItem->userShiftDay_id]->hours += $scheduledItem->hours;
                    }
                }

                $totalRecorded = [];
                foreach ($recordedMap as $recordedItem) {
                    if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id])) {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id] = [];
                    }
                    if (!isset($totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id])) {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id] = $recordedItem->copy();
                    } else {
                        $totalRecorded['us_' . $recordedItem->userShiftDay_id]['id_' . $recordedItem->userShiftTimeType_id]->hours += $recordedItem->hours;
                    }
                }

                foreach ($totalScheduled as $sK => $sV) {
                    if (isset($totalRecorded[$sK])) {
                        $totalExpected = etime_round($sV->hours, 2);
                        foreach ($totalRecorded[$sK] as /*$rK =>*/ $rV) {
                            if ($rV->data->isAlwaysExcessTimeOnGlide) {
                                $obj = new StructTimeRecordedBlock();

                                $obj->timeSheetDayTime_id = $rV->timeSheetDayTime_id;
                                $obj->userShiftTimeType_id = $rV->userShiftTimeType_id;
                                $obj->userShiftDay_id = $rV->userShiftDay_id;
                                $obj->formattedCode = $rV->formattedCode;
                                $obj->comment = $rV->comment;
                                $obj->hours = $rV->hours;
                                $obj->data = $this->getGeneralDataFromBlock($rV);

                                $extraMap[] = $obj;
                            } else {
                                if (etime_lte($rV->hours, $totalExpected, 2)) {
                                    $totalExpected -= etime_round($rV->hours, 2);
                                    if (etime_round($totalExpected, 2) <= 0) {
                                        $totalExpected = 0;
                                    }
                                } else {
                                    $obj = new StructTimeRecordedBlock();

                                    $obj->timeSheetDayTime_id = $rV->timeSheetDayTime_id;
                                    $obj->userShiftTimeType_id = $rV->userShiftTimeType_id;
                                    $obj->userShiftDay_id = $rV->userShiftDay_id;
                                    $obj->formattedCode = $rV->formattedCode;
                                    $obj->comment = $rV->comment;
                                    $obj->hours = $rV->hours - $totalExpected;
                                    $obj->data = $this->getGeneralDataFromBlock($rV);

                                    $extraMap[] = $obj;

                                    $totalExpected = 0;
                                }
                            }
                        }

                        if (etime_gt($totalExpected, 0, 2)) {
                            $obj = new StructTimeBlock();

                            $obj->userShiftDay_id = $sV->userShiftDay_id;
                            $obj->hours = $totalExpected;
                            $obj->data = $sV->data;

                            $missingMap[] = $obj;
                        }
                    }
                }
            }
        }

        $extraMap = collect($extraMap);
        $extraMap = $extraMap->sortBy('startDateTime');

        $missingMap = collect($missingMap);

        $this->addExcessTimeForMaximumDailyHours($recordedMap, $extraMap);

        foreach ($recordedMap as $recordedItem) {
            if (isset($recordedItem->data->remainingHours)) {
                unset($recordedItem->data->remainingHours);
            }
            if (isset($recordedItem->data->extraBlocks)) {
                unset($recordedItem->data->extraBlocks);
            }
        }

        foreach ($extraMap as $extraItem) {
            if (isset($extraItem->data->remainingHours)) {
                unset($extraItem->data->remainingHours);
            }
            if (isset($extraItem->data->extraBlocks)) {
                unset($extraItem->data->extraBlocks);
            }
        }

        /** @var \Illuminate\Support\Collection|StructTimeBalance $ret */
        $ret = new StructTimeBalance();
        $ret->timeSheetDay_id = $model->id;
        $ret->date = $model->date;
        $ret->scheduled = $scheduledMap;
        $ret->recorded = $recordedMap;
        $ret->extra = $extraMap;
        $ret->missing = $missingMap;

        $this->cachedBalanceMap = $ret;

        return $this->cachedBalanceMap;
    }

    /**
     * @param StructTimeRecordedBlock|StructTimeBlock $block
     */
    protected function getGeneralDataFromBlock($block):\stdClass
    {
        $ret = new \stdClass();

        if (!isset($block->data) || !$block->data) {
            return $ret;
        }

        $data = $block->data;

        if (isset($data->isAlwaysOrdinarySpanOnGlide)) {
            $ret->isAlwaysOrdinarySpanOnGlide = $data->isAlwaysOrdinarySpanOnGlide;
        }

        if (isset($data->isAlwaysExcessTimeOnGlide)) {
            $ret->isAlwaysExcessTimeOnGlide = $data->isAlwaysExcessTimeOnGlide;
        }

//        if (isset($data->remainingHours)) {
//            $ret->remainingHours = $data->remainingHours;
//        }

        if (isset($data->glideRequireTimeBlock)) {
            $ret->glideRequireTimeBlock = $data->glideRequireTimeBlock;
        }

        if (isset($data->id)) {
            $ret->id = $data->id;
        }

        if (isset($data->date)) {
            $ret->date = $data->date;
        }

        if (isset($data->glideRequireTimeBlock)) {
            $ret->glideRequireTimeBlock = $data->glideRequireTimeBlock;
        }

        if (isset($data->isOn)) {
            $ret->isOn = $data->isOn;
        }

        if (isset($data->glideDayHoursAverage)) {
            $ret->glideDayHoursAverage = $data->glideDayHoursAverage;
        }

        if (isset($data->glideDayMinHours)) {
            $ret->glideDayMinHours = $data->glideDayMinHours;
        }

        if (isset($data->glideDayMaxHours)) {
            $ret->glideDayMaxHours = $data->glideDayMaxHours;
        }

        if (isset($data->isGlideFirstDay)) {
            $ret->isGlideFirstDay = $data->isGlideFirstDay;
        }

        if (isset($data->isGlideLastDay)) {
            $ret->isGlideLastDay = $data->isGlideLastDay;
        }

        return $ret;
    }

    /**
     * @param SupportCollection|StructTimeRecordedBlock[] $recordedMap
     * @param SupportCollection|StructTimeRecordedBlock[] $extraMap
     */
    protected function addExcessTimeForMaximumDailyHours(Collection $recordedMap, Collection $extraMap)
    {
        $model = $this->getModel();
        $maxAllowed = $model->timeSheet->payRunItem->user->maximumHoursPerDay;

        if ($maxAllowed >= 24) {
            return ;
        }

        $currentTotalOrdinary = 0;
        foreach ($recordedMap as $recodedItem) {
            $currentTotalOrdinary += $recodedItem->hours;
        }

        foreach ($extraMap as $extraItem) {
            $currentTotalOrdinary -= $extraItem->hours;
        }

        if ($currentTotalOrdinary <= $maxAllowed) {
            return ;
        }

        /** @var StructTimeRecordedBlock[] $map */
        $map = [];
        foreach ($recordedMap as $item) {
            if (is_null($item->timeSheetDayTime_id)) {
                continue;
            }

            $obj = new StructTimeRecordedBlock();

            $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
            $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
            $obj->userShiftDay_id = $item->userShiftDay_id;
            $obj->formattedCode = $item->formattedCode;
            $obj->comment = $item->comment;
            if ($model->timeSheet->recordMethod == 'T') {
                $obj->startDateTime = $item->startDateTime->copy();
            }
            if (isset($item->data->extraBlocks) && is_array($item->data->extraBlocks) && count($item->data->extraBlocks) > 0) {
                foreach ($item->data->extraBlocks as $extraBlock) {
                    if ($model->timeSheet->recordMethod == 'T') {
                        $obj->endDateTime = $extraBlock->startDateTime->copy();
                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                    } else {
                        $obj->hours = $item->hours;
                    }
                    $obj->data = $extraBlock->data;

                    if ($obj->hours > 0) {
                        $map[] = $obj;
                    }

                    $obj = new StructTimeRecordedBlock();
                    $obj->timeSheetDayTime_id = $item->timeSheetDayTime_id;
                    $obj->userShiftTimeType_id = $item->userShiftTimeType_id;
                    $obj->userShiftDay_id = $item->userShiftDay_id;
                    $obj->formattedCode = $item->formattedCode;
                    if ($model->timeSheet->recordMethod == 'T') {
                        $obj->startDateTime = $extraBlock->endDateTime->copy();
                    }
                }
            }
            if ($model->timeSheet->recordMethod == 'T') {
                $obj->endDateTime = $item->endDateTime->copy();
                $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
            } else {
                $obj->hours = $item->hours;
            }
            $obj->data = $item->data;

            if ($obj->hours > 0) {
                $map[] = $obj;
            }
        }

        $extraHours = $currentTotalOrdinary - $maxAllowed;
        $r = count($map) - 1;

        while ($extraHours > 0) {
            if ($r < 0) {
                $this->reportLeaveExcessTime($extraHours);
                break;
            }

            $minutesDiff = Minute::instance($extraHours, 'hours')->toMinutes();

            if ($map[$r]->hours > 0) {
                if ($extraHours >= $map[$r]->hours) {
                    $extraHours -= $map[$r]->hours;

                    $obj = new StructTimeRecordedBlock();

                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                    $obj->formattedCode = $map[$r]->formattedCode;
                    $obj->comment = $map[$r]->comment;
                    if ($model->timeSheet->recordMethod == 'T') {
                        $obj->startDateTime = $map[$r]->startDateTime->copy();
                        $obj->endDateTime = $map[$r]->endDateTime->copy();
                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                    } else {
                        $obj->hours = $map[$r]->hours;
                    }
                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                    $extraMap->push($obj);
                } else {
                    $obj = new StructTimeRecordedBlock();

                    $obj->timeSheetDayTime_id = $map[$r]->timeSheetDayTime_id;
                    $obj->userShiftTimeType_id = $map[$r]->userShiftTimeType_id;
                    $obj->userShiftDay_id = $map[$r]->userShiftDay_id;
                    $obj->formattedCode = $map[$r]->formattedCode;
                    $obj->comment = $map[$r]->comment;
                    if ($model->timeSheet->recordMethod == 'T') {
                        $obj->endDateTime = $map[$r]->endDateTime->copy();
                        if ($obj->endDateTime->isStartOfDay()) {
                            $obj->startDateTime = $obj->endDateTime->copy()->addDay()->subMinutes($minutesDiff)->copy();
                        } else {
                            $obj->startDateTime = $obj->endDateTime->copy()->subMinutes($minutesDiff)->copy();
                        }
                        $obj->hours = TimeCalculations::getDiffInMinutes($obj->startDateTime, $obj->endDateTime) / 60;
                    } else {
                        $obj->hours = $extraHours;
                    }
                    $obj->data = $this->getGeneralDataFromBlock($map[$r]);

                    $extraMap->push($obj);

                    $extraHours = 0;
                }
            }

            $r--;
        }
    }

    protected function reportLeaveExcessTime($hours, $map = null, $more = null)
    {
        $model = $this->getModel();

        report(new ElementTimeException('Leave is triggering daily excess-time, please check', [
            'timeSheetDay_id' => $model->id,
            'timeSheet_id' => $model->timeSheet_id,
            'date' => $model->date->format('Y-m-d'),
            'user' => [
                'id' => $model->timeSheet->user->id,
                'name' => $model->timeSheet->user->fullName,
            ],
            'extraHours' => $hours,
            'map' => $map,
            'more' => $more,
        ]));
    }

    /**
     * @throws ExcessTimeAlreadyCalculatedException
     * @throws InvalidArgumentException
     * @throws InvalidValueException
     * @throws \Element\Core\Exceptions\InvalidPropertyException
     * @throws \Throwable
     */
    public function updateExcessTime(array $options = [], array $broadcasts = []):self
    {
        $model = $this->getModel();

        if (!$model->timeSheet->canBeUpdated) {
            return $this;
        }

        $doesPreventWorkflowEvents = $options['forcePreventEvent'] ?? false;
        $doesForceRecalculation = $options['forceRecalculation'] ?? false;

        try {
            $model->getConnection()->transaction(function () use ($model, $options, $broadcasts, $doesPreventWorkflowEvents, $doesForceRecalculation) {
                $model->load('timeSheetDayTimes.breaks');

                $balanceMap = $this->getBalanceTimeMap();
                $extraMap = $balanceMap->extra;

                /** @var Collection|TimeSheetExcessTime[] $excessTimes */
                $excessTimes = TimeSheetExcessTimeRepository::getMany([
                    'constraints' => [
                        ['TimeSheetDay.id', '=', $model->id],
                    ],
                    'relations' => [
                        'blocks',
                        'items.rules',
                    ],
                    'returnType' => 'model',
                ]);

                $previousExcessTimes = [];
                $previousBlocks = [];
                foreach ($excessTimes as $excessTime) {
                    $data = new \stdClass();
                    $data->timeSheetDayTime_id = $excessTime->timeSheetDayTime_id;
                    $data->comments = $excessTime->comments;
                    $data->items = [];
                    foreach ($excessTime->items as $item) {
                        $dataItem = new \stdClass();
                        $dataItem->rules = [];
                        $dataItem->hours = 0;
                        foreach ($item->rules as $rule) {
                            $dataRule = new \stdClass();
                            $dataRule->rule_id = $rule->excessTimeGroupSectionLevelRule_id;
                            $dataRule->comments = $rule->comments;
                            $dataItem->hours += $rule->hours;
                            $dataItem->rules[] = $dataRule;
                        }
                        $data->items[] = $dataItem;
                    }
                    foreach ($excessTime->blocks as $block) {
                        $dataBlock = new \stdClass();
                        $dataBlock->startDateTime = $block->startDateTime;
                        $dataBlock->endDateTime = $block->endDateTime;
                        $dataBlock->hours = $block->hours;
                        $previousBlocks[] = $dataBlock;
                    }
                    $previousExcessTimes['t_' . $excessTime->timeSheetDayTime_id] = $data;
                    $excessTime->forceDelete();
                }

                $extrasToAdd = [];
                foreach ($extraMap as $item) {
                    if (!isset($extrasToAdd['id_' . $item->timeSheetDayTime_id])) {
                        $extrasToAdd['id_' . $item->timeSheetDayTime_id] = [];
                    }
                    $extrasToAdd['id_' . $item->timeSheetDayTime_id][] = $item;
                }

                if (count($extrasToAdd) > 0) {
                    $blocks = collect([]);

                    foreach ($extrasToAdd as $k => $v) {
                        $hourlyRate = null;
                        $hasHolidays = false;

                        foreach ($v as $item) {
                            /** @var UserShiftDay $userShiftDay */
                            $userShiftDay = UserShiftDayRepository::getOne([
                                'constraints' => [
                                    ['UserShiftDay.id', '=', $item->userShiftDay_id],
                                ],
                                'returnType' => 'model',
                            ]);

                            $hasHolidays = $userShiftDay->hasHolidays;

                            if (!is_null($userShiftDay)) {
                                $hourlyRate = $userShiftDay->hourlyRateOvertime;
                                break;
                            }
                        }

                        $timeSheetDayTime_id = intval(substr($k, 3));

                        if (!$timeSheetDayTime_id) {
                            continue;
                        }

                        $new = new TimeSheetExcessTime;

                        if ($model->hasPublicHoliday || $hasHolidays) {
                            $new->type = 'H';
                        } elseif ($model->calendarDate->isSunday) {
                            $new->type = 'U';
                        } elseif ($model->calendarDate->isSaturday) {
                            $new->type = 'S';
                        } else {
                            $new->type = 'W';
                        }

                        $new->timeSheetDayTime_id = $timeSheetDayTime_id;
                        $new->date = $model->date;
                        $new->calendarDate_id = $model->calendarDate_id;
                        $new->hourlyRate = $hourlyRate;

                        if (
                            isset($previousExcessTimes['t_' . $timeSheetDayTime_id]->comments)
                            && strlen($previousExcessTimes['t_' . $timeSheetDayTime_id]->comments) > 0
                        ) {
                            $new->comments = $previousExcessTimes['t_' . $timeSheetDayTime_id]->comments;
                        } else {
                            foreach ($v as $item) {
                                if (isset($item->comment) && $item->comment && strlen($item->comment) > 0) {
                                    $new->comments = $item->comment;
                                }
                            }
                        }

                        $new->saveOrFail([
                            'preventDayJob' => true,
                            'preventEvent' => true,
                        ]);

                        $totalHours = 0;
                        foreach ($v as $item) {
                            $newBlock = new TimeSheetExcessTimeBlock;
                            $newBlock->timeSheetExcessTime_id = $new->id;
                            $newBlock->startDateTime = $item->startDateTime;
                            $newBlock->endDateTime = $item->endDateTime;
                            $newBlock->hours = $item->hours;
                            $newBlock->saveOrFail();

                            $blocks->push($newBlock);
                            $totalHours += $newBlock->hours;
                        }

                        $new->totalHours = $totalHours;

                        $new->saveOrFail([
                            'preventDayJob' => true,
                            'preventEvent' => true,
                        ]);
                    }

                    /** @var TimeSheetExcessTimeBlock[] $previousBlocks */
                    /** @var TimeSheetExcessTimeBlock[] $blocks */
                    if (!$doesForceRecalculation && count($blocks) == count($previousBlocks)) {
                        $allFound = true;
                        foreach ($previousBlocks as $previousBlock) {
                            $found = false;
                            foreach ($blocks as $block) {
                                if (
                                    (
                                        (
                                            is_null($previousBlock->startDateTime)
                                            && is_null($block->startDateTime)
                                        )
                                        || $previousBlock->startDateTime->eq($block->startDateTime)
                                    )
                                    && (
                                        (
                                            is_null($previousBlock->endDateTime)
                                            && is_null($block->endDateTime)
                                        )
                                        || $previousBlock->endDateTime->eq($block->endDateTime)
                                    )
                                    && $previousBlock->hours == $block->hours
                                ) {
                                    $found = true;
                                    break;
                                }
                            }

                            if (!$found) {
                                $allFound = false;
                                break;
                            }
                        }

                        if ($allFound) {
                            throw new ExcessTimeAlreadyCalculatedException($model->date->toDateString());
                        }
                    }

                    TimeSheetExcessTimeRepository::assignDailyRules($blocks, $previousExcessTimes, $doesPreventWorkflowEvents);
                }

                $this->calculatePenalties();

                try {
                    $this->updatePeriodExcessTime($doesPreventWorkflowEvents, $doesForceRecalculation);
                } catch (\Throwable $e) {
                    if (!$e instanceof ExcessTimeAlreadyCalculatedException) {
                        throw $e;
                    }
                }

                $model->saveOrFail($options);

                $model->timeSheet->repository->updateIssues([
                    ExcessTimeRuleNotAssignedType::class,
                    ExcessTimeNotCommentedType::class,
                    ExcessTimeNotApprovedType::class,
                ]);

                return true;
            });

            $this->calculatePenalties(forExcessTimeCalculations: true);

            if (count($broadcasts) > 0) {
                $model->timeSheet->sendBroadcast($broadcasts);

                if ($options['forcePreventEvent']) {
                    if ($model->isGlide && $model->glideDurationType != 'D' && !$model->isFirstGlideDay) {
                        $days = $model->getManyGlideSiblings(['timeSheetDayTimes.excessTimes']);
                        TimeCardTotalsUpdatedBroadcastEvent::fire($days->first());
                    }

                    TimeCardTotalsUpdatedBroadcastEvent::fire($model);
                }
            }
        } catch (\Throwable $e) {
            if (!$e instanceof ExcessTimeAlreadyCalculatedException) {
                throw $e;
            }

            $this->calculatePenalties();

            try {
                $this->updatePeriodExcessTime($doesPreventWorkflowEvents, $doesForceRecalculation);
            } catch (\Throwable $e) {
                if (!$e instanceof ExcessTimeAlreadyCalculatedException) {
                    throw $e;
                }
            }

            $this->calculatePenalties(forExcessTimeCalculations: true);
        }

        return $this;
    }

    /**
     * @param Collection|TimeSheetDay[] $days
     */
    public static function calculatePeriodWorkDetails(float $excessHours, Collection|array $days): \stdClass
    {
        $remainingForWorks = $excessHours;

        $workDetails = new \stdClass();
        $workDetails->totalWorked = 0;
        $workDetails->items = [];

        /** @var Collection|TimeSheetDay[] $checkDays */
        $checkDays = $days->reverse();

        $total = 0;

        foreach ($checkDays as $day) {
            if ($remainingForWorks == 0) {
                break;
            }

            foreach ($day->timeSheetDayTimes as $dayTime) {
                if (!$dayTime->recordTypeClass::IS_WORK) {
                    continue;
                }

                if ($remainingForWorks == 0) {
                    break;
                }

                $hoursMaster = $dayTime->hours;

                /** @var Collection|TimeSheetDayTimeWork[] $works */
                $works = collect([]);

                foreach ($dayTime->works as $work) {
                    $hoursMaster -= $work->hours;
                    $works->prepend($work);
                }

                if ($hoursMaster > 0) {
                    $totalAlreadyUsed = 0;
                    foreach ($dayTime->excessTimes as $excessTime) {
                        foreach ($excessTime->activeItem->rules as $rule) {
                            foreach ($rule->works as $excessWork) {
                                if (is_null($excessWork->timeSheetDayTimeWork_id)) {
                                    $totalAlreadyUsed += $excessWork->originalHours;
                                }
                            }
                        }
                    }

                    foreach ($dayTime->penalties as $penalty) {
                        if (!$penalty->userRolePenaltyRule->penaltyRule->calculationMethodClass::DOES_INCLUDE_EXCESS_TIME) {
                            foreach ($penalty->activeCalculation->items as $item) {
                                if (is_null($item->timeSheetDayTimeWork_id)) {
                                    $totalAlreadyUsed += $item->minutes->toHours();
                                }
                            }
                        }
                    }

                    $hoursMaster -= $totalAlreadyUsed;

                    if ($hoursMaster > 0) {
                        $newItem = new \stdClass();
                        $newItem->timeSheetDayTimeWork_id = null;
                        $newItem->timeSheetDayTimeWork = null;
                        $newItem->timeSheetDayTime_id = $dayTime->id;
                        $newItem->timeSheetDayTime = $dayTime;

                        if ($hoursMaster >= $remainingForWorks) {
                            $newItem->hours = $remainingForWorks;
                            $remainingForWorks = 0;
                        } else {
                            $newItem->hours = $hoursMaster;
                            $remainingForWorks -= $hoursMaster;
                        }
                        $newItem->remaining = $newItem->hours;

                        $workDetails->items[] = $newItem;

                        $total += $newItem->hours;
                    }
                }

                $i = 0;
                while ($remainingForWorks > 0 && count($works) > $i) {
                    $work = $works[$i];
                    $totalAlreadyUsed = 0;

                    foreach ($dayTime->excessTimes as $excessTime) {
                        foreach ($excessTime->activeItem->rules as $rule) {
                            foreach ($rule->works as $excessWork) {
                                if ($excessWork->timeSheetDayTimeWork_id == $work->id) {
                                    $totalAlreadyUsed += $excessWork->originalHours;
                                }
                            }
                        }
                    }

                    foreach ($dayTime->penalties as $penalty) {
                        if (!$penalty->userRolePenaltyRule->penaltyRule->calculationMethodClass::DOES_INCLUDE_EXCESS_TIME) {
                            foreach ($penalty->activeCalculation->items as $item) {
                                if ($item->timeSheetDayTimeWork_id == $work->id) {
                                    $totalAlreadyUsed += $item->minutes->toHours();
                                }
                            }
                        }
                    }

                    $hoursToDo = $work->hours - $totalAlreadyUsed;

                    if ($hoursToDo <= 0) {
                        $i++;
                        continue;
                    }

                    $newItem = new \stdClass();
                    $newItem->timeSheetDayTimeWork_id = $work->id;
                    $newItem->timeSheetDayTimeWork = $work;
                    $newItem->timeSheetDayTime_id = $dayTime->id;
                    $newItem->timeSheetDayTime = $dayTime;

                    if ($hoursToDo >= $remainingForWorks) {
                        $newItem->hours = $remainingForWorks;
                        $remainingForWorks = 0;
                    } else {
                        $newItem->hours = $hoursToDo;
                        $remainingForWorks -= $hoursToDo;
                        $i++;
                    }
                    $newItem->remaining = $newItem->hours;

                    $workDetails->items[] = $newItem;

                    $total += $newItem->hours;
                }
            }
        }

        $workDetails->totalExtra = $total;
        $workDetails->remainingExtra = $total;

        return $workDetails;
    }

    /** @throws \Throwable */
    public function updatePeriodExcessTime(bool $doesPreventWorkflowEvents = false, bool $doesForceRecalculation = false): self
    {
        $model = $this->getModel();

        if (!$model->isGlide || $model->glideDurationType == 'D') {
            return $this;
        }

        return $model->getConnection()->transaction(function () use ($model, $doesPreventWorkflowEvents, $doesForceRecalculation):self {
            if (!$model->timeSheet->canBeUpdated) {
                return $this;
            }

            $days = $model->getManyGlideSiblings(['timeSheetDayTimes.excessTimes']);

            /** @var TimeSheetDay $model */
            $model = $days->first();

            /** @var TimeSheetExcessTime[] $excessTimes */
            $excessTimes = TimeSheetExcessTimeRepository::getMany([
                'constraints' => [
                    ['TimeSheetDayGlide.id', '=', DB::raw($model->id)->getValue(DB::getQueryGrammar())],
                ],
                'returnType' => 'model',
            ]);

            $previousExcessTimes = [];
            $previousTotalHours = 0;
            foreach ($excessTimes as $excessTime) {
                $data = new \stdClass();
                $data->timeSheetDay_id = $excessTime->timeSheetDay_id;
                $previousTotalHours += $excessTime->totalHours;
                $data->comments = $excessTime->comments;
                $data->items = [];
                foreach ($excessTime->items as $item) {
                    $dataItem = new \stdClass();
                    $dataItem->rules = [];
                    $dataItem->hours = 0;
                    foreach ($item->rules as $rule) {
                        $dataRule = new \stdClass();
                        $dataRule->rule_id = $rule->excessTimeGroupSectionLevelRule_id;
                        $dataRule->comments = $rule->comments;
                        $dataItem->hours += $rule->hours;
                        $dataItem->rules[] = $dataRule;
                    }
                    $data->items[] = $dataItem;
                }
                $previousExcessTimes['t_' . $excessTime->timeSheetDay_id] = $data;
                $excessTime->forceDelete();
            }

            $excessTimeRecorded = 0;
            $userShiftDay_id = null;
            $userShiftTimeTypes = [];
            $userShiftTimeType = null;
            foreach ($days as $day) {
                $day->load('timeSheetDayTimes.excessTimes');
                $day->load('timeSheetDayTimes.userShiftTimeType.userShiftTimeTypeExcessTimeGroup.excessTimeGroup');
                foreach ($day->timeSheetDayTimes as $dayTime) {
                    if (is_null($userShiftDay_id) || $dayTime->userShift->userRole->repository->isMasterOnDate($day->date)) {
                        $userShiftDay_id = $dayTime->userShiftDay_id;
                    }

                    if (!in_array($dayTime->userShiftTimeType_id, $userShiftTimeTypes)) {
                        $userShiftTimeTypes[] = $dayTime->userShiftTimeType_id;
                    }

                    if (is_null($userShiftTimeType)) {
                        $userShiftTimeType = $dayTime->userShiftTimeType;
                    }

                    foreach ($dayTime->excessTimes as $excessTime) {
                        $excessTimeRecorded += $excessTime->totalHours;
                    }
                }
            }

            if (is_null($userShiftDay_id)) {
                return $this;
            }

            /** @var UserShiftDay $userShiftDay */
            $userShiftDay = UserShiftDayRepository::getOne([
                'constraints' => [
                    ['UserShiftDay.id', '=', $userShiftDay_id],
                ],
                'relations' => [
                    'userShift.user.userRoles.userShifts',
                ],
                'returnType' => 'model',
            ]);

            $userShiftDay = !$userShiftDay->isGlideFirstDay ? UserShiftDay::getOneByDate($userShiftDay->userShift, $userShiftDay->glidePeriodStartDate) : $userShiftDay;

            $total = $model->glidePeriodHoursTotal - $excessTimeRecorded;

            if (etime_round($total, 3) > etime_round($model->glidePeriodHoursExpected, 3)) {
                $excessHours = $total - $model->glidePeriodHoursExpected;
                $workDetails = static::calculatePeriodWorkDetails($excessHours, $days);
                $excessHours = $workDetails->totalExtra;

                if (etime_round($excessHours, 3) <= 0) {
                    return $this;
                }

                if (count($userShiftTimeTypes) > 1) {
                    if (!is_null($userShiftDay->userShift->user->masterUserRole->activeUserShifts) && count($userShiftDay->userShift->user->masterUserRole->activeUserShifts) > 0) {
                        /** @var \Element\ElementTime\Domains\Tenant\Schedules\Models\UserShiftTimeType $userShiftTimeType */
                        $userShiftTimeType = $userShiftDay->userShift->user->masterUserRole->activeUserShifts[0]->activeUserShiftTimeTypes()->where('isMaster', '=', true)->first();
                    } else {
                        /// In case the mater role doesn't have any shift assigned (WHAT? - Yes, just trust me - APC has an example of that)
                        $userShiftTimeType = $userShiftDay->userShift->activeUserShiftTimeTypes()->where('isMaster', '=', true)->first();
                    }
                }

                if (!$doesForceRecalculation && etime_equals($previousTotalHours, $excessHours)) {
                    throw new ExcessTimeAlreadyCalculatedException($model->date->toDateString());
                }

                $new = new TimeSheetExcessTime;
                $new->type = 'P';
                $new->timeSheetDay_id = $model->id;
                $new->userShiftDay_id = $userShiftDay->id;
                $new->userShiftTimeType_id = $userShiftTimeType->id;
                $new->hourlyRate = $userShiftDay->glidePeriodHourlyRate;
                $new->startDate = $model->glidePeriodStart;
                $new->endDate = $model->glidePeriodEnd;

                if (isset($previousExcessTimes['t_' . $model->id])) {
                    $new->comments = $previousExcessTimes['t_' . $model->id]->comments;
                }

                $new->totalHours = $excessHours;

                $new->saveOrFail([
                    'preventDayJob' => true,
                    'preventEvent' => true,
                ]);

                if (!is_null($userShiftTimeType->userShiftTimeTypeExcessTimeGroup) && !is_null($userShiftTimeType->userShiftTimeTypeExcessTimeGroup->excessTimeGroup)) {
                    TimeSheetExcessTimeRepository::assignPeriodRules(
                        $new,
                        $workDetails,
                        $userShiftTimeType->userShiftTimeTypeExcessTimeGroup->excessTimeGroup,
                        $previousExcessTimes,
                        $doesPreventWorkflowEvents,
                    );
                }
            }

            return $this;
        });
    }

    /**
     * @param UserShiftDay[]|Collection|null $userShiftDays
     * @throws UnauthorizedActionException
     * @throws \Throwable
     */
    public function alignSchedule(Collection|array|null $userShiftDays = null): self
    {
        $model = $this->getModel();

        if (!$this->canAlignSchedule(true, false)) {
            return $this;
        }

        $model->getConnection()->transaction(function () use ($model, $userShiftDays) {
            $timeSheet = $model->timeSheet;
            $user = $timeSheet->payRunItem->user;

            if (is_null($userShiftDays) || $userShiftDays->count() == 0) {
                /** @var UserShiftDay[]|Collection $userShiftDays */
                $userShiftDays = UserShiftDayRepository::getMany([
                    'constraints' => [
                        ['UserShift.user_id', '=', $user->id],
                        ['UserShiftDay.date', '=', $model->date->toDateString()],
                        [
                            [
                                ['IS NULL', 'UserShift.startDate', 'OR'],
                                ['UserShift.startDate', '<=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                        [
                            [
                                ['IS NULL', 'UserShift.endDate', 'OR'],
                                ['UserShift.endDate', '>=', $model->date->toDateString(), 'OR'],
                            ],
                        ],
                    ],
                    'relations' => [
                        'scheduledTimes',
                        'userShift',
                    ],
                    'returnType' => 'model',
                ]);
            }

            $model->load(['timeSheetDayTimes.excessTimes']);
            $model->load(['timeSheetDayTimes.breaks']);

            foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
                if ($userShiftDays->where('id', '=', $timeSheetDayTime->userShiftDay_id)->count() <= 0) {
                    continue;
                }

                foreach ($timeSheetDayTime->excessTimes as $excessTime) {
                    $excessTime->delete();
                }
            }

            foreach ($userShiftDays as $userShiftDay) {
                foreach ($userShiftDay->scheduledTimes as $scheduledTime) {
                    $scheduledTime->delete();
                }

                if ($userShiftDay->isPublicHoliday || $userShiftDay->hasHolidays) {
                    $userShiftDay->doesWorkOnPublicHoliday = false;
                }

                $userShiftDay->minutes = 0;
                $userShiftDay->clearSaveOrFail();
            }

            $recorded = $this->getTimeRecordedMap(true, true);

            $items = [];
            foreach ($recorded as $recordedItem) {
                $found = false;
                for ($i = 0; $i < count($items); $i++) {
                    if ($items[$i]['userShiftDay_id'] == $recordedItem->userShiftDay_id) {
                        $found = true;

                        $break = [
                            'startDateTime' => $items[$i]['endDateTime'],
                            'endDateTime' => $recordedItem->startDateTime->toDateTimeString(),
                        ];
                        $items[$i]['endDateTime'] = $recordedItem->endDateTime->toDateTimeString();
                        $items[$i]['breaks'][] = $break;
                    }
                }

                if (!$found) {
                    $items[] = [
                        'userShiftDay_id' => $recordedItem->userShiftDay_id,
                        'startDateTime' => $recordedItem->startDateTime->toDateTimeString(),
                        'endDateTime' => $recordedItem->endDateTime->toDateTimeString(),
                        'breaks' => [],
                    ];
                }
            }

            $calculatesExcessTime = false;
            foreach ($userShiftDays as $userShiftDay) {
                $maxMinutes = Minute::new(24, 'hours');
                if (
                    $userShiftDay->shiftType == 'X'
                    && $userShiftDay->userShift->doesHaveMaximumDailyHours
                    && $userShiftDay->userShift->maximumDailyHours < 24
                ) {
//                    $calculatesExcessTime = true;
                    $maxMinutes = Minute::new($userShiftDay->userShift->maximumDailyHours, 'hours');
                }

                if ($user->maximumHoursPerDay < $maxMinutes->toHours()) {
//                    $calculatesExcessTime = true;
                    $maxMinutes = Minute::new($user->maximumHoursPerDay, 'hours');
                }

                $calculatesExcessTime = true;

                if ((!$userShiftDay->isPublicHoliday || $userShiftDay->publicHolidayHours <= 0) && (!$userShiftDay->hasHolidays || $userShiftDay->holidayMinutes->lte(0))) {
                    $userShiftDay->isOn = false;
                }

                $userShiftDay->minutes = 0;

                foreach ($items as $item) {
                    if ($userShiftDay->id == $item['userShiftDay_id']) {
                        $newTime = new UserShiftDayTime;
                        $newTime->type = 'A';
                        $newTime->userShiftDay_id = $userShiftDay->id;
                        $newTime->startDateTime = $item['startDateTime'];
                        $newTime->endDateTime = $item['endDateTime'];
                        $newTime->saveOrFail();

//                        $minutes = $newTime->startDateTime->diffInMinutes($newTime->endDateTime);
                        $minutesReached = false;
                        $minutes = 0;
                        $lastEnd = $newTime->startDateTime->copy();

                        foreach ($item['breaks'] as $break) {
                            $newBreak = new UserShiftDayTimeBreak;
                            $newBreak->userShiftDayTime_id = $newTime->id;
                            $newBreak->startDateTime = $break['startDateTime'];
                            $newBreak->endDateTime = $break['endDateTime'];
                            $newBreak->minutes = TimeCalculations::getDiffInMinutes($newBreak->startDateTime, $newBreak->endDateTime);

                            $minutes += TimeCalculations::getDiffInMinutes($lastEnd, $newBreak->startDateTime);

                            if ($minutes >= $maxMinutes->toMinutes()) {
                                $minutesReached = true;
                                $diff = $minutes - $maxMinutes->toMinutes();
                                $newTime->endDateTime = $newBreak->startDateTime->copy()->subMinutes($diff);
                                $minutes = $maxMinutes->toMinutes();

                                break;
                            }

                            $newBreak->saveOrFail();
                            $lastEnd = $newBreak->endDateTime->copy();
                        }

                        if (!$minutesReached) {
                            $minutes += TimeCalculations::getDiffInMinutes($lastEnd, $newTime->endDateTime);

                            if ($minutes > $maxMinutes->toMinutes()) {
                                $diff = $minutes - $maxMinutes->toMinutes();
                                if ($newTime->endDateTime->isStartOfDay()) {
                                    $newTime->endDateTime = $newTime->endDateTime->copy()->addDay();
                                }
                                $newTime->endDateTime = $newTime->endDateTime->copy()->subMinutes($diff);
                                $minutes = $maxMinutes->toMinutes();
                            }
                        }

                        $newTime->minutes = $minutes;
                        $newTime->saveOrFail();

                        $userShiftDay->isOn = true;
                        $userShiftDay->minutes = $newTime->minutes;

                        if ($userShiftDay->isPublicHoliday || $userShiftDay->hasHolidays) {
                            $userShiftDay->doesWorkOnPublicHoliday = true;
                        }
                    }
                }

                $userShiftDay->saveOrFail([
                    'preventTimeSheetSave' => true,
                    'preventValidatePayRun' => true,
                    'preventScheduleEvent' => false,
                    'preventTimeSheetEvent' => true,
                ]);
            }

            if ($calculatesExcessTime) {
                $this->updateExcessTime([
                    'saveParent' => true,
                    'forceUpdateEvent' => false,
                    'forcePreventEvent' => true,
                    'forceSave' => true,
                ]);
            } else {
                $model->saveOrFail([
                    'saveParent' => true,
                ]);
            }
        });

        return $this;
    }

    /**
     * @param bool $throwsExceptions
     * @return bool
     * @throws UnauthorizedActionException
     */
    public function canCalculatePenalties(bool $throwsExceptions = true):bool
    {
        $model = $this->getModel();
        $timeSheet = $model->timeSheet;

        if ($timeSheet->payRun->status == 'F') {
            if ($throwsExceptions) {
                throw new UnauthorizedActionException([
                    'action' => 'calculate',
                    'model' => 'penalties',
                    'reason' => 'This pay-run is already finished',
                ]);
            }

            return false;
        }

        if ($timeSheet->payRun->status == 'C') {
            if ($throwsExceptions) {
                throw new UnauthorizedActionException([
                    'action' => 'calculate',
                    'model' => 'penalties',
                    'reason' => 'This pay-run has been cancelled',
                ]);
            }

            return false;
        }

        if ($timeSheet->repository->isApproved()) {
            if ($throwsExceptions) {
                throw new UnauthorizedActionException([
                    'action' => 'calculate',
                    'model' => 'penalties',
                    'reason' => 'The timesheet is already approved',
                ]);
            }

            return false;
        }

        return true;
    }

    /** @throws UnauthorizedActionException */
    public function clearPenalties(bool $reloadData = true, bool $onlyExcessTime = false): self
    {
        $model = $this->getModel();
        $this->canCalculatePenalties();

        if ($reloadData) {
            $model->load(['timeSheetDayTimes.penalties']);
        }

        foreach ($model->timeSheetDayTimes as $time) {
            foreach ($time->penalties as $penalty) {
                if (!$onlyExcessTime || $penalty->userRolePenaltyRule->penaltyRule->hasExcessTimeChangesCalculations) {
                    $penalty->delete();
                }
            }
        }

        return $this;
    }

    /** @throws \Throwable */
    public function calculatePenalties(bool $reloadData = true, bool $forExcessTimeCalculations = false): self
    {
        $model = $this->getModel();
        $this->clearPenalties($reloadData, $forExcessTimeCalculations);

        if ($reloadData) {
            $model->load([
                'timeSheetDayTimes.userShiftDay.userShift.userRole.user',
                'timeSheetDayTimes.userShiftDay.userShift.userRole.userRolePenaltyRules.userRole.user',
                'timeSheetDayTimes.works.penaltyCalculationItems.calculation.penalty.userRolePenaltyRule.penaltyRule',
                'timeSheetDayTimes.penalties.userRolePenaltyRule.penaltyRule',
            ]);
        }

        $calculatesNextDay = false;

        foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
            if (!$timeSheetDayTime->recordTypeClass::IS_WORK) {
                continue;
            }

            foreach ($timeSheetDayTime->userShiftDay->userShift->userRole->userRolePenaltyRules as $rule) {
                if ($forExcessTimeCalculations != $rule->penaltyRule->hasExcessTimeChangesCalculations) {
                    continue;
                }

                if ($rule->isActiveOnTime($model->date)) {
                    $rule->penaltyRule->repository->calculate($rule, $model, $timeSheetDayTime, $timeSheetDayTime->userShiftDay->userShift->userRole->user);
                }

                if ($rule->isActiveOnTime($model->date->copy()->addDays(1)) && $rule->penaltyRule->hasNextDayCalculations) {
                    $calculatesNextDay = true;
                }
            }
        }

        if ($calculatesNextDay) {
            $timeSheetDay = TimeSheetDay::q()
                ->joins([
                    ['TimeSheet', 'TimeSheet.id', '=', 'TimeSheetDay.timeSheet_id'],
                    ['PayRunItem', 'PayRunItem.id', '=', 'TimeSheet.payRunItem_id'],
                    ['User', 'User.id', '=', 'PayRunItem.user_id'],
                ])
                ->constraints([
                    ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                    ['TimeSheetDay.date', '=', $model->date->copy()->addDays(1)],
                ])
                ->relations([
                    'timeSheetDayTimes',
                ])
                ->first();


            if (is_null($timeSheetDay)) {
                return $this;
            }

            $timeSheetDayTimes = $timeSheetDay->timeSheetDayTimes->whereNotIn('recordType', BaseType::getAllHolidayTypeIds());

            if ($timeSheetDayTimes->whereNotIn('recordType', BaseType::getAllHolidayTypeIds())->count() > 0 && $timeSheetDayTimes->whereNotIn('recordType', BaseType::getAllHolidayTypeIds())[0]->startDateTime->isStartOfDay()) {
                $timeSheetDay->repository->calculatePenalties($reloadData, $forExcessTimeCalculations);
            }
        }

        return $this;
    }

    //endregion --\\   General calculation methods   //--

    //region --\\   Glide calculation methods   //--

    /**
     * @throws InvalidValueException
     * @throws \Throwable
     */
    public function updateGlideDataFromUserShiftDays():self
    {
        $model = $this->getModel();

        if (!$model->isGlide) {
            throw new InvalidValueException([
                'model' => 'timesheet day',
                'field' => 'is glide',
                'more' => 'Only glide timesheet days can use this method',
            ]);
        }

        $userShiftDays = UserShiftDay::getManyFromUser(
            $model->timeSheet->user,
            $model->date->copy(),
            null,
            true,
            false,
            [],
            false,
            true
        );

        if (count($userShiftDays) == 0) {
            return $this;
        }

        $hasSchedule = false;

        $model->glideDayMinHoursExpected = 0;
        $model->glideDayAverageHoursExpected = 0;
        try {
            if ($userShiftDays->count() > 0 && $userShiftDays[0]->shiftType == 'G') {
                $model->glidePeriodStart = $userShiftDays[0]->glidePeriodStartDate;
                $model->glidePeriodEnd = $userShiftDays[0]->glidePeriodEndDate;
                $model->glideFirstDayFromPeriod->glidePeriodHoursExpected = 0;
            }
        } catch (\Throwable) {
        }

        foreach ($userShiftDays as $userShiftDay) {
            if ($userShiftDay->shiftType != 'G') {
                throw new InvalidValueException(
                    [
                        'this' => 'user-shift day',
                        'field' => 'is glide',
                        'more' => 'Only glide user-shift days can use this method',
                    ]
                );
            }
            $model->glidePeriodStart = $userShiftDay->glidePeriodStartDate;
            $model->glidePeriodEnd = $userShiftDay->glidePeriodEndDate;
            $scheduledTime = $userShiftDay->scheduledTimes()->count() > 0
                ? $userShiftDay->scheduledTimes[0]
                : null;
            $model->glideDayIsOn = $model->glideDayIsOn || $userShiftDay->isOn;

            if ($model->glideDayIsOn && !is_null($scheduledTime)) {
                $hasSchedule = true;

                $model->glideDayExpectedStartDateTime =
                    is_null($model->glideDayExpectedStartDateTime) || $model->glideDayExpectedStartDateTime->gt(
                        $scheduledTime->startDateTime
                    )
                        ? $scheduledTime->startDateTime
                        : $model->glideDayExpectedStartDateTime;
                $model->glideDayExpectedEndDateTime =
                    is_null($model->glideDayExpectedEndDateTime) || $model->glideDayExpectedEndDateTime->lt(
                        $scheduledTime->endDateTime
                    )
                        ? $scheduledTime->endDateTime
                        : $model->glideDayExpectedEndDateTime;
            }

            $model->glideDayMinHoursExpected += $userShiftDay->glideDayMinHours;
            $model->glideDayMaxHoursExpected = max($model->glideDayMaxHoursExpected, $userShiftDay->glideDayMaxHours);

            $model->glideDayAverageHoursExpected += $userShiftDay->glideDayHoursAverage;
            $model->glideFirstDayFromPeriod->glidePeriodHoursExpected += $userShiftDay->minutesGlidePeriod->toHours();
        }

        if (!$hasSchedule) {
            $model->glideDayExpectedStartDateTime = null;
            $model->glideDayExpectedEndDateTime = null;
        }

        $model->glideFirstDayFromPeriod->clearSave();

        return $this;
    }

    /**
     * @throws InvalidValueException
     * @throws UnauthorizedActionException
     * @throws \BadMethodCallException
     * @throws \Element\Core\Exceptions\CompulsoryParametersMissingException
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     * @throws \OutOfRangeException
     * @throws \Throwable
     */
    public function runGlideCalculations(bool $save = true, bool $deep = false, bool $updateScheduling = true)
    {
        $model = $this->getModel();

        if (!$model->isGlide) {
            throw new InvalidValueException([
                'model' => 'timesheet day',
                'field' => 'is glide',
                'more' => 'Only glide timesheet days can use this method',
            ]);
        }

        $hoursWorked = 0.0;
        $hoursWorkedCalculation = 0.0;
        $hoursLeave = 0.0;
        $hoursLeaveCalculation = 0.0;
        $hoursExcessTimeAccrued = 0.0;
        $hoursExcessTimeAccruedAdjusted = 0.0;
        $hoursExcessTimePaid = 0.0;
        $hoursExcessTimePaidAdjusted = 0.0;
        $hoursExcessTimeUsed = 0.0;
        $hoursExcessTimeUsedCalculation = 0.0;
        $valueExcessTimePaid = 0.0;
        $hoursExcessTimeUnpaid = 0.0;
        $hoursRosteredTimeOff = 0.0;
        $hoursRosteredTimeOffCalculation = 0.0;
        $hoursPublicHoliday = 0.0;
        $hoursAllowance = 0.0;
        $milesAllowance = 0.0;
        $valueAllowance = 0.0;
        $hoursMore = 0.0;
        $hoursLess = 0.0;
        $hoursTotal = 0.0;
        $hoursTotalCalculation = 0.0;
        $hoursExpected = 0.0;

        /** @var UserShiftDay $userShiftDay */
        $userShiftDays = UserShiftDay::getManyFromUser($model->timeSheet->user, $model->date->copy(), null, true, false, [], false, !$updateScheduling);

        if (count($userShiftDays) == 0) {
            $model->hoursWorked = $hoursWorked;
            $model->hoursWorkedCalculation = $hoursWorkedCalculation;
            $model->hoursLeave = $hoursLeave;
            $model->hoursLeaveCalculation = $hoursLeaveCalculation;
            $model->hoursExcessTimeAccrued = $hoursExcessTimeAccrued;
            $model->hoursExcessTimeAccruedAdjusted = $hoursExcessTimeAccruedAdjusted;
            $model->hoursExcessTimePaid = $hoursExcessTimePaid;
            $model->hoursExcessTimePaidAdjusted = $hoursExcessTimePaidAdjusted;
            $model->valueExcessTimePaid = $valueExcessTimePaid;
            $model->hoursExcessTimeUnpaid = $hoursExcessTimeUnpaid;
            $model->hoursExcessTimeUsed = $hoursExcessTimeUsed;
            $model->hoursExcessTimeUsedCalculation = $hoursExcessTimeUsedCalculation;
            $model->hoursRosteredTimeOff = $hoursRosteredTimeOff;
            $model->hoursRosteredTimeOffCalculation = $hoursRosteredTimeOffCalculation;
            $model->hoursPublicHoliday = $hoursPublicHoliday;
            $model->hoursAllowance = $hoursAllowance;
            $model->hoursAllowanceCalculation = $hoursAllowance;
            $model->milesAllowance = $milesAllowance;
            $model->valueAllowance = $valueAllowance;
            $model->hoursMore = $hoursMore;
            $model->hoursLess = $hoursLess;
            $model->hoursTotal = $hoursTotal;
            $model->hoursTotalCalculation = $hoursTotalCalculation;
            $model->hoursExpected = $hoursExpected;

            if ($save) {
                $model->saveOrFail(['calculations' => 'none']);
            }

            return ;
        }

        $model->load(['timeSheetDayTimes.excessTimes.items']);

        foreach ($userShiftDays as $userShiftDay) {
            if ($userShiftDay->glideDurationType == 'D') {
                if ($userShiftDay->isPublicHoliday) {
                    $hoursExpected += $userShiftDay->publicHolidayHours;

                    if ($userShiftDay->doesWorkOnPublicHoliday) {
                        $hoursExpected += $userShiftDay->hours;
                    }
                } elseif ($userShiftDay->hasHolidays) {
                    $hoursExpected += $userShiftDay->holidayMinutes->toHours();

                    if ($userShiftDay->doesWorkOnPublicHoliday) {
                        $hoursExpected += $userShiftDay->hours;
                    }
                } else {
                    $hoursExpected += $userShiftDay->hours;
                }
            } else {
                if ($userShiftDay->isOn) {
                    if ($userShiftDay->isPublicHoliday) {
                        $hoursExpected += $userShiftDay->publicHolidayHours;

                        if ($userShiftDay->doesWorkOnPublicHoliday) {
                            $hoursExpected += $userShiftDay->glideDayHoursAverage;
                        }
                    } elseif ($userShiftDay->hasHolidays) {
                        $hoursExpected += $userShiftDay->holidayMinutes->toHours();

                        if ($userShiftDay->doesWorkOnPublicHoliday) {
                            $hoursExpected += $userShiftDay->glideDayHoursAverage;
                        }
                    } else {
                        $hoursExpected += $userShiftDay->glideDayHoursAverage;
                    }
                }
            }
        }

        // Time recorded
        foreach ($model->timeSheetDayTimes as $t) {
            if ($deep) {
                $t->saveOrFail([
                    'calculations' => 'deep',
                    'calculatesExcessTime' => false,
                ]);
            }

            if ($t->recordTypeClass::IS_WORK) {
                $hoursWorked += $t->finalCalculatedHours;
                $hoursWorkedCalculation += $t->finalCalculatedHours;
            } elseif ($t->recordTypeClass::IS_HOLIDAY) {
                $hoursPublicHoliday += $t->finalCalculatedHours;
            }

            $hoursTotal += $t->finalCalculatedHours;
            $hoursTotalCalculation += $t->finalCalculatedHours;

            foreach ($t->excessTimes as $excessTime) {
                $item = $excessTime->activeItem;
                if (!is_null($item)) {
                    if ($item->repository->isApproved()) {
                        $hoursExcessTimeAccrued += $item->totalHoursAccrued;
                        $hoursExcessTimeAccruedAdjusted += $item->totalHoursAccruedAdjusted;
                        $hoursExcessTimePaid += $item->totalHoursPaid;
                        $hoursExcessTimePaid += $item->totalHoursAdditional;
                        $hoursExcessTimePaidAdjusted += $item->totalHoursPaidAdjusted;
                        $hoursExcessTimePaidAdjusted += $item->totalHoursAdditional;
                        $valueExcessTimePaid += $item->totalHoursPaidAdjusted * $excessTime->hourlyRate;
                        $valueExcessTimePaid += $item->totalHoursAdditional * $excessTime->hourlyRate;
                        $hoursExcessTimeUnpaid += $item->totalHoursUnpaid;
                    }
                }
            }
        }

        /** @var LeaveRequestDay[] $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '=', $model->date],
                ['Workflow.status', '=', Statuses\ApprovedType::ID],
            ],
            'relations' => [
                'leaveRequest.userLeaveBankType',
            ],
            'returnType' => 'model',
        ]);

        foreach ($leaveRequestDays as $leaveRequestDay) {
            if ($leaveRequestDay->leaveRequest->userLeaveBankType->isLeave) {
                $hoursLeave += $leaveRequestDay->calculatedHours;
                $hoursLeaveCalculation += $leaveRequestDay->actualHours;
            } elseif ($leaveRequestDay->leaveRequest->userLeaveBankType->isRosteredTimeOff) {
                $hoursRosteredTimeOff += $leaveRequestDay->calculatedHours;
                $hoursRosteredTimeOffCalculation += $leaveRequestDay->actualHours;
            } elseif ($leaveRequestDay->leaveRequest->userLeaveBankType->isAccruedHours) {
                $hoursExcessTimeUsed += $leaveRequestDay->calculatedHours;
                $hoursExcessTimeUsedCalculation += $leaveRequestDay->actualHours;
            }

            $hoursTotal += $leaveRequestDay->calculatedHours;
            $hoursTotalCalculation += $leaveRequestDay->actualHours;
        }

        $model->load(['timeSheetAllowanceDays.timeSheetAllowance']);

        // Allowances recorded
        foreach ($model->timeSheetAllowanceDays as $allowanceDay) {
            if (is_null($allowanceDay->timeSheetAllowance)) {
                $allowanceDay->delete();
            }

            if ($deep) {
                $allowanceDay->save([
                    'preventJobsDispatching' => true,
                    'calculations' => 'deep',
                ]);
            }

            $hoursAllowance += $allowanceDay->hours;
            $milesAllowance += $allowanceDay->miles;
            $valueAllowance += $allowanceDay->value;
        }

        $model->hoursWorked = $hoursWorked;
        $model->hoursWorkedCalculation = $hoursWorkedCalculation;
        $model->hoursLeave = $hoursLeave;
        $model->hoursLeaveCalculation = $hoursLeaveCalculation;
        $model->hoursExcessTimeAccrued = $hoursExcessTimeAccrued;
        $model->hoursExcessTimeAccruedAdjusted = $hoursExcessTimeAccruedAdjusted;
        $model->hoursExcessTimePaid = $hoursExcessTimePaid;
        $model->hoursExcessTimePaidAdjusted = $hoursExcessTimePaidAdjusted;
        $model->valueExcessTimePaid = $valueExcessTimePaid;
        $model->hoursExcessTimeUnpaid = $hoursExcessTimeUnpaid;
        $model->hoursExcessTimeUsed = $hoursExcessTimeUsed;
        $model->hoursExcessTimeUsedCalculation = $hoursExcessTimeUsedCalculation;
        $model->hoursRosteredTimeOff = $hoursRosteredTimeOff;
        $model->hoursRosteredTimeOffCalculation = $hoursRosteredTimeOffCalculation;
        $model->hoursPublicHoliday = $hoursPublicHoliday;
        $model->hoursAllowance = $hoursAllowance;
        $model->hoursAllowanceCalculation = $hoursAllowance;
        $model->milesAllowance = $milesAllowance;
        $model->valueAllowance = $valueAllowance;
        $model->hoursMore = $hoursMore;
        $model->hoursLess = $hoursLess;
        $model->hoursTotal = $hoursTotal;
        $model->hoursTotalCalculation = $hoursTotalCalculation;
        $model->hoursExpected = $hoursExpected;

        if ($save) {
            $model->saveOrFail(['calculations' => 'none']);
        }
    }

    /**
     * @throws \BadMethodCallException
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidValueException
     * @throws \InvalidArgumentException
     * @throws \Throwable
     */
    public function updateGlideInfo(array $options = []):bool
    {
        $model = $this->getModel();

        if (!$model->isGlide) {
            throw new InvalidValueException([
                'model' => 'timesheet day',
                'field' => 'is glide',
                'more' => 'Only glide timesheet days can use this method',
            ]);
        }

        if (!$model->isFirstGlideDay) {
            return $model->glideFirstDayFromPeriod->repository->updateGlideInfo($options);
        }

        $allDays = $model->getManyGlideSiblings([], true);

        $glidePeriodHoursWorked = 0;
        $glidePeriodHoursLeave = 0;
        $glidePeriodHoursExcessTimeAccrued = 0;
        $glidePeriodHoursExcessTimeAccruedAdjusted = 0;
        $glidePeriodHoursExcessTimePaid = 0;
        $glidePeriodHoursExcessTimePaidAdjusted = 0;
        $glidePeriodValueExcessTimePaid = 0;
        $glidePeriodHoursExcessTimeUnpaid = 0;
        $glidePeriodHoursExcessTimeUsed = 0;
        $glidePeriodHoursRosteredTimeOff = 0;
        $glidePeriodHoursAllowance = 0;
        $glidePeriodMilesAllowance = 0;
        $glidePeriodValueAllowance = 0;
        $glidePeriodHoursMore = 0;
        $glidePeriodHoursLess = 0;
        $glidePeriodHoursTotal = 0;

        foreach ($allDays as $day) {
            $glidePeriodHoursWorked += $day->hoursWorkedCalculation;
            $glidePeriodHoursLeave += $day->hoursLeaveCalculation;
            $glidePeriodHoursExcessTimeAccrued += $day->hoursExcessTimeAccrued;
            $glidePeriodHoursExcessTimeAccruedAdjusted += $day->hoursExcessTimeAccruedAdjusted;
            $glidePeriodHoursExcessTimePaid += $day->hoursExcessTimePaid;
            $glidePeriodHoursExcessTimePaidAdjusted += $day->hoursExcessTimePaidAdjusted;
            $glidePeriodValueExcessTimePaid += $day->valueExcessTimePaid;
            $glidePeriodHoursExcessTimeUnpaid += $day->hoursExcessTimeUnpaid;
            $glidePeriodHoursExcessTimeUsed += $day->hoursExcessTimeUsedCalculation;
            $glidePeriodHoursRosteredTimeOff += $day->hoursRosteredTimeOffCalculation;
            $glidePeriodHoursAllowance += $day->hoursAllowanceCalculation;
            $glidePeriodMilesAllowance += $day->milesAllowance;
            $glidePeriodValueAllowance += $day->valueAllowance;
            $glidePeriodHoursMore += $day->hoursMore;
            $glidePeriodHoursLess += $day->hoursLess;
            $glidePeriodHoursTotal += $day->hoursTotalCalculation;
        }

        $model->load(['excessTime.items']);

        if (!is_null($model->excessTime) && isset($model->excessTime->activeItem) && $model->excessTime->activeItem->repository->isApproved()) {
            $glidePeriodHoursExcessTimeAccrued += $model->excessTime->activeItem->totalHoursAccrued;
            $glidePeriodHoursExcessTimeAccruedAdjusted += $model->excessTime->activeItem->totalHoursAccruedAdjusted;
            $glidePeriodHoursExcessTimePaid += $model->excessTime->activeItem->totalHoursPaid;
            $glidePeriodHoursExcessTimePaidAdjusted += $model->excessTime->activeItem->totalHoursPaidAdjusted;
            $glidePeriodValueExcessTimePaid += $model->excessTime->activeItem->totalHoursPaidAdjusted * $model->excessTime->hourlyRate;
            $glidePeriodHoursExcessTimeUnpaid += $model->excessTime->activeItem->totalHoursUnpaid;
        }

        $model->glidePeriodHoursWorked = $glidePeriodHoursWorked;
        $model->glidePeriodHoursLeave = $glidePeriodHoursLeave;
        $model->glidePeriodHoursExcessTimeAccrued = $glidePeriodHoursExcessTimeAccrued;
        $model->glidePeriodHoursExcessTimeAccruedAdjusted = $glidePeriodHoursExcessTimeAccruedAdjusted;
        $model->glidePeriodHoursExcessTimePaid = $glidePeriodHoursExcessTimePaid;
        $model->glidePeriodHoursExcessTimePaidAdjusted = $glidePeriodHoursExcessTimePaidAdjusted;
        $model->glidePeriodValueExcessTimePaid = $glidePeriodValueExcessTimePaid;
        $model->glidePeriodHoursExcessTimeUnpaid = $glidePeriodHoursExcessTimeUnpaid;
        $model->glidePeriodHoursExcessTimeUsed = $glidePeriodHoursExcessTimeUsed;
        $model->glidePeriodHoursRosteredTimeOff = $glidePeriodHoursRosteredTimeOff;
        $model->glidePeriodHoursAllowance = $glidePeriodHoursAllowance;
        $model->glidePeriodMilesAllowance = $glidePeriodMilesAllowance;
        $model->glidePeriodValueAllowance = $glidePeriodValueAllowance;
        $model->glidePeriodHoursMore = $glidePeriodHoursMore;
        $model->glidePeriodHoursLess = $glidePeriodHoursLess;
        $model->glidePeriodHoursTotal = $glidePeriodHoursTotal;

        if ($model->doesHaveChanges() || (!isset($options['forceSave']) || $options['forceSave'])) {
            $options['savingGlideInfo'] = true;

            return $model->saveOrFail($options);
        }

        return true;
    }

    //endregion --\\   Glide calculation methods   //--

    //region --\\   Data factory methods   //--

    /**
     * @throws InvalidStatusException
     * @throws \Throwable
     */
    public function putHoliday(Holiday|null $holiday = null, bool $force = false): bool
    {
        $model = $this->getModel();

        if (
            !$model->timeSheet->repository->isNew()
            && !$model->timeSheet->repository->isSubmitted()
            && !$model->timeSheet->repository->isExcluded()
            && !$model->timeSheet->repository->isPartiallyApproved()
        ) {
            throw new InvalidStatusException([
                'action' => 'update public holidays',
                'reason' => 'timesheet not editable',
            ]);
        }

        $constraints = [
            ['UserShift.user_id', '=', $model->timeSheet->payRunItem->user_id],
            ['UserShiftDay.date', '=', $model->date->toDateString()],
        ];

        if (!is_null($holiday)) {
            $constraints[] = ['Holiday.id', '=', $holiday->id];
        }

        $userShiftDayHolidays = UserShiftDayHoliday::q()
            ->constraints($constraints)
            ->many();

        $toSave = false;

        $holidayTimes = $model->timeSheetDayTimes->where('recordType', '=', HolidayType::ID);

        foreach ($holidayTimes as $holidayTime) {
            if ($userShiftDayHolidays->where('id', '=', $holidayTime->model_id)->count() < 1) {
                $holidayTime->forceDelete();

                $toSave = true;
            }
        }

        if ($toSave) {
            $model->load(['timeSheetDayTimes']);

            $holidayTimes = $model->timeSheetDayTimes->where('recordType', '=', HolidayType::ID);
        }

        foreach ($userShiftDayHolidays as $userShiftDayHoliday) {
            $thisHolidayTimes = $holidayTimes->where('model_id', '=', $userShiftDayHoliday->id);

            if ($thisHolidayTimes->count() > 0) {
                $removed = false;

                foreach ($thisHolidayTimes as $holidayTime) {
                    if ($force || $holidayTime->minutes->neq($userShiftDayHoliday->minutes)) {
                        $holidayTime->delete([
                            'saveParent' => false,
                        ]);

                        $toSave = true;
                        $removed = true;
                    }
                }

                if (!$removed) {
                    continue;
                }
            }

            if ($userShiftDayHoliday->minutes->gt(0)) {
                try {
                    $newRecord = new TimeSheetDayTime;
                    $newRecord->timeSheetDay_id = $model->id;
                    $newRecord->userShiftDay_id = $userShiftDayHoliday->userShiftDay_id;
                    $newRecord->userShiftTimeType_id = $userShiftDayHoliday->userShiftDay->userShift->repository->getMasterUserShiftTimeType($model->date)->id;
                    $newRecord->recordType = HolidayType::ID;
                    $newRecord->type_type = UserRoleProject::class;
                    $newRecord->type_id = $userShiftDayHoliday->userShiftDay->userShift->userRole->repository->getMasterUserRoleProject($userShiftDayHoliday->userShiftDay->date)->id;
                    $newRecord->model_type = UserShiftDayHoliday::class;
                    $newRecord->model_id = $userShiftDayHoliday->id;
                    $newRecord->minutes = $userShiftDayHoliday->minutes;
                    $newRecord->adjustedHours = $userShiftDayHoliday->minutes->toHours();

                    $newRecord->saveOrFail([
                        'calculatesExcessTime' => false,
                    ]);

                } catch (\Throwable $e) {
                    report($e);
                }

                $toSave = true;
            }
        }

        if ($toSave) {
            $hadExcessTime = false;

            $model->saveOrFail([
                'calculations' => 'normal',
                'forceUpdateEvent' => false,
                'forcePreventEvent' => true,
            ]);

            foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
                if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                    $hadExcessTime = true;

                    break;
                }
            }

            if (!$hadExcessTime) {
                $model->asyncSendBroadcast([
                    TimeCardTotalsUpdatedBroadcastEvent::class,
                ]);
                $model->timeSheet->asyncSendBroadcast([
                    TimeSheetUpdatedBroadcastEvent::class,
                ]);
            } else {
                $model->asyncUpdateExcessTime(2);
            }

            return true;
        }

        return false;
    }

    /**
     * @param null|PublicHoliday $publicHoliday
     * @param bool $force
     *
     * @return bool
     * @throws InvalidValueException
     * @throws \Element\Core\Exceptions\InvalidArgumentException
     * @throws \Throwable
     */
    public function putPublicHoliday(PublicHoliday $publicHoliday = null, bool $force = false):bool
    {
        $model = $this->getModel();

        if (
            !$model->timeSheet->repository->isNew()
            && !$model->timeSheet->repository->isSubmitted()
            && !$model->timeSheet->repository->isExcluded()
            && !$model->timeSheet->repository->isPartiallyApproved()
        ) {
            throw new InvalidStatusException([
                'action' => 'update public holidays',
                'reason' => 'timesheet not editable',
            ]);
        }

        if (
            $model->timeSheet->payRun->status != 'N'
            && $model->timeSheet->payRun->status != 'P'
            && $model->timeSheet->payRun->status != 'T'
        ) {
            throw new InvalidStatusException([
                'action' => 'update public holidays',
                'reason' => 'payrun finished',
            ]);
        }

        if (is_null($publicHoliday)) {
            /** @var PublicHoliday $publicHoliday */
            $publicHoliday = PublicHolidayRepository::getOne([
                'constraints' => [
                    ['PublicHoliday.date', '=', $model->date->toDateString()],
                ],
                'returnType' => 'model',
            ]);
        }

        if (is_null($publicHoliday)) {
            $found = false;

            if (!is_null($model->publicHoliday_id)) {
                $model->publicHoliday_id = null;
                $found = true;
            }

            foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
                if ($timeSheetDayTime->recordTypeClass::isType(PublicHolidayType::class)) {
                    $timeSheetDayTime->forceDelete();
                    $found = true;
                }
            }

            if ($force || $found) {
                $model->saveOrFail([
                    'calculations' => 'normal',
                    'forceUpdateEvent' => false,
                    'forcePreventEvent' => true,
                ]);

                return true;
            }

            return false;
        }

        /** @var Collection|UserShiftDay[] $userShiftDays */
        $userShiftDays = UserShiftDayRepository::getMany([
            'constraints' => [
                ['UserShiftDay.date', '=', $model->date->toDateString()],
                ['UserShift.user_id', '=', $model->timeSheet->payRunItem->user_id],
                ['UserShift.startDate', '<=', $model->date->toDateString()],
                [
                    [
                        ['IS NULL', 'UserShift.endDate', 'OR'],
                        ['UserShift.endDate', '=', '0000-00-00', 'OR'],
                        ['UserShift.endDate', '>=', $model->date->toDateString(), 'OR'],
                    ],
                ],
            ],
            'relations' => [
                'userShift.userShiftTimeTypes',
                'userShift.userRole.userRoleProjects',
                'scheduledTimes.breaks',
            ],
            'returnType' => 'model',
        ]);

        $found = false;
        foreach ($userShiftDays as $userShiftDay) {
            $data = $userShiftDay->repository->getPublicHolidayData();

            if ($force || $model->publicHoliday_id != $publicHoliday->id) {
                $model->publicHoliday_id = $publicHoliday->id;
                $model->clearSaveOrFail();
            }

            if (!is_null($data)) {
                $hasChanges = false;

                foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
                    if ($timeSheetDayTime->recordTypeClass::isType(PublicHolidayType::class) && $timeSheetDayTime->userShiftDay_id == $userShiftDay->id) {
                        if ($force || $timeSheetDayTime->minutes->toMinutes() != $data->minutes->toMinutes()) {
                            $hasChanges = true;
                            $timeSheetDayTime->delete([
                                'saveParent' => false,
                            ]);
                        }
                    }
                }

                if (!$hasChanges) {
                    $check = TimeSheetDayTimeRepository::readMany([
                        'constraints' => [
                            ['TimeSheetDayTime.timeSheetDay_id', '=', $model->id],
                            ['TimeSheetDayTime.userShiftDay_id', '=', $userShiftDay->id],
                            ['TimeSheetDayTime.recordType', '=', PublicHolidayType::ID],
                        ],
                    ]);

                    if ($check->count() > 0) {
                        continue;
                    }
                }

                $found = true;

                if ($data->minutes->toMinutes() > 0) {
                    try {
                        $newRecord = new TimeSheetDayTime;
                        $newRecord->timeSheetDay_id = $model->id;
                        $newRecord->userShiftDay_id = $userShiftDay->id;
                        $newRecord->userShiftTimeType_id = $data->userShiftTimeType_id;
                        $newRecord->recordType = PublicHolidayType::ID;
                        $newRecord->type_type = UserRoleProject::class;
                        $newRecord->type_id = $userShiftDay->userShift->userRole->repository->getMasterUserRoleProject($userShiftDay->date)->id;
                        if (!is_null($data->id)) {
                            $newRecord->model_type = PublicHoliday::class;
                            $newRecord->model_id = $data->id;
                        }
                        $newRecord->minutes = $data->minutes;
                        $newRecord->adjustedHours = $data->minutes->toHours();

                        $newRecord->saveOrFail([
                            'calculatesExcessTime' => false,
                        ]);
                    } catch (\Throwable $e) {
                        report($e);
                    }
                }
            }
        }

        if ($found) {
            $hadExcessTime = false;

            $model->saveOrFail([
                'calculations' => 'normal',
                'forceUpdateEvent' => false,
                'forcePreventEvent' => true,
            ]);

            foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
                if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                    $hadExcessTime = true;

                    break;
                }
            }

            if (!$hadExcessTime) {
                $model->asyncSendBroadcast([
                    TimeCardTotalsUpdatedBroadcastEvent::class,
                ]);
                $model->timeSheet->asyncSendBroadcast([
                    TimeSheetUpdatedBroadcastEvent::class,
                ]);
            } else {
                $model->asyncUpdateExcessTime(2);
            }

            return true;
        }

        return false;
    }

    /**
     * @throws \Throwable
     */
    public function getTimeSheetAllowanceDayFromType(int $allowanceType_id): TimeSheetAllowanceDay
    {
        $model = $this->getModel();

        $timeSheetAllowance = $model->timeSheet->repository->getTimeSheetAllowance($allowanceType_id);

        if (is_null($timeSheetAllowance)) {
            $timeSheetAllowance = new TimeSheetAllowance;
            $timeSheetAllowance->timeSheet_id = $model->timeSheet_id;
            $timeSheetAllowance->allowanceType_id = $allowanceType_id;
            $timeSheetAllowance->saveOrFail();
        }

        $model->load(['timeSheetAllowanceDays']);

        $timeSheetAllowanceDay = $model->timeSheetAllowanceDays
            ->where('timeSheetAllowance_id', '=', $timeSheetAllowance->id)
            ->where('date', '=', $model->date->copy())->first();

        return $timeSheetAllowanceDay;
    }

    //endregion --\\   Data factory methods   //--

    //region --\\   Data fetching methods   //--

    /** @throws \Throwable */
    public function getSiblingByDate(Carbon $date): TimeSheetDay|null
    {
        $model = $this->getModel();
        $date = $date->copy()->startOfDay();

        $timeSheet = $date->between($model->timeSheet->payRun->startDate->startOfDay(), $model->timeSheet->payRun->endDate->endOfDay()) ? $model->timeSheet : TimeSheetRepository::getFromUserByDate($model->timeSheet->user, $date);

        if (is_null($timeSheet)) {
            return null;
        }

        return TimeSheetDay::q()
            ->constraints([
                ['TimeSheetDay.timeSheet_id', '=', $timeSheet->id],
                ['TimeSheetDay.date', '=', $date->toDateString()],
            ])
            ->first();
    }

    public function getApprovedLeaveRequestDays($relations = ['leaveRequest.userLeaveBankType.type'])
    {
        $model = $this->getModel();

        /** @var LeaveRequestDay[]|Collection $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '=', $model->date->toDateString()],
                ['IN', 'Workflow.status', [
                    Statuses\ApprovedType::ID,
                ]],
            ],
            'relations' => $relations,
            'returnType' => 'model',
        ]);

        return $leaveRequestDays;
    }

    public function getAllLeaveRequestDays($relations = ['leaveRequest.userLeaveBankType.type', 'leaveRequest.workflow'])
    {
        $model = $this->getModel();

        /** @var LeaveRequestDay[]|Collection $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '=', $model->date->toDateString()],
                ['IN', 'Workflow.status', [
                    Statuses\NewType::ID,
                    Statuses\SubmittedType::ID,
                    Statuses\PartiallyApprovedType::ID,
                    Statuses\ApprovedType::ID,
                ]],
            ],
            'relations' => $relations,
            'returnType' => 'model',
        ]);

        return $leaveRequestDays;
    }

    /** @throws InvalidArgumentException */
    public function getApprovedLeaveRequestDaysOnPeriod(array $relations = ['leaveRequest.userLeaveBankType.type'])
    {
        $model = $this->getModel();

        if (!$model->isGlide || $model->glideDurationType == 'D') {
            throw new InvalidArgumentException('shift type');
        }

        /** @var LeaveRequestDay[]|Collection $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '>=', $model->glidePeriodStart->toDateString()],
                ['LeaveRequestDay.date', '<=', $model->glidePeriodEnd->toDateString()],
                ['IN', 'Workflow.status', [
                    Statuses\ApprovedType::ID,
                ]],
            ],
            'relations' => $relations,
            'returnType' => 'model',
        ]);

        return $leaveRequestDays;
    }

    /** @throws InvalidArgumentException */
    public function getAllLeaveRequestDaysOnPeriod(array $relations = ['leaveRequest.userLeaveBankType.type', 'leaveRequest.workflow'])
    {
        $model = $this->getModel();

        if (!$model->isGlide || $model->glideDurationType == 'D') {
            throw new InvalidArgumentException('shift type');
        }

        /** @var LeaveRequestDay[]|Collection $leaveRequestDays */
        $leaveRequestDays = LeaveRequestDayRepository::getMany([
            'constraints' => [
                ['User.id', '=', $model->timeSheet->payRunItem->user_id],
                ['LeaveRequestDay.date', '>=', $model->glidePeriodStart->toDateString()],
                ['LeaveRequestDay.date', '<=', $model->glidePeriodEnd->toDateString()],
                ['IN', 'Workflow.status', [
                    Statuses\NewType::ID,
                    Statuses\SubmittedType::ID,
                    Statuses\PartiallyApprovedType::ID,
                    Statuses\ApprovedType::ID,
                ]],
            ],
            'relations' => $relations,
            'returnType' => 'model',
        ]);

        return $leaveRequestDays;
    }

    /** @throws InvalidArgumentException */
    public static function getOneFromUserByDate(User $user, Carbon $date = null, array $options = []):TimeSheetDay
    {
        $date = is_null($date) ? Carbon::today() : $date->copy();
        $joins = isset($options['joins']) && is_array($options['joins']) ? $options['joins'] : [];
        $constraints = isset($options['constraints']) && is_array($options['constraints']) ? $options['constraints'] : [];

        $joins[] = ['TimeSheet', 'TimeSheet.id', '=', 'TimeSheetDay.timeSheet_id', 'inner'];
        $joins[] = ['PayRunItem', 'PayRunItem.id', '=', 'TimeSheet.payRunItem_id', 'inner'];

        $constraints[] = ['PayRunItem.user_id', '=', $user->id];
        $constraints[] = ['TimeSheetDay.date', '=', $date->toDateString()];

        $options['joins'] = $joins;
        $options['constraints'] = $constraints;
        $options['returnType'] = 'model';

        /** @var TimeSheetDay $ret */
        $ret = static::getOneOrFail($options);

        return $ret;
    }

    /** @throws InvalidArgumentException */
    public static function getMyTodayRecord(array $relations = []):TimeSheetDay
    {
        return static::getOneFromUserByDate(CurrentUser::getUser(), null, [
            'relations' => $relations,
        ]);
    }

    /** @throws InvalidValueException */
    public function getMissingHoursFromGlidePeriod(): float
    {
        $model = $this->getModel();
        $day = $model->glideFirstDayFromPeriod;

        $siblings = $model->getManyGlideSiblings([
            'timeSheetDayTimes.excessTimes',
        ]);

        if (!is_null($day->excessTime)) {
            $excessHours = $day->excessTime->totalHours;
        } else {
            $excessHours = 0;
        }

        $publicHolidayHours = 0;

        foreach ($siblings as $sibling) {
            foreach ($sibling->timeSheetDayTimes as $timeSheetDayTime) {
                foreach ($timeSheetDayTime->excessTimes as $excessTime) {
                    $excessHours += $excessTime->totalHours;
                }
            }

            $publicHolidayHours += $sibling->hoursPublicHoliday;
        }

        $ordinary = $day->glidePeriodHoursTotal - $excessHours;

        return $day->glidePeriodHoursExpected - $ordinary;
    }

    /**
     * TODO - TODO_SILICON: Holiday structure has been changed on Silicon
    */
    public function hasHoliday(): bool
    {
        $model = $this->getModel();

        foreach ($model->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::isType(HolidayType::class)) {
                return true;
            }
        }

        return false;
    }

    //endregion --\\   Data fetching methods   //--

    //region --\\   Issues methods   //--

    /**
     * @return ModelIssue[]|EloquentCollection
     * @throws UnauthorizedActionException
     */
    public function getSubmitIssues(): EloquentCollection|array
    {
        $model = $this->getModel();

        /** @var ModelIssue[]|SupportCollection $ret */
        $ret = new EloquentCollection;

        $issues = $model->timeSheet->repository->getActiveIssuesByStatusByLevel(Statuses\SubmittedType::class, StopperLevel::class, false, false);

        foreach ($issues as $issue) {
            if ($issue->rel_type == UserShiftDay::class) {
                /** @var UserShiftDay $usd */
                $usd = $issue->rel;

                if (is_null($usd)) {
                    $issue->delete();

                    continue;
                }

                if ($issue->typeClass::isType(UnderRecordedHoursDayType::class)) {
                    if ($usd->date->isSameDay($model->date)) {
                        $ret->push($issue);
                    }
                } elseif ($issue->typeClass::isType(UnderRecordedHoursPeriodType::class)) {
                    if ($usd->date->gte($model->glidePeriodStart) && $usd->date->lte($model->glidePeriodEnd)) {
                        $ret->push($issue);
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * @return StructTimeSheetIssue[]|SupportCollection
     * @throws UnauthorizedActionException
     */
    public function getApproveIssues()
    {
        $model = $this->getModel();

        /** @var StructTimeSheetIssue[]|SupportCollection $ret */
        $ret = collect([]);

        $issues = $model->timeSheet->repository->getActiveIssuesByStatusByLevel(Statuses\ApprovedType::class, StopperLevel::class, false, false);

        foreach ($issues as $issue) {
            if ($issue->rel_type == UserShiftDay::class) {
                /** @var UserShiftDay $usd */
                $usd = $issue->rel;

                if (is_null($usd)) {
                    $issue->delete();

                    continue;
                }

                if ($model->isGlide && $model->glideDurationType != 'D') {
                    if ($usd->date->gte($model->glidePeriodStart) && $usd->date->lte($model->glidePeriodEnd)) {
                        $ret->push($issue);
                    }
                } else {
                    if ($usd->date->isSameDay($model->date)) {
                        $ret->push($issue);
                    }
                }
            }
        }

        return $ret;
    }

    //endregion --\\   Issues methods   //--
}
