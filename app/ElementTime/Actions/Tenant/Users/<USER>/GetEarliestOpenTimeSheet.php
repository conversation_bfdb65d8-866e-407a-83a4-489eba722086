<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Users\User;

use Element\ElementTime\Actions\Tenant\Users\BaseUsersAction;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\User;

class GetEarliestOpenTimeSheet extends BaseUsersAction
{
    /** @throws \Throwable */
    public function handle(User $user, User|null $actor = null): TimeSheet|null
    {
        // if (is_null($actor)) {
        //     $actor = $user;
        // }

        // TODO: Implement this here - not in the model

        return $user->getOldestEditableTimeSheet();
    }
}
