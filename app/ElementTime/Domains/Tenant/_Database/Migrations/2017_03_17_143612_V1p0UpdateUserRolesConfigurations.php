<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();
        Schema::table('User', function (Blueprint $table) {
            $table->boolean('hasMultipleRoles');
            $table->boolean('canDoHigherDuties');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'hasMultipleRoles')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasMultipleRoles');
            });
        }
        if (Schema::hasColumn('User', 'canDoHigherDuties')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('canDoHigherDuties');
            });
        }
    }
};
