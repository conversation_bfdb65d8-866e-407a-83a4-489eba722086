<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\PayRuns\PayRun;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\BaseGetOptionsForLeaveRequest;
use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetMaximumOpenDateForSchedule;
use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetMinimumOpenDateForSchedule;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Database\Eloquent\Collection;

class GetOpenAndFuturePayRunsForSchedule extends BaseGetOptionsForLeaveRequest
{
    /**
     * @return Collection<PayRun>|PayRun[]
     * @throws \Throwable
     * @todo - test it with eager load on test-service
     */
    public function handle(UserRoleSchedule $userRoleSchedule, User $actor, CarbonInterface|null $starDate = null, int $count = 10): Collection|array
    {
        $this->validateAccess($userRoleSchedule->userRole->user, $actor);

        $minDate = GetMinimumOpenDateForSchedule::make()->handle(userRoleSchedule: $userRoleSchedule, actor: $actor);

        if (!is_null($starDate)) {
            $minDate = Carbon::make(max($minDate, $starDate));
        }

        $maxDate = GetMaximumOpenDateForSchedule::make()->handle($userRoleSchedule);

        $payRun = TenantSystemSettings::getPayRunByDate($minDate);
        $payRuns = Collection::make([$payRun]);

        while (
            $payRuns->count() < $count
            && (
                is_null($maxDate)
                || $maxDate->lt(Carbon::maxValue())
                || $payRun->startDate->lte($maxDate)
            )
        ) {
            $payRun = $payRun->next;
            $payRuns->push($payRun);
        }

        return $payRuns;
    }
}
