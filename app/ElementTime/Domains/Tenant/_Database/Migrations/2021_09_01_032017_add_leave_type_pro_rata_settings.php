<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        DB::statement('ALTER TABLE LeaveType MODIFY COLUMN created_at TIMESTAMP NULL DEFAULT NULL AFTER activityCode;');
        DB::statement('ALTER TABLE LeaveType MODIFY COLUMN updated_at TIMESTAMP NULL DEFAULT NULL AFTER created_at;');
        DB::statement('ALTER TABLE LeaveType MODIFY COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL AFTER updated_at;');

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->boolean('doesAllowStaffToUseProRata')->default(false)->after('isWithoutPay');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('LeaveType', 'doesAllowStaffToUseProRata')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('doesAllowStaffToUseProRata');
            });
        }
    }
};
