<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest\GetUserLeaveBankTypeOptionsForLeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\OrdinaryUserFlag;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Domains\Type\StatusTypes\InactiveStatus;

describe('Get user leave bank types from user', function () {
    it('returns only active options', function () {
        $user = User::q()->current()->constraints(OrdinaryUserFlag::getConstraints())->order('RAND()')->first();
        $manager = SuperUserFlag::getOne();

        $options = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        /** @var UserLeaveBankType $userLeaveBankType */
        $userLeaveBankType = $options->firstWhere('canTakeLeave', '=', true);
        $userLeaveBankType->status = InactiveStatus::ID;
        $userLeaveBankType->clearSave();

        $newOptions = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        expect($newOptions->count())->not()->toEqual($options->count())
            ->and($newOptions->firstWhere('id', '=', $userLeaveBankType->id))->toBeNull()
            ->and($options->firstWhere('id', '=', $userLeaveBankType->id))->not()->toBeNull()
        ;
    });

    it('returns only active options associated with active leave/rdo type', function () {
        $user = User::q()->order('RAND()')->first();
        $user->employeeType->doesHideLeave = false;
        $user->employeeType->clearSave();

        $manager = SuperUserFlag::getOne();
        $options = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        /** @var UserLeaveBankType $userLeaveBankType */
        $userLeaveBankType = $options
            ->whereNotNull('type_type')
            ->firstWhere('canTakeLeave', '=', true);
        $userLeaveBankType->type->status = InactiveStatus::ID;
        $userLeaveBankType->type->clearSave();

        $newOptions = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        expect($newOptions->count())->not()->toEqual($options->count())
            ->and($options->firstWhere('id','=', $userLeaveBankType->id))->not()->toBeNull()
            ->and($newOptions->firstWhere('id','=', $userLeaveBankType->id))->toBeNull()
        ;
    });

    it('returns only active options assigned to user leave bank', function () {
        $user = User::q()->order('RAND()')->first();
        $anotherUser = User::q()->constraints([['User.id', '<>', $user->id]])->order('RAND()')->first();
        $user->employeeType->doesHideLeave = false;
        $user->employeeType->clearSave();

        $manager = SuperUserFlag::getOne();
        $options = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        /** @var UserLeaveBankType $userLeaveBankType */
        $userLeaveBankType = $options
            ->whereNotNull('type_type')
            ->firstWhere('canTakeLeave', '=', true);

        $userLeaveBankType->userLeaveBank_id = $anotherUser->userLeaveBankTypes[0]->userLeaveBank_id;
        $userLeaveBankType->clearSave();

        $newOptions = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        expect($newOptions->count())->not()->toEqual($options->count())
            ->and($options->firstWhere('id','=', $userLeaveBankType->id))->not()->toBeNull()
            ->and($newOptions->firstWhere('id','=', $userLeaveBankType->id))->toBeNull()
        ;
    });

    it('returns only active options that can be used to take leave', function () {
        $user = User::q()->current()->constraints(OrdinaryUserFlag::getConstraints())->order('RAND()')->first();
        $manager = SuperUserFlag::getOne();

        $options = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        /** @var UserLeaveBankType $userLeaveBankType */
        $userLeaveBankType = $options->firstWhere('canTakeLeave', '=', true);
        $userLeaveBankType->canTakeLeave = false;
        $userLeaveBankType->clearSave();

        $newOptions = GetUserLeaveBankTypeOptionsForLeaveRequest::make()->handle($user, $manager);

        expect($newOptions->count())->not()->toEqual($options->count())
            ->and($newOptions->firstWhere('id', '=', $userLeaveBankType->id))->toBeNull()
            ->and($options->firstWhere('id', '=', $userLeaveBankType->id))->not()->toBeNull()
        ;
    });
});
