<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Support\Enums;

use Element\Core\Enums\Attributes\ElementEnumCase;
use Element\Core\Enums\Contracts\ElementEnumContract;
use Element\Core\Enums\Traits\ElementEnum;

enum RecordedVia : string implements ElementEnumContract
{
    use ElementEnum;

    const DEFAULT = self::Unknown;

    #[ElementEnumCase]
    case Unknown = 'unknown';

    #[ElementEnumCase]
    case Automatically = 'automatically';

    #[ElementEnumCase]
    case Api = 'api';

    #[ElementEnumCase(
        title: 'App real-time recorder',
    )]
    case AppTimer = 'app-timer';

    #[ElementEnumCase(
        title: 'App\'s timesheet',
    )]
    case AppTimeSheet = 'app-time-sheet';

    #[ElementEnumCase(
        title: 'Auto complete',
    )]
    case AutoComplete = 'auto-complete';

    #[ElementEnumCase(
        title: 'Copy from other timesheet',
    )]
    case CopyTimeSheet = 'copy-timesheet';

    #[ElementEnumCase(
        title: 'Copy from schedule',
    )]
    case CopySchedule = 'copy-schedule';

    #[ElementEnumCase(
        title: 'Import tool',
    )]
    case Import = 'import';

    #[ElementEnumCase]
    case Web = 'web';
}
