#!/bin/bash
#
# Edge case testing for optimized deployment scripts
#

set -euo pipefail

# Test configuration
readonly TEST_DIR="/tmp/deploy_edge_test"
readonly MOCK_AWS_DIR="${TEST_DIR}/mock_aws"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m' # No Color

log_test() {
    echo -e "${GREEN}[EDGE-TEST]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# Setup mock AWS environment with edge cases
setup_mock_aws_edge_cases() {
    log_test "Setting up mock AWS environment for edge cases"
    
    mkdir -p "$MOCK_AWS_DIR"
    
    cat > "${MOCK_AWS_DIR}/aws" << 'EOF'
#!/bin/bash
# Mock AWS CLI for edge case testing

case "$1" in
    "sts")
        case "$2" in
            "get-caller-identity")
                echo '{"UserId":"AIDACKCEVSQ6C2EXAMPLE","Account":"************","Arn":"arn:aws:iam::************:user/test"}'
                ;;
        esac
        ;;
    "autoscaling")
        case "$2" in
            "describe-auto-scaling-groups")
                # Simulate edge case: both ASGs have instances (deployment in progress)
                if [[ "$*" =~ --auto-scaling-group-names.*asg-etime-test-skt ]]; then
                    echo '{"AutoScalingGroups":[
                        {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                        {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"}
                    ]}'
                elif [[ "$*" =~ --auto-scaling-group-names.*asg-etime-test.*-b-14si ]]; then
                    echo '{"AutoScalingGroups":[{"DesiredCapacity":0,"AutoScalingGroupName":"'$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)'"}]}'
                else
                    # Default response
                    echo '{"AutoScalingGroups":[
                        {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                        {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"}
                    ]}'
                fi
                ;;
            "update-auto-scaling-group")
                echo "Mock: Updated ASG $(echo "$*" | grep -o 'asg-etime-[^[:space:]]*')"
                ;;
        esac
        ;;
    "elbv2")
        case "$2" in
            "describe-load-balancers")
                # Simulate load balancer not found
                if [[ "$*" =~ alb-etime-test-nonexistent ]]; then
                    echo '{"LoadBalancers":[]}'
                else
                    lb_name=$(echo "$*" | grep -o 'alb-etime-[^[:space:]]*' | head -1)
                    echo '{"LoadBalancers":[{"LoadBalancerArn":"arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/'$lb_name'/************3456"}]}'
                fi
                ;;
            "describe-listeners")
                echo '{"Listeners":[{
                    "ListenerArn":"arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/************3456/************3456",
                    "Port":443,
                    "DefaultActions":[{
                        "ForwardConfig":{
                            "TargetGroups":[{
                                "TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/************3456",
                                "Weight":100
                            }]
                        }
                    }]
                }]}'
                ;;
            "describe-target-groups")
                # Simulate target group not found for some cases
                if [[ "$*" =~ etime-tg.*nonexistent ]]; then
                    # Return empty result (no target groups found)
                    echo ""
                else
                    echo '{"TargetGroups":[
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-b-14si/************3456","TargetGroupName":"etime-tg-test-skt-b-14si"}
                    ]}'
                fi
                ;;
            "describe-target-health")
                # Simulate unhealthy targets initially
                if [[ "${HEALTH_FAIL_COUNT:-0}" -lt 2 ]]; then
                    export HEALTH_FAIL_COUNT=$((${HEALTH_FAIL_COUNT:-0} + 1))
                    echo "0"  # No healthy instances
                else
                    echo "2"  # Healthy instances after 2 attempts
                fi
                ;;
            "modify-listener")
                echo "Mock: Modified listener for traffic switching"
                ;;
        esac
        ;;
esac
EOF

    chmod +x "${MOCK_AWS_DIR}/aws"
    export PATH="${MOCK_AWS_DIR}:${PATH}"
    
    log_test "Mock AWS environment setup complete"
}

# Test deployment in progress scenario
test_deployment_in_progress() {
    log_test "Testing deployment in progress scenario"
    
    # This should fail because both ASGs have instances
    if ./deploy_blue_green_final_optimized.sh -e test -t skt 2>/dev/null; then
        log_error "Should have failed with deployment in progress"
        return 1
    else
        log_test "✓ Deployment in progress detection works"
    fi
}

# Test missing target group scenario
test_missing_target_group() {
    log_test "Testing missing target group scenario"
    
    # Create a temporary script that looks for nonexistent target group
    local temp_script="/tmp/test_missing_tg.sh"
    cat > "$temp_script" << 'EOF'
#!/bin/bash
source deploy_final_optimized.sh
get_tg_arn_by_name "etime-tg-test-nonexistent-b-14si"
EOF
    chmod +x "$temp_script"
    
    if "$temp_script" 2>/dev/null; then
        log_error "Should have failed with missing target group"
        rm -f "$temp_script"
        return 1
    else
        log_test "✓ Missing target group detection works"
    fi
    
    rm -f "$temp_script"
}

# Test health check timeout scenario
test_health_check_timeout() {
    log_test "Testing health check timeout scenario (shortened for testing)"
    
    # Create a temporary script with very short timeout
    local temp_script="/tmp/test_health_timeout.sh"
    cat > "$temp_script" << 'EOF'
#!/bin/bash
set -euo pipefail

# Override timeout for testing
HEALTH_CHECK_TIMEOUT=5  # 5 seconds
HEALTH_CHECK_INTERVAL=1  # 1 second
MIN_EC2_HEALTHY_COUNT=1

source deploy_final_optimized.sh

# Test the health check function directly
check_target_group_health "test" "etime-tg-test-skt-b-14si"
EOF
    chmod +x "$temp_script"
    
    # Reset health fail counter
    unset HEALTH_FAIL_COUNT
    
    if timeout 10 "$temp_script" 2>/dev/null; then
        log_test "✓ Health check eventually succeeds"
    else
        log_test "✓ Health check timeout works as expected"
    fi
    
    rm -f "$temp_script"
}

# Test invalid parameters
test_invalid_parameters() {
    log_test "Testing invalid parameter combinations"
    
    # Test invalid environment
    if ./deploy_final_optimized.sh -e invalid 2>/dev/null; then
        log_error "Should have failed with invalid environment"
        return 1
    else
        log_test "✓ Invalid environment validation works"
    fi
    
    # Test invalid type in blue-green script
    if ./deploy_blue_green_final_optimized.sh -e test -t invalid 2>/dev/null; then
        log_error "Should have failed with invalid type"
        return 1
    else
        log_test "✓ Invalid type validation works"
    fi
    
    # Test missing required parameters
    if ./deploy_final_optimized.sh 2>/dev/null; then
        log_error "Should have failed with missing parameters"
        return 1
    else
        log_test "✓ Missing parameter validation works"
    fi
}

# Test concurrent deployment detection
test_concurrent_deployment() {
    log_test "Testing concurrent deployment detection"

    # The mock AWS is set up to return both ASGs with instances for socket type
    # This should trigger the "deployment in progress" error
    local output
    output=$(./deploy_blue_green_final_optimized.sh -e test -t skt 2>&1 || true)

    if echo "$output" | grep -q "deployment is running\|Unable to determine next color"; then
        log_test "✓ Concurrent deployment detection works"
    else
        log_error "Concurrent deployment detection failed"
        log_error "Output was: $output"
        return 1
    fi
}

# Test error recovery and cleanup
test_error_recovery() {
    log_test "Testing error recovery and cleanup"
    
    # Create a script that will fail during health checks
    local temp_script="/tmp/test_error_recovery.sh"
    cat > "$temp_script" << 'EOF'
#!/bin/bash
set -euo pipefail

# Mock a deployment that fails during health checks
TARGET="test"
ENV_VERSION="14si"
next_color="b"

source deploy_final_optimized.sh

# Test cleanup function
cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
EOF
    chmod +x "$temp_script"
    
    if "$temp_script" 2>/dev/null; then
        log_test "✓ Error recovery and cleanup works"
    else
        log_error "Error recovery test failed"
        rm -f "$temp_script"
        return 1
    fi
    
    rm -f "$temp_script"
}

# Cleanup test environment
cleanup_test_env() {
    log_test "Cleaning up edge case test environment"
    rm -rf "$TEST_DIR"
    unset HEALTH_FAIL_COUNT
    log_test "Cleanup complete"
}

# Main edge case test function
run_edge_case_tests() {
    log_test "Starting edge case testing"
    
    # Setup
    setup_mock_aws_edge_cases
    
    # Run edge case tests
    test_invalid_parameters
    test_deployment_in_progress
    test_concurrent_deployment
    test_missing_target_group
    test_health_check_timeout
    test_error_recovery
    
    # Cleanup
    cleanup_test_env
    
    log_test "All edge case tests completed successfully!"
}

# Run edge case tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    cd "$(dirname "${BASH_SOURCE[0]}")"
    run_edge_case_tests
fi
