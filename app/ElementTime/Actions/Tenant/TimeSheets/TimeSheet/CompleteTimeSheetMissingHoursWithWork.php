<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet;

use Element\Core\Exceptions\UnauthorizedActionException;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod\CompleteTimeSheetPeriodMissingHoursWithWork;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleTimeType;
use Element\ElementTime\Support\Database\EDB;

class CompleteTimeSheetMissingHoursWithWork extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheet $timeSheet,
        array $scheduleSettings,
    ): void
    {
        if (!$timeSheet->canAutoCompleteMissingHoursWithWork) {
            throw new UnauthorizedActionException([
                'action' => 'complete-missing-hours-with-work',
                'model' => 'timesheet',
                'reason' => 'Not allowed to complete timesheet missing hours with work records'
            ]);
        }

        EDB::tenantConn()->transaction(function () use ($timeSheet, $scheduleSettings) {
            $startDate = $timeSheet->startDate;
            $endDate = $timeSheet->endDate;

            foreach ($scheduleSettings as $scheduleSettingsItem) {
                $scheduledPayRun = ScheduledPayRun::q()->findOrFail($scheduleSettingsItem['scheduledPayRun_id']);

                if (
                    !$scheduledPayRun->startDate->lte($timeSheet->startDate)
                    || !$scheduledPayRun->endDate->gte($timeSheet->endDate)
                ) {
                    throw new \Exception('Scheduled pay run period does not cover time sheet period');
                }

                if (!$scheduledPayRun->userRoleSchedule->isActiveOnWholePeriod($startDate, $endDate)) {
                    throw new \Exception('Schedule assignment period does not cover time sheet period');
                }

                $userRoleTimeType = UserRoleTimeType::q()->findOrFail($scheduleSettingsItem['userRoleTimeType_id']);

                if (!$userRoleTimeType->isActiveOnWholePeriod($startDate, $endDate)) {
                    throw new \Exception('Time type assignment period does not cover time sheet period');
                }

                if (!isset($scheduleSettingsItem['type'])) {
                    throw new \Exception('Missing work type');
                }

                if (!isset($scheduleSettingsItem['group_id']) && $scheduleSettingsItem['type'] !== 'project') {
                    throw new \Exception('Missing Work group');
                }

                if (!isset($scheduleSettingsItem['model_id'])) {
                    throw new \Exception('Missing Project / Work-order');
                }

                if (!isset($scheduleSettingsItem['sub_id']) && $scheduleSettingsItem['type'] !== 'project') {
                    throw new \Exception('Missing work Activity / Sub-task');
                }

                if ($userRoleTimeType->userRole_id !== $scheduledPayRun->userRoleSchedule->userRole_id) {
                    throw new \Exception('Time type assignment does not match schedule assignment');
                }

                foreach ($scheduledPayRun->periods as $period) {
                    CompleteTimeSheetPeriodMissingHoursWithWork::make()->handle(
                        timeSheetPeriod: $period->timeSheetPeriod,
                        userRoleTimeType: $userRoleTimeType,
                        workType: $scheduleSettingsItem['type'],
                        group_id: $scheduleSettingsItem['group_id'] ?? null,
                        model_id: $scheduleSettingsItem['model_id'],
                        sub_id: $scheduleSettingsItem['sub_id'] ?? null,
                        notes: $scheduleSettingsItem['notes'] ?? '',
                    );
                }
            }

            CalculateTimeSheet::make()->handle(timeSheet: $timeSheet, allCalculations: true, doesFailOnExceptions: true);
            $timeSheet->repository->updateIssues();
            $timeSheet->sendBroadcast();
        });
    }
}
