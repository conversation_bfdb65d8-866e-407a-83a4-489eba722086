<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        if (!Schema::hasColumn('UserRole', 'created_at')) {
            Schema::table('UserRole', function (Blueprint $table) {
                $table->timestamps();
            });
        }

        if (!Schema::hasColumn('UserShift', 'created_at')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->timestamps();
            });
        }

        if (!Schema::hasColumn('UserPayType', 'created_at')) {
            Schema::table('UserPayType', function (Blueprint $table) {
                $table->timestamps();
            });
        }

        //region Table: TimeType

        Schema::create('TimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();
            $table->string('hourCode');

            // Data
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('isOrdinary')->default(false);

            // Status
            $table->char('status', 1)->default('A');

            // Model control
            $table->boolean('is_deletable')->default(true);
            $table->timestamps();
        });
        DB::update('ALTER TABLE TimeType AUTO_INCREMENT = 101;');

        //endregion Table: TimeType

        //region Table: ShiftTimeType

        Schema::create('ShiftTimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('shift_id');
            $table->foreign('shift_id', 'stt_s_fn')->references('id')->on('Shift')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('timeType_id');
            $table->foreign('timeType_id', 'stt_tt_fn')->references('id')->on('TimeType')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('excessTimeGroup_id');
            $table->foreign('excessTimeGroup_id', 'stt_etg_fn')->references('id')->on('ExcessTimeGroup')->onUpdate('cascade')->onDelete('cascade');

            $table->text('comments')->nullable();
            $table->timestamps();
        });

        //endregion Table: ShiftTimeType

        //region Table: UserRoleMaster

        Schema::create('UserRoleMaster', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urm_ur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('cascade');

            $table->date('startDate');
            $table->date('endDate')->nullable();

            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserRoleMaster

        //region Table: UserRoleProject

        Schema::create('UserRoleProject', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urp_ur_fn')->references('id')->on('UserRole')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('project_id')->nullable();
            $table->foreign('project_id', 'ur_p_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('projectActivity_id')->nullable();
            $table->foreign('projectActivity_id', 'ur_pa_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('restrict');

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();

            $table->date('startDate');
            $table->date('endDate')->nullable();

            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserRoleProject

        //region Table: UserShift

        DB::statement('ALTER TABLE UserShift MODIFY COLUMN isLocked TINYINT(1) NOT NULL DEFAULT 0 AFTER id;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN startDate DATE AFTER userRole_id;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN endDate DATE AFTER startDate;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN doesDeductBreak TINYINT(1) NOT NULL DEFAULT 0 AFTER endDate;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN minimumHoursToDeductBreak DECIMAL(16,10) AFTER doesDeductBreak;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN doesWorkOnPublicHolidays TINYINT(1) NOT NULL DEFAULT 0 AFTER minimumHoursToDeductBreak;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glidePublicHolidaysRateType CHAR(1) NOT NULL DEFAULT "A" AFTER doesWorkOnPublicHolidays;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glidePublicHolidaysCustomRate DECIMAL(16,10) AFTER glidePublicHolidaysRateType;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glidePublicHolidaysRateTypeWeekend CHAR(1) NOT NULL DEFAULT "A" AFTER glidePublicHolidaysCustomRate;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glidePublicHolidaysCustomRateWeekend DECIMAL(16,10) AFTER glidePublicHolidaysRateTypeWeekend;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glideLeaveRateType CHAR(1) NOT NULL DEFAULT "A" AFTER glidePublicHolidaysCustomRateWeekend;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glideLeaveCustomRate DECIMAL(16,10) AFTER glideLeaveRateType;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glideLeaveRateTypeWeekend CHAR(1) NOT NULL DEFAULT "A" AFTER glideLeaveCustomRate;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN glideLeaveCustomRateWeekend DECIMAL(16,10) AFTER glideLeaveRateTypeWeekend;');
        DB::statement('ALTER TABLE UserShift MODIFY COLUMN status CHAR(1) DEFAULT "A" AFTER glideLeaveCustomRateWeekend;');

        //endregion Table: UserShift

        //region Table: UserShiftTimeType

        Schema::create('UserShiftTimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userShift_id');
            $table->foreign('userShift_id', 'ustt_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('timeType_id');
            $table->foreign('timeType_id', 'ustt_tt_fn')->references('id')->on('TimeType')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('doesUseParentDates')->default(true);
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->boolean('isMaster')->default(false);
            $table->boolean('doesHaveProjects')->default(false);

            $table->string('name')->nullable();
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserShiftTimeType

        //region Table: UserShiftTimeTypeExcessTimeGroup

        Schema::create('UserShiftTimeTypeExcessTimeGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userShiftTimeType_id');
            $table->foreign('userShiftTimeType_id', 'usttetg_ustt_fn')->references('id')->on('UserShiftTimeType')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('excessTimeGroup_id');
            $table->foreign('excessTimeGroup_id', 'usttetg_etg_fn')->references('id')->on('ExcessTimeGroup')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('doesUseParentDates')->default(true);
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->string('name')->nullable();
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserShiftTimeTypeExcessTimeGroup

        //region Table: UserShiftProject

        Schema::create('UserShiftProject', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userShift_id');
            $table->foreign('userShift_id', 'usp_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('userShiftTimeType_id')->nullable(); // Child of time type
            $table->foreign('userShiftTimeType_id', 'usp_ustt_fn')->references('id')->on('UserShiftTimeType')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('project_id');
            $table->foreign('project_id', 'usp_p_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('doesUseParentDates')->default(true);
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();

            $table->boolean('doesAllowAllActivities')->default(true);

            $table->string('name')->nullable();
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserShiftProject

        //region Table: UserShiftProjectActivity

        Schema::create('UserShiftProjectActivity', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userShiftProject_id');
            $table->foreign('userShiftProject_id', 'uspa_usp_fn')->references('id')->on('UserShiftProject')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('projectActivity_id');
            $table->foreign('projectActivity_id', 'uspa_pa_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('doesUseParentDates')->default(true);
            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();

            $table->string('name')->nullable();
            $table->text('comments')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserShiftProjectActivity

        //region Table: TimeSheetDayTime

        Schema::create('TimeSheetDayTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheetDay_id');
            $table->foreign('timeSheetDay_id', 'tsdt_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('cascade');

            // Relations/type data
            $table->unsignedBigInteger('userShiftDay_id');
            $table->foreign('userShiftDay_id', 'tsdt_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('userShiftTimeType_id')->nullable();
            $table->foreign('userShiftTimeType_id', 'tsdt_ustt_fn')->references('id')->on('UserShiftTimeType')->onUpdate('cascade')->onDelete('restrict');

            $table->string('recordType'); // ID of type class
            $table->string('type_type')->nullable();
            $table->unsignedBigInteger('type_id')->nullable();
            $table->string('model_type')->nullable(); // In the case of leave: the LeaveRequestDay record
            $table->unsignedBigInteger('model_id')->nullable();

            $table->string('customName')->nullable();
            $table->string('customProjectCode')->nullable();
            $table->string('customActivityCode')->nullable();

            // Time/calculation data
            $table->unsignedInteger('minutes')->nullable();
            $table->decimal('adjustedHours', 16, 10)->nullable();
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            // Additional data
            $table->string('name')->nullable();
            $table->text('notes')->nullable();

            // System data
            $table->boolean('arePenaltiesCalculated')->default(false);
            $table->boolean('arePenaltiesCalculating')->default(false);
            $table->timestamps();
        });

        //endregion Table: TimeSheetDayTime

        //region Table: TimeSheetDayTimeBreak

        Schema::create('TimeSheetDayTimeBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('timeSheetDayTime_id');
            $table->foreign('timeSheetDayTime_id', 'tsdtb_tsdt_fn')->references('id')->on('TimeSheetDayTime')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('userShiftDayTimeBreak_id')->nullable();
            $table->foreign('userShiftDayTimeBreak_id', 'tsdtb_usdtb_fn')->references('id')->on('UserShiftDayTimeBreak')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('leaveRequestDayBreak_id')->nullable();
            $table->foreign('leaveRequestDayBreak_id', 'tsdtb_lrdb_fn')->references('id')->on('LeaveRequestDayBreak')->onUpdate('cascade')->onDelete('cascade');

            // Time/calculation data
            $table->unsignedInteger('minutes')->nullable();
            $table->decimal('adjustedHours', 16, 10)->nullable();
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            // Additional data
            $table->string('name')->nullable();
            $table->text('notes')->nullable();

            // System data
            $table->timestamps();
        });

        //endregion Table: TimeSheetDayTimeBreak

        //region Table: TimeSheetExcessTime

        Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
            $table->unsignedBigInteger('timeSheetDayTime_id')->nullable()->after('type');
            $table->foreign('timeSheetDayTime_id', 'tset_tsdt_fn')->references('id')->on('TimeSheetDayTime')->onUpdate('cascade')->onDelete('cascade');
            $table->unique(['timeSheetDayTime_id'], 'tset_tsdt_uq');

            $table->unsignedBigInteger('userShiftTimeType_id')->nullable()->after('userShiftDay_id');
            $table->foreign('userShiftTimeType_id', 'tset_ustt_fn')->references('id')->on('UserShiftTimeType')->onUpdate('cascade')->onDelete('set null');
        });

        //endregion Table: TimeSheetExcessTime
    }

    public function down(): void
    {
        //region Table: TimeSheetExcessTime

        try {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropForeign('tset_tsdt_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetExcessTime', 'timeSheetDayTime_id')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('timeSheetDayTime_id');
            });
        }

        try {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropForeign('tset_ustt_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheetExcessTime', 'userShiftTimeType_id')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('userShiftTimeType_id');
            });
        }

        //endregion Table: TimeSheetExcessTime

        //region Table: TimeSheetDayTimeBreak

        Schema::dropIfExists('TimeSheetDayTimeBreak');

        //endregion Table: TimeSheetDayTimeBreak

        //region Table: TimeSheetDayTime

        Schema::dropIfExists('TimeSheetDayTime');

        //endregion Table: TimeSheetDayTime

        //region Table: UserShiftProjectActivity

        Schema::dropIfExists('UserShiftProjectActivity');

        //endregion Table: UserShiftProjectActivity

        //region Table: UserShiftProject

        Schema::dropIfExists('UserShiftProject');

        //endregion Table: UserShiftProject

        //region Table: UserShiftTimeTypeExcessTimeGroup

        Schema::dropIfExists('UserShiftTimeTypeExcessTimeGroup');

        //endregion Table: UserShiftTimeTypeExcessTimeGroup

        //region Table: UserShiftTimeType

        Schema::dropIfExists('UserShiftTimeType');

        //endregion Table: UserShiftTimeType

        //region Table: UserShift

        // OLD not used field
        try {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropForeign('user_shift_role_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('UserShift', 'role_id')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('role_id');
            });
        }

        //endregion Table: UserShift

        //region Table: UserRoleProject

        Schema::dropIfExists('UserRoleProject');

        //endregion Table: UserRoleProject

        //region Table: UserRoleMaster

        Schema::dropIfExists('UserRoleMaster');

        //endregion Table: UserRoleMaster

        //region Table: ShiftTimeType

        Schema::dropIfExists('ShiftTimeType');

        //endregion Table: ShiftTimeType

        //region Table: TimeType

        Schema::dropIfExists('TimeType');

        //endregion Table: TimeType
    }
};
