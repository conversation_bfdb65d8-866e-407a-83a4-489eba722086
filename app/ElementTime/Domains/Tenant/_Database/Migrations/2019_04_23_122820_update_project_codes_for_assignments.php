<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->string('projectCodeMask')->default('[[pc]]/[[ac]]');
        });

        Schema::table('Project', function (Blueprint $table) {
            $table->string('activityCode')->nullable()->after('projectCode');
        });

        Schema::table('AllowanceType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('UserAllowanceType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('LeaveType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('UserLeaveType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('UserRosteredTimeOffType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('Settings', function (Blueprint $table) {
            $table->string('excessTimeDefaultProjectCode')->nullable();
            $table->string('excessTimeDefaultActivityCode')->nullable();
        });

        Schema::table('UserExcessTimeBalance', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('PenaltyType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });

        Schema::table('UserPenaltyType', function (Blueprint $table) {
            $table->string('projectCode')->nullable();
            $table->string('activityCode')->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserPenaltyType', 'activityCode')) {
            Schema::table('UserPenaltyType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('UserPenaltyType', 'projectCode')) {
            Schema::table('UserPenaltyType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'activityCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'projectCode')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('UserExcessTimeBalance', 'activityCode')) {
            Schema::table('UserExcessTimeBalance', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('UserExcessTimeBalance', 'projectCode')) {
            Schema::table('UserExcessTimeBalance', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('Settings', 'excessTimeDefaultActivityCode')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeDefaultActivityCode');
            });
        }

        if (Schema::hasColumn('Settings', 'excessTimeDefaultProjectCode')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeDefaultProjectCode');
            });
        }

        if (Schema::hasColumn('UserRosteredTimeOffType', 'activityCode')) {
            Schema::table('UserRosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('UserRosteredTimeOffType', 'projectCode')) {
            Schema::table('UserRosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'activityCode')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'projectCode')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('UserLeaveType', 'activityCode')) {
            Schema::table('UserLeaveType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('UserLeaveType', 'projectCode')) {
            Schema::table('UserLeaveType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('LeaveType', 'activityCode')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('LeaveType', 'projectCode')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('UserAllowanceType', 'activityCode')) {
            Schema::table('UserAllowanceType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('UserAllowanceType', 'projectCode')) {
            Schema::table('UserAllowanceType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'projectCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('AllowanceType', 'projectCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('projectCode');
            });
        }

        if (Schema::hasColumn('Project', 'activityCode')) {
            Schema::table('Project', function (Blueprint $table) {
                $table->dropColumn('activityCode');
            });
        }

        if (Schema::hasColumn('Settings', 'projectCodeMask')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('projectCodeMask');
            });
        }
    }
};
