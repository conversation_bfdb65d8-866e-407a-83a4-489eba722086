form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$f.submit()' name='$f.form')
  .panel.panel-default
    .panel-heading
      h4.h4.m0 Cancel leave request
    .panel-body
      .row
        .col-xs-12
          .form-group
            label.label-control(for='LeaveCancel_inputReason') Reason for cancelling this request
            textarea.form-control(id='LeaveCancel_inputReason' ng-model='$f.data.reason' required='required')
      //.row
      //  .col-xs-12.col-md-6
      //    elt-attachment-file(id='LeaveCancel_attachmentFile' elt-label='Attachment file' name='cancel_attachmentFile' ng-model='$f.data.attachmentFile' elt-file-options='{{settings.form.WorkflowApproval.attachmentFileOptions}}')
      .row
        .col-xs-12
          hr.mt0.mb-lg

      .row.mb-lg(ng-if='!!$ctrl.leave.data.staffCoveringLeave')
        .col-xs-12
          .m0.p0.list-group
            .list-group-item.list-group-item-warning.text-warning
              h4.h4.m0 {{ $ctrl.leave.data.staffCoveringLeave.fullName }} is acting on higher duties on the period of this leave request.
              p.mb0.text-warning You might want to let them know that this leave is being cancelled.

      .row.visible-sm.visible-md.visible-lg
        .col-xs-12.text-right
          button.btn.btn-default.ml(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Don't cancel
          button.btn.btn-danger.ml(type='submit' ng-disabled='!!$f.isSubmitting')
            | Cancel request
      .row.visible-xs
        .col-xs-6
          button.btn.btn-default.btn-block(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Don't cancel
        .col-xs-6
          button.btn.btn-danger.btn-block(type='submit' ng-disabled='!!$f.isSubmitting')
            | Cancel request
