<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest;

use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetMinimumOpenDateForSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Database\Eloquent\Collection;

class GetUserRoleScheduleOptionsForLeaveRequest extends BaseGetOptionsForLeaveRequest
{
    /**
     * @return Collection<UserRoleSchedule>|UserRoleSchedule[]
     * @throws \Throwable
     */
    public function handle(User $user, User $actor): Collection|array
    {
        $this->validateAccess($user, $actor);

        $minDate = TenantSystemSettings::getEarliestOpenPayRun()?->startDate;

        $userRoleSchedules = UserRoleSchedule::q()
            ->notPast($minDate)
            ->constraints([
                ['User.id', '=', $user->id],
                ['Role.status', '=', ActiveStatus::ID],
                ...UserRole::availabilityConstraints()->notPast($minDate)->build(),
            ])
            ->many();

        return $userRoleSchedules->filter(function (UserRoleSchedule $userRoleSchedule) use ($actor) {
            $minDate = GetMinimumOpenDateForSchedule::make()->handle(
                userRoleSchedule: $userRoleSchedule,
                actor: $actor,
            );

            if ($userRoleSchedule->availability()->on($minDate)->isPast()) {
                return false;
            }

            return $userRoleSchedule->userRole->repository->canBeViewedByUser($actor, true);
        });
    }
}
