<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: WorkOrderGroup
        Schema::create('WorkOrderGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('name');
            $table->text('description')->nullable();
            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();

            $table->unique(['name'], 'wog_name_uq');
        });
        //endregion Table: WorkOrderGroup

        //region Table: WorkOrderGroupRelationSet
        Schema::create('WorkOrderGroupRelationSet', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('name');
            $table->text('description')->nullable();
            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
        });
        //endregion Table: WorkOrderGroupRelationSet

        //region Table: WorkOrderGroupRelationSetClassificationItem
        Schema::create('WorkOrderGroupRelationSetClassificationItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('workOrderGroupRelationSet_id');
            $table->foreign('workOrderGroupRelationSet_id', 'wogrsci_wogrs_fn')
                ->references('id')
                ->on('WorkOrderGroupRelationSet')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('workOrderClassificationItem_id');
            $table->foreign('workOrderClassificationItem_id', 'wogrsci_woci_fn')
                ->references('id')
                ->on('WorkOrderClassificationItem')
                ->onUpdate('cascade')
                ->onDelete('restrict');

            $table->unsignedTinyInteger('level');

            $table->timestamps();

            $table->unique(['workOrderGroupRelationSet_id', 'level'], 'wogrsci_wogrs_level_uq');
            $table->unique(['workOrderGroupRelationSet_id', 'workOrderClassificationItem_id'], 'wogrsci_wogrs_woci_uq');
        });
        //endregion Table: WorkOrderGroupRelationSetClassificationItem

        //region Table: WorkOrderGroupRelationSetGroup
        Schema::create('WorkOrderGroupRelationSetGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('workOrderGroupRelationSet_id');
            $table->foreign('workOrderGroupRelationSet_id', 'wogrsg_wogrs_fn')
                ->references('id')
                ->on('WorkOrderGroupRelationSet')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('workOrderGroup_id');
            $table->foreign('workOrderGroup_id', 'wogrsg_wog_fn')
                ->references('id')
                ->on('WorkOrderGroup')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->timestamps();

            $table->unique(['workOrderGroupRelationSet_id', 'workOrderGroup_id'], 'wogrsg_wogrs_wog_uq');
        });
        //endregion Table: WorkOrderGroupRelationSetGroup

        //region Table: WorkOrderTypeGroup
        Schema::create('WorkOrderTypeGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('workOrderType_id');
            $table->foreign('workOrderType_id', 'wotg_wot_fn')
                ->references('id')
                ->on('WorkOrderType')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('workOrderGroup_id');
            $table->foreign('workOrderGroup_id', 'wotg_wog_fn')
                ->references('id')
                ->on('WorkOrderGroup')
                ->onUpdate('cascade')
                ->onDelete('restrict');

            $table->unsignedBigInteger('workOrderGroupRelationSetGroup_id')->nullable();
            $table->foreign('workOrderGroupRelationSetGroup_id', 'wotg_wogrsg_fn')
                ->references('id')
                ->on('WorkOrderGroupRelationSetGroup')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->text('description')->nullable();
            $table->char('status', 1) -> default(ActiveStatus::ID);

            $table->timestamps();

            $table->unique(['workOrderType_id', 'workOrderGroup_id', 'workOrderGroupRelationSetGroup_id'], 'wotg_rel_uq');
        });
        //endregion Table: WorkOrderTypeGroup

        //region Table: WorkOrderGroupAssignmentRule
        Schema::create('WorkOrderGroupAssignmentRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();
            $table->string('externalId')->nullable();

            $table->string('name')->unique('wogar_name_uq');
            $table->text('description')->nullable();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
        });
        //endregion Table: WorkOrderGroupAssignmentRule

        //region Table: WorkOrderGroupAssignmentRuleCondition
        Schema::create('WorkOrderGroupAssignmentRuleCondition', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('workOrderGroupAssignmentRule_id');
            $table->foreign('workOrderGroupAssignmentRule_id', 'wogarc_wogar_fn')
                ->references('id')
                ->on('WorkOrderGroupAssignmentRule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('parentWorkOrderGroupAssignmentRuleCondition_id')->nullable();
            $table->foreign('parentWorkOrderGroupAssignmentRuleCondition_id', 'wogarc_parent_fn')
                ->references('id')
                ->on('WorkOrderGroupAssignmentRuleCondition')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->string('type');
            $table->json('options')->nullable();
            $table->boolean('isNot')->default(false);
            $table->integer('order')->default(1);

            $table->timestamps();
        });
        //endregion Table: WorkOrderGroupAssignmentRuleCondition

        //region Table: WorkOrderGroupAssignmentRuleTarget
        Schema::create('WorkOrderGroupAssignmentRuleTarget', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('workOrderGroupAssignmentRule_id');
            $table->foreign('workOrderGroupAssignmentRule_id', 'wogart_wogar_fn')
                ->references('id')
                ->on('WorkOrderGroupAssignmentRule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('parentWorkOrderGroupAssignmentRuleTarget_id')->nullable();
            $table->foreign('parentWorkOrderGroupAssignmentRuleTarget_id', 'wogart_parent_fn')
                ->references('id')
                ->on('WorkOrderGroupAssignmentRuleTarget')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->string('type');
            $table->json('options')->nullable();
            $table->boolean('isNot')->default(false);
            $table->integer('order')->default(1);

            $table->timestamps();
        });
        //endregion Table: WorkOrderGroupAssignmentRuleTarget

        //region Table: UserWorkOrderGroup
        Schema::create('UserRoleWorkOrderGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urwog_ur_fn')
                ->references('id')
                ->on('UserRole')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('workOrderGroup_id');
            $table->foreign('workOrderGroup_id', 'urwog_wog_fn')
                ->references('id')
                ->on('WorkOrderGroup')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unsignedBigInteger('workOrderGroupAssignmentRule_id')->nullable();
            $table->foreign('workOrderGroupAssignmentRule_id', 'urwog_wogar_fn')
                ->references('id')
                ->on('WorkOrderGroupAssignmentRule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->char('status', 1)->default(ActiveStatus::ID);

            $table->timestamps();
        });
        //endregion Table: UserWorkOrderGroup

        //region Table: TimeSheetDayTimeWork
        Schema::table('TimeSheetDayTimeWork', function (Blueprint $table) {
            $table->unsignedBigInteger('workOrderGroup_id')->nullable()->after('timeSheetDayTime_id');
            $table->foreign('workOrderGroup_id', 'tsdtw_wog_fn')
                ->references('id')
                ->on('WorkOrderGroup')
                ->onUpdate('cascade')
                ->onDelete('restrict');

        });
        //endregion Table: TimeSheetDayTimeWork
    }

    public function down(): void
    {
        try {
            Schema::table('TimeSheetDayTimeWork', function (Blueprint $table) {
                $table->dropForeign('tsdtw_wog_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetDayTimeWork', 'workOrderGroup_id')) {
            Schema::table('TimeSheetDayTimeWork', function (Blueprint $table) {
                $table->dropColumn('workOrderGroup_id');
            });
        }

        Schema::dropIfExists('UserRoleWorkOrderGroup');
        Schema::dropIfExists('WorkOrderGroupAssignmentRuleTarget');
        Schema::dropIfExists('WorkOrderGroupAssignmentRuleCondition');
        Schema::dropIfExists('WorkOrderGroupAssignmentRule');
        Schema::dropIfExists('WorkOrderTypeGroup');
        Schema::dropIfExists('WorkOrderGroupRelationSetGroup');
        Schema::dropIfExists('WorkOrderGroupRelationSetClassificationItem');
        Schema::dropIfExists('WorkOrderGroupRelationSet');
        Schema::dropIfExists('WorkOrderGroup');
    }
};
