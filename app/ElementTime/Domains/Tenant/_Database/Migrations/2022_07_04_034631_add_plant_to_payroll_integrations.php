<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('PayrollUnifiedUserTimeSheetEntry', function (Blueprint $table) {
            $table->string('plantHireLedger')->nullable()->after('excessTimeType');
            $table->decimal('plantUsage', 16, 10)->nullable()->after('plantHireLedger');
            $table->decimal('plantHireHours', 16, 10)->nullable()->after('plantUsage');
            $table->decimal('plantHireKilometers', 16, 10)->nullable()->after('plantHireHours');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('PayrollUnifiedUserTimeSheetEntry', 'plantHireLedger')) {
            Schema::table('PayrollUnifiedUserTimeSheetEntry', function (Blueprint $table) {
                $table->dropColumn('plantHireLedger');
            });
        }
        if (Schema::hasColumn('PayrollUnifiedUserTimeSheetEntry', 'plantUsage')) {
            Schema::table('PayrollUnifiedUserTimeSheetEntry', function (Blueprint $table) {
                $table->dropColumn('plantUsage');
            });
        }
        if (Schema::hasColumn('PayrollUnifiedUserTimeSheetEntry', 'plantHireHours')) {
            Schema::table('PayrollUnifiedUserTimeSheetEntry', function (Blueprint $table) {
                $table->dropColumn('plantHireHours');
            });
        }
        if (Schema::hasColumn('PayrollUnifiedUserTimeSheetEntry', 'plantHireKilometers')) {
            Schema::table('PayrollUnifiedUserTimeSheetEntry', function (Blueprint $table) {
                $table->dropColumn('plantHireKilometers');
            });
        }
    }
};
