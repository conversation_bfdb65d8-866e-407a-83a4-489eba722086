<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Carbon\Carbon;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $weekDays = [
            1 => Carbon::MONDAY,
            2 => Carbon::TUESDAY,
            3 => Carbon::WEDNESDAY,
            4 => Carbon::THURSDAY,
            5 => Carbon::FRIDAY,
            6 => Carbon::SATURDAY,
            7 => Carbon::SUNDAY,
            8 => Carbon::MONDAY,
            9 => Carbon::TUESDAY,
            10 => Carbon::WEDNESDAY,
            11 => Carbon::THURSDAY,
            12 => Carbon::FRIDAY,
            13 => Carbon::SATURDAY,
            14 => Carbon::SUNDAY,
        ];

        $this->down();

        Schema::table('PayRunType', function (Blueprint $table) {
            $table->date('firstPayRunStartsOn');
            $table->integer('firstPayRunNumber')->default(1);
            $table->integer('dayPayRunCloses')->default(1); // If period is W|F|O - 1 to 14 // If period is M|Q - any integer
            $table->integer('dayPayRunIsProcessed')->default(1); // If period is W|F|O - 1 to 14 // If period is M|Q - any integer
            $table->time('timePayRunIsProcessed'); // Time format
            $table->integer('dayPayRunIsPaid')->default(1); // If period is W|F|O - 1 to 14 // If period is M|Q - any integer
        });

        Schema::table('PayRun', function (Blueprint $table) {
            $table->integer('number')->default(1);
            $table->dateTime('processingDateTime')->nullable();
            $table->date('payingDate')->nullable();
        });

        $payRunTypes = DB::table('PayRunType')->get();
        foreach ($payRunTypes as $payRunType) {
            /** @var PayRun[] $payRuns */
            $payRuns = DB::table('PayRun')->where('payRunType_id', $payRunType->id)->orderBy('startDate')->get();
            if (count($payRuns) > 0) {
                $payRun = $payRuns[0];
                $startDate = Carbon::parse($payRun->startDate);
                $number = 1;

                $dateCloses = $startDate->copy()->subDays(1)->addDays($payRunType->dueDaysPriorForTimesheet);
                if ($payRunType->period == 'M' || $payRunType->period == 'Q') {
                    $closes = $payRunType->dueDaysPriorForTimesheet;
                } else {
                    $nextWeek = $startDate->copy()->addWeeks(1);
                    $dayOfWeek = $dateCloses->dayOfWeek == 0 ? 7 : $dateCloses->dayOfWeek;
                    $closes = $dateCloses->gt($nextWeek) ? $dayOfWeek + 7 : $dayOfWeek;
                }

                $dateProcess = $startDate->copy()->subDays(1)->addDays($payRunType->processedAtDaysAfter);
                if ($payRunType->period == 'M' || $payRunType->period == 'Q') {
                    $process = $payRunType->processedAtDaysAfter;
                } else {
                    $nextWeek = $dateCloses->copy()->addWeeks(1);
                    $dayOfWeek = $dateProcess->dayOfWeek == 0 ? 7 : $dateProcess->dayOfWeek;
                    $process = $dateProcess->gt($nextWeek) ? $dayOfWeek + 7 : $dayOfWeek;
                }
                $time = $payRunType->processedAtTime;

                $datePaid = $dateProcess->copy()->addDays(3);
                $paid = $datePaid->dayOfWeek == 0 || $datePaid->dayOfWeek == 6 ? 1 : $datePaid->dayOfWeek;

                DB::table('PayRunType')
                    ->where('id', $payRunType->id)
                    ->update(
                        [
                            'firstPayRunStartsOn' => $startDate->toDateString(),
                            'firstPayRunNumber' => $number,
                            'dayPayRunCloses' => $closes,
                            'dayPayRunIsProcessed' => $process,
                            'timePayRunIsProcessed' => $time,
                            'dayPayRunIsPaid' => $paid,
                        ]
                    );

                $i = $number;
                foreach ($payRuns as $payRun) {
                    $processingDateTime = Carbon::parse($payRun->processingDateTime)->setTimeFromTimeString($time);

                    $payingDate = $processingDateTime->copy();
                    if ($paid > 7) {
                        $payingDate->addWeeks(1);
                    }
                    $payingDate->next($weekDays[$paid]);

                    DB::table('PayRun')->where('id', $payRun->id)->update([
                        'number' => $i,
                        'processingDateTime' => $processingDateTime->toDateTimeString(),
                        'payingDate' => $payingDate->toDateString(),
                    ]);
                    $i++;
                }
            } else {
                DB::table('PayRunType')
                  ->where('id', $payRunType->id)
                  ->update(
                      [
                          'firstPayRunStartsOn' => '2017-01-01',
                          'firstPayRunNumber' => 1,
                          'dayPayRunCloses' => 1,
                          'dayPayRunIsProcessed' => 1,
                          'timePayRunIsProcessed' => '17:00',
                          'dayPayRunIsPaid' => 1,
                      ]
                  );
            }
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('cTotalLeaveEarnt', 16, 10)->nullable();
            $table->decimal('cTotalLeaveUsed', 16, 10)->nullable();
            $table->decimal('cTotalRosteredTimeOffEarnt', 16, 10)->nullable();
            $table->decimal('cTotalRosteredTimeOffUsed', 16, 10)->nullable();
            $table->decimal('cTotal', 16, 10)->nullable();
        });

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('hoursLeave', 16, 10)->nullable();
            $table->decimal('hoursToil', 16, 10)->nullable();
            $table->decimal('hoursRosteredTimeOff', 16, 10)->nullable();
            $table->decimal('hoursAllowance', 16, 10)->nullable();
            $table->decimal('hoursTotal', 16, 10)->nullable();
            $table->decimal('hoursMore', 16, 10)->nullable();
            $table->decimal('hoursLess', 16, 10)->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('TimeSheetDay', 'hoursToil')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursToil');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursLeave')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursLeave');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursRosteredTimeOff')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOff');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursAllowance')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursTotal')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursTotal');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursMore')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursMore');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursLess')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveUsed');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffUsed');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'cTotal')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotal');
            });
        }

        if (Schema::hasColumn('PayRun', 'number')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('number');
            });
        }
        if (Schema::hasColumn('PayRun', 'processingDateTime')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('processingDateTime');
            });
        }
        if (Schema::hasColumn('PayRun', 'payingDate')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('payingDate');
            });
        }

        if (Schema::hasColumn('PayRunType', 'firstPayRunStartsOn')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('firstPayRunStartsOn');
            });
        }
        if (Schema::hasColumn('PayRunType', 'firstPayRunNumber')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('firstPayRunNumber');
            });
        }
        if (Schema::hasColumn('PayRunType', 'dayPayRunCloses')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('dayPayRunCloses');
            });
        }
        if (Schema::hasColumn('PayRunType', 'dayPayRunIsProcessed')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('dayPayRunIsProcessed');
            });
        }
        if (Schema::hasColumn('PayRunType', 'timePayRunIsProcessed')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('timePayRunIsProcessed');
            });
        }
        if (Schema::hasColumn('PayRunType', 'dayPayRunIsPaid')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('dayPayRunIsPaid');
            });
        }
    }
};
