div
  .panel.panel-info-lighter.mb0
    .panel-heading.modal-topbar
      h3.h3.mb0.mt0.visible-md.visible-lg {{'form.field.CLASHES_DESCRIPTION' | translate}}
      h3.h4.mb0.mt0.visible-sm.visible-xs {{'form.field.CLASHES_DESCRIPTION' | translate}}
      button.btn.btn-modal-close(type='button' ng-click='$ctrl.cancel()')
    .panel-body
      .table-responsive.bg-white.mb-lg
        table.table.table-bordered.table-striped
          thead
            tr
              th Hours recorded
          tbody
            tr(ng-repeat='recordTime in $ctrl._data.recordedTimes')
              td(ng-click='$ctrl.goToShowTimeCard(recordTime.timeSheetDay_id)') {{recordTime.date | _Elt$DateFormat}} - {{recordTime.adjustedHours}} hour(s) - {{recordTime.timeType}}
    br
    .panel-footer
      .row
        .col-xs-12.text-center
          button.btn.btn-default.mr(type='button' ng-click='$ctrl.cancel()') {{'form.CANCEL' | translate}}
