<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Users\UserRole;

use Carbon\CarbonInterface;
use Element\ElementTime\Actions\Tenant\Users\BaseUsersAction;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;

class GetMaximumOpenDateForRole extends BaseUsersAction
{
    /** @throws \Throwable */
    public function handle(UserRole $userRole): CarbonInterface|null
    {
        $maxDate = $userRole->endDate?->copy();

        if (!is_null($userRole->user->endDate) && (is_null($maxDate) || $userRole->user->endDate->lt($maxDate))) {
            $maxDate = $userRole->user->endDate->copy();
        }

        return $maxDate;
    }
}
