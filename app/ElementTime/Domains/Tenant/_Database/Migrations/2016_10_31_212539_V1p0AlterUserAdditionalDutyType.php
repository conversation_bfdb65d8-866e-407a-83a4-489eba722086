<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('UserAdditionalDutyType', function (Blueprint $table) {
            $table->boolean('inheritsResponsibilities')->nullable();
            $table->bigInteger('onBehalfUserShift_id')->unsigned()->nullable();
            $table->text('comments')->nullable();

            $table->foreign('onBehalfUserShift_id', 'uadt_obus_obus_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::table('UserAdditionalDutyType', function (Blueprint $table) {
            $table->dropForeign([
                'uadt_obus_obus_fn',
            ]);
            $table->dropColumn([
                'inheritsResponsibilities',
                'onBehalfUserShift_id',
                'comments',
            ]);
        });
    }
};
