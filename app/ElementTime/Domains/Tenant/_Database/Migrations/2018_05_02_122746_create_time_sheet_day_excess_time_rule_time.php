<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('TimeSheetDayExcessTimeRuleTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetDayExcessTimeRule_id');
            $table->unsignedBigInteger('timeSheet_id');

            $table->unsignedBigInteger('userShiftDay_id')->nullable();
            $table->unsignedBigInteger('userPayType_id')->nullable();
            $table->unsignedBigInteger('userHigherDutyPayType_id')->nullable();
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('factor', 16, 10)->default(1);
            $table->decimal('hoursBase', 16, 10)->default(0);
            $table->decimal('hoursCalculated', 16, 10)->default(0);
            $table->decimal('valueCalculated', 16, 10)->default(0);
            $table->decimal('hourlyRate', 16, 10)->default(0);

            $table->foreign('timeSheetDayExcessTimeRule_id', 'tsdetrt_tsdetr')->references('id')->on('TimeSheetDayExcessTimeRule')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('timeSheet_id', 'tsdetrt_ts')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('userShiftDay_id', 'tsdetrt_usd')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('userPayType_id', 'tsdetrt_upt')->references('id')->on('UserPayType')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('userHigherDutyPayType_id', 'tsdetrt_uhdpt')->references('id')->on('PayType')->onUpdate('cascade')->onDelete('set null');

            $table->unique(['timeSheetDayExcessTimeRule_id', 'startDateTime'], 'tsdetrt_ruledate_uq');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('TimeSheetDayExcessTimeRuleTime');
    }
};
