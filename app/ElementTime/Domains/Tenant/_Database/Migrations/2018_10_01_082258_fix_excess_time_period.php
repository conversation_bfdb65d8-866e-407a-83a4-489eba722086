<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
            $table->unsignedBigInteger('timeSheetDay_id')->nullable()->after('timeSheetItemDayTime_id'); // Only if type not P (Period)
            $table->foreign('timeSheetDay_id', 'tset_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('userShiftDay_id')->nullable()->after('timeSheetDay_id'); // Only if type not P (Period)
            $table->foreign('userShiftDay_id', 'tset_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropForeign('tset_tsd_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetExcessTime', 'timeSheetDay_id')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('timeSheetDay_id');
            });
        }

        try {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropForeign('tset_usd_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetExcessTime', 'userShiftDay_id')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('userShiftDay_id');
            });
        }
    }
};
