<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\LeaveAccrualByPayRun;

use Carbon\Carbon;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Support\BankEntryTypes;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem;
use Element\ElementTime\Domains\Tenant\Settings\Models\LeaveType;
use Element\ElementTime\Domains\Tenant\Settings\Models\RosteredTimeOffType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class LeaveAccrualByPayRunExcelReport extends TenantExcelReport
{
    use HasPayRunParameters;

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'leave-accrual-by-pay-run';
    const TYPE_SLUG = 'leave-accrual-by-pay-run.xls';

    public $c = LeaveAccrualByPayRunReportConfig::class;

    /** @var LeaveType[]|Collection $leaveTypes */
    private array|Collection $leaveTypes;

    /** @var RosteredTimeOffType[]|Collection $rosteredTimeOffTypes */
    private array|Collection $rosteredTimeOffTypes;

    private function collectReportData(): void
    {
        $leaveTypes = LeaveType::q(
            constraints: [
                ['LeaveType.status', '=', ActiveStatus::ID],
                ['LeaveType.doesUseFormulaForForecastingOnly', '=', false],
            ],
            relations: [
                'userLeaveBankTypes',
            ],
            order: [
                'LeaveType.name ASC'
            ],
            select: [
                'LeaveType.id',
                'LeaveType.name',
            ],
        )->many();

        $this->leaveTypes = new Collection;

        foreach ($leaveTypes as $leaveType) {
            if ($leaveType->countAssignments > 0) {
                $this->leaveTypes->push($leaveType);
            }
        }

        $rosteredTimeOffTypes = RosteredTimeOffType::q(
            constraints: [
                ['RosteredTimeOffType.status', '=', ActiveStatus::ID],
            ],
            order: [
                'RosteredTimeOffType.name ASC'
            ],
            select: [
                'RosteredTimeOffType.id',
                'RosteredTimeOffType.name',
            ],
        )->many();

        $this->rosteredTimeOffTypes = new Collection;

        foreach ($rosteredTimeOffTypes as $rosteredTimeOffType) {
            $this->rosteredTimeOffTypes->push($rosteredTimeOffType);
        }

        $this->eagerLoadReportData();
    }

    private function eagerLoadReportData(): void
    {
        $this->payRun->load([
            'payRunItems.timeSheet',
            'payRunItems.timeSheet.workflow',
            'payRunItems.timeSheet.timeSheetDays.excessTime.items.rules.works.splitItems.excessTimeRuleSplitItem',
            'payRunItems.timeSheet.timeSheetDays.excessTime.items.rules.works.splitItems.work.rule',
            'payRunItems.timeSheet.timeSheetDays.excessTime.items.workflow',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimes.items.rules.works.splitItems.excessTimeRuleSplitItem',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimes.items.rules.works.splitItems.work.rule',
            'payRunItems.timeSheet.timeSheetDayTimes.excessTimes.items.workflow',
            'payRunItems.timeSheet.userLeaveBankEntries',
            'payRunItems.user' => function (Builder $query) {
                $query->join('EmployeeType', 'User.employeeType_id', '=', 'EmployeeType.id');
                $query->select([
                    'User.id',
                    'User.nameFirst',
                    'User.nameMiddle',
                    'User.nameLast',
                    'User.externalId',
                    'User.employeeType_id',
                    'User.startDate',
                    'User.startDatePermanent',
                    'User.startDateGovernment',
                ]);

                $query
                    ->where(function (Builder $endDateQuery) {
                        $endDateQuery
                            ->orWhereNull('User.endDate')
                            ->orWhere('User.endDate', '=', '0000-00-00')
                            ->orWhere('User.endDate', '>', $this->getStartDate());

                        return $endDateQuery;
                    })
                    ->where(function (Builder $startDateQuery) {
                        $startDateQuery
                            ->orWhereNull('User.startDate')
                            ->orWhere('User.startDate', '=', '0000-00-00')
                            ->orWhere('User.startDate', '<', $this->getEndDate());

                        return $startDateQuery;
                    })
                    ->where('EmployeeType.doesHideLeave', '=', false);
            },
            'payRunItems.user.employeeType',
            'payRunItems.user.userRoles' => function (Builder $query) {
                $query->select([
                    'id',
                    'user_id',
                    'role_id',
                    'startDate',
                    'endDate',
                ]);

                $query
                    ->where(function (Builder $endDateQuery) {
                        $endDateQuery
                            ->orWhereNull('UserRole.endDate')
                            ->orWhere('UserRole.endDate', '=', '0000-00-00')
                            ->orWhere('UserRole.endDate', '>', $this->getStartDate());

                        return $endDateQuery;
                    })
                    ->where(function (Builder $startDateQuery) {
                        $startDateQuery
                            ->orWhereNull('UserRole.startDate')
                            ->orWhere('UserRole.startDate', '=', '0000-00-00')
                            ->orWhere('UserRole.startDate', '<', $this->getEndDate());

                        return $startDateQuery;
                    });
            },
            'payRunItems.user.userRoles.role:id,name',
            'payRunItems.user.userRoles.userRoleDepartments:id,department_id,userRole_id,startDate,endDate,status',
            'payRunItems.user.userRoles.userRoleDepartments.department:id,name',

            'payRunItems.user.userLeaveBankTypes.bank.entries' => function (Builder $query) {
                $query->whereBetween('UserLeaveBankEntry.entryDateTime', [$this->getStartDate(), $this->getEndDate()]);
            },
            'payRunItems.user.userLeaveBankTypes.leaveRequests' => function (Builder $query) {
                $query->where('LeaveRequest.startDate', '>=', $this->getStartDate()->toDateString());
                $query->where('LeaveRequest.endDate', '<=', $this->getEndDate()->toDateString());

                return $query;
            },
            'payRunItems.user.userLeaveBankTypes.leaveRequests.userLeaveBankEntries',
            'payRunItems.user.userLeaveBankTypes.leaveRequests.days',
            'payRunItems.user.userLeaveBankTypes.leaveRequests.workflow',
            'payRunItems.user.userLeaveBankTypes.type',
            'payRunItems.user.userLeaveBankTypes.bank.types',
        ]);
    }

    protected function buildReportData(): void
    {
        $this->collectReportData();

        $this->fetchSheetSettings($sheet);
        $this->fetchSheetTitle($sheet);
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet);

        $this->sheets = [
            $sheet,
        ];
    }

    private function fetchSheetSettings(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $sheet->label = 'Leave accrual by payrun';
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    private function fetchSheetTitle(\stdClass $sheet): void
    {
        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = 'Leave accrual by payrun';

        $sheet->data[] = $title;
    }

    private function fetchSheetTotals(\stdClass &$sheet): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';

        $data->columns = [
            $this->createColumn('Payrun', span: 5),
            $this->createColumn('Organisation', span: 4),
            $this->createColumn('Date of report', span: 3),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                $this->payRunType->name . ' #' . $this->payRun->number . ' - '. $this->getStartDate()->format($this->dateFormat) . ' to ' . $this->getEndDate()->format($this->dateFormat),
                TenantSystemSettings::o()->organisationName,
                Carbon::now()->format($this->dateFormat),
            ],
        ]];

        $sheet->data[] = $data;
    }

    private function fetchSheetData(\stdClass $sheet): void
    {
        $data = new \stdClass();
        $data->type = 'data-table';
        $data->parentColumns = [
            $this->createColumn(label: 'Staff details', span: 8),
            $this->createColumn(label: TenantSystemSettings::o()->customExcessTimeLeaveName, span: 4),
        ];

        $data->columns = [
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
            $this->createColumn('Employee type'),
            $this->createColumn('Start date organisation'),
            $this->createColumn('Start permanent'),
            $this->createColumn('Start government'),
        ];
        $this->includeLeaveColumns($data->columns);

        foreach ($this->leaveTypes as $leaveType) {
            $data->parentColumns[] = $this->createColumn(label: $leaveType->name, span: 4);
            $this->includeLeaveColumns($data->columns);
        }

        foreach ($this->rosteredTimeOffTypes as $rosteredTimeOffType) {
            $data->parentColumns[] = $this->createColumn(label: $rosteredTimeOffType->name, span: 4);
            $this->includeLeaveColumns($data->columns);
        }

        $data->data = [];
        foreach ($this->payRun->payRunItems as $payRunItem) {
            if (is_null($payRunItem->user)) {
                continue;
            }

            if (is_null($payRunItem->timeSheet)) {
                continue;
            }

            $user = $payRunItem->user;
            $userRoles = $user->getActiveUserRoles($this->getStartDate(), $this->getEndDate());
            $roleNames = [];
            $departmentNames = [];
            foreach ($userRoles as $userRole) {
                $roleNames[] = $userRole->role->name;
                $userRoleDepartments = $userRole->getActiveUserRoleDepartments($this->getEndDate());

                if ($userRoleDepartments->count() < 1) {
                    continue;
                }

                foreach ($userRoleDepartments as $userRoleDepartment) {
                    $departmentNames[] = $userRoleDepartment->department->name;
                }
            }

            $rowData = [
                $user->externalId,
                $user->fullName,
                implode(' & ', $roleNames),
                implode(' & ', $departmentNames),
                $user->employeeType->name,
                !is_null($user->startDate) ? $user->startDate->format($this->dateFormat) : null,
                !is_null($user->startDatePermanent) ? $user->startDatePermanent->format($this->dateFormat) : null,
                !is_null($user->startDateGovernment) ? $user->startDateGovernment->format($this->dateFormat) : null,
            ];

            $accruedType = $user->userLeaveBankTypes->where('type_type', '=', null)->first();

            $this->includeLeaveBalanceDataFromUserLeaveBankType($rowData, $payRunItem, $accruedType);
            $this->includeLeaveBalanceDataFromLeaveTypes($rowData, $payRunItem, $this->leaveTypes);
            $this->includeLeaveBalanceDataFromLeaveTypes($rowData, $payRunItem, $this->rosteredTimeOffTypes);

            $data->data[] = [
                'config' => [],
                'data' => $rowData,
            ];
        }

        $sheet->data[] = $data;
    }

    private function includeLeaveColumns(array &$columns): void
    {
        $columns[] = $this->createColumn('Accrued');
        $columns[] = $this->createColumn('Taken');
        $columns[] = $this->createColumn('Manual adjustments');
        $columns[] = $this->createColumn('Balance variation');
    }

    /**
     */
    private function includeLeaveBalanceDataFromUserLeaveBankType(array &$rowData, PayRunItem $payRunItem, UserLeaveBankType|null $userLeaveBankType): void
    {
        $balanceAccrued = null;
        $balanceTaken = null;
        $balanceAdjusted = null;
        $balanceVariation = null;

        if (!is_null($userLeaveBankType) && $payRunItem->user->hasLeaveOptions && $userLeaveBankType->statusClass::isType(ActiveStatus::class)) {
            $balanceAccrued = static::fetchAccruedLeaveBalanceOnPayRun($userLeaveBankType, $payRunItem);
            $balanceTaken = static::fetchTakenLeaveBalance($userLeaveBankType);
            $balanceAdjusted = static::fetchManualAdjustmentBalance($userLeaveBankType);
            $balanceVariation = $balanceAccrued - $balanceTaken + $balanceAdjusted;
        }

        $rowData[] = $balanceAccrued;
        $rowData[] = $balanceTaken;
        $rowData[] = $balanceAdjusted;
        $rowData[] = $balanceVariation;
    }

    /**
     * @param LeaveType[]|RosteredTimeOffType[]|Collection $possibleLeaveTypes
     */
    private function includeLeaveBalanceDataFromLeaveTypes(array &$rowData, PayRunItem $payRunItem, array|Collection $possibleLeaveTypes): void
    {
        foreach ($possibleLeaveTypes as $possibleLeaveType) {
            $matchedLeaveType = null;

            foreach ($payRunItem->user->userLeaveBankTypes as $userLeaveBankType) {
                if (is_null($userLeaveBankType->type_type)) {
                    continue;
                }

                if ($userLeaveBankType->type->is($possibleLeaveType)) {
                    $matchedLeaveType = $userLeaveBankType;
                    break;
                }
            }

            $this->includeLeaveBalanceDataFromUserLeaveBankType($rowData, $payRunItem, $matchedLeaveType);
        }
    }

    private static function fetchAccruedLeaveBalanceOnPayRun(UserLeaveBankType $userLeaveBankType, PayRunItem $payRunItem): float
    {
        $balance = 0;
        if (
            !$userLeaveBankType->accruesBalance
            || is_null($payRunItem->timeSheet)
        ) {
            return 0;
        }


        foreach ($payRunItem->timeSheet->userLeaveBankEntries as $entry) {
            if ($entry->userLeaveBank_id !== $userLeaveBankType->userLeaveBank_id) {
                continue;
            }

            if (!$entry->entryTypeClass::isType(BankEntryTypes\AccrueAutomaticallyType::class)) {
                continue;
            }

            $balance += $entry->entryBalance;
        }

        $payRunItemRelatedEntries = $userLeaveBankType->bank->entries->where('payRunItem_id', '=', $payRunItem->id);
        foreach ($payRunItemRelatedEntries as $payRunItemRelatedEntry) {
            if ($payRunItemRelatedEntry->entryTypeClass::isType(BankEntryTypes\LoseType::class)) {
                $balance -= $payRunItemRelatedEntry->entryBalance;
            }
        }

        return $balance;
    }

    private static function fetchTakenLeaveBalance(UserLeaveBankType $userLeaveBankType): float
    {
        if (!$userLeaveBankType->canTakeLeave) {
            return 0;
        }

        $takenHours = 0;
        foreach ($userLeaveBankType->leaveRequests as $leaveRequest) {
            foreach ($leaveRequest->userLeaveBankEntries as $entry) {
                if (
                    $entry->entryTypeClass::isType(BankEntryTypes\TakeAsPaymentType::class)
                    || $entry->entryTypeClass::isType(BankEntryTypes\TakeAsLeaveType::class)
                ) {
                    $takenHours += $entry->entryBalance;
                }
            }
        }

        return $takenHours;
    }

    private static function fetchManualAdjustmentBalance(UserLeaveBankType $userLeaveBankType): float
    {
        $totalBalance = 0;
        foreach ($userLeaveBankType->bank->entries as $entry) {
            if ($entry->entryTypeClass::isType(BankEntryTypes\AccrueManuallyType::class)) {
                $totalBalance += $entry->entryBalance;
            }
            if ($entry->entryTypeClass::isType(BankEntryTypes\RemoveManuallyType::class)) {
                $totalBalance -= $entry->entryBalance;
            }
        }

        return $totalBalance;
    }
}
