<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\WorkPatternsByDepartment;

use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\Role;
use Element\ElementTime\Domains\Tenant\Settings\Repositories\DepartmentRepository;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDayTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Support\TimeSheetDayTimeRecordTypes\BaseType;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class WorkPatternsByDepartmentExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
        setValidityDate as _iSetValidityDate;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const TIME = '~1:30min';
    const REPORT_SLUG = 'work-patterns-by-department';
    const TYPE_SLUG = 'work-patterns-by-department.xls';
    const DOES_CACHE_REPORT = true;

    public $c = WorkPatternsByDepartmentConfig::class;

    protected CarbonPeriod $period;
    protected ?Department $department;
    private array $dateColumns = [];

    protected function setData(array $info): void
    {
        $this->_iSetData($info);

        /** @var Department $department */
        $department = DepartmentRepository::getOne([
            'constraints' => [
                ['Department.id', '=', $info['idDepartment']],
            ],
            'returnType' => 'model',
        ]);

        if (!is_null($department)) {
            $this->department = $department;
        } else {
            $this->department = null;
        }

        $this->period = CarbonPeriod::create($this->payRunStart->startDate, $this->payRunEnd->endDate);
    }

    public function getReplacedString(string $string): string
    {
        $string = $this->_iGetReplacedString($string);

        if (isset($this->department) && $this->department instanceof Department) {
            $string = Str::replace('{{department.id}}', $this->department->id, $string);
            $string = Str::replace('{{department.name}}', $this->department->name, $string);
        } else {
            $string = Str::replace('{{department.id}}', '0', $string);
            $string = Str::replace('{{department.name}}', 'All departments', $string);
        }

        return $string;
    }

    /** @throws InvalidArgumentException */
    protected function addManagementToConstraints(array &$constraints): void
    {
        $user = CurrentUser::getUser();

        if (!$this->doesUserHaveFullAccess($user)) {
            $subConstraints = [];

            if ($user->access->isStaffManager(true)) {
                $directReportUserIds = [];
                $reportUsers = $this->getReportsUser($user, false);
                foreach ($reportUsers as $reportUser) {
                    $directReportUserIds[] = $reportUser->id;
                }

                $subConstraints[] = ['IN', 'UserRole.user_id', $directReportUserIds, 'OR'];
            }

            if ($user->access->isRoleManager(true)) {
                $roleReportUserRoleIds = [];
                $activeUserRoleReports = $user->repository->getActiveUserRoleReports();
                foreach ($activeUserRoleReports as $activeUserRoleReport) {
                    $roleReportUserRoleIds[] = $activeUserRoleReport->userRole_id;
                }

                $subConstraints[] = ['IN', 'UserRole.id', $roleReportUserRoleIds, 'OR'];
            }

            if (count($subConstraints) < 1) {
                $constraints[] = ['TimeSheetDayTime.id', '=', 0];
            } else {
                $constraints[] = [
                    $subConstraints,
                ];
            }
        }
    }

    /** @throws \Element\Core\Exceptions\InvalidArgumentException */
    protected function collectReportData(): array
    {
        $rows = [];

        $timeSheetDayTimes = $this->getTimeSheetDayTimes();
        $leaveRequestDays = $this->getLeaveRequestDays();

        foreach ($timeSheetDayTimes as $timeSheetDayTime) {
            $date = $timeSheetDayTime->timeSheetDay->date;
            $this->buildRowData($timeSheetDayTime->userShiftDay->userShift->userRole, $date, $timeSheetDayTime, $rows);
        }

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $date = $leaveRequestDay->date;
            $this->buildRowData($leaveRequestDay->leaveRequest->userRoleSchedule->userRole, $date, $leaveRequestDay, $rows);
        }

        return $rows;
    }

    /**
     * @return Collection|TimeSheetDayTime[]
     * @throws InvalidArgumentException
     */
    private function getTimeSheetDayTimes(): Collection|array
    {
        $joins = [
            ['UserShiftDay', 'UserShiftDay.id', '=', 'TimeSheetDayTime.userShiftDay_id'],
            ['UserShift', 'UserShift.id', '=', 'UserShiftDay.userShift_id'],
            ['UserRole', 'UserRole.id', '=', 'UserShift.userRole_id'],
            ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserRole.id'],
        ];

        $constraints = [
            ['IN', 'TimeSheetDayTime.recordType', BaseType::getAllWorkTypeIds()],
            ['TimeSheetDay.date', '>=', $this->payRunStart->startDate->toDateString()],
            ['TimeSheetDay.date', '<=', $this->payRunEnd->endDate->toDateString()],
        ];

        if (!is_null($this->department)) {
            $constraints[] = ['IN', 'UserRoleDepartment.department_id', $this->getDepartmentIds($this->department)];
            $constraints[] = ['TimeSheetDay.date', '>=', DB::raw('UserRoleDepartment.startDate')];
            $constraints[] = [
                [
                    ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                    ['UserRoleDepartment.endDate', '=', DB::raw('0000-00-00'), 'OR'],
                    ['TimeSheetDay.date', '<=', DB::raw('UserRoleDepartment.endDate'), 'OR'],
                ],
            ];
        }

        $this->addManagementToConstraints($constraints);

        $timeSheetDayTimes = TimeSheetDayTime::q(/*groupBy: 'TimeSheetDayTime.id'*/)
            ->constraints($constraints)
            ->joins($joins)
            ->many();

        $timeSheetDayTimes->load([
            'timeSheetDay:id,date,timeSheet_id',
            'timeSheetDay.timeSheet:id,recordMethod',
            'userShiftDay:id,userShift_id',
            'userShiftDay.userShift:id,userRole_id',
            'userShiftDay.userShift.userRole:id,user_id,role_id,startDate,endDate',
            'userShiftDay.userShift.userRole.userRoleDepartments.department:id,name',
            'userShiftDay.userShift.userRole.user:id,externalId,nameFirst,nameMiddle,nameLast',
            'userShiftDay.userShift.userRole.user.userManagers.manager:id,nameFirst,nameMiddle,nameLast',
            'userShiftDay.userShift.userRole.role:id,name',
        ]);

        return $timeSheetDayTimes;
    }

    /**
     * @return Collection|LeaveRequestDay[]
     * @throws InvalidArgumentException
     */
    private function getLeaveRequestDays(): Collection|array
    {
        $joins = [
            ['UserRole', 'UserRole.id', '=', 'UserShift.userRole_id'],
            ['UserRoleDepartment', 'UserRoleDepartment.userRole_id', '=', 'UserRole.id'],
        ];

        $constraints = [
            ['LeaveRequestDay.date', '>=', $this->payRunStart->startDate->toDateString()],
            ['LeaveRequestDay.date', '<=', $this->payRunEnd->endDate->toDateString()],
            ['NOT IN', 'Workflow.status', [
                WorkflowStatusTypes\ExcludedType::ID,
                WorkflowStatusTypes\CancelledType::ID,
                WorkflowStatusTypes\ReplacedType::ID,
                WorkflowStatusTypes\SplitType::ID,
            ]],
        ];

        if (!is_null($this->department)) {
            $constraints[] = ['IN', 'UserRoleDepartment.department_id', $this->getDepartmentIds($this->department)];
            $constraints[] = ['LeaveRequestDay.date', '>=', DB::raw('UserRoleDepartment.startDate')];
            $constraints[] = [
                [
                    ['IS NULL', 'UserRoleDepartment.endDate', 'OR'],
                    ['UserRoleDepartment.endDate', '=', '0000-00-00', 'OR'],
                    ['LeaveRequestDay.date', '<=', DB::raw('UserRoleDepartment.endDate'), 'OR'],
                ],
            ];
        }

        $this->addManagementToConstraints($constraints);

        $leaveRequestDays = LeaveRequestDay::q()
            ->joins($joins)
            ->constraints($constraints)
            ->many();

        $leaveRequestDays->load([
            'userShift:id,shift_id,userRole_id',
            'userShift.shift:id,glideRequireTimeBlock',
            'userShift.userRole:id,user_id,role_id,startDate,endDate',
            'userShift.userRole.userRoleDepartments.department:id,name',
            'userShift.userRole.user:id,externalId,nameFirst,nameMiddle,nameLast',
            'userShift.userRole.user.userManagers.manager:id,nameFirst,nameMiddle,nameLast',
            'userShift.userRole.role:id,name',
            'leaveRequest:id,userLeaveBankType_id',
            'leaveRequest.userLeaveBankType:id,type_type,type_id',
            'leaveRequest.userLeaveBankType.type:id,name',
        ]);

        return $leaveRequestDays;
    }

    private function getDepartmentIds(Department $department, &$ids = []): array
    {
        $ids[] = $department->id;

        foreach ($department->children as $child) {
            $this->getDepartmentIds($child, $ids);
        }

        return $ids;
    }

    /** @throws \Element\Core\Exceptions\InvalidArgumentException */
    protected function buildRowData(UserRole $userRole, Carbon $date, $data, &$rows = []): void
    {
        $userRole_id = $userRole->id;
        $dateKey = $date->toDateString();

        if (!isset($rows[$userRole->id])) {
            $rows[$userRole->id] = [
                'department' => null,
                'userRole' => $userRole,
                'role' => $userRole->role,
                'user' => $userRole->user,
                'manager' => $userRole->user->repository->getManager($date, [], 'model'),
                'recordedTimes' => [],
            ];

            $nDate = $this->payRunStart->startDate->copy()->startOfDay();
            $endDate = $this->payRunStart->endDate->copy()->startOfDay();

            while ($nDate->lte($endDate)) {
                if (!isset($rows[$userRole_id]['recordedTimes'][$nDate->toDateString()])) {
                    $rows[$userRole_id]['recordedTimes'][$nDate->toDateString()] = [];
                }

                $nDate->addDay();
            }
        }

        if (is_null($rows[$userRole->id]['department'])) {
            $userRoleDepartments = $userRole->getActiveUserRoleDepartments($date);

            if ($userRoleDepartments->count() > 0) {
                $userRoleDepartment = null;

                if (!is_null($this->department)) {
                    $userRoleDepartment = $userRoleDepartments->where('department_id', '=', $this->department->id)->first();
                }

                if (is_null($userRoleDepartment)) {
                    $userRoleDepartment = $userRoleDepartments[0];
                }

                $rows[$userRole->id]['department'] = $userRoleDepartment?->department;
            }
        }

        if ($data instanceof TimeSheetDayTime) {
            $rows[$userRole_id]['recordedTimes'][$dateKey][] = $this->buildRowDataFromTimeSheetDayTime($data);
        } elseif ($data instanceof LeaveRequestDay) {
            $rows[$userRole_id]['recordedTimes'][$dateKey][] = $this->buildRowDataFromLeaveRequestDay($data);
        }
    }

    protected function buildRowDataFromTimeSheetDayTime(TimeSheetDayTime $timeSheetDayTime): string
    {
        if ($timeSheetDayTime->timeSheetDay->timeSheet->recordMethod === 'H') {
            return $timeSheetDayTime->hours . ' hours';
        }

        $start = $timeSheetDayTime->startDateTime;
        $end = $timeSheetDayTime->endDateTime;

        return $start->format('H:i') . ' - ' . $end->format('H:i');
    }

    protected function buildRowDataFromLeaveRequestDay(LeaveRequestDay $leaveRequestDay): string
    {
        if ($leaveRequestDay->isFullDay) {
            return $leaveRequestDay->leaveRequest->typeName;
        }

        if (!$leaveRequestDay->leaveRequest->scheduleType->isDurationOnly()) {
            $start = $leaveRequestDay->timeSpan->getStart();
            $end = $leaveRequestDay->timeSpan->getEnd();
            $leaveRequestDayPeriod = $start . ' - ' . $end;

            return $leaveRequestDay->leaveRequest->typeName . $this->br . $leaveRequestDayPeriod;
        }

        return  $leaveRequestDay->leaveRequest->typeName . $this->br . $leaveRequestDay->durationAdjusted->toHumanString(withAbbreviatedUnits: false);
    }

    /** @throws \Element\Core\Exceptions\InvalidArgumentException */
    protected function buildReportData(): void
    {
        $data = $this->collectReportData();

        $this->setSheetTitle($sheet, 'Work patterns by department');
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function fetchSheetTotals(\stdClass &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass;
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Start pay-run number', 'text', 'center', 3),
            $this->createColumn('End pay-run number', 'text', 'center', 2),
            $this->createColumn('Name of Organisation', 'text', 'center', 2),
            $this->createColumn('Date report run', 'text', 'center', 2),
            $this->createColumn('Department', 'text', 'center', 2),
        ];

        $data->data = [[
            'config' => [],
            'data' => [
                '#' . $this->payRunStart->number . ' - ' . $this->payRunStart->fiscalYear,
                '#' . $this->payRunEnd->number . ' - ' . $this->payRunEnd->fiscalYear,
                TenantSystemSettings::o()->organisationName,
                Carbon::now()->format($this->dateFormat),
                is_null($this->department) ? 'All' : $this->department->name,
            ],
        ]];

        $sheet->data[] = $data;
    }

    protected function fetchSheetData(\stdClass &$sheet, array $data): void
    {
        $table = new \stdClass();
        $this->validateSheet($sheet);

        $table->type = 'data-table';
        $table->parentColumns = $this->getSheetDataParentHeader();
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($data);

        $sheet->data[] = $table;
    }

    protected function getSheetDataParentHeader(): array
    {
        return [
            $this->createParentColumn(''),
            $this->createParentColumn('Employee details', 5),
            $this->createParentColumn('', $this->payRunStart->startDate->diffInDays($this->payRunEnd->endDate) + 1),
        ];
    }

    protected function getSheetDataHeader(): array
    {
        $columns = [
            $this->createColumn('#'),
            $this->createColumn('Employee Ref'),
            $this->createColumn('Employee Name'),
            $this->createColumn('Manager'),
            $this->createColumn('Department'),
            $this->createColumn('Role Name'),
        ];

        $start = $this->payRunStart->startDate->copy();

        while ($start->lte($this->payRunEnd->endDate)) {
            $columns[] = $this->createColumn($start->format(TenantSystemSettings::o()->dateFormat), 'text', 'center');
            $this->dateColumns[] = $start->toDateString();
            $start = $start->addDay();
        }

        return $columns;
    }

    protected function getSheetDataData($data): array
    {
        $tableData = [];

        $orderedData = Arr::sort($data, fn ($v) => $v['user']->externalId);

        $i = 1;
        foreach ($orderedData as $rowData) {
            /** @var UserRole $userRole */
            $userRole = $rowData['userRole'];

            /** @var User $user */
            $user = $rowData['user'];

            /** @var User $manager */
            $manager = $rowData['manager'];

            /** @var Department $department */
            $department = $rowData['department'];

            /** @var Role $role */
            $role = $rowData['role'];

            $thisData = [
                $i,
                $userRole->repository->getExternalId(),
                $user->fullName,
                is_null($manager) ? '' : $manager->fullName,
                is_null($department) ? '' : $department->name,
                $role->name,
            ];

            foreach ($this->dateColumns as $dateColumn) {
                if (isset($rowData['recordedTimes'][$dateColumn])) {
                    $thisData[] = implode($this->br, $rowData['recordedTimes'][$dateColumn]);
                } else {
                    $thisData[] = '';
                }
            }

            $tableData[] = [
                'data' => $thisData,
            ];

            $i++;
        }

        return $tableData;
    }
}
