<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('PlantItemDepartment', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('plantItem_id');
            $table->foreign('plantItem_id', 'pid_pi_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('department_id');
            $table->foreign('department_id', 'pid_d_fn')
                ->references('id')->on('Department')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();
            $table->unique(['plantItem_id', 'department_id'], 'pid_pi_d_uq');
        });

        Schema::create('PlantItemPayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('plantItem_id');
            $table->foreign('plantItem_id', 'pipgt_pi_fn')
                ->references('id')->on('PlantItem')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('payGroupType_id');
            $table->foreign('payGroupType_id', 'pipgt_pgt_fn')
                ->references('id')->on('PayGroupType')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->timestamps();
            $table->unique(['plantItem_id', 'payGroupType_id'], 'pipgt_pi_pgt_uq');
        });

        Schema::create('UserRolePlantClass', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urpc_ur_fn')
                ->references('id')->on('UserRole')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('plantClass_id');
            $table->foreign('plantClass_id', 'urpc_pc_fn')
                ->references('id')->on('PlantClass')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->date('startDate');
            $table->date('endDate')->nullable();
            $table->string('comments')->nullable();

            $table->timestamps();
        });

        Schema::table('PlantSheetDayTime', function (Blueprint $table) {
            $table->unsignedBigInteger('userRolePlantItem_id')->nullable()->change();
            $table->foreign('userRolePlantItem_id', 'psdt_urpi_fn')
                ->references('id')->on('UserRolePlantItem')
                ->onUpdate('cascade')->onDelete('set null');

            $table->string('rel_type')->after('userRolePlantItem_id')->nullable();
            $table->unsignedBigInteger('rel_id')->after('rel_type')->nullable();
        });
    }

    public function down(): void
    {
        try {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropForeign('psdt_urpi_fn');
            });
        } catch (\Throwable) {
        }

        try {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropForeign('psdt_pi_fn');
            });
        } catch (\Throwable) {
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'plantItem_id')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('plantItem_id');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'rel_type')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('rel_type');
            });
        }

        if (Schema::hasColumn('PlantSheetDayTime', 'rel_id')) {
            Schema::table('PlantSheetDayTime', function (Blueprint $table) {
                $table->dropColumn('rel_id');
            });
        }

        Schema::dropIfExists('PlantItemDepartment');
        Schema::dropIfExists('PlantItemPayGroupType');

        Schema::dropIfExists('UserRolePlantClass');
    }
};
