<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('LeaveType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->string('externalId')->nullable();
            $table->string('name')->nullable();

            $table->boolean('leaveDurationIsOverSendAlert')->nullable();
            $table->integer('leaveDurationIsOverTimeValue')->nullable();
            $table->char('leaveDurationIsOverTimeType', 1)->nullable();

            $table->boolean('instancesOfLeaveTypeAreTakenSendAlert')->nullable();
            $table->integer('instancesOfLeaveTypeAreTakenNumber')->nullable();
            $table->integer('instancesOfLeaveTypeAreTakenTimeValue')->nullable();
            $table->char('instancesOfLeaveTypeAreTakenTimeType', 1)->nullable();

            $table->boolean('allowsCapAmountOfLeave')->nullable();
            $table->integer('capAmountOfLeaveTimeValue')->nullable();
            $table->char('capAmountOfLeaveTimeType', 1)->nullable();

            $table->boolean('allowsToBeConvertedToPayment')->nullable();
            $table->boolean('isPaidOutOnFinalPayRun')->nullable();

            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();

            $table->char('isRealisedOn', 1)->nullable();
            $table->char('appliesByDefaultTo', 1)->nullable();

            $table->longText('formula')->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('project_id', 'leavetype_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('projectActivity_id', 'leavetype_projectactivity_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('LeaveType_PayGroupType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('leaveType_id')->unsigned();
            $table->bigInteger('payGroupType_id')->unsigned();

            $table->primary(['leaveType_id', 'payGroupType_id']);
            $table->foreign('leaveType_id', 'leavetype_paygrouptype_leavetype_fn')->references('id')->on('LeaveType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payGroupType_id', 'leavetype_paygrouptype_paygrouptype_fn')->references('id')->on('PayGroupType')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('LeaveType');
        Schema::drop('LeaveType_PayGroupType');
    }
};
