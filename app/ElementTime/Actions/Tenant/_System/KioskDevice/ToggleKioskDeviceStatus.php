<?php

namespace Element\ElementTime\Actions\Tenant\_System\KioskDevice;

use Element\ElementTime\Actions\Tenant\_System\BaseSystemAction;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDevice;

class ToggleKioskDeviceStatus extends BaseSystemAction
{
    /** @throws \Throwable */
    public function handle(KioskDevice $kioskDevice): KioskDevice
    {
        $kioskDevice->isActive = !$kioskDevice->isActive;

        // TODO: @isaac send socket
        $kioskDevice->saveOrFail();

        return $kioskDevice;
    }
}
