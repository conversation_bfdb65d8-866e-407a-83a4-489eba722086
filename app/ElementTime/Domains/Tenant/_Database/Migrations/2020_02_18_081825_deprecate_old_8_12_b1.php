<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        //region Colour scheme
        if (Schema::hasColumn('Settings', 'colourScheme')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('colourScheme');
            });
        }
        //endregion Colour scheme

        //region Overtime/TOIL
        if (Schema::hasColumn('Settings', 'allowToDecideOvertimeOrToil')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowToDecideOvertimeOrToil');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'allowToDecideOvertimeOrToil')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('allowToDecideOvertimeOrToil');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'automaticallyUsesToil')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('automaticallyUsesToil');
            });
        }

        if (Schema::hasColumn('Settings', 'allowsTOIL')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsTOIL');
            });
        }

        if (Schema::hasColumn('Settings', 'allowsOvertime')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsOvertime');
            });
        }

        if (Schema::hasColumn('Settings', 'requiresCommentOnExcessTime')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('requiresCommentOnExcessTime');
            });
        }

        if (Schema::hasColumn('Settings', 'requiresExcessTimeApproval')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('requiresExcessTimeApproval');
            });
        }

        if (Schema::hasColumn('User', 'excessTimeMethod')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('excessTimeMethod');
            });
        }
        //endregion Overtime/TOIL

        //region Timesheet calculated values
        if (Schema::hasColumn('TimeSheet', 'cTotalWorked')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalWorked');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalWorked')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalWorked');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalWorkedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalAllowanceCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cMilesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cMilesAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fMilesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fMilesAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cValueAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cValueAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fValueAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fValueAllowance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalHoursLess')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalHoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalHoursLess')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalHoursLess');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalOvertime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalOvertime');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalOvertime')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalOvertime');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalLeaveUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalLeaveUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalToilUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalToilUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalToilUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalToilUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalToilUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalToilUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalToilUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalToilUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffEarnt');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffUsed');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalRosteredTimeOffUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalRosteredTimeOffUsedCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotal')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotal');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotal')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotal');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'cTotalCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('cTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'fTotalCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('fTotalCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'totalExcessTimeHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalExcessTimeHours');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'totalToilHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalToilHours');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'totalOvertimeHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalOvertimeHours');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'totalOvertimeValue')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('totalOvertimeValue');
            });
        }
        //endregion Timesheet calculated values

        //region RosteredTimeOffType
        if (Schema::hasColumn('RosteredTimeOffType', 'hasOvertime')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('hasOvertime');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM RosteredTimeOffType WHERE Key_name=\'rdot_ot_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropForeign('rdot_ot_fn');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'overtimeType_id')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('overtimeType_id');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'hasToil')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('hasToil');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM RosteredTimeOffType WHERE Key_name=\'rdot_tt_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropForeign('rdot_tt_fn');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'toilType_id')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('toilType_id');
            });
        }
        //endregion RosteredTimeOffType

        //region Old excess-time
        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItemDayTime WHERE Key_name=\'tsidt_td_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_td_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItemDayTime', 'toilDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('toilDay_id');
            });
        }

        if (DB::select(DB::raw('SHOW KEYS FROM TimeSheetItem WHERE Key_name=\'timesheetitem_toiltype_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('TimeSheetItem', function (Blueprint $table) {
                $table->dropForeign('timesheetitem_toiltype_fn');
            });
        }

        if (Schema::hasColumn('TimeSheetItem', 'toilType_id')) {
            Schema::table('TimeSheetItem', function (Blueprint $table) {
                $table->dropColumn('toilType_id');
            });
        }

        Schema::dropIfExists('TimeSheetDayExcessTimeStatusHistory');
        Schema::dropIfExists('TimeSheetDayExcessTimeRuleTime');
        Schema::dropIfExists('TimeSheetDayExcessTimeRule');
        Schema::dropIfExists('TimeSheetDayExcessTimeAttachmentFile');
        Schema::dropIfExists('TimeSheetDayExcessTimeApproval');
        Schema::dropIfExists('TimeSheetDayExcessTime');
        Schema::dropIfExists('Overtime');
        Schema::dropIfExists('UserOvertimeType');
        Schema::dropIfExists('OvertimeType');
        Schema::dropIfExists('ToilStatusHistory');
        Schema::dropIfExists('ToilApproval');
        Schema::dropIfExists('ToilAttachmentFile');
        Schema::dropIfExists('UserToilTypeEntryRemoved');
        Schema::dropIfExists('UserToilTypeEntryCommitted');
        Schema::dropIfExists('UserToilTypeEntry');
        Schema::dropIfExists('ToilDay');
        Schema::dropIfExists('Toil');
        Schema::dropIfExists('UserToilType');
        Schema::dropIfExists('ToilType');
        //endregion Old excess-time

        //region AdditionalDuty
        Schema::dropIfExists('UserAdditionalDutyType');
        Schema::dropIfExists('AdditionalDutyType_LeaveType');
        Schema::dropIfExists('AdditionalDutyType');
        //endregion AdditionalDuty

        //region LeaveConfig
        if (Schema::hasColumn('Settings', 'showProRataLeaveBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('showProRataLeaveBalance');
            });
        }

        if (Schema::hasColumn('Settings', 'showEntitledLeaveBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('showEntitledLeaveBalance');
            });
        }

        if (Schema::hasColumn('Settings', 'showTotalLeaveBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('showTotalLeaveBalance');
            });
        }
        //endregion LeaveConfig
    }

    public function down(): void
    {
        //
    }
};
