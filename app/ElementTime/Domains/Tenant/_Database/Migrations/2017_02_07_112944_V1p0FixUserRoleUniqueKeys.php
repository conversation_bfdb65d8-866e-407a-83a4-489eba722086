<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM UserRole WHERE Key_name = "userrole_active_u"')->getValue(DB::getQueryGrammar()))) {
            Schema::table('UserRole', function (Blueprint $table) {
                $table->dropForeign('userrole_user_fn');
                $table->dropForeign('userrole_role_fn');
                $table->dropUnique('userrole_active_u');
            });

            Schema::table('UserRole', function (Blueprint $table) {
                $table->foreign('user_id', 'userrole_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
                $table->foreign('role_id', 'userrole_role_fn')->references('id')->on('Role')->onUpdate('cascade')->onDelete('restrict');
            });
        }
    }

    public function down(): void
    {
        Schema::table('UserRole', function (Blueprint $table) {
            //
        });
    }
};
