form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$ctrl.form.submit()')
  .row
    .col-xs-12
      .form-group(elt-help-text="modals.leave-request|fields.bankType")
        label.label-control Select leave type

        ui-select(
          ng-if='$ctrl.form.settings.options.bankType.length > 1'
          ng-model='$ctrl.form.data.userLeaveBankType_id'
          ng-required='true'
          ng-disabled='!!$ctrl.form.isSubmitting'
          ng-change='$ctrl.form.$events.onChangeBankType()'
          name='completeMissingHours_userLeaveBankType'
          theme='bootstrap'
          reset-search-input='true'
        )
          ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.name}} {{ $select.selected.availableBalance }} hours available
          ui-select-choices(repeat='object.id as object in $ctrl.form.settings.options.bankType | filter : $select.search | orderBy: "leaveName"')
            div(ng-bind-html='object.name + " " + object.availableBalance + " hours available" | highlight: $select.search')

        .lead.m0.bb(ng-if='$ctrl.form.settings.options.bankType.length === 1') {{$ctrl.form.userLeaveBankType.name }} {{ $ctrl.form.userLeaveBankType.availableBalance  }} hours available

  .row
    .col-xs-12
      .form-group
        label.label-control(for='completeMissingHours_inputReason') Reason / comments
        textarea.form-control(id='completeMissingHours_inputReason' name='completeMissingHours_inputReason' ng-model='$ctrl.form.data.reason' ng-required='true')

  .row(ng-if='!$ctrl.form.balancesValid && !!$ctrl.form.data.userLeaveBankType_id')
    .col-xs-12
      .list-group.mb-lg
        .list-group-item.list-group-item-warning
          .row
            .col-xs-12
              .item-description.text-center
                | Not enough balance to complete timesheet using this leave type. Please select another leave type or check with your payroll officer.

  .row
    .col-xs-12.col-md-6.col-md-offset-6
      button.btn.btn-block.btn-lg.btn-success(type='submit' ng-disabled='!$ctrl.form.balancesValid || $ctrl.form.isSubmitting')
        span(ng-if='!$ctrl.form.isSubmitting')
          i.fa.fa-check-square-o.mr-sm
          | Complete missing hours
        span(ng-if='!!$ctrl.form.isSubmitting')
          i.fa.fa-circle-o-notch.fa-spin
          | Wait
