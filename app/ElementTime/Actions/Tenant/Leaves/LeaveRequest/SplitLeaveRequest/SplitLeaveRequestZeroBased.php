<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SplitLeaveRequest;

use Carbon\Carbon;
use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Support\Enums\RecordedVia;

class SplitLeaveRequestZeroBased extends BaseSplitLeaveRequest
{
    /**@throws \Throwable */
    public function handle(
        LeaveRequest $leaveRequest,
        User $actor = null,
        string $notes = '',
        Carbon $dateTime = null,
        MediaFile $attachmentFile = null,
        RecordedVia|null $recordedVia = null,
        $runsStatusEvent = true,
    ): void
    {
        throw new \BadMethodCallException('Splitting leave is not available zero based schedule leave requests');
    }
}
