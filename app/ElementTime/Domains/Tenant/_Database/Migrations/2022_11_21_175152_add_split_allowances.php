<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('CodeSplit', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();
            $table->string('rel_type');
            $table->unsignedBigInteger('rel_id');

            $table->decimal('percentage', 16, 10);
            $table->string('code');

            $table->timestamps();
        });

        Schema::table('AllowanceType', function (Blueprint $table) {
            $table->boolean('canSplitAcrossCodes')->default(false)->after('incrementRate_id');
        });

        Schema::table('PenaltyType', function (Blueprint $table) {
            $table->boolean('canSplitAcrossCodes')->default(false)->after('description');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('CodeSplit');

        if (Schema::hasColumn('AllowanceType', 'canSplitAcrossCodes')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('canSplitAcrossCodes');
            });
        }

        if (Schema::hasColumn('PenaltyType', 'canSplitAcrossCodes')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('canSplitAcrossCodes');
            });
        }
    }
};
