<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\General\MediaFile;

use Element\ElementTime\Actions\Tenant\General\BaseGeneralAction;
use Element\ElementTime\Domains\Tenant\General\Models\MediaFile;

class GetMediaFileAndRename extends BaseGeneralAction
{
    /**
     * @throws \Throwable
     */
    public function handle(int $id, string|null $name = null): MediaFile
    {
        $attachmentFile = MediaFile::q()->findOrFail($id);

        if (!is_null($name)) {
            $attachmentFile->name = $name;
            $attachmentFile->save();
        }

        return $attachmentFile;
    }
}
