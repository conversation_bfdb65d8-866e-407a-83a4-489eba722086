<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetDay;

use Element\Core\Exceptions\ElementException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheet\CalculateTimeSheetTotals;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AccruedHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\AdditionalHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\PaidHoursType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes\UnpaidHoursType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;

class CalculateTimeSheetDayTotals extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetDay $timeSheetDay,

        bool $allValues = false,
        bool $scheduleValues = false,
        bool $recordedValues = false,
        bool $ordinaryValues = false,
        bool $workedValues = false,
        bool $leaveValues = false,
        bool $holidayValues = false,
        bool $excessTimeValues = false,
        bool $missingValues = false,
        bool $penaltyValues = false,
        bool $autoAllowanceValues = false,
        bool $adHocAllowanceValues = false,

        bool $upstream = false,
    ): TimeSheetDay
    {
        // TODO: Implement other value calculations

        if ($allValues || $excessTimeValues) {
            $timeSheetDay->load([
                'works.excessTimes.items.rules.works.splitItems',
            ]);

            $timeSheetDay->durationExcessTime = TimeDuration::zero();
            $timeSheetDay->durationExcessTimeAccrued = TimeDuration::zero();
            $timeSheetDay->durationExcessTimeAccruedAdjusted = TimeDuration::zero();
            $timeSheetDay->durationExcessTimeAdditionalHours = TimeDuration::zero();
            $timeSheetDay->durationExcessTimePaid = TimeDuration::zero();
            $timeSheetDay->durationExcessTimePaidAdjusted = TimeDuration::zero();
            $timeSheetDay->durationExcessTimeUnPaid = TimeDuration::zero();

            foreach ($timeSheetDay->works as $work) {
                foreach ($work->excessTimes as $excessTime) {
                    if (is_null($excessTime->activeItem)) {
                        continue;
                    }

                    foreach ($excessTime->activeItem->rules as $rule) {
                        foreach ($rule->works as $exWork) {
                            foreach ($exWork->splitItems as $splitItem) {
                                $timeSheetDay->durationExcessTime->add($splitItem->duration);
                                switch ($splitItem->ruleType) {
                                    case AccruedHoursType::ID:
                                        $timeSheetDay->durationExcessTimeAccrued->add($splitItem->duration);
                                        $timeSheetDay->durationExcessTimeAccruedAdjusted->add($splitItem->durationAdjusted);

                                        break;
                                    case AdditionalHoursType::ID:
                                        $timeSheetDay->durationExcessTimeAdditionalHours->add($splitItem->duration);

                                        break;
                                    case PaidHoursType::ID:
                                        $timeSheetDay->durationExcessTimePaid->add($splitItem->duration);
                                        $timeSheetDay->durationExcessTimePaidAdjusted->add($splitItem->durationAdjusted);

                                        break;
                                    case UnpaidHoursType::ID:
                                        $timeSheetDay->durationExcessTimeUnPaid->add($splitItem->duration);

                                        break;
                                    default:
                                        throw new ElementException('Invalid rule type');
                                }

                                $splitItem->ruleTypeClass::isType(AccruedHoursType::class);
                            }
                        }
                    }
                }
            }
        }

        if ($timeSheetDay->isDirty()) {
            /// TODO: Change this to normal save when cleared
            $timeSheetDay->clearSaveOrFail();
        }

        if ($upstream) {
            CalculateTimeSheetTotals::make()->handle(
                $timeSheetDay->timeSheet,
                $allValues,
                $scheduleValues,
                $recordedValues,
                $ordinaryValues,
                $workedValues,
                $leaveValues,
                $holidayValues,
                $excessTimeValues,
                $missingValues,
                $penaltyValues,
                $autoAllowanceValues,
                $adHocAllowanceValues,
                $upstream,
            );
        }

        return $timeSheetDay;
    }
}
