<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region PayRunTypeStep
        Schema::create('PayRunTypeStep', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('payRunType_id')->unsigned();
            $table->string('step_slug');

            $table->string('name');
            $table->text('description');

            $table->integer('order')->default(1);
            $table->boolean('isActive')->default(true);

            $table->timestamps();
            $table->softDeletes();

            $table->unique(['payRunType_id', 'order']);

            $table->foreign('payRunType_id', 'prts_prt_fn')->references('id')->on('PayRunType')->onUpdate('cascade')->onDelete('cascade');
        });
        //endregion

        //region PayRunStatusHistory
        Schema::create('PayRunStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('payRun_id')->unsigned();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();

            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRun_id', 'prsh_pr_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'prsh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region PayRunStep
        Schema::create('PayRunStep', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('payRun_id')->unsigned();
            $table->bigInteger('payRunTypeStep_id')->unsigned();

            $table->dateTime('startDateTime')->nullable();

            $table->string('name');
            $table->text('description')->nullable();

            $table->integer('order')->default(1);
            $table->char('status', 1)->default('N');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRun_id', 'prstep_pr_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('payRunTypeStep_id', 'prstep_prts_fn')->references('id')->on('PayRunTypeStep')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region PayRunStepStatusHistory
        Schema::create('PayRunStepStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('payRunStep_id')->unsigned();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();

            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRunStep_id', 'prstepsh_prs_fn')->references('id')->on('PayRunStep')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'prstepsh_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region PayRunItem
        Schema::create('PayRunItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('payRun_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->char('status', 1)->default('W');

            $table->dateTime('finishedDateTime')->nullable();
            $table->dateTime('paidDateTime')->nullable();
            $table->longText('paidData')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRun_id', 'pri_pr_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'pri_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region PayRunItemStatusHistory
        Schema::create('PayRunItemStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('payRunItem_id')->unsigned();
            $table->bigInteger('actorUser_id')->unsigned()->nullable();

            $table->dateTime('dateTime');
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();
            $table->char('status', 1);
            $table->char('oldStatus', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRunItem_id', 'pristatush_pri_fn')->references('id')->on('PayRunItem')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('actorUser_id', 'pristatush_actor_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion

        //region TimeSheet
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('payRunItem_id')->unsigned();
        });

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('timesheet_payrun_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropIndex('timesheet_payrun_fn');
            });
        } catch (Throwable) {
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('payRun_id')->unsigned()->nullable()->change();
        });

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('timesheet_user_fn');
            });
        } catch (Throwable) {
        }
        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropIndex('timesheet_user_fn');
            });
        } catch (Throwable) {
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('user_id')->unsigned()->nullable()->change();
        });

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropUnique('timesheet_u_p_unique');
            });
        } catch (Throwable) {
        }

        /** @var \stdClass[] $timeSheets */
        $timeSheets = DB::table('TimeSheet')->get();
        foreach ($timeSheets as $timeSheet) {
            if (is_null($timeSheet->payRun_id) || is_null($timeSheet->user_id)) {
                DB::table('TimeSheetAllowance')->where('timeSheet_id', '=', $timeSheet->id)->delete();
                DB::table('TimeSheetItem')->where('timeSheet_id', '=', $timeSheet->id)->delete();
                $days = DB::table('TimeSheetDay')->where('timeSheet_id', '=', $timeSheet->id)->get();
                foreach ($days as $day) {
                    $excessTimes = DB::table('TimeSheetDayExcessTime')->where('timeSheetDay_id', '=', $day->id)->get();
                    foreach ($excessTimes as $excessTime) {
                        DB::table('TimeSheetDayExcessTimeStatusHistory')->where('timeSheetDayExcessTime_id', '=', $excessTime->id)->delete();
                    }
                    DB::table('TimeSheetDayExcessTime')->where('timeSheetDay_id', '=', $day->id)->delete();
                }
                DB::table('TimeSheetDay')->where('timeSheet_id', '=', $timeSheet->id)->delete();
                DB::table('TimeSheetApproval')->where('timeSheet_id', '=', $timeSheet->id)->delete();
                DB::table('TimeSheet')->where('id', '=', $timeSheet->id)->delete();
            } else {
                /** @var \Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRunItem $payRunItem */
                $payRunItem = DB
                    ::table('PayRunItem')
                    ->where('payRun_id', '=', $timeSheet->payRun_id)
                    ->where('user_id', '=', $timeSheet->user_id)
                    ->first();
                if (is_null($payRunItem)) {
                    $payRunItem_id = DB::table('PayRunItem')->insertGetId([
                        'payRun_id' => $timeSheet->payRun_id,
                        'user_id' => $timeSheet->user_id,
                    ]);
                } else {
                    $payRunItem_id = $payRunItem->id;
                }
                DB::table('TimeSheet')->where('id', '=', $timeSheet->id)->update([
                    'payRunItem_id' => $payRunItem_id,
                ]);
            }
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->foreign('payRunItem_id', 'ts_pri_fn')->references('id')->on('PayRunItem')->onUpdate('cascade')->onDelete('restrict');
        });
        //endregion
    }

    public function down(): void
    {
        //region TimeSheet
        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropForeign('ts_pri_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('TimeSheet', 'payRunItem_id')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('payRunItem_id');
            });
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('payRun_id')->unsigned()->change();
        });

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->foreign('payRun_id', 'timesheet_payrun_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('restrict');
            });
        } catch (Throwable) {
        }

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('user_id')->unsigned()->change();
        });

        try {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->foreign('user_id', 'timesheet_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            });
        } catch (Throwable) {
        }
        //endregion

        //region PayRunItemStatusHistory
        Schema::dropIfExists('PayRunItemStatusHistory');
        //endregion

        //region PayRunItem
        Schema::dropIfExists('PayRunItem');
        //endregion

        //region PayRunStepStatusHistory
        Schema::dropIfExists('PayRunStepStatusHistory');
        //endregion

        //region PayRunStep
        Schema::dropIfExists('PayRunStep');
        //endregion

        //region PayRunStatusHistory
        Schema::dropIfExists('PayRunStatusHistory');
        //endregion

        //region PayRunTypeStep
        Schema::dropIfExists('PayRunTypeStep');
        //endregion
    }
};
