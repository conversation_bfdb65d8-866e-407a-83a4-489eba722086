<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();
    }

    public function down(): void
    {
        //region Table: UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'type')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('type');
            });
        }
        //endregion Table: UserShiftDay

        //region Table: Shift
        if (Schema::hasColumn('Shift', 'openDuration')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('openDuration');
            });
        }
        if (Schema::hasColumn('Shift', 'openCanEditDuration')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('openCanEditDuration');
            });
        }
        if (Schema::hasColumn('Shift', 'openBreakDuration')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('openBreakDuration');
            });
        }
        //endregion Table: Shift

        //region Table: User
        try {
            Schema::table('User', function (Blueprint $table) {
                $table->dropForeign('department_user_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('User', 'department_id')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('department_id');
            });
        }
        //endregion Table: User

        //region Table: Settings
        if (Schema::hasColumn('Settings', 'allowsIntegration')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('allowsIntegration');
            });
        }
        if (Schema::hasColumn('Settings', 'payrollSolution')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('payrollSolution');
            });
        }
        //endregion Table: Settings
    }
};
