<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Importing\Support\ImportTypeTypes\Enums\ImportTypeSubType\ImportTypeSubType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('ImportFile', function (Blueprint $table) {
            $table->string('subType')->nullable()->default(ImportTypeSubType::DEFAULT)->after('actorUser_id');

            $table->index(['subType'], 'if_subtype_ix');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('ImportFile', function (Blueprint $table) {
                $table->dropIndex('if_subtype_ix');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('ImportFile', 'subType')) {
            Schema::table('ImportFile', function (Blueprint $table) {
                $table->dropColumn('subType');
            });
        }
    }
};
