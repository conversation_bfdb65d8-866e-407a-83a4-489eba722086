elt-loading(ng-if='!!$ctrl.isLoading')
form.form-validate(ng-if='!$ctrl.isLoading' role='form' method='POST' action='javascript:' ng-submit='$ctrl.form.submit()')
  .panel.panel-default.m0
    .panel-heading.modal-topbar.pt0.pb0(ng-include='app.baseLayoutPath + "/modals/time-sheet-complete-missing-hours-with-work/heading.html"')

    .panel-body.pt0(ng-if='!!$ctrl.form.isLoading')
      elt-loading

    .panel-body.pt0(ng-if='!$ctrl.form.isLoading')
      .row.pt-lg.pb-lg(ng-repeat='settingsItem in $ctrl.form.scheduleSettings')
        .col-xs-12(ng-if='!$first')
          hr.hr

        .col-xs-12
          h3.h3.text-center.mt0.p0 Missing hours for role - {{ settingsItem.role }}

          .row
            .col-xs-12
              .form-group
                label.label-control Time type

                ui-select(
                  ng-if='settingsItem.options.time_types.length > 1'
                  ng-model='settingsItem.data.userRoleTimeType'
                  ng-required='true'
                  ng-disabled='!!$ctrl.form.isSubmitting'
                  ng-change='settingsItem.$events.onChangeTimeType()'
                  name='completeMisingHoursWork_{{$index}}_timeType'
                  theme='bootstrap'
                  reset-search-input='true'
                )
                  ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.name}}
                  ui-select-choices(repeat='object as object in settingsItem.options.time_types | filter : $select.search | orderBy: "id"')
                    div(ng-bind-html='object.name | highlight: $select.search')

                .lead.m0.bb(ng-if='settingsItem.options.time_types.length === 1') {{settingsItem.data.userRoleTimeType.name }}

          .row(ng-if='settingsItem.data.userRoleTimeType && settingsItem.data.userRoleTimeType.custom_content')
            .col-xs-12.mb-lg
              elt-custom-content.mb(ng-model='settingsItem.data.userRoleTimeType.custom_content')

          .row
            .col-xs-12.col-sm-4
              .form-group
                label.label-control Group

                ui-select(
                  ng-if='settingsItem.options.costCentreGroup.length > 1'
                  ng-model='settingsItem.data.costCentreGroup'
                  ng-required='true'
                  ng-disabled='!!$ctrl.form.isSubmitting'
                  ng-change='settingsItem.$events.onChangeCostCentreGroup()'
                  name='completeMisingHoursWork_{{$index}}_costCentreGroup'
                  theme='bootstrap'
                  reset-search-input='true'
                )
                  ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.label}}
                  ui-select-choices(repeat='object as object in settingsItem.options.costCentreGroup | filter : $select.search | orderBy: "id"')
                    div(ng-bind-html='object.label | highlight: $select.search')

                .lead.m0.bb(ng-if='settingsItem.options.costCentreGroup.length === 1') {{settingsItem.data.costCentreGroup.label }}

            .col-xs-12.col-sm-4(ng-if='!!settingsItem.data.costCentreGroup')
              .form-group
                label.label-control Project / Work-order

                ui-select(
                  ng-if='settingsItem.options.costCentreModel.length > 1'
                  ng-model='settingsItem.data.costCentreModel'
                  ng-required='true'
                  ng-disabled='!!$ctrl.form.isSubmitting'
                  ng-change='settingsItem.$events.onChangeCostCentreModel()'
                  name='completeMisingHoursWork_{{$index}}_costCentreModel'
                  theme='bootstrap'
                  reset-search-input='true'
                )
                  ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.label}}
                  ui-select-choices(repeat='object as object in settingsItem.options.costCentreModel | filter : $select.search | orderBy: "id"')
                    div(ng-bind-html='object.label | highlight: $select.search')

                .lead.m0.bb(ng-if='settingsItem.options.costCentreModel.length === 1') {{settingsItem.data.costCentreModel.label }}

            .col-xs-12.col-sm-4(ng-if='!!settingsItem.data.costCentreModel')
              .form-group
                label.label-control Activity / Sub-task

                ui-select(
                  ng-if='settingsItem.options.costCentreSub.length > 1'
                  ng-model='settingsItem.data.costCentreSub'
                  ng-required='true'
                  ng-disabled='!!$ctrl.form.isSubmitting'
                  ng-change='settingsItem.$events.onChangeCostCentreSub()'
                  name='completeMisingHoursWork_{{$index}}_costCentreSub'
                  theme='bootstrap'
                  reset-search-input='true'
                )
                  ui-select-match(placeholder='{{"form.search.TYPE_TO_SEARCH" | translate}}') {{$select.selected.label}}
                  ui-select-choices(repeat='object as object in settingsItem.options.costCentreSub | filter : $select.search | orderBy: "id"')
                    div(ng-bind-html='object.label | highlight: $select.search')

                .lead.m0.bb(ng-if='settingsItem.options.costCentreSub.length === 1') {{settingsItem.data.costCentreSub.label }}

          .row(ng-if='!!settingsItem.issues && settingsItem.issues.length > 0')
            .col-xs-12.pv-lg
              div(ng-include="app.baseLayoutPath + '/time-sheets/_common/issues-list.html'" ng-init='$_issuesList = {issues: settingsItem.issues, solve: null, color: "danger"};')

    .panel-footer
      .row
        .col-xs-12.text-center
          button.btn.btn-default(type='button' ng-click='$ctrl.cancel()') Close
          button.btn.btn-success.ml(type='submit' ng-disabled='$ctrl.form.isSubmitting || !$ctrl.form.isValid') Complete missing hours
