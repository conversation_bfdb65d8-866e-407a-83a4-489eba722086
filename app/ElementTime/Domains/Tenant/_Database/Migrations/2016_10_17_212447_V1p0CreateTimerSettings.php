<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        // Add fields on Settings table
        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('isTimerDefault')->nullable();
            $table->boolean('allowUsersToOptOutTimer')->nullable();
        });

        // Add fields on User table
        Schema::table('User', function (Blueprint $table) {
            $table->boolean('optOutTimer')->nullable();
        });
    }

    public function down(): void
    {
        // Remove fields from Settings table
        Schema::table('Settings', function (Blueprint $table) {
            $table->dropColumn([
                'isTimerDefault',
                'allowUsersToOptOutTimer',
            ]);
        });

        // Remove fields from User table
        Schema::table('User', function (Blueprint $table) {
            $table->dropColumn([
                'optOutTimer',
            ]);
        });
    }
};
