div(ng-if='!!$ctrl.form.calculation.data.doesHaveDayTimeAdjustments || !!$ctrl.form.calculation.data.doesHaveBreakDeductions')
  .row
    .col-xs-12.col-md-10.col-md-offset-1.mb-lg
      .list-group.mb0
        .list-group-item.list-group-item-warning(ng-if='!!$ctrl.form.calculation.data.doesHaveDayTimeAdjustments')
          .lead.m0
            strong Please note:
            |
            | you do not need to apply for leave for days or hours you are not scheduled to work. When applying for leave your requests will automatically adjust to match your schedule including accounting for unscheduled public holidays and weekends.
        .list-group-item.list-group-item-warning(ng-if='!!$ctrl.form.calculation.data.doesHaveBreakDeductions')
          .lead.m0
            strong Please note:
            |
            | this leave request period includes scheduled break time. The break time has been accounted for automatically and the leave request altered as a result.

.row
  .col-xs-12.col-md-10.col-md-offset-1
    .p-lg.rounded(ng-class='{"bg-info": !$ctrl.form.calculation.hasWarnings, "bg-warning": $ctrl.form.calculation.hasWarnings}')
      .row
        .col-xs-6
          .h4.mt.mb0.text-normal To take in this request
        .col-xs-6.text-right
          .h1.mt0.mb0.text-normal {{$ctrl.form.calculation.data.durationHuman}}
          span(ng-if='!!$ctrl.form.calculation.data.doesHaveFteAdjustments')
            .h5.sub.mt5.mb0 * ({{$ctrl.form.calculation.data.durationAdjustedHuman}} FTE adjusted)

      div(ng-if='!$ctrl.form.data.isFullDay && !!$ctrl.form.data.isApplyingByDuration')
        .row
          .col-xs-12
            hr.mt.mb
          .col-xs-6
            .h4.mt.mb0.text-normal Leave period
          .col-xs-6.text-right
            .h1.mt0.mb0.text-normal {{$ctrl.form.calculation.data.startTimeHuman}} to {{$ctrl.form.calculation.data.endTimeHuman}}

      .row(ng-if='$ctrl.form.calculation.data.afterAvailableDuration >= 0 || !!$ctrl.form.calculation.data.doesShowNegativeBalance')
        .col-xs-12
          hr.mt.mb
        .col-xs-6.text-normal
          .h4.mt.mb0.text-normal Estimated remaining balance after leave is taken
        .col-xs-6.text-right
          .h1.mt0.mb0.text-normal
            | {{$ctrl.form.calculation.data.afterAvailableDurationHuman}}
            span.text-bold {{$ctrl.form.calculation.data.afterAvailableDuration < 0 || !!$ctrl.form.calculation.data.doesUseProRata ? ' *' : ''}}

      .row(ng-if='!!$ctrl.form.calculation.hasWarnings')
        .col-xs-12
          hr.mt.mb-lg
        .col-xs-12.text-right(ng-repeat='warning in $ctrl.form.calculation.warnings')
          .h5.mt0.mb-xs * {{warning}}
