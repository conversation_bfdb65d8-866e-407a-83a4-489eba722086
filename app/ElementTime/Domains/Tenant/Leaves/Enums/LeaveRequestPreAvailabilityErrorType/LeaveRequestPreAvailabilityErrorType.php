<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\Leaves\Enums\LeaveRequestPreAvailabilityErrorType;

use Leocello\SweetEnum\SweetCase;
use Leocello\SweetEnum\SweetEnum;
use Leocello\SweetEnum\SweetEnumContract;

enum LeaveRequestPreAvailabilityErrorType: string implements SweetEnumContract
{
    use SweetEnum;

    // Note: Be careful to change the IDs of the cases - they are used on the front-end

    #[SweetCase(
        title: 'Not scheduled',
    )]
    case NotScheduled = 'not-scheduled';

    #[SweetCase(
        title: 'Duration is over the allowed',
    )]
    case OverMaximumAllowed = 'over-maximum-allowed';

    #[SweetCase(
        title: 'Clashed leave requests',
    )]
    case ClashedLeaveRequests = 'clashed-leave-requests';
}
