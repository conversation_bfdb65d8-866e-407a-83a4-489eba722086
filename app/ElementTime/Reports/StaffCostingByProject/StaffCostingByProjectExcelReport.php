<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\StaffCostingByProject;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\PayRunDetails\BasePayRunDetailsRow;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\PayRunDetails\ExcessTimeRow;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\PayRunDetails\PayRunDetailsDataSource;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\PayRunDetails\WorkedRow;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheet\TimeSheetRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Repositories\UserRepository;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Reports\_Traits\HasPayRunStartAndPayRunEndParameters;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Reports\TenantExcelReport;

class StaffCostingByProjectExcelReport extends TenantExcelReport
{
    use HasPayRunStartAndPayRunEndParameters {
        setData as _iSetData;
        getReplacedString as _iGetReplacedString;
    }

    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const REPORT_SLUG = 'staff-costing-by-project';
    const TYPE_SLUG = 'staff-costing-by-project.xls';

    /** @var string|StaffCostingByProjectReportConfig */
    public $c = StaffCostingByProjectReportConfig::class;

    /** @var User */
    protected $user;

    /** @var User */
    protected $loggedUser;

    protected function setData(array $info)
    {
        if (!isset($info['idUser']) || !is_numeric($info['idUser'])) {
            throw new InvalidArgumentException('User ID is missing');
        }

        $this->_iSetData($info);
        $this->user = UserRepository::getOneOrFail($info['idUser'])->getModel();
        $this->loggedUser = CurrentUser::isLogged()
            ? CurrentUser::getUser()
            : null;
    }

    public function getReplacedString(string $string): string
    {
        $string = $this->_iGetReplacedString($string);

        // user.id
        if (isset($this->user) && $this->user instanceof User) {
            $string = Str::replace('{{user.id}}', $this->user->id, $string);
        }

        // user.externalId
        if (isset($this->user) && $this->user instanceof User) {
            $string = Str::replace('{{user.externalId}}', $this->user->externalId, $string);
        }

        // user.fullName
        if (isset($this->user) && $this->user instanceof User) {
            $string = Str::replace('{{user.fullName}}', $this->user->fullName, $string);
        }

        return $string;
    }

    protected function buildReportData()
    {
        $data = $this->collectData();

        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetTotals($sheet, $data);
        $this->fetchSheetData($sheet, $data);

        $this->sheets = [
            $sheet,
        ];
    }

    /**
     * @return \Illuminate\Support\Collection|BasePayRunDetailsRow[]
     * @throws InvalidArgumentException
     */
    protected function collectData(): \Illuminate\Support\Collection|array
    {
        $constraints = [
            ['User.id', '=', $this->user->id],
            ['PayRun.startDate', '>=', $this->payRunStart->startDate->toDateString()],
            ['PayRun.endDate', '<=', $this->payRunEnd->endDate],
        ];

        if (!is_null($this->loggedUser) && !$this->doesUserHaveFullAccess($this->loggedUser)) {
            $reports = $this->getReportsUser($this->loggedUser, true);
            $reportIds = [];
            foreach ($reports as $report) {
                $reportIds[] = $report->id;
            }
            $constraints[] = ['IN', 'Workflow.ownerUser_id', $reportIds];
        }

        $timeSheets = TimeSheetRepository::getMany([
            'constraints' => $constraints,
            'returnType' => 'model',
        ]);

        return PayRunDetailsDataSource
            ::new($timeSheets)
            ->withWorked()
            ->withExcessTime()
            ->sortBy('projectName')
            ->get();
    }

    /**
     * @param \stdClass|null $sheet
     * @param \Illuminate\Support\Collection|BasePayRunDetailsRow[]|null $rows
     */
    protected function fetchSheetTotals(\stdClass &$sheet = null, \Illuminate\Support\Collection|array $rows = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Staff', 'text', 'center', 4),
            $this->createColumn('Period', 'text', 'center', 4),
            $this->createColumn('Total hours recorded', 'float', 'center', 3),
            $this->createColumn('Total hours adjusted', 'float', 'center', 4),
        ];

        $actualHours = 0;
        $adjustedHours = 0;

        foreach ($rows as $row) {
            if (!$this->isRowShown($row)) {
                continue;
            }

            if ($row instanceof WorkedRow) {
                $actualHours += $row->adjustedHours;
                $adjustedHours += $row->adjustedHours;
            } elseif ($row instanceof ExcessTimeRow) {
                $actualHours += $row->recordedHours;
                $adjustedHours += $row->adjustedHours;
            } else {
                throw new \TypeError('data');
            }
        }

        $data->data = [[
            'config' => [],
            'data' => [
                $this->user->externalId . ' - '  . $this->user->fullName, // Staff
                $this->payRunStart->startDate->format($this->dateFormat) . ' to ' . $this->payRunEnd->endDate->format($this->dateFormat), // Period
                $this->parseFloat($actualHours), // Total hours paid recorded
                $this->parseFloat($adjustedHours), // Total hours paid adjusted
            ],
        ]];
        $sheet->data[] = $data;
    }

    /**
     * @param \stdClass|null $sheet
     * @param \Illuminate\Support\Collection|BasePayRunDetailsRow[]|null $rows
     */
    protected function fetchSheetData(\stdClass &$sheet = null, \Illuminate\Support\Collection|array $rows = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = $this->getSheetDataColumns();
        $data->data = $this->getSheetDataData($rows);
        $sheet->data[] = $data;
    }

    protected function getSheetDataColumns(): array
    {
        return [
            $this->createColumn('#', 'integer', 'center'),
            $this->createColumn('Project / Work order name > Task > Activity'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Hour code'),
            $this->createColumn('Project / Work order Code'),
            $this->createColumn('Task / Cost Code'),
            $this->createColumn('Activity / Resource Code'),
            $this->createColumn('Owner'),
            $this->createColumn('Status'),
            $this->createColumn('Start date'),
            $this->createColumn('End date'),
            $this->createFloatColumn('Actual recorded hours'),
            $this->createFloatColumn('Adjusted hours'),
        ];
    }

    protected function getSheetDataData(\Illuminate\Support\Collection $rows = null): array
    {
        $data = $this->compileDataRows($rows);

        $ret = [];

        $i = 1;
        foreach ($data as $datum) {
            $rowConfig = [
                'color' => $i % 2 != 0 ? 'FFFFFF' : 'DFDFF3',
            ];

            $ret[] = [
                'config' => $rowConfig,
                'data' => [
                    $i, // #
                    $datum->projectName, // Project / Work order name > Task > Activity
                    $datum->role, // Role
                    $datum->department, // Department
                    $datum->externalId, // Ext ref ID
                    $datum->hourCode, // Hour code
                    $datum->projectCode, // Project / Work order Code
                    $datum->costCode, // Task / Cost Code
                    $datum->activityCode, // Activity / Resource Code
                    $datum->projectOwner, // Owner
                    $datum->projectStatus, // Status
                    $datum->projectStartDate, // Start date
                    $datum->projectEndDate ?? '-', // End date
                    $datum->actualHours, // Actual recorded hours
                    $datum->adjustedHours, // Adjusted hours
                ],
            ];

            $i++;
        }

        return $ret;
    }

    /**
     * @param \Illuminate\Support\Collection|WorkedRow[]|ExcessTimeRow[]|null $rows
     * @return array
     */
    protected function compileDataRows(\Illuminate\Support\Collection|array $rows = null): array
    {
        if (is_null($rows)) {
            return [];
        }

        $ret = [];

        foreach ($rows as $row) {
            if (!$this->isRowShown($row)) {
                continue;
            }

            $rowId = '_'
                . $row->formattedCode . '__'
                . $row->projectName . '__'
                . $row->userRoleId . '__'
                . $row->hourCode . '__'
            ;

            if (!isset($ret[$rowId])) {
                $obj = new \stdClass;

                $obj->projectName = $row->projectName;
                $obj->role = $row->roleName;
                $obj->department = $row->departmentName;
                $obj->externalId = $row->projectExternalId;
                $obj->hourCode = $row->hourCode;
                $obj->projectCode = $row->projectCode;
                $obj->costCode = $row->costCode;
                $obj->activityCode = $row->activityCode;
                $obj->projectOwner = $row->projectOwnerFullName;
                $obj->projectStatus = is_null($row->projectStatus) ? null : ($row->projectStatus == 'I' ? 'Inactive' : (!is_null($row->projectEndDate) && $row->projectEndDate->lt(Carbon::today()) ? 'Ended' : 'Active'));
                $obj->projectStartDate = $row->projectStartDate ? $row->projectStartDate->format($this->dateFormat) : null;
                $obj->projectEndDate = $row->projectEndDate ? $row->projectEndDate->format($this->dateFormat) : null;
                $obj->actualHours = 0;
                $obj->adjustedHours = 0;

                $ret[$rowId] = $obj;
            }

            if ($row instanceof WorkedRow) {
                $ret[$rowId]->actualHours += $row->adjustedHours;
                $ret[$rowId]->adjustedHours += $row->adjustedHours;
            } elseif ($row instanceof ExcessTimeRow) {
                $ret[$rowId]->actualHours += $row->recordedHours;
                $ret[$rowId]->adjustedHours += $row->adjustedHours;
            }
        }

        return array_values($ret);
    }

    protected function isRowShown(WorkedRow|ExcessTimeRow $row): bool
    {
        if (is_null($this->loggedUser) || $this->doesUserHaveFullAccess($this->loggedUser)) {
            return true;
        }
        if ($this->loggedUser->repository->isManagerOfStaff($this->user, false)) {
            return true;
        }

        $activeUserRoles = $this->user->getActiveUserRoles();
        foreach ($activeUserRoles as $activeUserRole) {
            $activeUserRoleManager = $activeUserRole->activeUserRoleManager;
            if (is_null($activeUserRoleManager)) {
                continue;
            }
            if ($activeUserRoleManager->userRole_id != $row->userRoleId) {
                continue;
            }
            if ($activeUserRoleManager->manager_id != $this->loggedUser->id) {
                continue;
            }

            return true;
        }

        return false;
    }
}
