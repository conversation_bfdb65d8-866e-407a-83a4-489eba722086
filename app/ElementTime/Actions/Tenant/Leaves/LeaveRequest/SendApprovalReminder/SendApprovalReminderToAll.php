<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SendApprovalReminder;

use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Exceptions\UnauthorizedWorkflowException;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes;

class SendApprovalReminderToAll extends BaseLeavesAction
{
    /** @throws \Throwable */
    public function handle(User $actor): void
    {
        if (!$actor->access->isPayrollOfficer(true)) {
            throw new UnauthorizedWorkflowException([
                'action' => 'send',
                'type' => 'reminders',
                'reason' => 'Only payroll officers can do it',
            ]);
        }

        $constraints = [
            ['IN', 'Workflow.status', [
                WorkflowStatusTypes\SubmittedType::ID,
                WorkflowStatusTypes\PartiallyApprovedType::ID,
            ]],
        ];

        $relations = [
            'workflow.rel',
            'workflow.steps.items',
        ];

        $regs = LeaveRequest::q(constraints: $constraints, relations: $relations)->many();

        foreach ($regs as $reg) {
            SendApprovalReminder::make()->handle($actor, $reg);
        }
    }
}
