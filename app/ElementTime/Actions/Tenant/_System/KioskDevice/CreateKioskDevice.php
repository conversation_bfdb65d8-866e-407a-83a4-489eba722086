<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\_System\KioskDevice;

use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Actions\Tenant\_System\BaseSystemAction;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDevice;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;
use Ramsey\Uuid\Uuid;

class CreateKioskDevice extends BaseSystemAction
{
    /** @throws \Throwable */
    public function handle(KioskDeviceRequest $kioskDeviceRequest): KioskDevice
    {
        $kioskDevice = new KioskDevice;

        $kioskDevice->deviceId = $kioskDeviceRequest->deviceId;
        $kioskDevice->name = $kioskDeviceRequest->name;

        $kioskDevice->appId = Uuid::uuid4()->toString();
        $kioskDevice->secret = Str::random(40) . hash('crc32b', $kioskDevice->deviceId);

        $kioskDevice->saveOrFail();

        $kioskDeviceRequest->kioskDevice_id = $kioskDevice->id;
        $kioskDeviceRequest->saveOrFail();

        return $kioskDevice;
    }
}
