<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Leave', function (Blueprint $table) {
            $table->bigInteger('cancelManager_id')->unsigned()->nullable();
            $table->text('cancelReason')->nullable();
            $table->dateTime('cancelDateTime')->nullable();
            $table->foreign('cancelManager_id', 'leave_cm_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->bigInteger('cancelManager_id')->unsigned()->nullable();
            $table->text('cancelReason')->nullable();
            $table->dateTime('cancelDateTime')->nullable();
            $table->foreign('cancelManager_id', 'toil_cm_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->bigInteger('cancelManager_id')->unsigned()->nullable();
            $table->text('cancelReason')->nullable();
            $table->dateTime('cancelDateTime')->nullable();
            $table->foreign('cancelManager_id', 'rto_cm_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        if (DB::select(DB::raw('SHOW KEYS FROM RosteredTimeOff WHERE Key_name=\'rto_cm_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropForeign('rto_cm_fn');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'cancelReason')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('cancelReason');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'cancelDateTime')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('cancelDateTime');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'cancelManager_id')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('cancelManager_id');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM Toil WHERE Key_name=\'toil_cm_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropForeign('toil_cm_fn');
            });
        }
        if (Schema::hasColumn('Toil', 'cancelReason')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('cancelReason');
            });
        }
        if (Schema::hasColumn('Toil', 'cancelDateTime')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('cancelDateTime');
            });
        }
        if (Schema::hasColumn('Toil', 'cancelManager_id')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('cancelManager_id');
            });
        }
        if (DB::select(DB::raw('SHOW KEYS FROM `Leave` WHERE Key_name=\'leave_cm_fn\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropForeign('leave_cm_fn');
            });
        }
        if (Schema::hasColumn('Leave', 'cancelReason')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('cancelReason');
            });
        }
        if (Schema::hasColumn('Leave', 'cancelDateTime')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('cancelDateTime');
            });
        }
        if (Schema::hasColumn('Leave', 'cancelManager_id')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('cancelManager_id');
            });
        }
    }
};
