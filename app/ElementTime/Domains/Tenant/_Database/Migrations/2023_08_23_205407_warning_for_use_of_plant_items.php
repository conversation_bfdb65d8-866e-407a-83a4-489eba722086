<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('PlantItem', function(Blueprint $table) {
            $table->boolean('doesShowWarningIfUsed')->default(false)->after('workOrderSuffixCode');
            $table->boolean('doesSuggestPayTypeOnWarning')->default(false)->after('doesShowWarningIfUsed');
        });

        Schema::table('PlantItem', function (Blueprint $table) {
            $table->unsignedBigInteger('suggestedPayTypeOnWarning_id')->after('doesSuggestPayTypeOnWarning')->nullable();
            $table->foreign('suggestedPayTypeOnWarning_id', 'pi_sptow_fn')->references('id')->on('PayType')->onDelete('set null');
        });
    }

    public function down(): void
    {
        try {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropForeign('pi_sptow_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('PlantItem', 'doesShowWarningIfUsed')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('doesShowWarningIfUsed');
            });
        }

        if (Schema::hasColumn('PlantItem', 'doesSuggestPayTypeOnWarning')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('doesSuggestPayTypeOnWarning');
            });
        }

        if (Schema::hasColumn('PlantItem', 'suggestedPayTypeOnWarning_id')) {
            Schema::table('PlantItem', function (Blueprint $table) {
                $table->dropColumn('suggestedPayTypeOnWarning_id');
            });
        }
    }
};
