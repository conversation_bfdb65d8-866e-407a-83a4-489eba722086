<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('ExcessTimeRuleSplitItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('excessTimeRule_id')->nullable();
            $table->foreign('excessTimeRule_id', 'etri_etr_fn')
                ->references('id')->on('ExcessTimeRule')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('externalId')->nullable();
            $table->string('ruleType');
            $table->decimal('percentage', 16, 10);
            $table->decimal('ratio', 16, 10)->nullable();
        });

        Schema::create('TimeSheetExcessTimeItemRuleWorkSplitItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('excessTimeRuleSplitItem_id')->nullable();
            $table->foreign('excessTimeRuleSplitItem_id', 'tsetirwsi_etri_fn')
                ->references('id')->on('ExcessTimeRuleSplitItem')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('timeSheetExcessTimeItemRuleWork_id');
            $table->foreign('timeSheetExcessTimeItemRuleWork_id', 'tsetirwsi_tsetirw_fn')
                ->references('id')->on('TimeSheetExcessTimeItemRuleWork')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->decimal('actualHours', 16, 10);
            $table->decimal('adjustedHours', 16, 10);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('TimeSheetExcessTimeItemRuleWorkSplitItem');
        Schema::dropIfExists('ExcessTimeRuleSplitItem');
    }
};
