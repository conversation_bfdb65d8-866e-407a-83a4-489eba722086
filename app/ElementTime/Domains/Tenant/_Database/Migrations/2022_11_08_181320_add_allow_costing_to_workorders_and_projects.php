<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('AllowanceType', function (Blueprint $table) {
            $table->boolean('allowCostingToWorkOrdersAndProjects')->default(false)->after('incrementRate_id');

            $table->string('workOrderProjectCode')->nullable()->after('activityCode');
            $table->string('workOrderCostCode')->nullable()->after('activityCode');
            $table->string('workOrderActivityCode')->nullable()->after('activityCode');
        });

        Schema::table('UserAllowanceType', function (Blueprint $table) {
            $table->string('workOrderProjectCode')->nullable()->after('activityCode');
            $table->string('workOrderCostCode')->nullable()->after('activityCode');
            $table->string('workOrderActivityCode')->nullable()->after('activityCode');
        });

        Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
            $table->unsignedBigInteger('timeSheetDayTime_id')->nullable()->after('timeSheetAllowanceDay_id');
            $table->foreign('timeSheetDayTime_id', 'tsade_tsdt_fn')
                ->references('id')->on('TimeSheetDayTime')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('timeSheetDayTimeWork_id')->nullable()->after('timeSheetDayTime_id');
            $table->foreign('timeSheetDayTimeWork_id', 'tsade_tsdtw_fn')
                ->references('id')->on('TimeSheetDayTimeWork')
                ->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('AllowanceType', 'allowCostingToWorkOrdersAndProjects')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('allowCostingToWorkOrdersAndProjects');
            });
        }
        if (Schema::hasColumn('AllowanceType', 'workOrderProjectCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderProjectCode');
            });
        }
        if (Schema::hasColumn('AllowanceType', 'workOrderCostCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderCostCode');
            });
        }
        if (Schema::hasColumn('AllowanceType', 'workOrderActivityCode')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityCode');
            });
        }

        if (Schema::hasColumn('UserAllowanceType', 'workOrderProjectCode')) {
            Schema::table('UserAllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderProjectCode');
            });
        }
        if (Schema::hasColumn('UserAllowanceType', 'workOrderCostCode')) {
            Schema::table('UserAllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderCostCode');
            });
        }
        if (Schema::hasColumn('UserAllowanceType', 'workOrderActivityCode')) {
            Schema::table('UserAllowanceType', function (Blueprint $table) {
                $table->dropColumn('workOrderActivityCode');
            });
        }

        try {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropForeign('tsade_tsdt_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetAllowanceDayEntry', 'timeSheetDayTime_id')) {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropColumn('timeSheetDayTime_id');
            });
        }

        try {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropForeign('tsade_tsdtw_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetAllowanceDayEntry', 'timeSheetDayTimeWork_id')) {
            Schema::table('TimeSheetAllowanceDayEntry', function (Blueprint $table) {
                $table->dropColumn('timeSheetDayTimeWork_id');
            });
        }
    }
};
