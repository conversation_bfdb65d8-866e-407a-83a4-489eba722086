<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\ExternalIntegrations\Enums\ExternalCalendarEntryEventType\ExternalCalendarEntryEventType;
use Element\ElementTime\Domains\Tenant\ExternalIntegrations\Enums\ExternalCalendarEntryStatus\ExternalCalendarEntryStatus;
use Element\ElementTime\Domains\Tenant\ExternalIntegrations\Enums\ExternalCalendarEntryType\ExternalCalendarEntryType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('ExternalCalendarSet', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('model_type')->nullable();
            $table->unsignedBigInteger('model_id')->nullable();

            $table->string('entryType')->default(ExternalCalendarEntryType::DEFAULT->id());

            $table->string('email', 250);
            $table->string('name')->nullable();
            $table->boolean('doesIncludeNested')->default(false);

            $table->timestamps();

            $table->index(['model_type', 'model_id'], 'ecs_model_ix');
            $table->index(['entryType'], 'ecs_entryType_ix');
        });

        Schema::create('ExternalCalendarEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('type')->default(ExternalCalendarEntryType::DEFAULT->id());

            $table->string('event_type')->nullable();
            $table->unsignedBigInteger('event_id')->nullable();

            $table->string('recipient_type')->nullable();
            $table->unsignedBigInteger('recipient_id')->nullable();

            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->string('email', 250);
            $table->text('externalId')->nullable();

            $table->string('status')->nullable();
            $table->timestamps();

            $table->unique(['event_type', 'event_id', 'email'], 'ece_ev_em_uq');
            $table->index(['type'], 'ece_type_ix');
            $table->index(['event_type', 'event_id'], 'ece_event_ix');
            $table->index(['recipient_type', 'recipient_id'], 'ece_recipient_ix');
            $table->index(['email'], 'ece_email_ix');
            $table->index(['status'], 'ece_status_ix');
        });

        Schema::create('ExternalCalendarEntryEvent', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('externalCalendarEntry_id');
            $table->foreign('externalCalendarEntry_id', 'ecee_ece_fn')
                ->references('id')->on('ExternalCalendarEntry')
                ->onUpdate('cascade')->onDelete('cascade');

            $table->string('trigger_type')->nullable();
            $table->unsignedBigInteger('trigger_id')->nullable();

            $table->string('type')->default(ExternalCalendarEntryEventType::DEFAULT);

            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->string('status')->default(ExternalCalendarEntryStatus::DEFAULT);
            $table->dateTime('dateTime');
            $table->json('details')->nullable();

            $table->timestamps();

            $table->index(['trigger_type', 'trigger_id'], 'ecee_trigger_ix');
            $table->index(['type'], 'ecee_type_ix');
            $table->index(['status'], 'ecee_status_ix');
            $table->index(['dateTime'], 'ecee_dateTime_ix');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ExternalCalendarEntryEvent');
        Schema::dropIfExists('ExternalCalendarEntry');
        Schema::dropIfExists('ExternalCalendarSet');
    }
};
