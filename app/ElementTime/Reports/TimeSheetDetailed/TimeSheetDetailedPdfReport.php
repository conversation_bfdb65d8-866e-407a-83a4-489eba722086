<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\TimeSheetDetailed;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Facades\Avatar;
use Element\Core\Support\Facades\Image;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\LeaveRequestDayRepository;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Projects\Models\Project;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayRunType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAdvisoryNote;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowance;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheet\TimeSheetRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserAdvisoryNote;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Support\Enums\ExcessTimeHourType;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Helpers\FileSystem;
use Element\ElementTime\Support\Reports\TenantPdfReport;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class TimeSheetDetailedPdfReport extends TenantPdfReport
{
    const IS_LISTED = false;
    const REPORT_SLUG = 'time-sheet-detailed';
    const TYPE_SLUG = 'time-sheet-detailed.pdf';

    public $c = TimeSheetDetailedReportConfig::class;
    protected $viewContent = 'Reports::TimeSheetDetailed.pdf';
    protected $dataHeader = ['type' => 'Individual pay run report'];

    /** @var TimeSheet */
    protected $timeSheet;

    /** @var User */
    protected $user;

    /** @var PayRun */
    protected $payRun;

    /** @var PayRunType */
    protected $payRunType;

    /**
     * @param array $info
     * @throws InvalidArgumentException
     */
    protected function setData(array $info): void
    {
        if (!isset($info['id']) || !is_numeric($info['id'])) {
            throw new InvalidArgumentException('Timesheet ID is missing');
        }

        $this->timeSheet = TimeSheetRepository::getOneOrFail([
            'constraints' => [
                ['TimeSheet.id', '=', $info['id']],
            ],
            'relations' => [
                'workflow',
                'payRunItem.payRun.payRunType',
                'payRunItem.user',
            ],
            'returnType' => 'model',
        ]);

        $this->user = $this->timeSheet->payRunItem->user;
        $this->payRun = $this->timeSheet->payRunItem->payRun;
        $this->payRunType = $this->timeSheet->payRunItem->payRun->payRunType;
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$this->payRun->isFinished()) {
            $this->reportValidity = Carbon::now()->addWeeks(2);
        }

        return $this;
    }

    /**
     * @throws InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     * @throws \Throwable
     */
    protected function buildReportData(): void
    {
        /** @var TimeSheet $timeSheet */
        $timeSheet = TimeSheetRepository::getOneOrFail([
            'constraints' => [
                ['TimeSheet.id', '=', $this->timeSheet->id],
            ],
            'relations' => [
                'workflow.timelineEntries.actorUser',

                'payRunItem.user.departments',
                'payRunItem.user.employeeType',
                'payRunItem.user.userRoles.role',
                'payRunItem.user.userRoles.userRoleMasters',
                'payRunItem.user.userRoles.userPayTypes.payType.payBand',
                'payRunItem.user.userManagers.manager',

                'penaltySets.userPenaltyType.penaltyType',

                'timeSheetDayTimes.model',
                'timeSheetDayTimes.type.project',
                'timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.userRoles.userRoleProjects.project',
                'timeSheetDayTimes.excessTimes.items.rules.item.timeSheetExcessTime',
                'timeSheetDayTimes.userShiftTimeType.timeType',
                'timeSheetDayTimes.penalties',

                'timeSheetAllowances.timeSheet.payRunItem',
                'timeSheetAllowances.allowanceType',
                'timeSheetAllowances.timeSheetAllowanceDays.timeSheetAllowanceDayEntries',

                'payRunItem.user.userLeaveBankTypes.type',
                'payRunItem.user.userLeaveBankTypes.bank.user.userRoles.userRoleProjects.project',
                'payRunItem.user.userLeaveBankTypes.bank.entries',
                'payRunItem.user.userLeaveBankTypes.bank.types.type',
                'payRunItem.user.userLeaveBankTypes.bank.types.bank.user',

                'timeSheetDays.excessTime.items.rules.item.timeSheetExcessTime',
                'timeSheetDays.timeSheetDayTimes.userShiftDay.userShift.userRole.role',
                'timeSheetDays.timeSheetDayTimes.userShiftTimeType.timeType',
                'timeSheetDays.timeSheetDayTimes.breaks',
                'timeSheetDays.timeSheetDayTimes.model',
                'timeSheetDays.timeSheetDayTimes.type',
                'timeSheetDays.timeSheetDayTimes.timeSheetDay.timeSheet.payRunItem.user.userRoles',
                'timeSheetDays.timeSheetAllowanceDays.timeSheetAllowance.allowanceType',
                'timeSheetDays.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTime',
                'timeSheetDays.timeSheetAllowanceDays.timeSheetAllowanceDayEntries.timeSheetDayTimeWork',
            ],
            'returnType' => 'model',
        ]);

        $this->dataContent = [
            'config' => $this->getConfigData($timeSheet),
            'tenant' => $this->getTenantData(),
            'report' => $this->getReportData(),

            'projects' => $this->getProjectsList($timeSheet),
            'leaves' => $this->getLeaveTypesList($timeSheet, $leaveRequestDays),
            'timeTypes' => $this->getTimeTypesList($timeSheet, $leaveRequestDays),
            'allowances' => $this->getAllowancesList($timeSheet),
            'publicHolidays' => $this->getPublicHolidaysList($timeSheet),

            'timeSheet' => $this->getTimeSheetData($timeSheet, $leaveRequestDays),
        ];
    }

    protected function getReportData(): \stdClass
    {
        return json_decode(json_encode([
            'date' => Carbon::today()->format($this->dateFormat),
        ]));
    }

    protected function getTenantData(): \stdClass
    {
        return json_decode(json_encode([
            'name' => $this->tenant->name,
        ]));
    }

    protected function getConfigData(TimeSheet $timeSheet): \stdClass
    {
        $ret = new \stdClass();
        $ret->dateFormat = $this->dateFormat;
        $ret->dateTimeFormat = $this->dateTimeFormat;
        $ret->hasLeaveOptions = $timeSheet->user->hasLeaveOptions;
        $ret->doesShowLeaveObs = false;
        $ret->accruedHoursLeaveName = TenantSystemSettings::getValue('settings.customExcessTimeLeaveName');
        $ret->accruedHoursBalanceName = TenantSystemSettings::getValue('settings.customExcessTimeBalanceName');

        return $ret;
    }

    protected function getProjectsList(TimeSheet $timeSheet): Collection
    {
        $ret = [];
        foreach ($timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                $row_id = $timeSheetDayTime->model_type . '_' . $timeSheetDayTime->model_id . '_' . $timeSheetDayTime->formattedCode;
                if (!isset($ret[$row_id])) {
                    $retItem = new \stdClass();
                    $retItem->code = Str::replace(' ', '$nbsp;', $timeSheetDayTime->formattedCode);
                    $retItem->name = $timeSheetDayTime->model instanceof Project ? $timeSheetDayTime->model->name : $timeSheetDayTime->model->project->name . ' - ' . $timeSheetDayTime->model->name;
                    $retItem->hours = $timeSheetDayTime->finalCalculatedHours;
                    $ret[$row_id] = $retItem;
                } else {
                    $ret[$row_id]->hours += $timeSheetDayTime->finalCalculatedHours;
                }
            }
        }

        return collect(array_values(Arr::sort($ret, function ($item) {
            return $item->name;
        })));
    }

    /**
     * @param Collection|LeaveRequestDay[]|null $leaveRequestDays $leaveRequestDays
     */
    protected function getLeaveTypesList(TimeSheet $timeSheet, Collection|array|null &$leaveRequestDays = null): Collection
    {
        $payRun = $timeSheet->payRun;
        $user = $timeSheet->user;

        if (is_null($leaveRequestDays)) {
            /** @var Collection|LeaveRequestDay[] $leaveRequestDays */
            $leaveRequestDays = LeaveRequestDayRepository::getMany([
                'constraints' => [
                    ['User.id', '=', $this->user->id],
                    ['LeaveRequestDay.date', '<=', $timeSheet->payRun->endDate->toDateString()],
                    ['LeaveRequestDay.date', '>=', $timeSheet->payRun->startDate->toDateString()],
                    ['IN', 'Workflow.status', [
                        ApprovedType::ID,
                    ]],
                ],
                'relations' => [
                    'leaveRequest.userLeaveBankType.type',
                    // 'leaveRequest.userLeaveBankType.bank.user.userRoles.userRoleProjects',
                    'userShiftDay.userShift.shift',
                    'userShiftDay.userShift.userRole.role',
                ],
                'returnType' => 'model',
            ]);
        }

        $userLeaveBankTypes = $user->userLeaveBankTypes;

        $ret = [];
        foreach ($userLeaveBankTypes as $userLeaveBankType) {
            $retItem = new \stdClass();
            $retItem->code = Str::replace(' ', '$nbsp;', $userLeaveBankType->formattedCode);
            $retItem->name = $userLeaveBankType->typeName;

            $retItem->hoursUsed = 0;
            foreach ($leaveRequestDays as $leaveRequestDay) {
                if ($leaveRequestDay->leaveRequest->userLeaveBankType_id == $userLeaveBankType->id) {
                    $retItem->hoursUsed += $leaveRequestDay->calculatedHours;
                }
            }
            $retItem->hoursEarnt = $userLeaveBankType->repository->getBalanceEarntByPayRunItem($timeSheet->payRunItem);
            $retItem->balanceAvailable = $userLeaveBankType->bank->repository->getAvailableBalance($payRun->endDate->copy()->addDays(1));

            if ($retItem->hoursEarnt != 0 || $retItem->hoursUsed != 0 || $retItem->balanceAvailable != 0) {
                $ret[] = $retItem;
            }
        }

        return collect(array_values(Arr::sort($ret, function ($item) {
            return $item->name;
        })));
    }

    /**
     * @param Collection|LeaveRequestDay[]|null $leaveRequestDays
     */
    protected function getTimeTypesList(TimeSheet $timeSheet, Collection|array|null $leaveRequestDays = null): Collection
    {
        $ret = [];
        foreach ($timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                $row_id = '_holiday';
                if (!isset($ret[$row_id])) {
                    $retItem = new \stdClass();
                    $retItem->code = '';
                    $retItem->name = 'Public holidays';
                    $retItem->hours = $timeSheetDayTime->finalCalculatedHours;
                    $ret[$row_id] = $retItem;
                } else {
                    $ret[$row_id]->hours += $timeSheetDayTime->finalCalculatedHours;
                }
            }
            if ($timeSheetDayTime->recordTypeClass::IS_WORK) {
                $row_id = '_' . $timeSheetDayTime->userShiftTimeType->timeType_id;
                if (!isset($ret[$row_id])) {
                    $retItem = new \stdClass();
                    $retItem->code = $timeSheetDayTime->userShiftTimeType->timeType->hourCode;
                    $retItem->name = $timeSheetDayTime->userShiftTimeType->timeType->name;
                    $retItem->hours = $timeSheetDayTime->finalCalculatedHours;
                    $ret[$row_id] = $retItem;
                } else {
                    $ret[$row_id]->hours += $timeSheetDayTime->finalCalculatedHours;
                }
            }
        }

        foreach ($leaveRequestDays as $leaveRequestDay) {
            $row_id = '_leave';
            if (!isset($ret[$row_id])) {
                $retItem = new \stdClass();
                $retItem->code = '';
                $retItem->name = 'Leave';
                $retItem->hours = $leaveRequestDay->calculatedHours;
                $ret[$row_id] = $retItem;
            } else {
                $ret[$row_id]->hours += $leaveRequestDay->calculatedHours;
            }
        }

        return collect(array_values(Arr::sort($ret, function ($item) {
            return $item->name;
        })));
    }

    protected function getAllowanceAmount(TimeSheetAllowance $allowance): string
    {
        switch ($allowance->allowanceType->rateType) {
            case 'H':
                return etime_round($allowance->totalHours, 2) . ' hours';
            case 'D':
            case 'F':
                $count = 0;
                foreach ($allowance->timeSheetAllowanceDays as $allowanceDay) {
                    $count += $allowanceDay->timeSheetAllowanceDayEntries->count();
                }

                return $count . ' instances';
            case 'M':
                return etime_round($allowance->totalMiles, 2) . ' km';
        }

        return '-';
    }

    protected function getAllowancesList(TimeSheet $timeSheet): Collection
    {
        $allowances = [];
        $mappedAllowances = [];

        foreach ($timeSheet->timeSheetAllowances as $item) {
            $name = $item->allowanceType->name;

            foreach ($item->timeSheetAllowanceDays as $day) {
                foreach ($day->timeSheetAllowanceDayEntries as $entry) {
                    if (!isset($mappedAllowances[$entry->formattedCode])) {
                        $mappedAllowances[$entry->formattedCode] = new \stdClass();
                        $mappedAllowances[$entry->formattedCode]->code = Str::replace(' ', '$nbsp;', $entry->formattedCode);
                        $mappedAllowances[$entry->formattedCode]->name = $name;
                        $mappedAllowances[$entry->formattedCode]->quantity = 0;
                        $mappedAllowances[$entry->formattedCode]->amount = '';
                        $mappedAllowances[$entry->formattedCode]->value = 0.0;
                        $mappedAllowances[$entry->formattedCode]->miles = 0.0;
                        $mappedAllowances[$entry->formattedCode]->hours = 0.0;
                        $mappedAllowances[$entry->formattedCode]->type = $item->allowanceType;
                    }
                    $mappedAllowances[$entry->formattedCode]->quantity += 1;
                    $mappedAllowances[$entry->formattedCode]->value += $entry->value;
                    $mappedAllowances[$entry->formattedCode]->miles += $entry->miles;
                    $mappedAllowances[$entry->formattedCode]->hours += $entry->hours;
                }
            }
        }

        foreach ($mappedAllowances as $mappedAllowanceItem) {
            $mappedAllowanceItem->amount = '-';
            if ($mappedAllowanceItem->type == 'H') {
                $mappedAllowanceItem->amount = etime_round($mappedAllowanceItem->hours, 2) . ' hours';
            } elseif ($mappedAllowanceItem->type == 'D' || $mappedAllowanceItem->type == 'F') {
                $mappedAllowanceItem->amount = $mappedAllowanceItem->quantity . ' instances';
            } elseif ($mappedAllowanceItem->type == 'M') {
                $mappedAllowanceItem->amount = etime_round($mappedAllowanceItem->miles, 2) . ' km';
            }
            $allowances[] = $mappedAllowanceItem;
        }

        $penalties = [];
        foreach ($timeSheet->penaltySets as $item) {
            if (!isset($penalties['_' . $item->userPenaltyType_id])) {
                $penalties['_' . $item->userPenaltyType_id] = new \stdClass();
                $penalties['_' . $item->userPenaltyType_id]->code = Str::replace(' ', '$nbsp;', $item->formattedCode);
                $penalties['_' . $item->userPenaltyType_id]->name = $item->userPenaltyType->penaltyType->name;
                $penalties['_' . $item->userPenaltyType_id]->amount = null;
                $penalties['_' . $item->userPenaltyType_id]->value = 0.0;
            }
            $penalties['_' . $item->userPenaltyType_id]->value += $item->totalValue;
        }

        foreach ($penalties as $penalty) {
            $allowances[] = $penalty;
        }

        return collect(array_values(Arr::sort($allowances, function ($item) {
            return $item->name;
        })));
    }

    public function getPublicHolidaysList(TimeSheet $timeSheet): Collection
    {
        $ret = [];
        foreach ($timeSheet->timeSheetDayTimes as $timeSheetDayTime) {
            if ($timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                $row_id = $timeSheetDayTime->model_id . '_' . $timeSheetDayTime->formattedCode;
                if (!isset($ret[$row_id])) {
                    $retItem = new \stdClass();
                    $retItem->code = Str::replace(' ', '$nbsp;', $timeSheetDayTime->formattedCode);
                    $retItem->name = $timeSheetDayTime->model->name;
                    $retItem->date = $timeSheetDayTime->timeSheetDay->date->format($this->dateFormat);
                    $retItem->hours = $timeSheetDayTime->finalCalculatedHours;
                    $ret[$row_id] = $retItem;
                } else {
                    $ret[$row_id]->hours += $timeSheetDayTime->finalCalculatedHours;
                }
            }
        }

        return collect(array_values(Arr::sort($ret, function ($item) {
            return $item->name;
        })));
    }

    /**
     * @throws InvalidArgumentException
     * @throws \Element\Core\Exceptions\InvalidStatusException
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     * @throws \Throwable
     */
    protected function getTimeSheetData(TimeSheet $timeSheet, Collection|array|null $leaveRequestDays = null): \stdClass
    {
        $workflow = $timeSheet->workflow;

        return json_decode(json_encode([
            'payRun' => [
                'startDate' => $timeSheet->payRun->startDate->format($this->dateFormat),
                'endDate' => $timeSheet->payRun->endDate->format($this->dateFormat),
                'fiscalYear' => $timeSheet->payRun->fiscalYear,
                'number' => $timeSheet->payRun->number,
                'name' => $timeSheet->payRun->name,
            ],

            'user' => $this->getUserData($timeSheet),
            'summary' => $this->getTimeSheetSummary($timeSheet),

            'timeSheetDays' => $this->getTimeSheetDaysData($timeSheet, $leaveRequestDays),
            'timeSheetAllowances' => $this->getTimeSheetAllowancesDetails($timeSheet),
            'timeSheetPenalties' => $this->getTimeSheetPenaltiesDetails($timeSheet),

            'currentStatus' => [
                'name' => Str::replace(' ', '&nbsp;', $this->timeSheet->workflow->statusClass::NAME),
                'color' => str_starts_with($workflow->statusClass::COLOR_REPORT, '#') ? $workflow->statusClass::COLOR_REPORT : '#' . $workflow->statusClass::COLOR_REPORT,
            ],
            'timeline' => $this->getWorkflowTimeline($timeSheet),
        ]));
    }

    /**
     * @throws InvalidArgumentException
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     * @throws \Throwable
     */
    protected function getUserData(TimeSheet $timeSheet): array
    {
        $user = $timeSheet->payRunItem->user;
        $userRole = $user->repository->getMasterUserRole($timeSheet->payRun->startDate, false);
        $userPayType = !is_null($userRole) ? $userRole->repository->getUserPayType($this->payRun->startDate) : null;
        $hourlyPayRate = !is_null($userPayType) ? $userPayType->payTypeName . ' = ' . etime_money($userPayType->getHourlyAmount(), false, 6) . ' / h' : '-';

        $departmentName = '-';
        foreach ($user->departments as $department) {
            if (
                (
                    is_null($department->pivot->startDate)
                    || Carbon::parse($department->pivot->startDate)->lt(Carbon::minValue())
                    || Carbon::parse($department->pivot->startDate)->lte($timeSheet->payRun->endDate)
                )
                && (
                    is_null($department->pivot->endDate)
                    || Carbon::parse($department->pivot->endDate)->lt(Carbon::minValue())
                    || Carbon::parse($department->pivot->endDate)->gte($timeSheet->payRun->startDate)
                )
            ) {
                $departmentName = $department->name;

                break;
            }
        }

        return [
            'image' => $this->getUserImage($user),
            'id' => $user->id,
            'externalId' => $user->externalId,
            'birthday' => !is_null($user->birthday) ? $user->birthday->format($this->dateFormat) : '-',
            'fullName' => $user->fullName,
            'role' => !is_null($userRole) ? $userRole->role->name : '-',
            'department' => $departmentName,
            'employeeType' => $user->employeeType->name,
            'manager' => !is_null($user->currentManager) ? $user->currentManager->fullName : '-',
            'startDatePermanent' => !is_null($user->startDatePermanent) ? $user->startDatePermanent->format($this->dateFormat) : '-',
            'startDateGovernment' => !is_null($user->startDateGovernment) ? $user->startDateGovernment->format($this->dateFormat) : '-',
            'payRate' => $hourlyPayRate,
            'payCode' => !is_null($userPayType) && !is_null($userPayType->payType) ? $userPayType->payType->externalId : '-',
            'advisoryNotes' => array_values($timeSheet->repository->getWorkflowAdvisoryNotes()->map(fn (UserAdvisoryNote|TimeSheetAdvisoryNote $note) => [
                'title' => $note->title,
                'description' => $note->description,
            ])->toArray()),
        ];
    }

    /**
     * @throws InvalidArgumentException
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    protected function getUserImage(User $user): string
    {
        $path = $user->photo;
        $storage = FileSystem::bucket('TenantSite');
        if ($path != '' && $storage->exists($path)) {
            $file = $storage->get($path);

            $img = Image::make($file);
        } else {
            $name = $user->fullName;
            $file = Avatar::create($name);
            $file->setBackground('fd9300');
            $file->setForeground('ffffff');

            $img = $file->getImageObject();
        }

        $img->fit(100, 100);

        return $img->getDriver()->encode($img, 'data-url', '100')->getEncoded();
    }

    protected function getTimeSheetSummary(TimeSheet $timeSheet): array
    {
        $user = $timeSheet->user;

        $accruedTakenName = TenantSystemSettings::getValue('settings.customExcessTimeLeaveName');
        $accruedTakenName = !is_null($accruedTakenName) && strlen($accruedTakenName) > 0 ? $accruedTakenName : 'Accrued hours';

        $accruedHoursName = TenantSystemSettings::getValue('settings.customExcessTimeBalanceName');
        $accruedHoursName = !is_null($accruedHoursName) && strlen($accruedHoursName) > 0 ? $accruedHoursName : 'Accrued hours earnt';
        $accruedHoursName = !str_contains($accruedHoursName, 'earnt') ? $accruedHoursName . ' earnt' : $accruedHoursName;

        if ($user->hasLeaveOptions) {
            $summary = [
                [
                    [
                        'name' => 'Scheduled hours',
                        'value' => etime_round($timeSheet->hoursScheduled, 2),
                    ],
                    [
                        'name' => 'Total hours (recorded)',
                        'value' => etime_round($timeSheet->hoursRecordedCalculation, 2),
                    ],
                    [
                        'name' => 'Worked hours',
                        'value' => etime_round($timeSheet->hoursWorked, 2),
                    ],
                    [
                        'name' => 'Leave taken',
                        'value' => etime_round($timeSheet->hoursLeaveUsed, 2),
                    ],
                ],
                [
                    [
                        'name' => $accruedTakenName . ' (taken)',
                        'value' => etime_round($timeSheet->hoursExcessTimeUsed, 2),
                    ],
                    [
                        'name' => 'RDO taken',
                        'value' => etime_round($timeSheet->hoursRosteredTimeOffUsed, 2),
                    ],
                    [
                        'name' => 'Public holidays',
                        'value' => etime_round($timeSheet->hoursPublicHoliday, 2),
                    ],
                    [
                        'name' => 'Paid excess-time (actual)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimePaidHours(ExcessTimeHourType::ActualOriginal) + $timeSheet->repository->getTotalExcessTimeAdditionalHours(ExcessTimeHourType::ActualOriginal), 2),
                    ],
                ],
                [
                    [
                        'name' => $accruedHoursName . ' (actual)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimeAccruedHours(ExcessTimeHourType::ActualOriginal), 2),
                    ],
                    [
                        'name' => 'Unpaid excess-time hours',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimeUnpaidHours(ExcessTimeHourType::ActualOriginal), 2),
                    ],
                    [
                        'name' => 'Total hours (adjusted)',
                        'value' => etime_round($timeSheet->hoursRecorded, 2),
                    ],
                    [
                        'name' => 'Paid excess-time (adjusted)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimePaidHoursAdjusted() + $timeSheet->repository->getTotalExcessTimeAdditionalHoursAdjusted(), 2),
                    ],
                ],
                [
                    [
                        'name' => $accruedHoursName . ' (adjusted)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimeAccruedHoursAdjusted(), 2),
                    ],
                    [
                        'name' => 'Total adhoc allowances',
                        'value' => etime_money($timeSheet->getTotalAllowanceAmount(), false),
                    ],
                    [
                        'name' => 'Total auto allowances/penalties',
                        'value' => etime_money($timeSheet->getTotalPenaltyAmount(), false),
                    ],
                    [
                        'name' => 'Total excess-time amount',
                        'value' => etime_money($timeSheet->repository->getTotalExcessTimeAmountPaid(), false),
                    ],
                ],
            ];
        } else {
            $summary = [
                [
                    [
                        'name' => 'Scheduled hours',
                        'value' => etime_round($timeSheet->hoursScheduled, 2),
                    ],
                    [
                        'name' => 'Total hours (recorded)',
                        'value' => etime_round($timeSheet->hoursRecordedCalculation, 2),
                    ],
                    [
                        'name' => 'Worked hours',
                        'value' => etime_round($timeSheet->hoursWorked, 2),
                    ],
                    [
                        'name' => 'Public holidays',
                        'value' => etime_round($timeSheet->hoursPublicHoliday, 2),
                    ],
                ],
                [
                    [
                        'name' => 'Paid excess-time (actual)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimePaidHours(ExcessTimeHourType::ActualOriginal) + $timeSheet->repository->getTotalExcessTimeAdditionalHours(ExcessTimeHourType::ActualOriginal), 2),
                    ],
                    [
                        'name' => 'Unpaid excess-time hours',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimeUnpaidHours(ExcessTimeHourType::ActualOriginal), 2),
                    ],
                    [
                        'name' => 'Total hours (adjusted)',
                        'value' => etime_round($timeSheet->hoursRecorded, 2),
                    ],
                    [
                        'name' => 'Paid excess-time (adjusted)',
                        'value' => etime_round($timeSheet->repository->getTotalExcessTimePaidHoursAdjusted() + $timeSheet->repository->getTotalExcessTimeAdditionalHoursAdjusted(), 2),
                    ],
                ],
                [
                    [
                        'name' => 'Total adhoc allowances',
                        'value' => etime_money($timeSheet->getTotalAllowanceAmount(), false),
                    ],
                    [
                        'name' => 'Total auto allowances/penalties',
                        'value' => etime_money($timeSheet->getTotalPenaltyAmount(), false),
                    ],
                    [
                        'name' => 'Total excess-time amount',
                        'value' => etime_money($timeSheet->repository->getTotalExcessTimeAmountPaid(), false),
                    ],
                ],
            ];
        }

        return $summary;
    }

    /**
     * @param Collection|LeaveRequestDay[]|null $leaveRequestDays
     */
    protected function getTimeSheetDaysData(TimeSheet $timeSheet, Collection|array|null $leaveRequestDays = null): Collection
    {
        $ret = [];

        foreach ($timeSheet->timeSheetDays as $timeSheetDay) {
            $day = new \stdClass();
            $day->date = $timeSheetDay->date->format($this->dateFormat);
            $day->hoursTotal = $timeSheetDay->hoursTotal;
            $day->style = new \stdClass();
            $day->style->backgroundColor = '#ffffff';
            $day->style->color = '#000000';

            if ($timeSheetDay->hasPublicHoliday) {
                $day->style->backgroundColor = '#e7fff1';
            } elseif ($timeSheetDay->date->isWeekend()) {
                $day->style->backgroundColor = '#e7f1f7';
                $day->style->color = '#222222';
            }

            $day->itemDays = [];
            foreach ($timeSheetDay->timeSheetDayTimes as $timeSheetDayTime) {
                if ($timeSheetDayTime->userShiftDay->hasHolidays) {
                    $day->style->backgroundColor = '#e7fff1';
                }

                $itemDay = new \stdClass();

                $itemDay->role = $timeSheetDayTime->roleName;
                $itemDay->shift = $timeSheetDayTime->shiftName;
                $itemDay->timeType = $timeSheetDayTime->timeTypeName;
                $itemDay->name = $timeSheetDayTime->itemName;
                $itemDay->formattedCode = $timeSheetDayTime->formattedCode;
                $itemDay->color = $timeSheetDayTime->recordTypeClass::IS_HOLIDAY ? '#d2322d' : '#333333'; // #754200 - leave
                $itemDay->hours = $timeSheetDayTime->finalCalculatedHours;
                $itemDay->comments = $timeSheetDayTime->notes;
                $itemDay->works = [];

                $itemDay->type = $timeSheetDayTime->recordTypeClass::IS_HOLIDAY ? 'holiday' : 'time';

                if ($timeSheet->recordMethod == 'T' && !$timeSheetDayTime->recordTypeClass::IS_HOLIDAY) {
                    $itemDay->start = $timeSheetDayTime->startDateTime->toDateTimeString();

                    $breaks = 0;
                    foreach ($timeSheetDayTime->breaks as $break) {
                        $breaks += $break->hours;
                    }

                    $itemDay->timeBlock = $timeSheetDayTime->startDateTime->format($this->timeFormat) . ' to ' . $timeSheetDayTime->endDateTime->format($this->timeFormat);

                    if ($breaks > 0) {
                        $itemDay->timeBlock .= ' (-' . $breaks . 'h break)';
                    }
                } else {
                    $itemDay->start = '';
                    $itemDay->timeBlock = '';
                }

                if ($timeSheetDayTime->works->count() > 0) {
                    $itemDay->type = 'work';

                    $itemWork = new \stdClass;
                    $itemWork->name = $timeSheetDayTime->itemName;
                    $itemWork->formattedCode = $timeSheetDayTime->formattedCode;
                    $itemWork->hours = $timeSheetDayTime->finalCalculatedHours;
                    $itemWork->plant = '';

                    foreach ($timeSheetDayTime->plantTimes as $plant) {
                        if (!is_null($plant->timeSheetDayTimeWork_id)) {
                            continue;
                        }

                        if (strlen($itemWork->plant) > 0) {
                            $itemWork->plant .= ', ';
                        }

                        $itemWork->plant .= $plant->plantItem->title;
                    }

                    $itemDay->works[] = $itemWork;

                    foreach ($timeSheetDayTime->works as $work) {
                        $itemWork = new \stdClass;
                        $itemWork->name = $work->itemName;
                        $itemWork->formattedCode = $work->formattedCode;
                        $itemWork->hours = $work->hours;
                        $itemWork->plant = '';

                        foreach ($work->plantTimes as $plant) {
                            if (strlen($itemWork->plant) > 0) {
                                $itemWork->plant .= ', ';
                            }

                            $itemWork->plant .= $plant->plantItem->title;
                        }

                        $itemDay->works[0]->hours -= $work->hours;
                        $itemDay->works[] = $itemWork;
                    }

                    if (etime_round($itemDay->works[0]->hours, 2) <= 0) {
                        unset($itemDay->works[0]);
                        $itemDay->works = array_values($itemDay->works);
                    }
                }

                $day->itemDays[] = $itemDay;
            }

            if (!is_null($leaveRequestDays) && $leaveRequestDays->count() > 0) {
                /** @var Collection|LeaveRequestDay[] $relevantLeaveRequestDays */
                $relevantLeaveRequestDays = $leaveRequestDays->where('date', '=', $timeSheetDay->date);

                foreach ($relevantLeaveRequestDays as $leaveRequestDay) {
                    $itemDay = new \stdClass();

                    $itemDay->role = $leaveRequestDay->userShiftDay->userShift->userRole->role->name;
                    $itemDay->shift = $leaveRequestDay->userShiftDay->userShift->shift->name;
                    $itemDay->timeType = 'Leave';
                    $itemDay->name = $leaveRequestDay->typeName;
                    $itemDay->formattedCode = $leaveRequestDay->leaveRequest->formattedCode;
                    $itemDay->color = '#754200';
                    $itemDay->hours = etime_round($leaveRequestDay->calculatedHours, 2);
                    $itemDay->comments = $leaveRequestDay->leaveRequest->reason;
                    $itemDay->type = 'leave';

                    if ($timeSheet->recordMethod == 'T') {
                        $itemDay->start = $leaveRequestDay->startDateTime->toDateTimeString();
                        $itemDay->timeBlock = $leaveRequestDay->startDateTime->format($this->timeFormat) . ' to ' . $leaveRequestDay->endDateTime->format($this->timeFormat);
                    } else {
                        $itemDay->timeBlock = '';
                    }

                    $day->itemDays[] = $itemDay;
                }
            }

            $day->itemDays = collect(array_values(Arr::sort($day->itemDays, function ($item) {
                return $item->start ?? 1;
            })));

            $ret[] = $day;
        }

        return collect($ret);
    }

    /**
     * @throws \Element\Core\Exceptions\InvalidStatusException
     */
    protected function getTimeSheetAllowancesDetails(TimeSheet $timeSheet): array
    {
        $ret = [];

        foreach ($timeSheet->timeSheetDays as $timeSheetDay) {
            foreach ($timeSheetDay->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
                $dailyMappedByFormattedCode = [];

                foreach ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries as $entry) {
                    if (!isset($dailyMappedByFormattedCode[$entry->formattedCode])) {
                        $dailyMappedByFormattedCode[$entry->formattedCode] = [];
                    }
                    $dailyMappedByFormattedCode[$entry->formattedCode][] = $entry;
                }

                foreach ($dailyMappedByFormattedCode as $code => $allowanceDayEntry) {
                    $item = new \stdClass();
                    $item->date = $timeSheetDay->date->format($this->dateFormat);
                    $item->code = Str::replace(' ', '$nbsp;', $code);
                    $item->name = $timeSheetAllowanceDay->timeSheetAllowance->allowanceType->name;
                    $item->type = $timeSheetAllowanceDay->timeSheetAllowance->allowanceType->getOptionObject('rateType')['name'];
                    $item->value = 0;

                    foreach ($allowanceDayEntry as $allowanceDayEntryItem) {
                        $item->value += $allowanceDayEntryItem->value;
                    }

                    $ret[] = $item;
                }
            }
        }

        return $ret;
    }

    protected function getTimeSheetPenaltiesDetails(TimeSheet $timeSheet): Collection
    {
        $ret = [];
        foreach ($timeSheet->penaltySets as $item) {
            if ($item->userPenaltyType->penaltyType->type == 'P') {
                $penaltyDetailed = new \stdClass();
                if (!is_null($item->dayDate) && $item->dayDate instanceof Carbon) {
                    $penaltyDetailed->date = $item->dayDate->format($this->dateFormat);
                } else {
                    $penaltyDetailed->date = $this->payRun->startDate->format($this->dateFormat) . ' - ' . $this->payRun->endDate->format($this->dateFormat);
                }

                $penaltyDetailed->code = $item->formattedCode;
                $penaltyDetailed->name = $item->userPenaltyType->penaltyType->name;
                $penaltyDetailed->value = $item->totalValue;
                $ret[] = $penaltyDetailed;
            }
        }

        return collect(array_values(Arr::sort($ret, function ($item) {
            return $item->name;
        })));
    }

    protected function getWorkflowTimeline(TimeSheet $timeSheet): array
    {
        $ret = [];
        foreach ($timeSheet->workflow->timelineEntries as $entry) {
            $ret[] = [
                'dateTime' => $entry->dateTime->format($this->dateTimeFormat),
                'description' => $entry->typeClass::getDescription(!is_null($entry->actorUser) ? $entry->actorUser->fullName : '', 'timesheet', $entry->comment != $entry->reason ? $entry->comment : null),
                'reason' => $entry->reason,
            ];
        }

        return $ret;
    }
}
