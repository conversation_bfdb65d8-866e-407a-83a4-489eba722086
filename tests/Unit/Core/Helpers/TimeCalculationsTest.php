<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\WorkPattern;

use Carbon\Carbon;
use Element\Core\Support\Helpers\TimeCalculations;
use Element\Core\Types\TimeSpanList;

describe('Time calculation methods', function () {
    it('can re-calculate end time from breaks', function (string $expectedTime, string $startTime, string $endTime, array $breaks, string|null $maxEnd = null) {
        $breakList = TimeSpanList::make($breaks);

        $end = TimeCalculations::recalculateEndTimeWithBreaks(
            Carbon::today()->setTimeFromTimeString($startTime),
            Carbon::today()->setTimeFromTimeString($endTime),
            $breakList,
            !is_null($maxEnd) ? Carbon::today()->setTimeFromTimeString($maxEnd) : null,
        );

        expect($end->format('H:i'))->toEqual($expectedTime);
    })->with([
        ['12:00', '09:00', '12:00', [['start' => '12:00', 'end' => '13:00']]],
        ['13:30', '09:00', '12:30', [['start' => '12:00', 'end' => '13:00']]],
        ['14:00', '09:00', '13:00', [['start' => '12:00', 'end' => '13:00']]],
        ['19:00', '09:00', '17:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['15:00', '09:00', '14:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['13:00', '09:00', '12:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['12:30', '09:00', '11:30', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['11:00', '09:00', '11:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['17:00', '13:00', '16:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']]],
        ['16:30', '13:00', '16:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']], '16:30'],
        ['06:30', '13:00', '16:00', [['start' => '11:00', 'end' => '12:00'], ['start' => '15:00', 'end' => '16:00']], '06:30'],
    ]);
});
