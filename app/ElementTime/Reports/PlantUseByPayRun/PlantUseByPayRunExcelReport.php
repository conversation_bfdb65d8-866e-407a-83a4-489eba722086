<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\PlantUseByPayRun;

use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantSheetDayTime;
use Element\ElementTime\Domains\Tenant\Plant\Support\PlantItemMileageTypes\OdometerType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags;
use Element\ElementTime\Reports\_Traits\HasPayRunParameters;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class PlantUseByPayRunExcelReport extends TenantExcelReport
{
    use HasPayRunParameters;

    const ALLOWED_SYSTEM_ACCESSES = [
        UserSystemAccessFlags\PayrollOfficerFlag::class,
        UserSystemAccessFlags\BusinessIntelligenceFlag::class,
        UserSystemAccessFlags\PlantCoordinatorFlag::class,
    ];

    const TIME = '~30 sec';
    const REPORT_SLUG = 'plant-use-by-pay-run';
    const TYPE_SLUG = 'plant-use-by-pay-run.xls';

    /** @var string|PlantUseByPayRunReportConfig  */
    public $c = PlantUseByPayRunReportConfig::class;

    /** @throws InvalidArgumentException */
    protected function buildReportData(): void
    {
        $this->setSheetTitle($sheet, $this->c::NAME);
        $this->fetchSheetTotals($sheet);
        $this->fetchSheetData($sheet);

        $this->sheets = [
            $sheet,
        ];
    }

    private function fetchSheetTotals(\stdClass &$sheet = null): void
    {
        $this->setSheetDefaultSettings($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Organisation', span: 3),
            $this->createColumn('Payrun', span: 6),
        ];
        $data->data = [[
            'config' => [],
            'data' => [
                $this->tenant->name, // Organisation
                $this->payRunType->name . ' #' . $this->payRun->number . ' - ' . $this->payRun->startDate->format($this->dateFormat) . ' to ' . $this->payRun->endDate->format($this->dateFormat), // Payrun
            ],
        ]];

        $sheet->data[] = $data;
    }

    /**
     * @throws InvalidArgumentException
     */
    private function fetchSheetData($sheet): void
    {
        $this->setSheetDefaultSettings($sheet);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = [];
        $table->columns = [];

        $this->loadUserColumns($table);
        $this->loadCostCentreColumns($table);
        $this->loadPlantColumns($table);

        $rows = [];
        $groupedPlantSheetDayTimes = $this->getGroupedPlantSheetDayTimes();

        foreach ($groupedPlantSheetDayTimes as $plantSheetDayTimes) {
            $row = [];
            $this->loadUserData($row, $plantSheetDayTimes[0]);
            $this->loadCostCentreData($row, $plantSheetDayTimes[0]);
            $this->loadPlantData($row, $plantSheetDayTimes);

            $rows[] = [
                'config' => [],
                'data' => $row,
            ];
        }

        $table->data = $rows;

        $sheet->data[] = $table;
    }

    private function getGroupedPlantSheetDayTimes(): array
    {
        $rows = [];
        $plantSheetDayTimes = $this->collectReportData();
        static::eagerLoad($plantSheetDayTimes);

        foreach ($plantSheetDayTimes as $plantSheetDayTime) {
            $rowId = $this->getRowId($plantSheetDayTime);
            if (!isset($rows[$rowId])) {
                $rows[$rowId] = [];
            }

            $rows[$rowId][] = $plantSheetDayTime;
        }

        return $rows;
    }

    private function collectReportData(): Collection|array
    {
        return PlantSheetDayTime::q(
            constraints: [
                ['PayRunItem.payRun_id', '=', $this->payRun->id],
            ],
            joins: [
                ['PayRunItem', 'PayRunItem.id', '=', 'TimeSheet.payRunItem_id'],
            ],
        )->many();
    }

    /**
     * @param PlantSheetDayTime[]|Collection $collection
     */
    private static function eagerLoad(array|Collection $collection): void
    {
        $collection->load([
            'time.userShiftDay.userShift.userRole',
            'time.userShiftDay.userShift.userRole.user',
            'time.userShiftDay.userShift.userRole.role',
            'time.userShiftDay.userShift.userRole.userRoleDepartments.department',
            'time.userShiftDay.userShift.user',
            'time.userShiftDay.userShift.user.userManagers.manager',
            'time.userShiftDay.userShift.user',
            'plantSheet.plantItem.plantClass',
            'work.workOrderType.workOrderTypeActivityTypes.activityType',
            'work.workOrderType.workOrderTypeActivityTypes.workOrderType',
            'work.workOrderType.workOrderTypeActivityTypes.activityGroupType.activityType',
            'time.timeSheetDay',
            'rel',
        ]);
    }

    private function getRowId(PlantSheetDayTime $plantSheetDayTime): string
    {
        $userRole = $plantSheetDayTime->userRole;
        $user_id = $userRole->user_id;
        $role_id = $userRole->role->id;
        $plantItem_id = $plantSheetDayTime->plantItem->id;
        $formattedCode = $plantSheetDayTime->formattedCode;
        $date = $plantSheetDayTime->timeSheetDay->date;

        if (!is_null($plantSheetDayTime->work)) {
            $time_id = $plantSheetDayTime->work->workOrderType_id;
        } elseif (!is_null($plantSheetDayTime->time)) {
            $time_id = $plantSheetDayTime->time->projectId;
        } else {
            $masterUserRoleProject = $plantSheetDayTime->userRole->repository->getMasterUserRoleProject($date);
            $time_id = $masterUserRoleProject->project_id;
        }

        return implode('_', [
            $date->format($this->dateFormat),
            $user_id,
            $role_id,
            $plantItem_id,
            $time_id,
            $formattedCode,
        ]);
    }

    private function loadUserColumns(\stdClass $table): void
    {
        $table->parentColumns[] = $this->createColumn(label: 'Staff', span: 6);
        $table->columns[] = $this->createColumn('Ext ref ID');
        $table->columns[] = $this->createColumn('Last Name');
        $table->columns[] = $this->createColumn('First Name');
        $table->columns[] = $this->createColumn('Role name');
        $table->columns[] = $this->createColumn('Manager');
        $table->columns[] = $this->createColumn('Department');
    }

    /**
     * @throws InvalidArgumentException
     */
    private function loadUserData(array &$row, PlantSheetDayTime $plantSheetDayTime): void
    {
        $userRole = $plantSheetDayTime->userRole;
        $user = $userRole->user;
        $date = $plantSheetDayTime->timeSheetDay->date;

        $row[] = $userRole->repository->getExternalId();
        $row[] = $user->nameLast;
        $row[] = $user->nameFirst;
        $row[] = $userRole->role->name;
        $row[] = $user->repository->getManager($date)?->fullName ?? '';
        $departments = $userRole->getActiveUserRoleDepartments($date);
        $row[] = count($departments) > 0
            ? $departments[0]->department->name
            : '';
    }

    private function loadCostCentreColumns(\stdClass $table): void
    {
        $table->parentColumns[] = $this->createParentColumn(label: 'Cost Centre', span: 3);
        $table->columns[] = $this->createColumn('Name');
        $table->columns[] = $this->createColumn('Ext Ref Code');
        $table->columns[] = $this->createColumn('Cost Centre code');
    }

    private function loadCostCentreData(array &$row, PlantSheetDayTime $plantSheetDayTime): void
    {
        $row[] = $plantSheetDayTime->itemName;
        $row[] = !is_null($plantSheetDayTime->work)
            ? $plantSheetDayTime->work->formattedCode
            : (!is_null($plantSheetDayTime->time)
                ? $plantSheetDayTime->time->formattedCode
                : $plantSheetDayTime->userRole->formattedCode
            );
        $row[] = $plantSheetDayTime->formattedCode;
    }

    private function loadPlantColumns(\stdClass $table): void
    {
        $table->parentColumns[] = $this->createParentColumn(label: 'Plant', span: 9);
        $table->columns[] = $this->createColumn('Number');
        $table->columns[] = $this->createColumn('Type');
        $table->columns[] = $this->createColumn('Plant item');
        $table->columns[] = $this->createColumn('Hours');
        $table->columns[] = $this->createColumn('Mileage');
        $table->columns[] = $this->createColumn('Cartage');
        $table->columns[] = $this->createColumn('Odometer');
        $table->columns[] = $this->createColumn('Comments');
        $table->columns[] = $this->createColumn('Date used');
    }

    /**
     * @param PlantSheetDayTime[] $plantSheetDayTimes
     */
    private function loadPlantData(array &$row, array $plantSheetDayTimes): void
    {
        $firstPlantSheetDayTime = $plantSheetDayTimes[0];

        $row[] = $firstPlantSheetDayTime->plantItem->plantNumber;
        $row[] = $firstPlantSheetDayTime->plantItem->plantClass->name;
        $row[] = $firstPlantSheetDayTime->plantItem->title;

        $hours = 0;
        $mileage = 0;
        $cartage = 0;
        $odometers = [];
        $descriptions = [];

        foreach ($plantSheetDayTimes as $plantSheetDayTime) {
            if ($plantSheetDayTime->plantItem->doesHaveHours && !!$plantSheetDayTime->hours) {
                $hours += $plantSheetDayTime->hours;
            }

            if ($plantSheetDayTime->plantItem->doesHaveMileage && !!$plantSheetDayTime->mileage) {
                $mileage += $plantSheetDayTime->mileage;
            }

            if ($plantSheetDayTime->plantItem->doesHaveCartage && !!$plantSheetDayTime->cartage) {
                $cartage += $plantSheetDayTime->cartage;
            }

            if ($plantSheetDayTime->plantItem->doesHaveMileage && $plantSheetDayTime->plantItem->mileageRecordingTypeClass::isType(OdometerType::class) && !!$plantSheetDayTime->mileageStart) {
                $odometers[] = $plantSheetDayTime->mileageStart . ' - ' . $plantSheetDayTime->mileageEnd . $plantSheetDayTime->plantItem->mileageUnitClass::ABBREVIATION;
            }

            if (!!$plantSheetDayTime->description) {
                $descriptions[] = $plantSheetDayTime->description;
            }
        }

        $row[] = $hours;
        $row[] = $mileage ? $firstPlantSheetDayTime->plantItem->mileageUnitClass::getAbbreviatedMaskedValue($mileage) : '';
        $row[] = $cartage ? $firstPlantSheetDayTime->plantItem->cartageUnitClass::getAbbreviatedMaskedValue($cartage) : '';
        $row[] = implode($this->br, $odometers);
        $row[] = implode($this->br, $descriptions);
        $row[] = $firstPlantSheetDayTime->timeSheetDay->date->format($this->dateFormat);
    }
}
