<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: AllowanceType

        \Illuminate\Support\Facades\DB::statement('ALTER TABLE AllowanceType MODIFY COLUMN requiresTime TINYINT(1) NOT NULL DEFAULT 0 AFTER rateType;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE AllowanceType MODIFY COLUMN addsTimeToWorked TINYINT(1) NOT NULL DEFAULT 0 AFTER requiresTime;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE AllowanceType MODIFY COLUMN projectCode VARCHAR(255) DEFAULT NULL AFTER isEarntDuringLeave;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE AllowanceType MODIFY COLUMN activityCode VARCHAR(255) DEFAULT NULL AFTER projectCode;');

        Schema::table('AllowanceType', function (Blueprint $table) {
            $table->boolean('doesShowValue')->default(false)->after('addsTimeToWorked');
        });

        //endregion Table: AllowanceType

        //region Table: PenaltyType

        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PenaltyType MODIFY COLUMN projectCode VARCHAR(255) DEFAULT NULL AFTER projectActivity_id;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PenaltyType MODIFY COLUMN activityCode VARCHAR(255) DEFAULT NULL AFTER projectCode;');

        Schema::table('PenaltyType', function (Blueprint $table) {
            $table->char('type', 1)->default('P')->after('incrementRate_id'); // P - Penalty | A - Allowance
        });

        //endregion Table: PenaltyType
    }

    public function down(): void
    {
        //region Table: PenaltyType

        if (Schema::hasColumn('PenaltyType', 'type')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('type');
            });
        }

        //endregion Table: PenaltyType

        //region Table: AllowanceType

        if (Schema::hasColumn('AllowanceType', 'doesShowValue')) {
            Schema::table('AllowanceType', function (Blueprint $table) {
                $table->dropColumn('doesShowValue');
            });
        }

        //endregion Table: AllowanceType
    }
};
