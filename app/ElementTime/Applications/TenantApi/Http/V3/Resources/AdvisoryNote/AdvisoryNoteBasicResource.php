<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V3\Resources\AdvisoryNote;

use Element\ElementTime\Domains\Tenant\Users\Models\UserAdvisoryNote;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'AdvisoryNoteBasicResource',
    title: 'Individual advisory note - basic info',
    required: ['id', 'title', 'description'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'title', type: 'string', example: 'You are Grand Master'),
        new OA\Property(property: 'description', type: 'string', example: 'This is a description'),
    ],
)]
class AdvisoryNoteBasicResource extends Resource
{
    public static $relations = [
        ///
    ];

    public function toArray(Request $request): array
    {
        /** @var UserAdvisoryNote $r */
        $r = $this->getModel();

        return [
            "id" => $r->id,
            "title" => $r->title,
            "description" => $r->description,
        ];
    }
}
