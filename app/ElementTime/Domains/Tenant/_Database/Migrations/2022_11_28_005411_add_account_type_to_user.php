<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Users\Enums\UserAccountType\UserAccountType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('User', function (Blueprint $table) {
            $table->string('accountType')->nullable()->default(UserAccountType::DEFAULT->id())->after('id');
            $table->unsignedBigInteger('profileManager_id')->nullable();
            $table->foreign('profileManager_id', 'user_pm_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('set null');
            $table->boolean('isMainSubProfile')->default(false);
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('User', 'isMainSubProfile')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('isMainSubProfile');
            });
        }

        try {
            Schema::table('User', function (Blueprint $table) {
                $table->dropForeign('user_pm_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('User', 'profileManager_id')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('profileManager_id');
            });
        }

        if (Schema::hasColumn('User', 'accountType')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('accountType');
            });
        }
    }
};
