#!/bin/bash
#
#       _                           _  _____ ___ __  __ _____
#   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
#  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
# |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
#  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
#
# @link https://www.elementtime.com
# @copyright 2024 Adroit Creations
#

set -euo pipefail

# Global variables for caching
declare -A LB_ARN_CACHE
declare -A TG_ARN_CACHE

# Logging functions
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $*" >&2
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $*" >&2
}

log_debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] DEBUG: $*" >&2
    fi
}

function usage() {
    cat <<EOF
Usage:
    -h|--help
    -e|--env                    [ENV: test | stage | prod]
    -s|--sleep                  [SLEEP: sleep seconds before stop old EC2, default: 0s]
    -n|--env-version            [ENV_VERSION: default: 14si ]

Examples:
    ./deploy.sh -e test -s 5
    ./deploy.sh -e stage
    ./deploy.sh -e prod
    ./deploy.sh -e test -n 14si
EOF
}

if [[ $# -lt 2 ]]; then
    usage
    exit 1
fi

POSITIONAL=()
while [[ $# -gt 0 ]]; do
    key="$1"

    case $key in
    -h | --help)
        usage
        exit
        ;;
    -e | --env)
        env="$2"
        shift # past argument
        shift # past value
        ;;
    -s | --sleep)
        sleep="$2"
        shift # past argument
        shift # past value
        ;;
    -n | --env-version)
        env_version="$2"
        shift # past argument
        shift # past value
        ;;
    *)                     # unknown option
        POSITIONAL+=("$1") # save it in an array for later
        shift              # past argument
        ;;
    esac
done

set -- "${POSITIONAL[@]}" # restore positional parameters

echo "environment: ${env}"
echo "env_version: ${env_version}"
echo "sleep: ${sleep:-0}s"

###########################

# Set TARGET: test/stage/prod
TARGET=${env:-test}
SLEEP=${sleep:-0}
ENV_VERSION=${env_version:-14si}
DEPLOY_SH="./deploy_blue_green.sh"

# Set SLEEP to 0 sec before scaling down old ASGs
SLEEP_BEFORE_SCALE_DOWN=${SLEEP}

# Usually within 45 mins, for re-try, double it
ELB_TIMEOUT="90 minutes"

# Get ASG/ELB name for Blue/Green from Socket
asg_skt_b="asg-etime-${TARGET}-skt-b-${ENV_VERSION}"
asg_skt_g="asg-etime-${TARGET}-skt-g-${ENV_VERSION}"
elb_skt_b="etime-alb-${TARGET}-skt-b-${ENV_VERSION}"
elb_skt_g="etime-alb-${TARGET}-skt-g-${ENV_VERSION}"

# Get ASG/ELB name for Blue/Green from Command
asg_cmd_b="asg-etime-${TARGET}-cmd-b-${ENV_VERSION}"
asg_cmd_g="asg-etime-${TARGET}-cmd-g-${ENV_VERSION}"
elb_cmd_b="etime-alb-${TARGET}-cmd-b-${ENV_VERSION}"
elb_cmd_g="etime-alb-${TARGET}-cmd-g-${ENV_VERSION}"

# Get ASG/ELB name for Blue/Green from Normal
asg_normal_b="asg-etime-${TARGET}-b-${ENV_VERSION}"
asg_normal_g="asg-etime-${TARGET}-g-${ENV_VERSION}"
elb_normal_b="etime-alb-${TARGET}-b-${ENV_VERSION}"
elb_normal_g="etime-alb-${TARGET}-g-${ENV_VERSION}"

# ASG count for Test/Stage/Prod env - SOCKET
ASG_COUNT_SOCKET=1
ASG_COUNT_SOCKET_MIN=1
ASG_COUNT_SOCKET_MAX=1

# ASG count for Test/Stage/Prod env - CMD
ASG_COUNT_CMD=0
ASG_COUNT_CMD_MIN=0
ASG_COUNT_CMD_MAX=1

# Test
ASG_COUNT_TEST=1

# Stage
ASG_COUNT_STAGE=1
ASG_COUNT_STAGE_MIN_DAY=1
ASG_COUNT_STAGE_MIN_NIGHT=1
ASG_COUNT_STAGE_MAX_DAY=2
ASG_COUNT_STAGE_MAX_NIGHT=1

# Prod
# Set the MIN up a bit (from 1 to 2) before the traffic comes up
ASG_COUNT_PROD=2
ASG_COUNT_PROD_MIN_DAY=2
ASG_COUNT_PROD_MIN_NIGHT=2
ASG_COUNT_PROD_MAX_DAY=6
ASG_COUNT_PROD_MAX_NIGHT=2


# Minimal EC2 instances in ASG Target Group with Healthy state
MIN_EC2_HEALTHY_COUNT=1


###########################


function set_asg_count {
    asg=$1
    min=$2
    max=$3
    desired=$4
    aws autoscaling update-auto-scaling-group \
        --auto-scaling-group-name ${asg} \
        --min-size $min \
        --max-size $max \
        --desired-capacity ${desired}
}

function get_lb_arn_by_name {
    lb_name=$1
    aws elbv2 describe-load-balancers --names ${lb_name} | jq -r '.LoadBalancers[].LoadBalancerArn'
}

# get current color from traffic weight (100) - before switching
# note: for fresh new start - the current running color is Blue (by default)
function get_current_color_from_traffic_weight {
    TARGET=$1
    type=$2
    ENV_VERSION=$3
    if [[ ${type} == "skt" ]] || [[ ${type} == "cmd" ]]
    then
        lb_name=alb-etime-${TARGET}-${type}-${ENV_VERSION}
    elif [[ ${type} == "normal" ]]
    then
        lb_name=alb-etime-${TARGET}-${ENV_VERSION}
    fi
    lb_arn=$(get_lb_arn_by_name ${lb_name})
    current_color=$(aws elbv2 describe-listeners --load-balancer-arn "${lb_arn}" | jq -r '.Listeners[] | select(.Port==443) | .DefaultActions[].ForwardConfig.TargetGroups[] | select(.Weight == 100) | .TargetGroupArn' | awk -F'/' '{print $(NF-1)}' | awk -F'-' '{print $(NF-1)}')
    echo "${current_color}"
}


# Get Target Group ARN for Socket - current
function get_tg_current_arn {
    type=$1
    tg_current_name="elb_${type}_${current_color}"
    tg_current_arn=$(aws elbv2 describe-target-groups | jq -r '.TargetGroups[] | .TargetGroupArn' | grep -E -i ${!tg_current_name})
    echo ${tg_current_arn}
}

# Get Target Group ARN for Socket - next
function get_tg_next_arn {
    type=$1
    tg_next_name="elb_${type}_${next_color}"
    tg_next_arn=$(aws elbv2 describe-target-groups | jq -r '.TargetGroups[] | .TargetGroupArn' | grep -E -i ${!tg_next_name})
    echo ${tg_next_arn}
}

# Get Target Group Health
function get_tg_next_health_status {
    type=$1
    runtime=${ELB_TIMEOUT}
    endtime=$(date -ud "$runtime" +%s)
    while [[ $(date -u +%s) -le $endtime ]]
    do
        tg_next_arn=$(get_tg_next_arn $type)
        count_healthy=$(aws elbv2 describe-target-health --target-group-arn ${tg_next_arn} | jq -r '.TargetHealthDescriptions[].TargetHealth.State' | uniq -c | grep -i -w "healthy" | awk '{print $1}')
        # Wait until at least 1 EC2 becomes healthy
        if [[ ${count_healthy} -ge ${MIN_EC2_HEALTHY_COUNT} ]]
        then
            break
        else
            # reduce check time to 60 seconds
            sleep 1m
            continue
        fi
    done
    state="healthy"
    echo ${state}
}

function get_lb_arn_by_name {
    lb_name=$1
    aws elbv2 describe-load-balancers --names ${lb_name} | jq -r '.LoadBalancers[].LoadBalancerArn'
}

function get_lb_listener_443_arn {
    TARGET=$1
    type=$2
    if [[ ${type} == "skt" ]] || [[ ${type} == "cmd" ]]
    then
        lb_name=alb-etime-${TARGET}-${type}-${ENV_VERSION}
    elif [[ ${type} == "normal" ]]
    then
        lb_name=alb-etime-${TARGET}-${ENV_VERSION}
    fi
    lb_arn=$(get_lb_arn_by_name ${lb_name})
    aws elbv2 describe-listeners --load-balancer-arn "${lb_arn}" | jq -r '.Listeners[] | select(.Port==443) | .ListenerArn'
}

# get traffic weight for current color - before switching
function get_lb_listener_tg_weight_current {
    TARGET=$1
    type=$2
    if [[ ${type} == "skt" ]] || [[ ${type} == "cmd" ]]
    then
        lb_name=alb-etime-${TARGET}-${type}-${ENV_VERSION}
    elif [[ ${type} == "normal" ]]
    then
        lb_name=alb-etime-${TARGET}-${ENV_VERSION}
    fi
    lb_arn=$(get_lb_arn_by_name ${lb_name})
    weight_current=$(aws elbv2 describe-listeners --load-balancer-arn "${lb_arn}" | jq -r --arg current_color "$current_color" '.Listeners[] | select(.Port==443) | .DefaultActions[].ForwardConfig.TargetGroups[] | select(.TargetGroupArn | contains("-$current_color-")) | .Weight')
    echo "${weight_current}"
}

# Set Autoscaling Schedule actions for Stage/Prod env - to avoid scale up to MAX during night

#function set_asg_autoscaling_schedule_normal_next {
#    TARGET=$1
#    type=$2
#
#    if [[ ${type} == "normal" ]] && [[ ${TARGET} != "test" ]]
#    then
#        if [[ ${TARGET} == "stage" ]]
#        then
#            min_day=$ASG_COUNT_STAGE_MIN_DAY
#            min_night=$ASG_COUNT_STAGE_MIN_NIGHT
#            max_day=$ASG_COUNT_STAGE_MAX_DAY
#            max_night=$ASG_COUNT_STAGE_MAX_NIGHT
#            desired=$ASG_COUNT_STAGE
#        elif [[ ${TARGET} == "prod" ]]
#        then
#            min_day=$ASG_COUNT_PROD_MIN_DAY
#            min_night=$ASG_COUNT_PROD_MIN_NIGHT
#            max_day=$ASG_COUNT_PROD_MAX_DAY
#            max_night=$ASG_COUNT_PROD_MAX_NIGHT
#            desired=$ASG_COUNT_PROD
#        else
#            echo -e "\nInvalid target: ${TARGET}\n" && usage
#            exit 1
#        fi
#
#        asg_next_name=asg-etime-${TARGET}-${next_color}
#
#        # Fixed ASG count daily during night (11pm - 9am)
#        # Stage: 1/1/1 (Desired / Min / Max)
#        # Prod: 2/2/3 (Desired / Min / Max)
#        aws autoscaling put-scheduled-update-group-action \
#            --scheduled-action-name asg-etime-${TARGET}-schedule-fixed-daily-${next_color} \
#            --auto-scaling-group-name "${asg_next_name}" \
#            --recurrence "00 23 * * *" \
#            --time-zone "Pacific/Auckland" \
#            --min-size $min_night \
#            --max-size $max_night \
#            --desired-capacity $desired
#
#        # Allow autoscaling during workday at 9am (9am - 11pm)
#        # Stage: 1/1/2 (Desired / Min / Max)
#        # Prod: 2/2/6 (Desired / Min / Max)
#        aws autoscaling put-scheduled-update-group-action \
#            --scheduled-action-name asg-etime-${TARGET}-schedule-allow-autoscale-workday-${next_color} \
#            --auto-scaling-group-name "${asg_next_name}" \
#            --recurrence "00 9 * * 1-5" \
#            --time-zone "Pacific/Auckland" \
#            --min-size $min_day \
#            --max-size $max_day \
#            --desired-capacity $desired
#
#        echo -e "\nSet Autoscaling Schedule actions for next ASG: ${asg_next_name}:\n"
#        aws autoscaling describe-scheduled-actions --auto-scaling-group-name ${asg_next_name}
#    fi
#}

# Function to get the EC2 instance count for a specified ASG
# Arguments: ASG name
get_ec2_count() {
    local asg_name="$1"

    local count
    count=$(aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names "$asg_name" \
        --query "AutoScalingGroups[0].DesiredCapacity" \
        --output text 2>/dev/null)

    # Set count to 0 if it is empty or not a number
    if ! [[ $count =~ ^[0-9]+$ ]]; then
        count=0
    fi

    echo "$count"
}

# Function to check EC2 counts for two ASGs and return status based on counts
# Arguments: ASG name a, ASG name b
status_check_both_asg_counts() {
    local asg_name_a="$1"
    local asg_name_b="$2"

    local count_a=$(get_ec2_count "$asg_name_a")
    local count_b=$(get_ec2_count "$asg_name_b")

    if [[ $count_a -eq 0 || $count_b -eq 0 ]]; then
        echo "True"
    else
        echo "False"
    fi
}

#############################

# Spin up ASG in background for Next color
${DEPLOY_SH} -x -e ${TARGET} -t skt -s ${ENV_VERSION} &
${DEPLOY_SH} -x -e ${TARGET} -t normal -s ${ENV_VERSION} &

# Needed?
${DEPLOY_SH} -x -e ${TARGET} -t cmd -s ${ENV_VERSION} &

#############################

# Get current color from Socket LB Target - before switching
current_color=$(get_current_color_from_traffic_weight ${TARGET} skt ${ENV_VERSION})

# For fresh new start init

############################

# For fresh new start init - default is Blue - overwrite current_color
status_both_asg_counts_either_one_empty_skt=$(status_check_both_asg_counts "$asg_skt_b" "$asg_skt_g")
status_both_asg_counts_either_one_empty_normal=$(status_check_both_asg_counts "$asg_normal_b" "$asg_normal_g")

if [[ "${status_both_asg_counts_either_one_empty_normal}" == "True" ]] && [[ "${status_both_asg_counts_either_one_empty_skt}" == "True" ]]
then
    echo -e "\nFresh new init - ASGs (Blue/Green) either one empty for Normal and Socket"
    echo "\nCurrent running ASG color should be Blue (by default when init)"
    echo "\nSo set next_color to Blue - ready for LB TG switch logic"

    # Set next_color="b"
    current_color="g"
fi

if [[ ${current_color} == "b" ]]
then
    next_color="g"
elif [[ ${current_color} == "g" ]]
then
    next_color="b"
fi

###########################

tg_current_arn_skt=$(get_tg_current_arn skt)
tg_current_arn_normal=$(get_tg_current_arn normal)
tg_current_arn_cmd=$(get_tg_current_arn cmd)

tg_next_arn_skt=$(get_tg_next_arn skt)
tg_next_arn_normal=$(get_tg_next_arn normal)
tg_next_arn_cmd=$(get_tg_next_arn cmd)

###########################

# The following tg_next_health_status will loop check (for 90min) when Zero EC2 in both ASG
# Especially for complete fresh start - e.g. Test env
# So set next_color to Blue (current running is Blue)

tg_next_health_status_skt=$(get_tg_next_health_status skt)
echo -e "\ntg_next_health_status for skt: ${tg_next_health_status_skt}"

tg_next_health_status_normal=$(get_tg_next_health_status normal)
echo -e "\ntg_next_health_status for normal: ${tg_next_health_status_normal}"

# No need - by default 0 instance in TG with health status
#tg_next_health_status_cmd=$(get_tg_next_health_status cmd)
#echo -e "\ntg_next_health_status for cmd: ${tg_next_health_status_cmd}"

###########################

# Check if both types are Healthy and then switch
if [[ "${tg_next_health_status_skt}" == "healthy" ]] && [[ "${tg_next_health_status_normal}" == "healthy" ]]
then
    # Check target weight
    weight_current_skt=$(get_lb_listener_tg_weight_current ${TARGET} skt)
    echo -e "\nWeight for Socket current color ${current_color}: ${weight_current_skt}"
    weight_current_normal=$(get_lb_listener_tg_weight_current ${TARGET} normal)
    echo -e "\nWeight for Normal current color ${current_color}: ${weight_current_normal}"
    weight_current_cmd=$(get_lb_listener_tg_weight_current ${TARGET} cmd)
    echo -e "\nWeight for Command current color ${current_color}: ${weight_current_cmd}"

    lb_listener_443_arn_skt=$(get_lb_listener_443_arn ${TARGET} skt)
    lb_listener_443_arn_normal=$(get_lb_listener_443_arn ${TARGET} normal)
    lb_listener_443_arn_cmd=$(get_lb_listener_443_arn ${TARGET} cmd)

    echo -e "\nSwitching Target Group for Socket - from ${current_color} to ${next_color}"
    aws elbv2 modify-listener --listener-arn "${lb_listener_443_arn_skt}" --default-actions \
        '[{
          "Type": "forward",
          "Order": 1,
          "ForwardConfig": {
            "TargetGroups": [
              {
                "TargetGroupArn": "'${tg_current_arn_skt}'",
                "Weight": 0
              },
              {
                "TargetGroupArn": "'${tg_next_arn_skt}'",
                "Weight": 100
              }
            ]
          }
        }]'

    echo -e "\nSwitching Target Group for Normal - from ${current_color} to ${next_color}"
    aws elbv2 modify-listener --listener-arn "${lb_listener_443_arn_normal}" --default-actions \
        '[{
          "Type": "forward",
          "Order": 1,
          "ForwardConfig": {
            "TargetGroups": [
              {
                "TargetGroupArn": "'${tg_current_arn_normal}'",
                "Weight": 0
              },
              {
                "TargetGroupArn": "'${tg_next_arn_normal}'",
                "Weight": 100
              }
            ]
          }
        }]'

    echo -e "\nSwitching Target Group for Command - from ${current_color} to ${next_color}"
    aws elbv2 modify-listener --listener-arn "${lb_listener_443_arn_cmd}" --default-actions \
        '[{
          "Type": "forward",
          "Order": 1,
          "ForwardConfig": {
            "TargetGroups": [
              {
                "TargetGroupArn": "'${tg_current_arn_cmd}'",
                "Weight": 0
              },
              {
                "TargetGroupArn": "'${tg_next_arn_cmd}'",
                "Weight": 100
              }
            ]
          }
        }]'
else
    # Tidy up when failed
    tg_next_name_skt="elb_skt_$next_color"
    tg_next_name_normal="elb_normal_$next_color"
    tg_next_name_cmd="elb_cmd_$next_color"

    echo -e "\nTarget Group is unhealthy for ${!tg_next_name_skt} and/or ${!tg_next_name_normal} and/or ${!tg_next_name_cmd}, clean up && exit..."

    # Clean up new EC2
    set_asg_count asg-etime-${TARGET}-skt-${next_color}-${ENV_VERSION} 0 0 0
    set_asg_count asg-etime-${TARGET}-${next_color}-${ENV_VERSION} 0 0 0
    set_asg_count asg-etime-${TARGET}-cmd-${next_color}-${ENV_VERSION} 0 0 0

    echo -e "\nTidy up new unhealthy EC2:\n"
    echo -e "asg-etime-${TARGET}-skt-${next_color}"
    echo -e "asg-etime-${TARGET}-${next_color}"
    echo -e "asg-etime-${TARGET}-cmd-${next_color}"

    # Exit
    exit 1
fi

# Disable Scheduled Actions
# Set Scheduled Actions for Normal
#set_asg_autoscaling_schedule_normal_next ${TARGET} normal

# Scale down current/old ASGs - immediately
sleep ${SLEEP_BEFORE_SCALE_DOWN}

# Seems can not use traffic weight to determine color (default to b)
# Use skt LB traffic weight

# Already Switched
new_color=$(get_current_color_from_traffic_weight ${TARGET} skt ${ENV_VERSION})
echo -e "\nCurrent new color: ${new_color}\n"

if [[ ${new_color} == "b" ]]
then
    old_color="g"
elif [[ ${new_color} == "g" ]]
then
    old_color="b"
fi

echo -e "\nOld color: ${old_color}\n"

if [[ "${old_color}" =~ ^(b|g)$ ]]
then
    set_asg_count asg-etime-${TARGET}-skt-${old_color}-${ENV_VERSION} 0 0 0
    set_asg_count asg-etime-${TARGET}-${old_color}-${ENV_VERSION} 0 0 0
    set_asg_count asg-etime-${TARGET}-cmd-${old_color}-${ENV_VERSION} 0 0 0

    echo -e "\nScale down old ASG / EC2:\n"
    echo -e "asg-etime-${TARGET}-skt-${old_color}-${ENV_VERSION}"
    echo -e "asg-etime-${TARGET}-${old_color}-${ENV_VERSION}"
    echo -e "asg-etime-${TARGET}-cmd-${old_color}-${ENV_VERSION}"

    # Delete old/current ASG autoscaling schedule actions if there's any - Normal only
    #if [[ ${TARGET} != "test" ]]
    #then
    #    aws autoscaling delete-scheduled-action \
    #        --scheduled-action-name asg-etime-${TARGET}-schedule-fixed-daily-${old_color} \
    #        --auto-scaling-group-name asg-etime-${TARGET}-${old_color} || true # Allow command to fail

    #    aws autoscaling delete-scheduled-action \
    #        --scheduled-action-name asg-etime-${TARGET}-schedule-allow-autoscale-workday-${old_color} \
    #        --auto-scaling-group-name asg-etime-${TARGET}-${old_color} || true # Allow command to fail

    #    echo -e "\nDelete Autoscaling Scheduled Actions for old Normal ASG: asg-etime-${TARGET}-${old_color}\n"
    #fi
else
    echo -e "\nInvalid value for old_color, exit..."
fi
