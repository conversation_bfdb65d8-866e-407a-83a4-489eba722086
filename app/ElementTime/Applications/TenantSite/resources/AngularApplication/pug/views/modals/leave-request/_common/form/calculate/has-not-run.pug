.row(ng-if='!!$ctrl.form.calculation.canRun && !$ctrl.form.calculation.isRunning')
  .col-xs-12.text-center
    button.btn.btn-info.pl-xl.pr-xl.m0(type='button' ng-click='$ctrl.form.calculation.run()' elt-help-text='modals.leave-request|actions.calculate')
      span.lead.text-bold.m0
        i.fa.fa-bolt.mr
        span Check / Calculate request

.row(ng-if='!!$ctrl.form.calculation.isRunning')
  .col-xs-12.col-md-10.col-md-offset-1
    .list-group.m0
      .list-group-item.list-group-item-info.text-center
        span.lead.m0.text-normal Calculating request...

.list-group.m0(ng-if='!$ctrl.form.calculation.canRun')
  .list-group-item.list-group-item-danger
    span.lead.m0.text-normal Please add all required fields with valid values to calculate request.
