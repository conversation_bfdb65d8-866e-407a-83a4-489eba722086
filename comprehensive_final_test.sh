#!/bin/bash
#
# Comprehensive Final Test Suite for Optimized Deployment Scripts
# Tests all logic paths, edge cases, and deployment scenarios
#

set -euo pipefail

# Test configuration
readonly TEST_DIR="/tmp/deploy_comprehensive_test"
readonly MOCK_AWS_DIR="${TEST_DIR}/mock_aws"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

log_test() {
    echo -e "${GREEN}[FINAL-TEST]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_scenario() {
    echo -e "${PURPLE}[SCENARIO]${NC} $*"
}

# Test result tracking
pass_test() {
    local test_name="$1"
    TESTS_PASSED=$((TESTS_PASSED + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "✓ PASS: $test_name"
}

fail_test() {
    local test_name="$1"
    local reason="$2"
    TESTS_FAILED=$((TESTS_FAILED + 1))
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_error "✗ FAIL: $test_name - $reason"
}

# Setup comprehensive mock AWS environment
setup_comprehensive_mock_aws() {
    log_test "Setting up comprehensive mock AWS environment"
    
    mkdir -p "$MOCK_AWS_DIR"
    
    cat > "${MOCK_AWS_DIR}/aws" << 'EOF'
#!/bin/bash
# Comprehensive mock AWS CLI for all test scenarios

# Global state variables for testing different scenarios
SCENARIO="${TEST_SCENARIO:-fresh_deployment}"
HEALTH_CHECK_ATTEMPTS="${HEALTH_CHECK_ATTEMPTS:-0}"

case "$1" in
    "sts")
        case "$2" in
            "get-caller-identity")
                echo '{"UserId":"AIDACKCEVSQ6C2EXAMPLE","Account":"************","Arn":"arn:aws:iam::************:user/test"}'
                ;;
        esac
        ;;
    "autoscaling")
        case "$2" in
            "describe-auto-scaling-groups")
                case "$SCENARIO" in
                    "fresh_deployment")
                        # Both ASGs empty (fresh deployment)
                        if [[ "$*" =~ --auto-scaling-group-names ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":0,"AutoScalingGroupName":"'$asg_name'"}]}'
                        else
                            echo '{"AutoScalingGroups":[
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-b-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-g-14si"}
                            ]}'
                        fi
                        ;;
                    "blue_to_green")
                        # Blue has instances, Green is empty
                        if [[ "$*" =~ --auto-scaling-group-names.*-b- ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":2,"AutoScalingGroupName":"'$asg_name'"}]}'
                        elif [[ "$*" =~ --auto-scaling-group-names.*-g- ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":0,"AutoScalingGroupName":"'$asg_name'"}]}'
                        else
                            echo '{"AutoScalingGroups":[
                                {"DesiredCapacity":2,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"},
                                {"DesiredCapacity":2,"AutoScalingGroupName":"asg-etime-test-b-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-g-14si"}
                            ]}'
                        fi
                        ;;
                    "green_to_blue")
                        # Green has instances, Blue is empty
                        if [[ "$*" =~ --auto-scaling-group-names.*-g- ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":2,"AutoScalingGroupName":"'$asg_name'"}]}'
                        elif [[ "$*" =~ --auto-scaling-group-names.*-b- ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":0,"AutoScalingGroupName":"'$asg_name'"}]}'
                        else
                            echo '{"AutoScalingGroups":[
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                                {"DesiredCapacity":2,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"},
                                {"DesiredCapacity":0,"AutoScalingGroupName":"asg-etime-test-b-14si"},
                                {"DesiredCapacity":2,"AutoScalingGroupName":"asg-etime-test-g-14si"}
                            ]}'
                        fi
                        ;;
                    "deployment_in_progress")
                        # Both ASGs have instances (deployment in progress)
                        if [[ "$*" =~ --auto-scaling-group-names ]]; then
                            asg_name=$(echo "$*" | grep -o 'asg-etime-[^[:space:]]*' | head -1)
                            echo '{"AutoScalingGroups":[{"DesiredCapacity":1,"AutoScalingGroupName":"'$asg_name'"}]}'
                        else
                            echo '{"AutoScalingGroups":[
                                {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-b-14si"},
                                {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-skt-g-14si"},
                                {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-b-14si"},
                                {"DesiredCapacity":1,"AutoScalingGroupName":"asg-etime-test-g-14si"}
                            ]}'
                        fi
                        ;;
                esac
                ;;
            "update-auto-scaling-group")
                echo "Mock: Updated ASG $(echo "$*" | grep -o 'asg-etime-[^[:space:]]*')"
                ;;
        esac
        ;;
    "elbv2")
        case "$2" in
            "describe-load-balancers")
                lb_name=$(echo "$*" | grep -o 'alb-etime-[^[:space:]]*' | head -1)
                echo '{"LoadBalancers":[{"LoadBalancerArn":"arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/'$lb_name'/************3456"}]}'
                ;;
            "describe-listeners")
                # Return current traffic based on scenario
                case "$SCENARIO" in
                    "fresh_deployment")
                        # No current traffic (fresh deployment)
                        echo '{"Listeners":[{
                            "ListenerArn":"arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/************3456/************3456",
                            "Port":443,
                            "DefaultActions":[{
                                "ForwardConfig":{
                                    "TargetGroups":[{
                                        "TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/************3456",
                                        "Weight":100
                                    }]
                                }
                            }]
                        }]}'
                        ;;
                    "blue_to_green")
                        # Traffic currently on Blue
                        echo '{"Listeners":[{
                            "ListenerArn":"arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/************3456/************3456",
                            "Port":443,
                            "DefaultActions":[{
                                "ForwardConfig":{
                                    "TargetGroups":[{
                                        "TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-b-14si/************3456",
                                        "Weight":100
                                    }]
                                }
                            }]
                        }]}'
                        ;;
                    "green_to_blue")
                        # Traffic currently on Green
                        echo '{"Listeners":[{
                            "ListenerArn":"arn:aws:elasticloadbalancing:us-east-1:************:listener/app/test-lb/************3456/************3456",
                            "Port":443,
                            "DefaultActions":[{
                                "ForwardConfig":{
                                    "TargetGroups":[{
                                        "TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/************3456",
                                        "Weight":100
                                    }]
                                }
                            }]
                        }]}'
                        ;;
                esac
                ;;
            "describe-target-groups")
                # Return appropriate target groups based on query
                if [[ "$*" =~ --query.*contains.*TargetGroupName ]]; then
                    # Extract the target group name pattern from the query
                    if [[ "$*" =~ etime-tg-test-skt-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-b-14si/************3456"
                    elif [[ "$*" =~ etime-tg-test-skt-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/************3456"
                    elif [[ "$*" =~ etime-tg-test-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-b-14si/************3456"
                    elif [[ "$*" =~ etime-tg-test-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-g-14si/************3456"
                    elif [[ "$*" =~ etime-tg-test-cmd-b-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-b-14si/************3456"
                    elif [[ "$*" =~ etime-tg-test-cmd-g-14si ]]; then
                        echo "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-g-14si/************3456"
                    else
                        echo ""
                    fi
                else
                    # Return full JSON for general queries
                    echo '{"TargetGroups":[
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-b-14si/************3456","TargetGroupName":"etime-tg-test-skt-b-14si"},
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-skt-g-14si/************3456","TargetGroupName":"etime-tg-test-skt-g-14si"},
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-b-14si/************3456","TargetGroupName":"etime-tg-test-b-14si"},
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-g-14si/************3456","TargetGroupName":"etime-tg-test-g-14si"},
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-b-14si/************3456","TargetGroupName":"etime-tg-test-cmd-b-14si"},
                        {"TargetGroupArn":"arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/etime-tg-test-cmd-g-14si/************3456","TargetGroupName":"etime-tg-test-cmd-g-14si"}
                    ]}'
                fi
                ;;
            "describe-target-health")
                # Simulate healthy targets immediately for faster testing
                echo "2"
                ;;
            "modify-listener")
                echo "Mock: Modified listener for traffic switching"
                ;;
        esac
        ;;
esac
EOF

    chmod +x "${MOCK_AWS_DIR}/aws"
    export PATH="${MOCK_AWS_DIR}:${PATH}"
    
    log_test "Comprehensive mock AWS environment setup complete"
}

# Test 1: Fresh Deployment Logic
test_fresh_deployment_logic() {
    log_scenario "Testing Fresh Deployment Logic"
    
    export TEST_SCENARIO="fresh_deployment"
    export HEALTH_CHECK_ATTEMPTS=0
    
    local output
    output=$(timeout 15 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)

    # Debug output if test fails
    if [[ ! "$output" =~ "Deployment completed successfully" ]]; then
        log_error "Debug output for fresh deployment:"
        echo "$output" | tail -10
    fi

    # Check for correct color assignment
    if echo "$output" | grep -q "Fresh deployment detected.*Blue"; then
        pass_test "Fresh deployment detection"
    else
        fail_test "Fresh deployment detection" "Fresh deployment not detected"
        return 1
    fi

    if echo "$output" | grep -q "current=g, next=b"; then
        pass_test "Fresh deployment color logic (current=g, next=b)"
    else
        fail_test "Fresh deployment color logic" "Expected current=g, next=b"
        return 1
    fi

    if echo "$output" | grep -q "Deployment completed successfully"; then
        pass_test "Fresh deployment completion"
    else
        fail_test "Fresh deployment completion" "Deployment did not complete successfully"
        return 1
    fi
}

# Test 2: Blue to Green Deployment
test_blue_to_green_deployment() {
    log_scenario "Testing Blue to Green Deployment"
    
    export TEST_SCENARIO="blue_to_green"
    export HEALTH_CHECK_ATTEMPTS=0
    
    local output
    output=$(timeout 30 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    
    if echo "$output" | grep -q "current=b, next=g"; then
        pass_test "Blue to Green color logic (current=b, next=g)"
    else
        fail_test "Blue to Green color logic" "Expected current=b, next=g"
        return 1
    fi
    
    if echo "$output" | grep -q "Deployment completed successfully"; then
        pass_test "Blue to Green deployment completion"
    else
        fail_test "Blue to Green deployment completion" "Deployment did not complete"
        return 1
    fi
}

# Test 3: Green to Blue Deployment  
test_green_to_blue_deployment() {
    log_scenario "Testing Green to Blue Deployment"
    
    export TEST_SCENARIO="green_to_blue"
    export HEALTH_CHECK_ATTEMPTS=0
    
    local output
    output=$(timeout 30 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    
    if echo "$output" | grep -q "current=g, next=b"; then
        pass_test "Green to Blue color logic (current=g, next=b)"
    else
        fail_test "Green to Blue color logic" "Expected current=g, next=b"
        return 1
    fi
    
    if echo "$output" | grep -q "Deployment completed successfully"; then
        pass_test "Green to Blue deployment completion"
    else
        fail_test "Green to Blue deployment completion" "Deployment did not complete"
        return 1
    fi
}

# Test 4: Socket-First Dependency Logic
test_socket_first_dependency() {
    log_scenario "Testing Socket-First Dependency Logic"

    export TEST_SCENARIO="fresh_deployment"
    export HEALTH_CHECK_ATTEMPTS=0

    local output
    output=$(timeout 30 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)

    # Check that socket health check comes before normal
    local socket_line normal_line
    socket_line=$(echo "$output" | grep -n "Waiting for Socket instances" | cut -d: -f1)
    normal_line=$(echo "$output" | grep -n "Socket is healthy, now checking Normal" | cut -d: -f1)

    if [[ -n "$socket_line" && -n "$normal_line" && $socket_line -lt $normal_line ]]; then
        pass_test "Socket-first dependency order"
    else
        fail_test "Socket-first dependency order" "Socket health check not before Normal"
        return 1
    fi

    if echo "$output" | grep -q "Both Socket and Normal instances are healthy"; then
        pass_test "Socket-first dependency completion"
    else
        fail_test "Socket-first dependency completion" "Both health checks not completed"
        return 1
    fi
}

# Test 5: Deployment In Progress Detection
test_deployment_in_progress() {
    log_scenario "Testing Deployment In Progress Detection"

    export TEST_SCENARIO="deployment_in_progress"

    local output
    output=$(./deploy_blue_green_final_optimized.sh -e test -t skt 2>&1 || true)

    if echo "$output" | grep -q "deployment is running\|Unable to determine next color"; then
        pass_test "Deployment in progress detection"
    else
        fail_test "Deployment in progress detection" "Should have detected deployment in progress"
        return 1
    fi
}

# Test 6: Health Check Logic and Timing
test_health_check_logic() {
    log_scenario "Testing Health Check Logic and Timing"

    export TEST_SCENARIO="fresh_deployment"
    export HEALTH_CHECK_ATTEMPTS=0

    local start_time=$(date +%s)
    local output
    output=$(timeout 30 ./deploy_final_optimized.sh -e test -v 14si -s 1 2>&1 || true)
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Check for 10-second intervals mentioned in logs
    if echo "$output" | grep -q "10s intervals"; then
        pass_test "Health check interval configuration (10s)"
    else
        fail_test "Health check interval configuration" "10s intervals not found in logs"
    fi

    # Check for 90-minute timeout mentioned in logs
    if echo "$output" | grep -q "90min timeout"; then
        pass_test "Health check timeout configuration (90min)"
    else
        fail_test "Health check timeout configuration" "90min timeout not found in logs"
    fi

    # Check that health checks complete reasonably quickly in mock environment
    if [[ $duration -lt 15 ]]; then
        pass_test "Health check performance (completed in ${duration}s)"
    else
        fail_test "Health check performance" "Took too long: ${duration}s"
    fi
}

# Test 7: Parameter Validation
test_parameter_validation() {
    log_scenario "Testing Parameter Validation"

    # Test invalid environment
    if ./deploy_final_optimized.sh -e invalid_env 2>/dev/null; then
        fail_test "Invalid environment validation" "Should have failed"
    else
        pass_test "Invalid environment validation"
    fi

    # Test missing required parameters
    if ./deploy_final_optimized.sh 2>/dev/null; then
        fail_test "Missing parameter validation" "Should have failed"
    else
        pass_test "Missing parameter validation"
    fi

    # Test invalid type in blue-green script
    if ./deploy_blue_green_final_optimized.sh -e test -t invalid_type 2>/dev/null; then
        fail_test "Invalid type validation" "Should have failed"
    else
        pass_test "Invalid type validation"
    fi

    # Test help functionality
    if ./deploy_final_optimized.sh --help 2>&1 | grep -q "Usage:"; then
        pass_test "Help functionality"
    else
        fail_test "Help functionality" "Help not displayed correctly"
    fi
}

# Test 8: Individual Component Deployments
test_individual_components() {
    log_scenario "Testing Individual Component Deployments"

    export TEST_SCENARIO="fresh_deployment"

    # Test socket deployment
    if ./deploy_blue_green_final_optimized.sh -e test -t skt -n b -s 14si 2>&1 | grep -q "ASG deployment completed for skt"; then
        pass_test "Socket component deployment"
    else
        fail_test "Socket component deployment" "Socket deployment failed"
    fi

    # Test normal deployment
    if ./deploy_blue_green_final_optimized.sh -e test -t normal -n b -s 14si 2>&1 | grep -q "ASG deployment completed for normal"; then
        pass_test "Normal component deployment"
    else
        fail_test "Normal component deployment" "Normal deployment failed"
    fi

    # Test command deployment
    if ./deploy_blue_green_final_optimized.sh -e test -t cmd -n b -s 14si 2>&1 | grep -q "ASG deployment completed for cmd"; then
        pass_test "Command component deployment"
    else
        fail_test "Command component deployment" "Command deployment failed"
    fi
}

# Test 9: Error Handling and Cleanup
test_error_handling() {
    log_scenario "Testing Error Handling and Cleanup"

    # Test cleanup function
    local temp_script="/tmp/test_cleanup.sh"
    cat > "$temp_script" << 'EOF'
#!/bin/bash
set -euo pipefail
source deploy_final_optimized.sh
cleanup_failed_deployment "test" "b" "14si"
EOF
    chmod +x "$temp_script"

    if "$temp_script" 2>&1 | grep -q "Cleaning up failed deployment"; then
        pass_test "Error cleanup functionality"
    else
        fail_test "Error cleanup functionality" "Cleanup function failed"
    fi

    rm -f "$temp_script"
}

# Test 10: Performance and Timing
test_performance() {
    log_scenario "Testing Performance and Timing"

    export TEST_SCENARIO="fresh_deployment"
    export HEALTH_CHECK_ATTEMPTS=0

    local iterations=3
    local total_time=0

    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s)
        timeout 30 ./deploy_final_optimized.sh -e test -v 14si -s 0 >/dev/null 2>&1 || true
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        total_time=$((total_time + duration))
    done

    local avg_time=$((total_time / iterations))

    if [[ $avg_time -lt 10 ]]; then
        pass_test "Performance test (${avg_time}s average over $iterations runs)"
    else
        fail_test "Performance test" "Average time too high: ${avg_time}s"
    fi
}

# Test 11: Signal Handling
test_signal_handling() {
    log_scenario "Testing Signal Handling"

    export TEST_SCENARIO="fresh_deployment"

    # Start deployment in background and then kill it
    timeout 5 ./deploy_final_optimized.sh -e test -v 14si -s 10 >/dev/null 2>&1 &
    local pid=$!
    sleep 2
    kill -TERM $pid 2>/dev/null || true
    wait $pid 2>/dev/null || true

    # Check that no background processes are left
    if pgrep -f "deploy.*optimized" >/dev/null; then
        fail_test "Signal handling cleanup" "Background processes still running"
    else
        pass_test "Signal handling cleanup"
    fi
}

# Cleanup test environment
cleanup_test_env() {
    log_test "Cleaning up comprehensive test environment"
    rm -rf "$TEST_DIR"
    unset TEST_SCENARIO HEALTH_CHECK_ATTEMPTS

    # Kill any remaining background processes
    pkill -f "deploy.*optimized" 2>/dev/null || true

    log_test "Cleanup complete"
}

# Generate test report
generate_test_report() {
    echo
    echo "=========================================="
    echo "         COMPREHENSIVE TEST REPORT"
    echo "=========================================="
    echo
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    echo

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
        echo "The optimized deployment scripts are production-ready!"
    else
        echo -e "${RED}❌ $TESTS_FAILED TESTS FAILED ❌${NC}"
        echo "Please review and fix the failing tests before production deployment."
    fi

    echo
    echo "Test Coverage:"
    echo "✓ Fresh deployment logic"
    echo "✓ Blue-Green switching logic"
    echo "✓ Socket-first dependency"
    echo "✓ Health check timing and logic"
    echo "✓ Parameter validation"
    echo "✓ Individual component deployments"
    echo "✓ Error handling and cleanup"
    echo "✓ Performance testing"
    echo "✓ Signal handling"
    echo "✓ Deployment in progress detection"
    echo
}

# Main comprehensive test function
run_comprehensive_tests() {
    log_test "Starting Comprehensive Final Test Suite"
    log_test "Testing all deployment logic, edge cases, and scenarios"

    # Setup
    setup_comprehensive_mock_aws

    # Run all test scenarios
    test_fresh_deployment_logic
    test_blue_to_green_deployment
    test_green_to_blue_deployment
    test_socket_first_dependency
    test_deployment_in_progress
    test_health_check_logic
    test_parameter_validation
    test_individual_components
    test_error_handling
    test_performance
    test_signal_handling

    # Cleanup
    cleanup_test_env

    # Generate report
    generate_test_report

    # Return appropriate exit code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# Run comprehensive tests if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    cd "$(dirname "${BASH_SOURCE[0]}")"
    run_comprehensive_tests
fi
