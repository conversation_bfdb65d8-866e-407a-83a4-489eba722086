<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry;

use Element\Core\Exceptions\ElementException;
use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDayEntry;

class DeleteTimeSheetAllowanceEntry extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetAllowanceDayEntry $entry,

        bool $doesSaveParent = true,
        bool $doesPreventJobDispatching = false,
    ): void
    {
        $timeSheetAllowanceDay = $entry->timeSheetAllowanceDay;
        $timeSheet = $timeSheetAllowanceDay->timeSheetAllowance->timeSheet;

        if (!$timeSheet->canBeUpdated($this->loggedUser)) {
            throw new ElementException('Timesheet is closed for editing');
        }

        $entry->forceDelete();

        if ($doesSaveParent) {
            $timeSheetAllowanceDay->saveOrFail([
                'saveParent' => true,
                'preventJobsDispatching' => $doesPreventJobDispatching,
            ]);
        }
    }
}
