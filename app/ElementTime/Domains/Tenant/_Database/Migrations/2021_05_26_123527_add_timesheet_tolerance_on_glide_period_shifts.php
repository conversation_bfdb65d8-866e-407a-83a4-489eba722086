<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesAllowTimeSheetTolerance')->default(false)->after('hasSystemAudit');
        });

        //endregion Table: Settings

        //region Table: Shift

        Schema::table('Shift', function (Blueprint $table) {
            $table->boolean('doesAllowTolerance')->default(false)->after('glideRequireTimeBlock');
            $table->integer('toleratedMinutes')->default(0)->after('doesAllowTolerance');
            $table->boolean('doesShowToleranceAdjustedHours')->default(true)->after('toleratedMinutes');
        });

        //endregion Table: Shift

        //region Table: TimeSheet

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->boolean('doesHaveTolerance')->default(false)->after('valueTax');
            $table->integer('toleratedMinutes')->default(0)->after('doesHaveTolerance');
            $table->decimal('toleranceAdjustedHours', 16, 10)->default(0)->after('toleratedMinutes');
        });

        //endregion Table: TimeSheet
    }

    public function down(): void
    {
        //region Table: TimeSheet

        if (Schema::hasColumn('TimeSheet', 'doesHaveTolerance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('doesHaveTolerance');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'toleratedMinutes')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('toleratedMinutes');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'toleranceAdjustedHours')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('toleranceAdjustedHours');
            });
        }

        //endregion Table: TimeSheet

        //region Table: Shift

        if (Schema::hasColumn('Shift', 'doesAllowTolerance')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('doesAllowTolerance');
            });
        }

        if (Schema::hasColumn('Shift', 'toleratedMinutes')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('toleratedMinutes');
            });
        }

        if (Schema::hasColumn('Shift', 'doesShowToleranceAdjustedHours')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('doesShowToleranceAdjustedHours');
            });
        }

        //endregion Table: Shift

        //region Table: Settings

        if (Schema::hasColumn('Settings', 'doesAllowTimeSheetTolerance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAllowTimeSheetTolerance');
            });
        }

        //endregion Table: Settings
    }
};
