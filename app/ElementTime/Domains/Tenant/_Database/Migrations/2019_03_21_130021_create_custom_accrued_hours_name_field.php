<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            $table->string('customExcessTimeBalanceExternalId')->nullable()->default('hours-accrued');
            $table->string('customExcessTimeBalanceName')->nullable()->default('Hours accrued');
            $table->string('customExcessTimeLeaveExternalId')->nullable()->default('accrued-hours-taken');
            $table->string('customExcessTimeLeaveName')->nullable()->default('Accrued hours taken');
        });

        //endregion Table: Settings
    }

    public function down(): void
    {
        //region Table: Settings

        if (Schema::hasColumn('Settings', 'customExcessTimeLeaveName')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('customExcessTimeLeaveName');
            });
        }
        if (Schema::hasColumn('Settings', 'customExcessTimeLeaveExternalId')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('customExcessTimeLeaveExternalId');
            });
        }
        if (Schema::hasColumn('Settings', 'customExcessTimeBalanceName')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('customExcessTimeBalanceName');
            });
        }
        if (Schema::hasColumn('Settings', 'customExcessTimeBalanceExternalId')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('customExcessTimeBalanceExternalId');
            });
        }

        //endregion Table: Settings
    }
};
