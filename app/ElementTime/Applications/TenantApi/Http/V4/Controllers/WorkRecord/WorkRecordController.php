<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\WorkRecord;

use Element\Core\Exceptions\MissingCompulsoryArgumentException;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\BuildItemsAndBreaksInfoFromArray;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\CreateTimeSheetWork;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\DeleteTimeSheetWork;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\UpdateTimeSheetWork;
use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\BaseController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\WorkRecord\WorkRecordBasicResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\WorkRecord\WorkRecordDetailsResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\TenantApiV4Response;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleTimeType;
use Element\ElementTime\Support\Enums\RecordedVia;

class WorkRecordController extends BaseController
{
    /** @throws \Throwable */
    public function listWorks(string $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timeSheetDay_id = $this->i('timeSheetDay_id');

        if (!is_null($timeSheetDay_id)) {
            $timeSheetDay = $this->getTimeSheetDayById($timeSheetDay_id);
            $this->validateAuthorization($timeSheetDay->timeSheet);

            $works = TimeSheetWork::q()
                ->constraints([
                    ['TimeSheetWork.timeSheetDay_id', '=', $timeSheetDay->id],
                ])
                ->many();

            return TenantApiV4Response::fetch(WorkRecordBasicResource::collection($works));
        }

        throw new MissingCompulsoryArgumentException('Missing filter (timesheet day identification)');
    }

    /** @throws \Throwable */
    public function getWork(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $work = TimeSheetWork::q()->findOrFail($id);
        $this->validateAuthorization($work->timeSheetDay->timeSheet);

        return TenantApiV4Response::fetch(WorkRecordDetailsResource::make($work));
    }

    /** @throws \Throwable */
    public function createWork(string $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timeSheetDay_id = $this->i('timeSheetDay_id', null, 'integer');
        $scheduledPayRunPeriodDay_id = $this->i('scheduledPayRunPeriodDay_id', type: 'integer');
        $userRoleTimeType_id = $this->i('userRoleTimeType_id', type: 'integer');
        $start = $this->i('start', type: 'datetime');
        $end = $this->i('end', type: 'datetime');
        $recordedVia = RecordedVia::from($this->i('recordedVia', RecordedVia::AppTimeSheet, 'string'));

        $timeSheetDay = $this->getTimeSheetDayById($timeSheetDay_id);
        $scheduledPayRunPeriodDay = ScheduledPayRunPeriodDay::q()->findOrFail($scheduledPayRunPeriodDay_id);
        $userRoleTimeType = UserRoleTimeType::q()->findOrFail($userRoleTimeType_id);

        $itemsAndBreaksInfo = BuildItemsAndBreaksInfoFromArray::make()->handle(
            original: $this->i('items_and_breaks', []),
            timeSheetDay: $timeSheetDay,
        );

        $work = CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: $recordedVia,
            start: $start,
            end: $end,
            itemsAndBreaksInfo: $itemsAndBreaksInfo,
        );

        return TenantApiV4Response::fetch(new \stdClass);
    }

    /** @throws \Throwable */
    public function updateWork(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $work_id = $id;
        $scheduledPayRunPeriodDay_id = $this->i('scheduledPayRunPeriodDay_id', type: 'integer');
        $userRoleTimeType_id = $this->i('userRoleTimeType_id', type: 'integer');
        $start = $this->i('start', type: 'datetime');
        $end = $this->i('end', type: 'datetime');
        $recordedVia = RecordedVia::from($this->i('recordedVia', RecordedVia::AppTimeSheet, 'string'));

        $work = TimeSheetWork::q()->findOrFail($work_id);
        $timeSheetDay = $work->timeSheetDay;

        $this->validateAuthorization($timeSheetDay->timeSheet);

        $scheduledPayRunPeriodDay = ScheduledPayRunPeriodDay::q()->findOrFail($scheduledPayRunPeriodDay_id);
        $userRoleTimeType = UserRoleTimeType::q()->findOrFail($userRoleTimeType_id);

        $itemsAndBreaksInfo = BuildItemsAndBreaksInfoFromArray::make()->handle(
            original: $this->i('items_and_breaks', []),
            timeSheetDay: $timeSheetDay,
        );

        $work = UpdateTimeSheetWork::make()->handle(
            timeSheetWork: $work,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: $recordedVia,
            start: $start,
            end: $end,
            itemsAndBreaksInfo: $itemsAndBreaksInfo,
        );

        return TenantApiV4Response::fetch(new \stdClass);
    }

    /** @throws \Throwable */
    public function deleteWork(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $work = TimeSheetWork::q()->findOrFail($id);
        $this->validateAuthorization($work->timeSheetDay->timeSheet);

        DeleteTimeSheetWork::make()->handle($work);

        return TenantApiV4Response::fetch(new \stdClass);
    }

    // -----------------------------------------------------

    private function getTimeSheetDayById(int $id): TimeSheetDay
    {
        return TimeSheetDay::q()
            ->constraints([
                ['TimeSheetDay.id', '=', $id],
            ])
            ->firstOrFail();
    }
}
