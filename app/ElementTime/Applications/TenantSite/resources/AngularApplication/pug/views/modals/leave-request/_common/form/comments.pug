.row(ng-if='!!$ctrl.form.calculation.hasRun && !!$ctrl.form.calculation.canBeRequested')
  .col-xs-12
    .form-group
      label.label-control(for='Leave_reason') Reason / comments{{!!$ctrl.form.calculation.data.doesRequireCommentsOnSubmit ? ' (Required)' : ''}}
      textarea.form-control(id='Leave_reason' name='reason' ng-model='$ctrl.form.data.reason' ng-change='$ctrl.form.$events.onChangeComments()' ng-required='$ctrl.form.calculation.data.doesRequireCommentsOnSubmit' ng-class='{"ng-dirty": !!$ctrl.form.calculation.data.doesRequireCommentsOnSubmit}')
