<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region isFTE on EmployeeType
        Schema::table('EmployeeType', function (Blueprint $table) {
            $table->boolean('isFTE')->default(true);
        });
        //endregion

        //region Field isFullDay
        Schema::table('Leave', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });

        Schema::table('LeaveDay', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });

        Schema::table('ToilDay', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });

        Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
            $table->boolean('isFullDay')->default(false);
        });
        //endregion

        //region Leave/Toil/RDO relationship with UserShift
        Schema::table('Leave', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
        });

        Schema::table('Toil', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->bigInteger('userShift_id')->unsigned()->nullable()->change();
        });
        //endregion
    }

    public function down(): void
    {
        //region Field isFullDay
        if (Schema::hasColumn('RosteredTimeOffDay', 'isFullDay')) {
            Schema::table('RosteredTimeOffDay', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }

        if (Schema::hasColumn('RosteredTimeOff', 'isFullDay')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }

        if (Schema::hasColumn('ToilDay', 'isFullDay')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }

        if (Schema::hasColumn('Toil', 'isFullDay')) {
            Schema::table('Toil', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }

        if (Schema::hasColumn('LeaveDay', 'isFullDay')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }

        if (Schema::hasColumn('Leave', 'isFullDay')) {
            Schema::table('Leave', function (Blueprint $table) {
                $table->dropColumn('isFullDay');
            });
        }
        //endregion

        //region isFTE on EmployeeType
        if (Schema::hasColumn('EmployeeType', 'isFTE')) {
            Schema::table('EmployeeType', function (Blueprint $table) {
                $table->dropColumn('isFTE');
            });
        }
        //endregion
    }
};
