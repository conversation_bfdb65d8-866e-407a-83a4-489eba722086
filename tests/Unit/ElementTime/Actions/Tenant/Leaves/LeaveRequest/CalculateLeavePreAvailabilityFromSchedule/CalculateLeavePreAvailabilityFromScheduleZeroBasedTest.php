<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\Carbon;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleZeroBased;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityZeroBased;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\PayRuns\Enums\PayRunStatus\PayRunStatus;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodZeroBased;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;

uses(LeaveGeneralFunctionality::class);

describe('Zero based - Calculate leave pre availability', function () {
    it('can calculate in an open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($payRun->startDate)
            ->and($availability->endDate)->toEqual($payRun->endDate)
            ->and($availability->duration)->toEqual($duration)
            ->and($availability->days)->toHaveCount(0)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($duration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('can calculate in a future payrun', function () {
        $payRun = TenantSystemSettings::getPayRunByDate(Carbon::today());

        while ($payRun->id) {
            $payRun = $payRun->next;
        }

        expect($payRun->id)->toBeNull()
            ->and($payRun->status)->toEqual(PayRunStatus::NotStarted);

        $date = $payRun->startDate;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with future active schedules at the moment');
        }

        $duration = TimeDuration::parseFromHours(10);

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeTrue()
            ->and($availability->startDate)->toEqual($payRun->startDate)
            ->and($availability->endDate)->toEqual($payRun->endDate)
            ->and($availability->duration)->toEqual($duration)
            ->and($availability->days)->toHaveCount(0)
            ->and($availability->afterAvailableDuration)->toEqual($availability->beforeAvailableDuration->copy()->deduct($duration))
            ->and($availability->errorMessages)->toHaveCount(0);
    });

    it('cannot have more than the maximum hours per period', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        ScheduledPayRunPeriodZeroBased::query()->update([
            'doesHaveMaxDuration' => true,
            'maxDuration' => TimeDuration::parseFromHours(40),
        ]);

        [$user, $userRoleSchedule, $userLeaveBankType, $days, $periods] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $duration = $periods[0]->type->maxDuration->copy()->add(2 , 'hours');

        $availability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: $duration,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($availability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($availability->canBeRequested)->toBeFalse()
            ->and($availability->errorMessages)->toBeGreaterThan(0);
        // TODO: Validate type of error
    });

    test('all requests in a period summed with new request cannot have more than the maximum hours defined', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        ScheduledPayRunPeriodZeroBased::query()->update([
            'doesHaveMaxDuration' => true,
            'maxDuration' => TimeDuration::parseFromHours(40),
        ]);

        [$user, $userRoleSchedule, $userLeaveBankType, $days, $periods] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
                userLeaveBankType: $userLeaveBankType,
                userRoleSchedule: $userRoleSchedule,
                startDate: $payRun->startDate,
                duration: TimeDuration::parseFromHours(30),
                actorUser: SuperUserFlag::getOne(),
            ),
            doesApprove: true,
        );

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: TimeDuration::parseFromHours(30),
            actorUser: SuperUserFlag::getOne(),
        );

        expect($preAvailability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($preAvailability->canBeRequested)->toBeFalse()
            ->and($preAvailability->errorMessages)->toBeGreaterThan(0);
        // TODO: Validate type of error
    });

    test('all requests in a period summed with new request cannot have more than the maximum hours defined unless its being replaced', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        $date = $payRun->startDate;

        ScheduledPayRunPeriodZeroBased::query()->update([
            'doesHaveMaxDuration' => true,
            'maxDuration' => TimeDuration::parseFromHours(40),
        ]);

        [$user, $userRoleSchedule, $userLeaveBankType, $days, $periods] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::ZeroBased, date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('There is no user on a zero based schedule with actual schedules at the moment');
        }

        $placedLeaveRequest = PlaceLeaveRequestFromPreAvailabilityZeroBased::make()->handle(
            userRoleSchedule: $userRoleSchedule,
            userLeaveBankType: $userLeaveBankType,
            preAvailability: CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
                userLeaveBankType: $userLeaveBankType,
                userRoleSchedule: $userRoleSchedule,
                startDate: $payRun->startDate,
                duration: TimeDuration::parseFromHours(30),
                actorUser: SuperUserFlag::getOne(),
            ),
            doesApprove: true,
        );

        $preAvailability = CalculateLeavePreAvailabilityFromScheduleZeroBased::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            startDate: $payRun->startDate,
            duration: TimeDuration::parseFromHours(30),
            replaced: $placedLeaveRequest,
            actorUser: SuperUserFlag::getOne(),
        );

        expect($preAvailability)->toBeInstanceOf(StructLeaveRequestPreAvailability::class)
            ->and($preAvailability->canBeRequested)->toBeTrue()
            ->and($preAvailability->errorMessages)->toHaveCount(0);
    });
});
