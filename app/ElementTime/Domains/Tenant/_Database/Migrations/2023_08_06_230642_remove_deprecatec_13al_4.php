<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        //region Table User
        try {
            Schema::table('User', function (Blueprint $table) {
                $table->dropForeign('tenant_user_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('User', 'tenant_id')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('tenant_id');
            });
        }
        if (Schema::hasColumn('User', 'isMain')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('isMain');
            });
        }
        if (Schema::hasColumn('User', 'isLocked')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('isLocked');
            });
        }
        if (Schema::hasColumn('User', 'systemAccess')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('systemAccess');
            });
        }
        if (Schema::hasColumn('User', 'hasPublicHolidaysOff')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('hasPublicHolidaysOff');
            });
        }
        //endregion Table User

        Schema::dropIfExists('Address');
        Schema::dropIfExists('Tenant');

        //region Table ExcessTimeGroupSectionLevelRule
        try {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropForeign('etgslr_project_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'project_id')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('project_id');
            });
        }
        if (Schema::hasColumn('ExcessTimeGroupSectionLevelRule', 'projectAccountCode')) {
            Schema::table('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
                $table->dropColumn('projectAccountCode');
            });
        }
        //endregion Table ExcessTimeGroupSectionLevelRule

        //region Table UserPasswordReminder
        try {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropForeign('upr_rn_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserPasswordReminder', 'requestNotification_id')) {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropColumn('requestNotification_id');
            });
        }
        try {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropForeign('upr_sn_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserPasswordReminder', 'successNotification_id')) {
            Schema::table('UserPasswordReminder', function (Blueprint $table) {
                $table->dropColumn('successNotification_id');
            });
        }
        //endregion Table UserPasswordReminder
    }

    public function down(): void
    {
        //
    }
};
