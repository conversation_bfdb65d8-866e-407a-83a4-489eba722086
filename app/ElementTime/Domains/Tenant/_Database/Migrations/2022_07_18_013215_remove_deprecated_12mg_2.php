<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('UserProject_ProjectActivity');

        Schema::dropIfExists('UserProject');

        try {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropForeign('tset_tsidt_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetExcessTime', 'timeSheetItemDayTime_id')) {
            Schema::table('TimeSheetExcessTime', function (Blueprint $table) {
                $table->dropColumn('timeSheetItemDayTime_id');
            });
        }

        try {
            Schema::table('TimeSheetPenaltyTrigger', function (Blueprint $table) {
                $table->dropForeign('tspt_tsidt_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetPenaltyTrigger', 'timeSheetItemDayTime_id')) {
            Schema::table('TimeSheetPenaltyTrigger', function (Blueprint $table) {
                $table->dropColumn('timeSheetItemDayTime_id');
            });
        }

        Schema::dropIfExists('TimeSheetItemDayTimeBreak');
        Schema::dropIfExists('TimeSheetItemDayTime');
        Schema::dropIfExists('TimeSheetItemDay');
        Schema::dropIfExists('TimeSheetItem');

        if (Schema::hasColumn('UserShift', 'minimumHoursToDeductBreak')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('minimumHoursToDeductBreak');
            });
        }
    }

    public function down(): void
    {
        //
    }
};
