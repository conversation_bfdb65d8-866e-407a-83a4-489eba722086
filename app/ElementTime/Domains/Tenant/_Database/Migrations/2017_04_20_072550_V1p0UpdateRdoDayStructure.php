<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->boolean('allowsToTakePartDay')->default(true);
        });

        Schema::table('RosteredTimeOff', function (Blueprint $table) {
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
        });

        Schema::create('RosteredTimeOffDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('rosteredTimeOff_id')->unsigned();
            $table->bigInteger('userShift_id')->unsigned();
            $table->bigInteger('userShiftDay_id')->unsigned();
            $table->date('date');

            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id', 'rdoday_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('rosteredTimeOff_id', 'rdoday_leave_fn')->references('id')->on('RosteredTimeOff')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShift_id', 'rdoday_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userShiftDay_id', 'rdoday_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
        });

        if (Schema::hasColumn('RosteredTimeOff', 'date')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('date');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'userShiftDay_id')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropForeign('rto_usd_fn');
                $table->dropColumn('userShiftDay_id');
            });
        }

        if (!Schema::hasColumn('LeaveDay', 'deleted_at')) {
            Schema::table('LeaveDay', function (Blueprint $table) {
                $table->timestamps();
                $table->softDeletes();
            });
        }

        if (!Schema::hasColumn('ToilDay', 'deleted_at')) {
            Schema::table('ToilDay', function (Blueprint $table) {
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    public function down(): void
    {
        if (!Schema::hasColumn('RosteredTimeOff', 'date')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->date('date')->nullable();
            });
        }
        if (!Schema::hasColumn('RosteredTimeOff', 'userShiftDay_id')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->bigInteger('userShiftDay_id')->unsigned()->nullable();
                $table->foreign('userShiftDay_id', 'rto_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
            });
        }

        Schema::dropIfExists('RosteredTimeOffDay');
        if (Schema::hasColumn('RosteredTimeOff', 'startDateTime')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }
        if (Schema::hasColumn('RosteredTimeOff', 'endDateTime')) {
            Schema::table('RosteredTimeOff', function (Blueprint $table) {
                $table->dropColumn('endDateTime');
            });
        }

        if (Schema::hasColumn('RosteredTimeOffType', 'allowsToTakePartDay')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('allowsToTakePartDay');
            });
        }
    }
};
