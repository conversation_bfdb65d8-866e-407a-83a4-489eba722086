<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('UserMediaFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('mediaFile_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('mediaFile_id', 'umf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'umf_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::dropIfExists('UserFile');
    }

    public function down(): void
    {
        Schema::dropIfExists('UserMediaFile');
    }
};
