<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\AdHocAllowance;

use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry\CreateTimeSheetAllowanceDayEntry;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry\DeleteTimeSheetAllowanceEntry;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry\UpdateTimeSheetAllowanceDayEntry;
use Element\ElementTime\Applications\TenantApi\Http\V4\Controllers\BaseController;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance\AdHocAllowanceDayConfigResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\AdHocAllowance\AdHocAllowanceEntryResource;
use Element\ElementTime\Applications\TenantApi\Http\V4\TenantApiV4Response;
use Element\ElementTime\Domains\Tenant\Settings\Exceptions\AllowanceTypeCostingTypeCannotBeUsed;
use Element\ElementTime\Domains\Tenant\Settings\Models\AllowanceType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Exceptions\AdHocAllowanceRelatedToTimeCannotBeChanged;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDayEntry;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Repositories\TimeSheetDayRepository;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Database\Eloquent\Collection;

class AdHocAllowanceController extends BaseController
{
    /** @throws \Throwable */
    public function getEntry(string $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $reg = TimeSheetAllowanceDayEntry::q()
            ->relations(AdHocAllowanceEntryResource::getRelations())
            ->findOrFail($id);

        $this->validateAuthorization($reg->timeSheet);

        if ($reg->timeSheetWorkItem_id) {
            throw new AdHocAllowanceRelatedToTimeCannotBeChanged('Allowance entry associated to time cannot be updated');
        }

        return TenantApiV4Response::fetch(AdHocAllowanceEntryResource::make($reg));
    }

    /** @throws \Throwable */
    public function createEntry(string $tenant): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $timeSheetDay_id = $this->i('timeSheetDay_id');
        $allowanceType_id = $this->i('allowanceType_id');
        $duration = $this->i('duration', type: 'float');
        $miles = $this->i('miles', type: 'float');
        $quantity = $this->i('quantity', type: 'float');
        $startDateTime = $this->i('startDateTime', type: 'datetime');
        $endDateTime = $this->i('endDateTime', type: 'datetime');
        $notes = $this->i('notes');
        $recordedVia = RecordedVia::from($this->i('recordedVia', RecordedVia::KioskTimer));

        $allowanceType = AllowanceType::q()->findOrFail($allowanceType_id);

        if (!$allowanceType->costingType->canCostToMaster()) {
            throw new AllowanceTypeCostingTypeCannotBeUsed('Allowance type cannot be costed to master');
        }

        $timeSheetDay = TimeSheetDay::q()->findOrFail($timeSheetDay_id);
        $timeSheetAllowance = $timeSheetDay->timeSheet->repository->getTimeSheetAllowance($allowanceType_id);
        $timeSheetAllowanceDay = $timeSheetAllowance->timeSheetAllowanceDays
            ->where('timeSheetDay_id', '=', $timeSheetDay->id)->firstOrFail();

        $this->validateAuthorization($timeSheetDay->timeSheet);

        CreateTimeSheetAllowanceDayEntry::make()->handle(
            timeSheetAllowanceDay: $timeSheetAllowanceDay,
            duration: $duration,
            miles: $miles,
            quantity: $quantity,
            startDateTime: $startDateTime,
            endDateTime: $endDateTime,
            notes: $notes,
            recordedVia: $recordedVia,
        );

        return TenantApiV4Response::fetch(new \stdClass);
    }

    /** @throws \Throwable */
    public function updateEntry(string $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $entry = TimeSheetAllowanceDayEntry::q()->findOrFail($id);
        $this->validateAuthorization($entry->timeSheet);

        if ($entry->timeSheetWorkItem_id) {
            throw new AdHocAllowanceRelatedToTimeCannotBeChanged('Allowance entry associated to time cannot be updated');
        }

        $duration = $this->i('duration', type: 'float');
        $miles = $this->i('miles', type: 'float');
        $quantity = $this->i('quantity', type: 'float');
        $startDateTime = $this->i('startDateTime', type: 'datetime');
        $endDateTime = $this->i('endDateTime', type: 'datetime');
        $notes = $this->i('notes');

        UpdateTimeSheetAllowanceDayEntry::make()->handle(
            entry: $entry,
            duration: $duration,
            miles: $miles,
            quantity: $quantity,
            startDateTime: $startDateTime,
            endDateTime: $endDateTime,
            notes: $notes,
        );

        return TenantApiV4Response::fetch(new \stdClass);
    }

    /** @throws \Throwable */
    public function deleteEntry(string $tenant, int $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        $entry = TimeSheetAllowanceDayEntry::q()->findOrFail($id);
        $this->validateAuthorization($entry->timeSheet);

        if ($entry->timeSheetWorkItem_id) {
            throw new AdHocAllowanceRelatedToTimeCannotBeChanged('Allowance entry associated to time cannot be deleted');
        }

        DeleteTimeSheetAllowanceEntry::make()->handle(
            entry: $entry,
        );

        return TenantApiV4Response::fetch(new \stdClass);
    }

    /** @throws \Throwable */
    public function getDayConfig(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        if ($id == 'current') {
            $reg = TimeSheetDayRepository::getMyTodayRecord();
        } else {
            $reg = TimeSheetDay::q()->findOrFail($id);
        }

        $this->validateAuthorization($reg->timeSheet);

        $reg->load(AdHocAllowanceDayConfigResource::getRelations());

        return TenantApiV4Response::fetch(AdHocAllowanceDayConfigResource::make($reg));
    }

    /** @throws \Throwable */
    public function getDayEntries(string $tenant, int|string $id): \Illuminate\Http\JsonResponse
    {
        $this->validateTenant($tenant);

        if ($id == 'current') {
            $reg = TimeSheetDayRepository::getMyTodayRecord();
        } else {
            $reg = TimeSheetDay::q()->findOrFail($id);
        }

        $this->validateAuthorization($reg->timeSheet);

        $reg->load([
            'timeSheetAllowanceDays.timeSheetAllowanceDayEntries',
        ]);

        $entries = new Collection;

        foreach ($reg->timeSheetAllowanceDays as $timeSheetAllowanceDay) {
            foreach ($timeSheetAllowanceDay->timeSheetAllowanceDayEntries as $entry) {
                if (!is_null($entry->timeSheetWorkItem_id)) {
                    continue;
                }

                $entries->push($entry);
            }
        }

        $entries->load(AdHocAllowanceEntryResource::getRelations());

        return TenantApiV4Response::fetch(AdHocAllowanceEntryResource::collection($entries));
    }
}
