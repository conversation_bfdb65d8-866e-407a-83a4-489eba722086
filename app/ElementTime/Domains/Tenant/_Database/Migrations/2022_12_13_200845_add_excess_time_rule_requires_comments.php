<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('ExcessTimeRule', function (Blueprint $table) {
            $table->boolean('doesRequireComments')->default(false)->after('doesRestrictToPayrollOfficers');
            $table->float('minimumHoursToRequireComments')->default(0)->after('doesRequireComments');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('ExcessTimeRule', 'doesRequireComments')) {
            Schema::table('ExcessTimeRule', function (Blueprint $table) {
                $table->dropColumn('doesRequireComments');
            });
        }

        if (Schema::hasColumn('ExcessTimeRule', 'minimumHoursToRequireComments')) {
            Schema::table('ExcessTimeRule', function (Blueprint $table) {
                $table->dropColumn('minimumHoursToRequireComments');
            });
        }
    }
};
