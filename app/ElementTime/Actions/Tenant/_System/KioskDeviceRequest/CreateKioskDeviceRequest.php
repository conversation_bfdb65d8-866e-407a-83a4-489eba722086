<?php

namespace Element\ElementTime\Actions\Tenant\_System\KioskDeviceRequest;

use Element\ElementTime\Actions\Tenant\_System\BaseSystemAction;
use Element\ElementTime\Domains\Tenant\_System\Models\KioskDeviceRequest;

class CreateKioskDeviceRequest extends BaseSystemAction
{
    /** @throws \Throwable */
    public function handle(
        string $deviceId,
        string $ipAddress,
        string $deviceName,
    ): KioskDeviceRequest
    {
        $kioskDeviceRequest = new KioskDeviceRequest;

        $kioskDeviceRequest->deviceId = $deviceId;
        $kioskDeviceRequest->ipAddress = $ipAddress;
        $kioskDeviceRequest->name = $deviceName;

        $kioskDeviceRequest->saveOrFail();

        return $kioskDeviceRequest;
    }
}
