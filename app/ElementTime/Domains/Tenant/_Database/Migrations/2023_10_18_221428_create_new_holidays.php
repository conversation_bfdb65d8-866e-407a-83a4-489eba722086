<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('Holiday', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();
            $table->boolean('doesOverrideDefaultExternalId')->default(false);
            $table->string('externalId')->nullable();

            $table->date('date');
            $table->unsignedBigInteger('calendarDate_id');
            $table->foreign('calendarDate_id', 'h_cd_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');

            $table->string('name');

            $table->boolean('isFullDay')->default(true);
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->text('description');

            $table->timestamps();
            $table->index(['date'], 'holiday_date_ix');
        });

        Schema::create('HolidayCondition', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('holiday_id');
            $table->foreign('holiday_id', 'hc_h_fn')->references('id')->on('Holiday')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('parentHolidayCondition_id')->nullable();
            $table->foreign('parentHolidayCondition_id', 'hc_parent_fn')->references('id')->on('HolidayCondition')->onUpdate('cascade')->onDelete('cascade');

            $table->string('type');
            $table->json('options')->nullable();
            $table->boolean('isNot')->default(false);
            $table->integer('order')->default(1);

            $table->timestamps();
        });

        Schema::create('UserShiftDayHoliday', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->unsignedBigInteger('userShiftDay_id');
            $table->foreign('userShiftDay_id', 'usdh_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('holiday_id');
            $table->foreign('holiday_id', 'usdh_h_fn')->references('id')->on('Holiday')->onUpdate('cascade')->onDelete('cascade');

            $table->boolean('isEntitled')->default(true);
            $table->integer('minutes')->default(0);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UserShiftDayHoliday');
        Schema::dropIfExists('HolidayCondition');
        Schema::dropIfExists('Holiday');
    }
};
