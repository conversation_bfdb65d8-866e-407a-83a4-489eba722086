<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('PayrollIntegrationRun', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->bigInteger('payRun_id');
            $table->integer('succeeded')->default(0);
            $table->integer('failed')->default(0);
            $table->integer('skipped')->default(0);
            $table->string('errors', 4096)->nullable();
            $table->string('warnings', 4096)->nullable();
            $table->string('info', 4096)->nullable();
            $table->string('status')->default('A');
            $table->timestamps();
        });

        Schema::create('UnifiedUserTimeSheet', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->unsignedBigInteger('payrollIntegrationRun_id')->nullable();
            $table->foreign('payrollIntegrationRun_id')
                ->references('id')
                ->on('PayrollIntegrationRun')
                ->onDelete('cascade');
            $table->bigInteger('timeSheet_id')->nullable();
            $table->integer('userId')->nullable();
            $table->string('errors', 4096)->nullable();
            $table->string('warnings', 4096)->nullable();
            $table->string('info', 4096)->nullable();
            $table->string('status')->default('A');
            $table->timestamps();
        });

        Schema::create('UnifiedUserTimeSheetEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // common fields
            $table->bigIncrements('id');
            $table->unsignedBigInteger('payrollIntegrationRun_id')->nullable();
            $table->foreign('payrollIntegrationRun_id')
                ->references('id')
                ->on('PayrollIntegrationRun')
                ->onDelete('cascade');
            $table->bigInteger('unifiedUserTimeSheet_id');
            $table->string('firstName')->nullable();
            $table->string('lastName')->nullable();
            $table->date('payRunStartDate')->nullable();
            $table->date('payRunEndDate')->nullable();
            $table->bigInteger('timeSheet_id')->nullable();
            $table->integer('userId')->nullable();
            $table->string('employeeId')->nullable();
            $table->string('costAccount')->nullable();

            $table->string('rowType')->nullable();

            $table->string('hourCode')->nullable();
            $table->string('rateCode')->nullable();
            $table->decimal('actualHours')->nullable();

            // leave
            $table->date('leaveStartDate')->nullable();
            $table->date('leaveEndDate')->nullable();
            $table->string('leaveHasCertificate')->nullable();
            $table->string('leaveComment')->nullable();

            // allowance
            $table->string('allowanceCode')->nullable();
            $table->decimal('allowanceValue')->nullable();
            $table->decimal('allowanceQuantity')->nullable();
            $table->decimal('allowanceAmount')->nullable();

            // excess time
            $table->string('excessTimeType')->nullable();

            $table->string('errors', 4096)->nullable();
            $table->string('warnings', 4096)->nullable();
            $table->string('info', 4096)->nullable();

            $table->string('status')->default('A');

            $table->string('batchNumber')->nullable();
            $table->string('batchStatus')->nullable();
            $table->string('lineNumber')->nullable();
            $table->string('lineStatus')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UnifiedUserTimeSheetEntry');
        Schema::dropIfExists('UnifiedUserTimeSheet');
        Schema::dropIfExists('PayrollIntegrationRun');
    }
};
