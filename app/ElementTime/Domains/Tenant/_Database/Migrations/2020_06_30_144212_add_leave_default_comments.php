<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: LeaveType
        Schema::table('LeaveType', function (Blueprint $table) {
            $table->text('defaultComment')->nullable()->after('commentRequiredDurationType');
        });
        //endregion Table: LeaveType

        //region Table: RosteredTimeOffType
        Schema::table('RosteredTimeOffType', function (Blueprint $table) {
            $table->text('defaultComment')->nullable()->after('commentRequiredDurationType');
        });
        //endregion Table: RosteredTimeOffType

        //region Table: Settings
        Schema::table('Settings', function (Blueprint $table) {
            $table->text('excessTimeLeaveDefaultComment')->nullable()->after('excessTimeLeaveCommentRequiredDurationType');
        });
        //endregion Table: Settings

        //region Table: UserLeaveBankType
        Schema::table('UserLeaveBankType', function (Blueprint $table) {
            $table->text('defaultCommentWhenTakingLeave')->nullable()->after('doesIgnorePublicHolidays');
        });
        //endregion Table: UserLeaveBankType
    }

    public function down(): void
    {
        //region Table: UserLeaveBankType
        if (Schema::hasColumn('UserLeaveBankType', 'defaultCommentWhenTakingLeave')) {
            Schema::table('UserLeaveBankType', function (Blueprint $table) {
                $table->dropColumn('defaultCommentWhenTakingLeave');
            });
        }
        //endregion Table: UserLeaveBankType

        //region Table: Settings
        if (Schema::hasColumn('Settings', 'excessTimeLeaveDefaultComment')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveDefaultComment');
            });
        }
        //endregion Table: Settings

        //region Table: RosteredTimeOffType
        if (Schema::hasColumn('RosteredTimeOffType', 'defaultComment')) {
            Schema::table('RosteredTimeOffType', function (Blueprint $table) {
                $table->dropColumn('defaultComment');
            });
        }
        //endregion Table: RosteredTimeOffType

        //region Table: LeaveType
        if (Schema::hasColumn('LeaveType', 'defaultComment')) {
            Schema::table('LeaveType', function (Blueprint $table) {
                $table->dropColumn('defaultComment');
            });
        }
        //endregion Table: LeaveType
    }
};
