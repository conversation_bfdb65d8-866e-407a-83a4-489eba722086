<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays;

use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriod;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodZeroBased;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayZeroBased;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDefaultData;

class GetScheduledDaysZeroBased extends BaseGetScheduledDays
{
    protected function makeDayActual(
        UserRoleSchedule $userRoleSchedule,
        ScheduledPayRunPeriodDay $dayData,
        ScheduledPayRunPeriod $scheduledPayRunPeriod,
    ): StructScheduledDayZeroBased
    {
        /** @var ScheduledPayRunPeriodZeroBased $scheduledPayRunPeriodZeroBased */
        $scheduledPayRunPeriodZeroBased = $dayData->scheduledPayRunPeriod->specific;

        $ret = new StructScheduledDayZeroBased;
        $ret->date = $dayData->date->copy();
        $ret->maxPeriodDuration = $scheduledPayRunPeriodZeroBased->doesHaveMaxDuration ? $scheduledPayRunPeriodZeroBased->maxDuration : null;
        $ret->maxDuration = $scheduledPayRunPeriodZeroBased->doesHaveDailyMaxDuration ? $scheduledPayRunPeriodZeroBased->dailyMaxDuration : null;
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;

        return $ret;
    }

    protected function makeDayDefault(
        UserRoleSchedule $userRoleSchedule,
        StructScheduledPayRunPeriodDayDefaultData $dayData,
        StructScheduledPayRunPeriodDefaultData $specificPeriod,
    ): StructScheduledDayZeroBased
    {
        $ret = new StructScheduledDayZeroBased;
        $ret->date = $dayData->date->copy();
        $ret->maxPeriodDuration = $userRoleSchedule->zeroBased->doesHaveMaxDuration ? $userRoleSchedule->zeroBased->maxDuration : null;
        $ret->maxDuration = $userRoleSchedule->zeroBased->doesHaveDailyMaxDuration ? $userRoleSchedule->zeroBased->dailyMaxDuration : null;
        $ret->isFte = $userRoleSchedule->userRole->user->isFte;

        return $ret;
    }
}
