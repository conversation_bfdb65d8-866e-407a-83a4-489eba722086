<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Workflow MODIFY COLUMN type VARCHAR(255) AFTER id;');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE Workflow MODIFY COLUMN status VARCHAR(255) DEFAULT \'' . NewType::ID . '\' AFTER type;');

        Schema::table('Workflow', function (Blueprint $table) {
            $table->dateTime('relStartDateTime')->nullable()->after('rel_id');
            $table->dateTime('relEndDateTime')->nullable()->after('relStartDateTime');

            $table->unsignedBigInteger('payRun_id')->nullable()->after('otherRel_id');
            $table->foreign('payRun_id', 'wflow_pr_fn')
                ->references('id')
                ->on('PayRun')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->unsignedBigInteger('timeSheet_id')->nullable()->after('payRun_id');
            $table->foreign('timeSheet_id', 'wflow_ts_fn')
                ->references('id')
                ->on('TimeSheet')
                ->onUpdate('cascade')
                ->onDelete('set null');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('Workflow', 'relStartDateTime')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('relStartDateTime');
            });
        }

        if (Schema::hasColumn('Workflow', 'relEndDateTime')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('relEndDateTime');
            });
        }

        try {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropForeign('wflow_pr_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('Workflow', 'payRun_id')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('payRun_id');
            });
        }

        try {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropForeign('wflow_ts_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('Workflow', 'timeSheet_id')) {
            Schema::table('Workflow', function (Blueprint $table) {
                $table->dropColumn('timeSheet_id');
            });
        }
    }
};
