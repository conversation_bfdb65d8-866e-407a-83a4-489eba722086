<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetExcessTime;

use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetDay\CalculateTimeSheetDayRelatedRecords;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetPeriod\CalculateTimeSheetPeriodRelatedRecords;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeNotApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeNotCommentedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\ModelIssueTypes\TimeSheet\ExcessTimeRuleNotAssignedType;

class ApplyRelatedCalculationsPostExcessTimeChanges extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetExcessTime $timeSheetExcessTime,
    ): TimeSheetExcessTime
    {
        if ($timeSheetExcessTime->type == 'P') {
            CalculateTimeSheetPeriodRelatedRecords::make()->handle(
                timeSheetPeriod: $timeSheetExcessTime->timeSheetPeriod,
                doesCalculateExcessTime: false,
                doesPreventBroadcasting: true,
            );
        } else {
            CalculateTimeSheetDayRelatedRecords::make()->handle(
                timeSheetDay: $timeSheetExcessTime->timeSheetWork->timeSheetDay,
                doesCalculateExcessTime: false,
                doesCalculatePenalty: true,
                doesPreventBroadcasting: true,
            );
        }

        $timeSheetExcessTime->timeSheet->repository->updateIssues([
            ExcessTimeRuleNotAssignedType::class,
            ExcessTimeNotCommentedType::class,
            ExcessTimeNotApprovedType::class,
        ]);

        return $timeSheetExcessTime;
    }
}
