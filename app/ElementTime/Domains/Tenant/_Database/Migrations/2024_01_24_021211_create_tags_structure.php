<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('Tag', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->string('name');
            $table->text('description')->nullable();

            $table->timestamps();

            $table->unique(['name'], 'tag_name_uq');
        });

        Schema::create('ModelTag', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->id();

            $table->unsignedBigInteger('tag_id');
            $table->foreign('tag_id', 'mt_tag_fn')
                ->references('id')
                ->on('Tag')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->string('model_type');
            $table->unsignedBigInteger('model_id');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'mt_au_fn')
                ->references('id')
                ->on('User')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->timestamps();

            $table->unique(['tag_id', 'model_type', 'model_id'], 'mt_tag_model_uq');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ModelTag');
        Schema::dropIfExists('Tag');
    }
};
