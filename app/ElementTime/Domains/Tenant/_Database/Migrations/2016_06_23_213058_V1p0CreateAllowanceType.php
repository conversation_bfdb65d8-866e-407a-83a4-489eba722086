<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('AllowanceType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->string('externalId')->nullable();
            $table->string('name')->nullable();
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();

            $table->char('rateType', 1)->nullable();
            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->bigInteger('projectActivity_id')->unsigned()->nullable();
            $table->boolean('isEarntDuringLeave')->nullable();

            $table->float('awardRateValue')->nullable();
            $table->char('awardRateType', 1)->nullable();
            $table->float('capAmountValue')->nullable();
            $table->char('capAmountType', 1)->nullable();
            $table->char('ifCapReached', 1)->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('AllowanceType_LeaveType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigInteger('allowanceType_id')->unsigned();
            $table->bigInteger('leaveType_id')->unsigned();

            $table->primary(['allowanceType_id', 'leaveType_id']);
            $table->foreign('allowanceType_id', 'allowanceType_leavetype_allowancetype_fn')->references('id')->on('AllowanceType')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('leaveType_id', 'allowancetype_leavetype_leavetype_fn')->references('id')->on('LeaveType')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('AllowanceType');
        Schema::drop('AllowanceType_LeaveType');
    }
};
