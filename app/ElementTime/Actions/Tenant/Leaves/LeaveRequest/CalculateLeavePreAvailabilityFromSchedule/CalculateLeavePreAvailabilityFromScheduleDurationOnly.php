<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule;

use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Schedules\UserRoleSchedule\GetScheduledDays\GetScheduledDaysDurationOnly;
use Element\ElementTime\Domains\Tenant\Leaves\Enums\LeaveRequestPreAvailabilityErrorType\LeaveRequestPreAvailabilityErrorType;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestDayPreAvailability;
use Element\ElementTime\Domains\Tenant\Leaves\Structures\StructLeaveRequestPreAvailability;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedule;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledDayDurationOnly;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeType\ModelActionTypeType;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelActionTypeValidationFlag\ModelActionTypeValidationFlagLeave;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Illuminate\Support\Collection;

class CalculateLeavePreAvailabilityFromScheduleDurationOnly extends BaseCalculateLeavePreAvailabilityFromSchedule
{
    /** @throws \Throwable */
    public function handle(
        UserLeaveBankType $userLeaveBankType,
        UserRoleSchedule $userRoleSchedule,
        CarbonInterface $startDate,
        CarbonInterface $endDate,
        TimeDuration $dailyDuration,
        LeaveRequest|null $replaced = null,
        User|null $actorUser = null,
    ): StructLeaveRequestPreAvailability
    {
        $this->validateUserLeaveBankType($userLeaveBankType);

        if (!$userRoleSchedule->type->is(UserRoleScheduleType::DurationOnly)) {
            throw new \InvalidArgumentException('This schedule is not a duration only');
        }

        $this->checkUserRoleScheduleAvailability($userRoleSchedule, $startDate, $endDate);

        if (is_null($actorUser)) {
            $actorUser = $userLeaveBankType->bank->user;
        }

        $scheduledDaysData = GetScheduledDaysDurationOnly::make()->handle($userRoleSchedule, $startDate, $endDate);
        $holidays = $this->getHolidays($userRoleSchedule, $userLeaveBankType, $startDate, $endDate);

        $relevantDaysData = $this->getRelevantDaysData(
            $scheduledDaysData,
            $holidays,
            $startDate,
            $endDate,
        );

        $ret = StructLeaveRequestPreAvailability::make();

        if (count($relevantDaysData) < 1) {
            $ret->canBeRequested = false;
            $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::NotScheduled, 'Not scheduled to work on selected period');

            return $ret;
        }

        $period = $this->getPeriod($relevantDaysData);
        $doesHaveDayTimeAdjustments = !$startDate->isSameDay($period->start) || !$endDate->isSameDay($period->end);

        $ret->scheduleType = $userRoleSchedule->type;
        $ret->canBeRequested = true;
        $ret->warningMessages = [];

        $ret->userLeaveBankType_id = $userLeaveBankType->id;
        $ret->userRoleSchedule_id = $userRoleSchedule->id;
        $ret->hasDays = true;

        $ret->startDate = $period->start;
        $ret->endDate = $period->end;

        $ret->days = $this->makeDays(
            $userLeaveBankType,
            $relevantDaysData,

            $dailyDuration,
            $duration,
            $durationAdjusted,
        );

        $ret->duration = $duration;
        $ret->durationAdjusted = $durationAdjusted;
        $ret->doesHaveFteAdjustments = $duration->neq($durationAdjusted);

        $doesExceedMaximumDuration = $this->doesExceedMaximumDuration($ret, $userRoleSchedule, $scheduledDaysData, $replaced);

        if ($doesExceedMaximumDuration) {
            $ret->canBeRequested = false;
            $ret->addErrorMessage(LeaveRequestPreAvailabilityErrorType::OverMaximumAllowed, 'Leave request duration is over the maximum hours allowed');
        }

        $this->calculateBalances($userLeaveBankType, $replaced, $ret);
        $this->calculateNegativeBalancesAvailability($userLeaveBankType, $actorUser, $ret);

        $model = new LeaveRequest;
        $model->scheduleType = $userRoleSchedule->type;
        $model->actor_id = $actorUser->id;
        $model->userLeaveBankType_id = $userLeaveBankType->id;
        $model->userRoleSchedule_id = $userRoleSchedule->id;
        $model->startDate = $ret->startDate->copy();
        $model->endDate = $ret->endDate->copy();
        $model->duration = $ret->duration;
        $model->durationAdjusted = $ret->durationAdjusted;
        $ret->doesHaveDayTimeAdjustments = $doesHaveDayTimeAdjustments;

        $submitModelActionFlags = ModelActionTypeType::Leave->getValidationFlags(ModelActionTypeValidationFlagLeave::Submit, $model, [$userLeaveBankType->bank->user], ['days' => $ret->days]);
        $approveModelActionFlags = ModelActionTypeType::Leave->getValidationFlags(ModelActionTypeValidationFlagLeave::Approve, $model, [$userLeaveBankType->bank->user], ['days' => $ret->days]);

        $ret->doesRequireAttachmentsOnSubmit = $submitModelActionFlags->isAttachmentRequired;
        $ret->doesRequireAttachmentsOnApprove = $approveModelActionFlags->isAttachmentRequired;
        $ret->doesRequireCommentsOnSubmit = $submitModelActionFlags->isCommentRequired;
        $ret->doesRequireCommentsOnApprove = $approveModelActionFlags->isCommentRequired;

        return $ret;
    }

    /**
     * @param Collection<StructScheduledDayDurationOnly>|StructScheduledDayDurationOnly[] $scheduledDaysData
     * @return Collection<StructScheduledDayDurationOnly>|StructScheduledDayDurationOnly[]
     */
    protected function getRelevantDaysData(
        Collection|array $scheduledDaysData,
        Collection|array $holidays,
        CarbonInterface $startDate,
        CarbonInterface $endDate
    ): Collection|array
    {
        $ret = Collection::make([]);

        foreach ($scheduledDaysData as $scheduledDaysDatum) {
            if (!$scheduledDaysDatum->isScheduled) {
                continue;
            }

            if ($scheduledDaysDatum->isRdo) {
                continue;
            }

            if (!$scheduledDaysDatum->date->betweenIncluded($startDate , $endDate)) {
                continue;
            }

            $dayHolidays = $holidays->filter(fn (PublicHoliday|Holiday $holiday) => $holiday->date->isSameDay($scheduledDaysDatum->date));
            if ($dayHolidays->contains(fn (PublicHoliday|Holiday $holiday) => $holiday instanceof PublicHoliday || $holiday->isFullDay)) {
                continue;
            }

            $duration = $scheduledDaysDatum->hasFixedHours
                ? $scheduledDaysDatum->duration
                : $scheduledDaysDatum->maxDuration;

            if ($duration->lte(TimeDuration::zero())) {
                continue;
            }

            $ret->push($scheduledDaysDatum);
        }

        return $ret;
    }

    /**
     * @param Collection<StructScheduledDayDurationOnly> $scheduledDaysData
     */
    protected function getPeriod(Collection $scheduledDaysData): CarbonPeriod|null
    {
        return CarbonPeriod::createFromArray([
            $scheduledDaysData->first()->date,
            $scheduledDaysData->last()->date,
        ]);
    }

    protected function makeDays(
        UserLeaveBankType $userLeaveBankType,
        Collection|array $relevantDaysData,

        TimeDuration $dailyDuration,
        TimeDuration|null &$duration = null,
        TimeDuration|null &$durationAdjusted = null,
    ): Collection|array
    {
        $days = [];

        if (is_null($duration)) {
            $duration = TimeDuration::zero();
        }

        if (is_null($durationAdjusted)) {
            $durationAdjusted = TimeDuration::zero();
        }

        foreach ($relevantDaysData as $relevantDayData) {
            $fteRatio = $this->getFteRatio($userLeaveBankType, $relevantDayData);
            $leaveRequestDayStruct = $this->makeDay(
                $fteRatio,
                $relevantDayData,
                $dailyDuration,
            );

            $duration->add($leaveRequestDayStruct->duration);
            $durationAdjusted->add($leaveRequestDayStruct->durationAdjusted);

            $days[] = $leaveRequestDayStruct;
        }

        return $days;
    }

    protected function makeDay(
        float $fteRatio,
        StructScheduledDayDurationOnly $dayData,
        TimeDuration|null $dailyDuration = null,
    ): StructLeaveRequestDayPreAvailability
    {
        $date = $dayData->date->copy();

        $ret = new StructLeaveRequestDayPreAvailability;
        $ret->date = $date;
        $ret->duration = $dailyDuration;
        $ret->durationAdjusted = $dailyDuration->copy()->multiply($fteRatio);

        return $ret;
    }

    protected function getFteRatio(UserLeaveBankType $userLeaveBankType, StructScheduledDayDurationOnly $dayData): float
    {
        if (!$dayData->isFte) {
            return 1;
        }

        if (!$userLeaveBankType->doesApplyFteAdjustmentsOnFullDayLeave) {
            return 1;
        }

        return $dayData->fteRatio;
    }

    /**
     * @param Collection<StructScheduledDayDurationOnly>|StructScheduledDayDurationOnly[] $scheduledDaysData
     */
    protected function doesExceedMaximumDuration(StructLeaveRequestPreAvailability $availability, UserRoleSchedule $userRoleSchedule, Collection|array $scheduledDaysData, LeaveRequest|null $replaced = null): bool
    {
        $constraints = [
            ['LeaveRequest.userRoleSchedule_id', '=', $userRoleSchedule->id],
            ['LeaveRequestDay.date', '>=', $availability->startDate->toDateTimeString()],
            ['LeaveRequestDay.date', '<=', $availability->endDate->toDateTimeString()],
            ['IN', 'Workflow.status', [
                NewType::ID,
                SubmittedType::ID,
                PartiallyApprovedType::ID,
                ApprovedType::ID,
            ]],
        ];

        if (!is_null($replaced)) {
            $constraints[] = ['LeaveRequestDay.leaveRequest_id', '<>', $replaced->id];
        }

        $leaveRequestDays = LeaveRequestDay::q()
            ->constraints($constraints)
            ->many();

        $works = TimeSheetWork::q()
            ->constraints([
                ['UserRoleTimeType.userRole_id', '=', $userRoleSchedule->userRole_id],
                ['TimeSheetDay.date', '>=', $availability->startDate->toDateTimeString()],
                ['TimeSheetDay.date', '<=', $availability->endDate->toDateTimeString()],
            ])
            ->relations(['timeSheetDay'])
            ->many();

        if (count($leaveRequestDays) < 1 && count($works) < 1) {
            return false;
        }

        /// TODO: Validate against maximum on the period

        foreach ($availability->days as $day) {
            /** @var StructScheduledDayDurationOnly $scheduledDayData */
            $scheduledDayData = $scheduledDaysData->where('date', '=', $day->date)->first();
            $maxDuration = $scheduledDayData->hasFixedHours
                ? $scheduledDayData->duration
                : $scheduledDayData->maxDuration;

            $totalDurationOnDay = $day->duration->copy();
            $totalDurationOnDay = $leaveRequestDays
                ->where(fn (LeaveRequestDay $leaveRequestDay) => $leaveRequestDay->date->isSameDay($day->date))
                ->reduce(fn(TimeDuration $totalDuration, LeaveRequestDay $day) => $totalDuration->add($day->duration), $totalDurationOnDay);

            if ($totalDurationOnDay->gt($maxDuration)) {
                return true;
            }

            $totalDurationOnDay = $works
                ->where(fn (TimeSheetWork $work) => $work->timeSheetDay->date->isSameDay($day->date))
                ->reduce(fn (TimeDuration $totalDuration, TimeSheetWork $work) => $totalDuration->add($work->workDuration), $totalDurationOnDay);

            if ($totalDurationOnDay->gt(24, 'hours')) {
                return true;
            }
        }

        return false;
    }
}
