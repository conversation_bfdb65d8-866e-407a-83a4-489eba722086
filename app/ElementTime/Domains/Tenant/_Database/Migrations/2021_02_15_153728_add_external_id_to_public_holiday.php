<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN name VARCHAR(255) NULL DEFAULT NULL AFTER calendarDate_id;');
        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN date DATE NULL DEFAULT NULL AFTER name;');
        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN status CHAR(1) NULL DEFAULT NULL AFTER date;');
        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN created_at TIMESTAMP NULL DEFAULT NULL AFTER status;');
        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN updated_at TIMESTAMP NULL DEFAULT NULL AFTER created_at;');
        DB::statement('ALTER TABLE PublicHoliday MODIFY COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL AFTER updated_at;');

        Schema::table('EmployeeType', function (Blueprint $table) {
            $table->string('publicHolidayExternalId')->nullable()->after('externalId');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('EmployeeType', 'publicHolidayExternalId')) {
            Schema::table('EmployeeType', function (Blueprint $table) {
                $table->dropColumn('publicHolidayExternalId');
            });
        }
    }
};
