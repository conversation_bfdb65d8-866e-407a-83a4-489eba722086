<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        if (DB::select(DB::raw('SHOW KEYS FROM EdrmsIntegratedFile WHERE Key_name=\'edrmsif_file_uq\'')->getValue(DB::getQueryGrammar()))) {
            Schema::table('EdrmsIntegratedFile', function (Blueprint $table) {
                $table->dropForeign('edrmsif_edrmsdt_fn');
                $table->dropUnique('edrmsif_file_uq');
            });

            Schema::table('EdrmsIntegratedFile', function (Blueprint $table) {
                $table->foreign('documentType_id', 'edrmsif_edrmsdt_fn')->references('id')->on('EdrmsIntegratedDocumentType')->onUpdate('cascade')->onDelete('restrict');
            });
        }

        //region Table: LeaveRequest
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE LeaveRequest MODIFY COLUMN isScheduled TINYINT(1) NOT NULL DEFAULT \'0\' AFTER doesHaveFTECalculation');

        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->boolean('isDoingEdrmsIntegrationLeavePdfFile')->default(false)->after('externalCalendarId');
            $table->boolean('hasEdrmsIntegrationDoneLeavePdfFile')->default(false)->after('isDoingEdrmsIntegrationLeavePdfFile');
            $table->dateTime('dateTimeEdrmsIntegrationDoneLeavePdfFile')->nullable()->after('hasEdrmsIntegrationDoneLeavePdfFile');
        });
        //endregion Table: LeaveRequest

        //region Table: PayRunItem
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRunItem MODIFY COLUMN areLeaveBalancesAccrued TINYINT(1) NOT NULL DEFAULT \'0\' AFTER paidData');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRunItem MODIFY COLUMN isDoingEdrmsIntegration TINYINT(1) NOT NULL DEFAULT \'0\' AFTER areLeaveBalancesAccrued');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRunItem MODIFY COLUMN hasEdrmsIntegrationDone TINYINT(1) NOT NULL DEFAULT \'0\' AFTER isDoingEdrmsIntegration');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRunItem MODIFY COLUMN dateTimeEdrmsIntegrationDone DATETIME DEFAULT NULL AFTER hasEdrmsIntegrationDone');

        Schema::table('PayRunItem', function (Blueprint $table) {
            $table->boolean('isDoingEdrmsIntegrationLeavePdfFile')->default(false)->after('dateTimeEdrmsIntegrationDone');
            $table->boolean('hasEdrmsIntegrationDoneLeavePdfFile')->default(false)->after('isDoingEdrmsIntegrationLeavePdfFile');
            $table->dateTime('dateTimeEdrmsIntegrationDoneLeavePdfFile')->nullable()->after('hasEdrmsIntegrationDoneLeavePdfFile');
        });
        //endregion Table: PayRunItem

        //region Table: PayRun
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN status CHAR(1) DEFAULT NULL AFTER id');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN isClosed TINYINT(1) NOT NULL DEFAULT \'0\' AFTER status');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN closingDate DATE DEFAULT NULL AFTER dueDate');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN processingDateTime DATETIME DEFAULT NULL AFTER closingDate');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN payingDate DATE DEFAULT NULL AFTER processingDateTime');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN canSubmitAnyTime TINYINT(1) NOT NULL DEFAULT \'1\' AFTER approvalWorkflowMap');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN areLeaveBalancesAccruing TINYINT(1) NOT NULL DEFAULT \'0\' AFTER canSubmitAnyTime');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN areLeaveBalancesAccrued TINYINT(1) NOT NULL DEFAULT \'0\' AFTER areLeaveBalancesAccruing');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN isDoingEdrmsIntegration TINYINT(1) NOT NULL DEFAULT \'0\' AFTER areLeaveBalancesAccrued');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN hasEdrmsIntegrationDone TINYINT(1) NOT NULL DEFAULT \'0\' AFTER isDoingEdrmsIntegration');
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE PayRun MODIFY COLUMN dateTimeEdrmsIntegrationDone DATETIME DEFAULT NULL AFTER hasEdrmsIntegrationDone');

        Schema::table('PayRun', function (Blueprint $table) {
            $table->boolean('isDoingEdrmsIntegrationLeavePdfFile')->default(false)->after('dateTimeEdrmsIntegrationDone');
            $table->boolean('hasEdrmsIntegrationDoneLeavePdfFile')->default(false)->after('isDoingEdrmsIntegrationLeavePdfFile');
            $table->dateTime('dateTimeEdrmsIntegrationDoneLeavePdfFile')->nullable()->after('hasEdrmsIntegrationDoneLeavePdfFile');
        });
        //endregion Table: PayRun

        //region Table: LeaveRequestAttachmentFile
        Schema::table('LeaveRequestAttachmentFile', function (Blueprint $table) {
            $table->boolean('isDoingEdrmsIntegrationLeavePdfFile')->default(false)->after('leaveRequest_id');
            $table->boolean('hasEdrmsIntegrationDoneLeavePdfFile')->default(false)->after('isDoingEdrmsIntegrationLeavePdfFile');
            $table->dateTime('dateTimeEdrmsIntegrationDoneLeavePdfFile')->nullable()->after('hasEdrmsIntegrationDoneLeavePdfFile');
        });
        //endregion Table: LeaveRequestAttachmentFile
    }

    public function down(): void
    {
        //region Table: LeaveRequestAttachmentFile
        if (Schema::hasColumn('LeaveRequestAttachmentFile', 'dateTimeEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('LeaveRequestAttachmentFile', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('LeaveRequestAttachmentFile', 'hasEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('LeaveRequestAttachmentFile', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('LeaveRequestAttachmentFile', 'isDoingEdrmsIntegrationLeavePdfFile')) {
            Schema::table('LeaveRequestAttachmentFile', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegrationLeavePdfFile');
            });
        }
        //endregion Table: LeaveRequestAttachmentFile

        //region Table: PayRun
        if (Schema::hasColumn('PayRun', 'dateTimeEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('PayRun', 'hasEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('PayRun', 'isDoingEdrmsIntegrationLeavePdfFile')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegrationLeavePdfFile');
            });
        }
        //endregion Table: PayRun

        //region Table: PayRunItem
        if (Schema::hasColumn('PayRunItem', 'dateTimeEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('PayRunItem', 'hasEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('PayRunItem', 'isDoingEdrmsIntegrationLeavePdfFile')) {
            Schema::table('PayRunItem', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegrationLeavePdfFile');
            });
        }
        //endregion Table: PayRunItem

        //region Table: LeaveRequest
        if (Schema::hasColumn('LeaveRequest', 'dateTimeEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('dateTimeEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('LeaveRequest', 'hasEdrmsIntegrationDoneLeavePdfFile')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('hasEdrmsIntegrationDoneLeavePdfFile');
            });
        }
        if (Schema::hasColumn('LeaveRequest', 'isDoingEdrmsIntegrationLeavePdfFile')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('isDoingEdrmsIntegrationLeavePdfFile');
            });
        }
        //endregion Table: LeaveRequest
    }
};
