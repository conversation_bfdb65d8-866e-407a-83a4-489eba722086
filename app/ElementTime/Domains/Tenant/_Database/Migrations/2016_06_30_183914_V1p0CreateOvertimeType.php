<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('OvertimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Details
            $table->string('name')->nullable();
            $table->char('applicationRate', 1)->nullable();
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();
            $table->char('type', 1)->nullable();
            $table->float('amount')->nullable();

            // Codes
            $table->bigInteger('project_id')->unsigned()->nullable();
            $table->string('accountCode')->nullable();

            // Rules
            $table->string('ruleDescription')->nullable();
            $table->char('conditionType', 1)->nullable();
            $table->integer('conditionNumber')->nullable();
            $table->char('conditionPeriod', 1)->nullable();
            $table->boolean('appliesOnNotRegionalPublicHolidays')->nullable();
            $table->boolean('appliesOnRegionalPublicHolidays')->nullable();
            $table->boolean('appliesOnOrganisationalPublicHolidays')->nullable();
            $table->boolean('appliesOnSaturdays')->nullable();
            $table->boolean('appliesOnSundays')->nullable();
            $table->boolean('appliesOnHours')->nullable();
            $table->float('appliesOnHoursTimeFrom')->nullable();
            $table->float('appliesOnHoursTimeTo')->nullable();
            $table->char('ifTriggered', 1)->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('incrementRate_id', 'overtime_ir_fn')->references('id')->on('IncrementRate')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('project_id', 'overtime_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::drop('OvertimeType');
    }
};
