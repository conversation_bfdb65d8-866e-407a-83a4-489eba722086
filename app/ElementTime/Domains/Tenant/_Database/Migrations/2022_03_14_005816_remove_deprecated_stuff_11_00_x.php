<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::dropIfExists('TimeSheetPenaltyAmendment');
        Schema::dropIfExists('TimeSheetPenaltyTrigger');
        Schema::dropIfExists('TimeSheetPenalty');

        if (Schema::hasColumn('TimeSheet', 'status')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('status');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursToil')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursToil');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'hoursToilCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursToilCalculation');
            });
        }

        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursToil')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursToil');
            });
        }

        if (Schema::hasColumn('PayRun', 'approvalWorkflowMap')) {
            Schema::table('PayRun', function (Blueprint $table) {
                $table->dropColumn('approvalWorkflowMap');
            });
        }

        if (Schema::hasColumn('PayRunType', 'approvalWorkflowMap')) {
            Schema::table('PayRunType', function (Blueprint $table) {
                $table->dropColumn('approvalWorkflowMap');
            });
        }

        try {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropForeign('pnlty_prjct_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('PenaltyType', 'project_id')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('project_id');
            });
        }

        try {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropForeign('pnlty_prjcta_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('PenaltyType', 'projectActivity_id')) {
            Schema::table('PenaltyType', function (Blueprint $table) {
                $table->dropColumn('projectActivity_id');
            });
        }

        try {
            Schema::table('User', function (Blueprint $table) {
                $table->dropForeign('manager_user_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('User', 'manager_id')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('manager_id');
            });
        }
    }

    public function down(): void
    {
        //
    }
};
