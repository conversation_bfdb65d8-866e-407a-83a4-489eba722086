<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: ReportFileRequest

        // Truncate table just in case - don't worry, data in this table was never used before this version
        \Illuminate\Support\Facades\DB::table('ReportFileRequest')->truncate();

        // Remove fields that won't be necessary
        if (Schema::hasColumn('ReportFileRequest', 'wasNotified')) {
            Schema::table('ReportFileRequest', function (Blueprint $table) {
                $table->dropColumn('wasNotified');
            });
        }
        if (Schema::hasColumn('ReportFileRequest', 'wasDelivered')) {
            Schema::table('ReportFileRequest', function (Blueprint $table) {
                $table->dropColumn('wasDelivered');
            });
        }

        // Update table structure
        Schema::table('ReportFileRequest', function (Blueprint $table) {
            // New fields
            $table->unsignedBigInteger('reportType_id')->after('id'); // From Admin database
            $table->string('slug')->nullable()->after('reportType_id');
            $table->string('downloadFileName')->nullable()->after('slug');

            // Changes
            $table->unsignedBigInteger('reportFile_id')->nullable()->change();
        });

        //endregion Table: ReportFileRequest
    }

    public function down(): void
    {
        //region Table: ReportFileRequest

        if (Schema::hasColumn('ReportFileRequest', 'reportType_id')) {
            Schema::table('ReportFileRequest', function (Blueprint $table) {
                $table->dropColumn('reportType_id');
            });
        }
        if (Schema::hasColumn('ReportFileRequest', 'slug')) {
            Schema::table('ReportFileRequest', function (Blueprint $table) {
                $table->dropColumn('slug');
            });
        }

        //endregion Table: ReportFileRequest
    }
};
