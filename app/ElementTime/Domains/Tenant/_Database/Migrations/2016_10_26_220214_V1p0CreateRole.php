<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('Role', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('slug')->nullable();
            $table->string('externalId')->nullable();
            $table->string('relElementStaff_Id')->nullable();

            $table->string('name')->nullable();
            $table->string('reference')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('User_Role', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('role_id')->unsigned();
            $table->dateTime('startDateTime');
            $table->dateTime('endDateTime')->nullable();

            $table->primary(['user_id', 'role_id', 'startDateTime']);

            $table->foreign('user_id', 'user_role_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('role_id', 'user_role_role_fn')->references('id')->on('Role')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserShift', function (Blueprint $table) {
            $table->bigInteger('role_id')->unsigned()->nullable();

            $table->foreign('role_id', 'user_shift_role_fn')->references('id')->on('Role')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('Role');
        Schema::drop('User_Role');
        Schema::table('UserShift', function (Blueprint $table) {
            $table->dropForeign([
                'user_shift_role_fn',
            ]);
            $table->dropColumn([
                'role_id',
            ]);
        });
    }
};
