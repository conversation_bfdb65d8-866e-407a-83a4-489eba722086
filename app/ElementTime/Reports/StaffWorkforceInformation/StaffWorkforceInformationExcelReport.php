<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\StaffWorkforceInformation;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\InvalidStatusException;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Users\Models\UserEntitlement;
use Element\ElementTime\Domains\Tenant\Users\Models\UserManager;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRole;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleDepartment;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleManager;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleMaster;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRolePayType;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class StaffWorkforceInformationExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
    ];

    const TIME = '~1 min';
    const REPORT_SLUG = 'staff-workforce-information';
    const TYPE_SLUG = 'staff-workforce-information.xls';

    public $c = StaffWorkforceInformationReportConfig::class;

    protected ?PayRun $payRun;

    protected function setData(array $info): void
    {
        $this->payRun = null;

        if (!isset($info['id']) || !is_numeric($info['id'])) {
            throw new InvalidArgumentException('Pay run ID is missing');
        }

        $this->payRun = PayRun::q(
            constraints: [
                ['PayRun.id', '=', $info['id']],
            ],
        )->firstOrFail();
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$doesCache || !$this->payRun->isFinished()) {
            $this->reportValidity = Carbon::now();
        }

        return $this;
    }

    protected function collectData(): array|Collection
    {
        $userRoles = UserRole::q(
            constraints: [
                ['User.startDate', '<=', $this->payRun->endDate],
                ['UserRole.startDate', '<=', $this->payRun->endDate, 'AND'],
                [
                    [
                        ['IS NULL','UserRole.endDate', 'OR'],
                        ['UserRole.endDate', '=', '0000-00-00', 'OR'],
                        ['UserRole.endDate', '>=', $this->payRun->startDate, 'OR'],
                    ],
                ],
            ],
            relations: [
                'user.employeeType',
                'user.userManagers',
                'user.entitlements.entitlementType',
                'user.userManagers.manager',

                'userRoleDepartments.department',
                'userRoleMasters',
                'userRoleManagers.manager',

                'userRolePayTypes.payType.payBand',
                'role',
            ],
            order: 'CAST(User.externalId AS SIGNED) ASC, Role.name ASC',
        )->many();

        return $userRoles;
    }

    /**
     * @throws InvalidStatusException
     */
    protected function buildReportData(): void
    {
        $userRoles = $this->collectData();

        $this->fetchSheetSettings($sheet);
        $this->fetchSheetTitle($sheet);
        $this->fetchSheetTotals($sheet, $userRoles);
        $this->fetchSheetData($sheet, $userRoles);

        $this->sheets = [
            $sheet,
        ];
    }

    /**
     * @param Collection<UserRoleMaster|UserRolePayType|UserManager|UserRoleManager|UserRoleDepartment> $models
     */
    private function getActiveModelOnPayRun(Collection $models): UserRoleMaster|UserRolePayType|UserManager|UserRoleManager|UserRoleDepartment|null
    {
        return $models->filter(function (UserRoleMaster|UserRolePayType|UserManager|UserRoleManager|UserRoleDepartment $model) {
            return $model->availability($this->payRun->startDate, $this->payRun->endDate)->isCurrent(true);
        })->first();
    }

    protected function fetchSheetSettings(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $sheet->label = 'Staff workforce information';
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    protected function fetchSheetTitle(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = 'Staff workforce information';
        if ($this->payRun) {
            $title->title .= ' ' . $this->payRun->payRunType->name . ' #' . $this->payRun->number . ' (' . $this->payRun->startDate->format($this->dateFormat) . ' - ' . $this->payRun->endDate->format($this->dateFormat) . ')';
        }
        $sheet->data[] = $title;
    }

    /**
     * @param array|Collection<UserRole>|UserRole[] $userRoles
     */
    protected function fetchSheetTotals(\stdClass &$sheet = null, array|Collection $userRoles = []): void
    {
        $this->validateSheet($sheet);
        $user_ids = $userRoles->map(fn (UserRole $userRole) => $userRole->user_id);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Date/Time', 'text', 'center', 3),
            $this->createColumn('Selected pay-run', 'text', 'center', 3),
            $this->createColumn('Total number of staff', 'integer', 'center', 3),
        ];
        $now = Carbon::now();
        $data->data = [[
            'config' => [],
            'data' => [
                $now->format($this->dateFormat) . ' at ' . $now->format($this->timeFormat),
                $this->payRun->number . ' - ' . $this->payRun->startDate->format($this->dateFormat) . ' to ' . $this->payRun->endDate->format($this->dateFormat),
                $user_ids->unique()->count(),
            ],
        ]];
        $sheet->data[] = $data;
    }

    /**
     * @throws InvalidStatusException
     */
    protected function fetchSheetData(\stdClass &$sheet = null, $userRoles = []): void
    {
        $this->validateSheet($sheet);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->columns = $this->getSheetDataHeader();
        $table->data = $this->getSheetDataData($userRoles);
        $sheet->data[] = $table;
    }

    protected function getSheetDataHeader(): array
    {
        return [
            $this->createColumn('#'),
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Last name'),
            $this->createColumn('First name'),
            $this->createColumn('Email'),
            $this->createColumn('Payroll email'),
            $this->createColumn('Current account status'),
            $this->createColumn('Employee type'),
            $this->createColumn('System access'),
            $this->createColumn('Department'),

            $this->createColumn('Role'),
            $this->createColumn('Master role'),
            $this->createColumn('Role start date'),
            $this->createColumn('Role manager'),

            $this->createColumn('Role Pay-type'),
            $this->createColumn('Role Pay-code'),
            $this->createColumn('Pay-type start date'),

            $this->createColumn('Organisation start date'),
            $this->createColumn('Length of service'),
            $this->createColumn('Start date of permanent employment'),
            $this->createColumn('Start date government employment'),
            $this->createColumn('Birthday'),
            $this->createColumn('Age'),

            $this->createColumn('Gender'),
            $this->createColumn('Highest qualification'),
            $this->createColumn('Ethnicity'),
            $this->createColumn('Citizenship status'),
            $this->createColumn('Entitlements'),
        ];
    }

    /**
     * @throws InvalidStatusException
     */
    protected function getSheetDataData(array|Collection $userRoles): array
    {
        $tableData = [];

        foreach ($userRoles as $userRole) {
            $tableData[] = [
                'config' => [],
                'data' => $this->getUserRoleRowData($userRole),
            ];
        }

        return $tableData;
    }

    /**
     * @throws InvalidStatusException
     */
    protected function getUserRoleRowData(UserRole $userRole): array
    {
        $today = Carbon::now();

        $user = $userRole->user;

        return array_merge(
            [
                $user->id,
                $user->externalId,
                $user->nameLast,
                $user->nameFirst,
                $user->getUsedEmail(),
                $user->payrollEmail,
                $user->statusClass::NAME,
                $user->employeeTypeName,
                $user->access->getSystemAccessFlags()->map(fn ($flag) => $flag::NAME)->join($this->br),
                $this->getDepartmentName($userRole),
            ],
            $this->getStaffUserRoleData($userRole),
            [
                $user->startDate->format($this->dateFormat),
                $user->startDate->copy()->setYear($today->year)->gt($today)
                    ? $user->startDate->diffInYears($today) . ' years ' . $user->startDate->copy()->setYear($today->year - 1)->diffInDays($today) . ' days'
                    : $user->startDate->diffInYears($today) . ' years ' . $user->startDate->copy()->setYear($today->year)->diffInDays($today) . ' days',
                !is_null($user->startDatePermanent)
                    ? $user->startDatePermanent->format($this->dateFormat)
                    : '',
                !is_null($user->startDateGovernment)
                    ? $user->startDateGovernment->format($this->dateFormat)
                    : '',
                !is_null($user->birthday)
                    ? $user->birthday->format($this->dateFormat)
                    : '',
                !is_null($user->birthday)
                    ? $user->birthday->diffInYears($today)
                    : '',
            ],
            $this->getWorkforceData($user),
        );
    }

    protected function getDepartmentName(UserRole $userRole): string|null
    {
        /** @var UserRoleDepartment $userRoleDepartment */
        $userRoleDepartment = $this->getActiveModelOnPayRun($userRole->userRoleDepartments);

        if (!is_null($userRoleDepartment)) {
            return $userRoleDepartment->department->name;
        }

        return '-';
    }

    protected function getStaffUserRoleData(UserRole $userRole): array
    {
        $manager = null;

        /** @var UserRoleManager $userRoleManager */
        $userRoleManager = $this->getActiveModelOnPayRun($userRole->userRoleManagers);

        if (!is_null($userRoleManager)) {
            $manager = $userRoleManager->manager;
        }

        if (is_null($manager)) {
            /** @var UserManager $userManager */
            $userManager = $this->getActiveModelOnPayRun($userRole->user->userManagers);

            if (!is_null($userManager)) {
                $manager = $userManager->manager;
            }
        }

        /** @var UserRolePayType $userRolePayType */
        $userRolePayType = $this->getActiveModelOnPayRun($userRole->userRolePayTypes);

        /** @var UserRoleMaster $userRoleMaster */
        $userRoleMaster = $this->getActiveModelOnPayRun($userRole->userRoleMasters);

        return array_merge(
            [
                $userRole->role->name,
                is_null($userRoleMaster)
                    ? 'No'
                    : 'Yes',
                $userRole->startDate->format($this->dateFormat),
                !is_null($manager)
                    ? $manager->fullName
                    : '',
            ],
            $this->getStaffUserRolePayData($userRolePayType)
        );
    }

    protected function getStaffUserRolePayData(UserRolePayType|null $userRolePayType): array
    {
        if (is_null($userRolePayType)) {
            return [
                '',
                '',
                '',
            ];
        }

        return [
            $userRolePayType->payTypeName,
            $userRolePayType->payTypeExternalId,
            $userRolePayType->startDate,
        ];
    }

    /**
     * @throws \Element\Core\Exceptions\InvalidStatusException
     */
    protected function getWorkforceData(User $user): array
    {
        $genderObject = $user->getOptionObject('gender');
        $qualificationOption = $user->getOptionObject('qualification');
        $ethnicityOption = $user->getOptionObject('ethnicity');
        $citizenshipTypeOption = $user->getOptionObject('citizenshipType');

        $entitlements = $user->entitlements
            ->filter(fn (UserEntitlement $userEntitlement) => $userEntitlement->availability($this->payRun->startDate, $this->payRun->endDate)->isCurrent(true))
            ->map(fn (UserEntitlement $userEntitlement) => $userEntitlement->entitlementType->name)
            ->join($this->br);

        return [
            !is_null($genderObject) ? $genderObject['name'] : '',
            !is_null($qualificationOption) ? $qualificationOption['name'] : '',
            !is_null($ethnicityOption) ? $ethnicityOption['name'] : '',
            !is_null($citizenshipTypeOption) ? $citizenshipTypeOption['name'] : '',
            $entitlements,
        ];
    }
}
