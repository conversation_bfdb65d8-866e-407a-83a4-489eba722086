<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRun\ScheduledPayRun;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;

class SetLeaveRequestRelationshipsFromPayRun extends BaseSetLeaveRequestRelationships
{
    /** @throws \Throwable */
    public function handle(PayRun $payRun): void
    {
        if ($payRun->isFinished()) {
            return;
        }

        $allLeaveRequests = LeaveRequest::q()
            ->constraints([
                ['Workflow.status', '=', ApprovedType::ID],
                ['LeaveRequest.startDate', '>=', $payRun->startDate->toDateString()],
                ['LeaveRequest.endDate', '<=', $payRun->endDate->toDateString()],
            ])
            ->relations(static::getLeaveRequestRelationships())
            ->many();

        if ($allLeaveRequests->count() < 1) {
            return;
        }

        $scheduledPayRuns = ScheduledPayRun::q()
            ->constraints([
                ['ScheduledPayRun.startDate', '=', $payRun->startDate],
                ['ScheduledPayRun.endDate', '=', $payRun->endDate],
                ['LeaveRequest.startDate', '>=', $payRun->startDate],
                ['LeaveRequest.startDate', '<=', $payRun->endDate],
            ])
            ->joins([
                ['LeaveRequest', 'LeaveRequest.userRoleSchedule_id', '=', 'ScheduledPayRun.userRoleSchedule_id', 'INNER']
            ])
            ->relations(static::getScheduledPayRunRelationships())
            ->many();

        if ($scheduledPayRuns->count() < 1) {
            return;
        }

        foreach ($allLeaveRequests as $leaveRequest) {
            $scheduledPayRun = $scheduledPayRuns
                ->where('userRoleSchedule_id', '=', $leaveRequest->userRoleSchedule_id)
                ->where('startDate', '<=', $leaveRequest->startDate)
                ->where('endDate', '>=', $leaveRequest->startDate)
                ->first();

            if (is_null($scheduledPayRun)) {
                continue;
            }

            $timeSheet = $scheduledPayRun->timeSheet ?? TimeSheet::getByDate($leaveRequest->userLeaveBankType->bank->user, $leaveRequest->startDate);

            $this->setLeaveRequestRelationships($leaveRequest, $scheduledPayRun, $timeSheet);
        }
    }

    /** @throws \Throwable */
    public function asJob(PayRun $payRun): void
    {
        $this->handle($payRun);
    }
}
