elt-loading(ng-if='!!$ctrl.isLoading')
div(ng-if='!$ctrl.isLoading')
  .panel.panel-default.m0
    .panel-heading.modal-topbar.pt0.pb0(ng-include='app.baseLayoutPath + "/modals/time-sheet-complete-missing-hours-with-leave/heading.html"')

    .panel-body.pt0
      .row
        hr.hr.m0.p0
      .row.pt-lg.pb-lg
        .col-xs-12
          h3.h3.text-center.m0.p0 Missing hours

      .row(ng-if='!!$ctrl.form.settings.issues && $ctrl.form.settings.issues.length > 0')
        .col-xs-12.pv-lg
          div(ng-include="app.baseLayoutPath + '/time-sheets/_common/issues-list.html'" ng-init='$_issuesList = {issues: $ctrl.form.settings.issues, solve: null, color: "danger"};')

      .row(ng-if='!!$ctrl.form.settings.canAutoCompleteMissingHours')
        .col-xs-12
          .row
            hr.hr

          .row
            .col-xs-12.text-center
              .lead.full-width.pl-xl.pr-xl
                p This will complete any missing hours in the timesheet with leave requests including any daily missing hours.
                p Missing period hours may be spread over multiple requests.

          .row
            .col-xs-12
              div(ng-include='app.baseLayoutPath + "/modals/time-sheet-complete-missing-hours-with-leave/form.html"')


    .panel-footer
      .row
        .col-xs-12.text-center
          button.btn.btn-default(type='button' ng-click='$ctrl.cancel()') Close
