#!/bin/bash
#
#       _                           _  _____ ___ __  __ _____
#   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
#  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
# |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
#  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
#
# @link https://www.elementtime.com
# @copyright 2024 Adroit Creations
# Final Optimized Blue-Green Deployment Script
#

set -euo pipefail

# Global variables for caching and performance
declare -A LB_ARN_CACHE
declare -A TG_ARN_CACHE

# Track background processes for cleanup
declare -a BACKGROUND_PIDS=()

# Cleanup function for signal handling
cleanup_on_exit() {
    local exit_code=$?
    if [[ ${#BACKGROUND_PIDS[@]} -gt 0 ]]; then
        log_info "Cleaning up background processes..."
        for pid in "${BACKGROUND_PIDS[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid" 2>/dev/null || true
            fi
        done
    fi
    exit $exit_code
}

# Set up signal handlers
trap cleanup_on_exit EXIT INT TERM

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly DEPLOY_BG_SCRIPT="${SCRIPT_DIR}/deploy_blue_green_final_optimized.sh"
readonly HEALTH_CHECK_INTERVAL=10  # 10 seconds for faster feedback
readonly HEALTH_CHECK_TIMEOUT=5400  # 90 minutes in seconds
readonly MAX_PARALLEL_JOBS=3

# Logging functions
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $*" >&2
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $*" >&2
}

log_debug() {
    if [[ "${DEBUG:-0}" == "1" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] DEBUG: $*" >&2
    fi
}

function usage() {
    cat <<EOF
Usage:
    -h|--help
    -e|--env                    [ENV: test | stage | prod]
    -s|--sleep                  [SLEEP: sleep seconds before stop old EC2, default: 0s]
    -v|--env-version            [ENV_VERSION: default: 14si]

Examples:
    ./deploy_final_optimized.sh -e test -s 5
    ./deploy_final_optimized.sh -e stage -v 14si
    ./deploy_final_optimized.sh -e prod
EOF
}

# Validate required tools
validate_dependencies() {
    local missing_tools=()
    
    for tool in aws jq; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
}

# Validate AWS credentials
validate_aws_credentials() {
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured or invalid"
        exit 1
    fi
}

# Parse command line arguments
parse_arguments() {
    if [[ $# -lt 2 ]]; then
        usage
        exit 1
    fi

    local POSITIONAL=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -e|--env)
                env="$2"
                shift 2
                ;;
            -s|--sleep)
                sleep="$2"
                shift 2
                ;;
            -v|--env-version)
                env_version="$2"
                shift 2
                ;;
            *)
                POSITIONAL+=("$1")
                shift
                ;;
        esac
    done

    set -- "${POSITIONAL[@]}"

    # Validate required parameters
    if [[ -z "${env:-}" ]]; then
        log_error "Environment parameter (-e|--env) is required"
        usage
        exit 1
    fi

    if [[ ! "$env" =~ ^(test|stage|prod)$ ]]; then
        log_error "Invalid environment: $env. Must be test, stage, or prod"
        exit 1
    fi
}

# Initialize global variables
initialize_variables() {
    readonly TARGET="${env}"
    readonly SLEEP="${sleep:-0}"
    readonly ENV_VERSION="${env_version:-14si}"
    readonly SLEEP_BEFORE_SCALE_DOWN="$SLEEP"

    log_info "Deployment configuration:"
    log_info "  Environment: $TARGET"
    log_info "  Version: $ENV_VERSION"
    log_info "  Sleep before scale down: ${SLEEP_BEFORE_SCALE_DOWN}s"
}

# Generate resource names
generate_resource_names() {
    # Socket resources
    readonly asg_skt_b="asg-etime-${TARGET}-skt-b-${ENV_VERSION}"
    readonly asg_skt_g="asg-etime-${TARGET}-skt-g-${ENV_VERSION}"

    # Command resources
    readonly asg_cmd_b="asg-etime-${TARGET}-cmd-b-${ENV_VERSION}"
    readonly asg_cmd_g="asg-etime-${TARGET}-cmd-g-${ENV_VERSION}"

    # Normal resources
    readonly asg_normal_b="asg-etime-${TARGET}-b-${ENV_VERSION}"
    readonly asg_normal_g="asg-etime-${TARGET}-g-${ENV_VERSION}"

    readonly MIN_EC2_HEALTHY_COUNT=1
}

# Optimized AWS functions with error handling
get_lb_arn_by_name() {
    local lb_name=$1
    local cache_key="lb_arn_${lb_name}"
    
    if [[ -n "${LB_ARN_CACHE[$cache_key]:-}" ]]; then
        echo "${LB_ARN_CACHE[$cache_key]}"
        return 0
    fi
    
    local lb_arn
    if ! lb_arn=$(aws elbv2 describe-load-balancers --names "$lb_name" \
        --query 'LoadBalancers[0].LoadBalancerArn' --output text 2>/dev/null); then
        log_error "Failed to get load balancer ARN for: $lb_name"
        return 1
    fi
    
    if [[ "$lb_arn" == "None" || -z "$lb_arn" ]]; then
        log_error "Load balancer not found: $lb_name"
        return 1
    fi
    
    LB_ARN_CACHE[$cache_key]="$lb_arn"
    echo "$lb_arn"
}

# Get current color from traffic weight
get_current_color_from_traffic_weight() {
    local target=$1
    local type=$2
    local env_version=$3
    
    local lb_name
    if [[ "$type" == "skt" || "$type" == "cmd" ]]; then
        lb_name="alb-etime-${target}-${type}-${env_version}"
    elif [[ "$type" == "normal" ]]; then
        lb_name="alb-etime-${target}-${env_version}"
    else
        log_error "Invalid type: $type"
        return 1
    fi
    
    local lb_arn
    if ! lb_arn=$(get_lb_arn_by_name "$lb_name"); then
        return 1
    fi
    
    local current_color
    if ! current_color=$(aws elbv2 describe-listeners --load-balancer-arn "$lb_arn" \
        --query 'Listeners[?Port==`443`].DefaultActions[0].ForwardConfig.TargetGroups[?Weight==`100`].TargetGroupArn' \
        --output text 2>/dev/null | awk -F'/' '{print $(NF-1)}' | awk -F'-' '{print $(NF-1)}'); then
        log_error "Failed to get current color for $type"
        return 1
    fi
    
    echo "$current_color"
}

# Get target group ARN by name pattern
get_tg_arn_by_name() {
    local tg_name=$1
    local cache_key="tg_arn_${tg_name}"

    if [[ -n "${TG_ARN_CACHE[$cache_key]:-}" ]]; then
        echo "${TG_ARN_CACHE[$cache_key]}"
        return 0
    fi

    local tg_arn
    if ! tg_arn=$(aws elbv2 describe-target-groups \
        --query "TargetGroups[?contains(TargetGroupName, '$tg_name')].TargetGroupArn" \
        --output text 2>/dev/null | head -1); then
        log_error "Failed to query target groups for: $tg_name"
        return 1
    fi

    # Check if result is empty or None
    if [[ -z "$tg_arn" || "$tg_arn" == "None" || "$tg_arn" == "null" ]]; then
        log_error "Target group not found: $tg_name"
        return 1
    fi

    # Validate ARN format
    if [[ ! "$tg_arn" =~ ^arn:aws:elasticloadbalancing: ]]; then
        log_error "Invalid target group ARN format for $tg_name: $tg_arn"
        return 1
    fi

    TG_ARN_CACHE[$cache_key]="$tg_arn"
    echo "$tg_arn"
}

# Optimized health check with 10-second intervals and better feedback
check_target_group_health() {
    local type=$1
    local tg_name=$2
    local start_time=$(date +%s)
    local check_count=0

    log_info "Starting health check for $type target group: $tg_name (10s intervals, 90min timeout)"

    while [[ $(($(date +%s) - start_time)) -lt $HEALTH_CHECK_TIMEOUT ]]; do
        check_count=$((check_count + 1))
        local elapsed=$(( $(date +%s) - start_time ))

        local tg_arn
        if ! tg_arn=$(get_tg_arn_by_name "$tg_name"); then
            log_debug "[$type] Check #$check_count (${elapsed}s): Target group not found yet: $tg_name"
            sleep $HEALTH_CHECK_INTERVAL
            continue
        fi

        local healthy_count
        if healthy_count=$(aws elbv2 describe-target-health --target-group-arn "$tg_arn" \
            --query 'length(TargetHealthDescriptions[?TargetHealth.State==`healthy`])' \
            --output text 2>/dev/null); then

            log_debug "[$type] Check #$check_count (${elapsed}s): $healthy_count healthy instances (need $MIN_EC2_HEALTHY_COUNT)"

            if [[ $healthy_count -ge $MIN_EC2_HEALTHY_COUNT ]]; then
                log_info "✓ Health check PASSED for $type after ${elapsed}s ($check_count checks)"
                return 0
            fi

            # Progress indicator every minute
            if [[ $((check_count % 6)) -eq 0 ]]; then
                log_info "[$type] Still waiting for healthy instances... (${elapsed}s elapsed, $healthy_count/$MIN_EC2_HEALTHY_COUNT healthy)"
            fi
        else
            log_debug "[$type] Check #$check_count (${elapsed}s): Failed to query target health"
        fi

        sleep $HEALTH_CHECK_INTERVAL
    done

    local final_elapsed=$(( $(date +%s) - start_time ))
    log_error "✗ Health check TIMEOUT for $type after ${final_elapsed}s ($check_count checks)"
    return 1
}

# ASG operations
set_asg_count() {
    local asg=$1
    local min=$2
    local max=$3
    local desired=$4
    
    log_info "Updating ASG $asg: min=$min, max=$max, desired=$desired"
    
    if ! aws autoscaling update-auto-scaling-group \
        --auto-scaling-group-name "$asg" \
        --min-size "$min" \
        --max-size "$max" \
        --desired-capacity "$desired" 2>/dev/null; then
        log_error "Failed to update ASG: $asg"
        return 1
    fi
    
    log_info "Successfully updated ASG: $asg"
}

get_ec2_count() {
    local asg_name="$1"
    
    local count
    if ! count=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "$asg_name" \
        --query "AutoScalingGroups[0].DesiredCapacity" \
        --output text 2>/dev/null); then
        count=0
    fi
    
    if ! [[ $count =~ ^[0-9]+$ ]]; then
        count=0
    fi
    
    echo "$count"
}

# Check if either ASG is empty
status_check_both_asg_counts() {
    local asg_name_a="$1"
    local asg_name_b="$2"
    
    local count_a count_b
    count_a=$(get_ec2_count "$asg_name_a")
    count_b=$(get_ec2_count "$asg_name_b")
    
    if [[ $count_a -eq 0 || $count_b -eq 0 ]]; then
        echo "True"
    else
        echo "False"
    fi
}

# Get listener ARN
get_lb_listener_443_arn() {
    local target=$1
    local type=$2
    local env_version=$3
    
    local lb_name
    if [[ "$type" == "skt" || "$type" == "cmd" ]]; then
        lb_name="alb-etime-${target}-${type}-${env_version}"
    elif [[ "$type" == "normal" ]]; then
        lb_name="alb-etime-${target}-${env_version}"
    else
        log_error "Invalid type: $type"
        return 1
    fi
    
    local lb_arn listener_arn
    if ! lb_arn=$(get_lb_arn_by_name "$lb_name"); then
        return 1
    fi
    
    if ! listener_arn=$(aws elbv2 describe-listeners --load-balancer-arn "$lb_arn" \
        --query 'Listeners[?Port==`443`].ListenerArn' --output text 2>/dev/null); then
        log_error "Failed to get listener ARN for $type"
        return 1
    fi
    
    echo "$listener_arn"
}

# Modify listener traffic
modify_listener_traffic() {
    local listener_arn=$1
    local current_tg_arn=$2
    local next_tg_arn=$3
    local service_name=$4
    
    log_info "Switching traffic for $service_name"
    
    local action_json
    action_json=$(cat <<EOF
[{
  "Type": "forward",
  "Order": 1,
  "ForwardConfig": {
    "TargetGroups": [
      {
        "TargetGroupArn": "$current_tg_arn",
        "Weight": 0
      },
      {
        "TargetGroupArn": "$next_tg_arn",
        "Weight": 100
      }
    ]
  }
}]
EOF
)
    
    if ! aws elbv2 modify-listener \
        --listener-arn "$listener_arn" \
        --default-actions "$action_json" >/dev/null 2>&1; then
        log_error "Failed to switch traffic for $service_name"
        return 1
    fi
    
    log_info "Successfully switched traffic for $service_name"
}

# Cleanup function for failed deployments
cleanup_failed_deployment() {
    local target=$1
    local next_color=$2
    local env_version=$3

    log_info "Cleaning up failed deployment"

    local cleanup_asgs=(
        "asg-etime-${target}-skt-${next_color}-${env_version}"
        "asg-etime-${target}-${next_color}-${env_version}"
        "asg-etime-${target}-cmd-${next_color}-${env_version}"
    )

    for asg in "${cleanup_asgs[@]}"; do
        log_info "Scaling down ASG: $asg"
        set_asg_count "$asg" 0 0 0 || log_error "Failed to scale down $asg"
    done
}

# Main deployment function
main() {
    local script_start_time=$(date +%s)

    # Parse arguments and validate
    parse_arguments "$@"

    # Initialize
    validate_dependencies
    validate_aws_credentials
    initialize_variables
    generate_resource_names

    log_info "Starting optimized blue-green deployment"

    # Check if deployment script exists
    if [[ ! -f "$DEPLOY_BG_SCRIPT" ]]; then
        log_error "Deploy blue-green script not found: $DEPLOY_BG_SCRIPT"
        exit 1
    fi

    # Start background deployments
    log_info "Starting parallel ASG deployments"
    local bg_pids=()

    # Launch background processes
    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t skt -s "$ENV_VERSION" &
    bg_pids+=($!)
    BACKGROUND_PIDS+=($!)

    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t normal -s "$ENV_VERSION" &
    bg_pids+=($!)
    BACKGROUND_PIDS+=($!)

    "$DEPLOY_BG_SCRIPT" -e "$TARGET" -t cmd -s "$ENV_VERSION" &
    bg_pids+=($!)
    BACKGROUND_PIDS+=($!)

    # Wait for background processes to complete
    log_info "Waiting for ASG deployments to complete"
    for pid in "${bg_pids[@]}"; do
        if ! wait "$pid"; then
            log_error "Background deployment process failed (PID: $pid)"
            exit 1
        fi
    done

    log_info "All ASG deployments completed successfully"

    # Determine current and next colors
    log_info "Determining deployment colors"
    local current_color
    if ! current_color=$(get_current_color_from_traffic_weight "$TARGET" skt "$ENV_VERSION"); then
        log_error "Failed to determine current color"
        exit 1
    fi

    # Handle fresh deployment scenario
    local status_skt status_normal
    status_skt=$(status_check_both_asg_counts "$asg_skt_b" "$asg_skt_g")
    status_normal=$(status_check_both_asg_counts "$asg_normal_b" "$asg_normal_g")

    if [[ "$status_normal" == "True" && "$status_skt" == "True" ]]; then
        log_info "Fresh deployment detected - new instances will deploy to Blue (default)"
        log_info "Setting current color to Green (traffic source) for Blue deployment"
        current_color="g"
    fi

    local next_color
    if [[ "$current_color" == "b" ]]; then
        next_color="g"
    elif [[ "$current_color" == "g" ]]; then
        next_color="b"
    else
        log_error "Invalid current color: $current_color"
        exit 1
    fi

    log_info "Deployment colors: current=$current_color, next=$next_color"

    # Generate target group names
    local tg_current_name_skt="etime-tg-${TARGET}-skt-${current_color}-${ENV_VERSION}"
    local tg_current_name_normal="etime-tg-${TARGET}-${current_color}-${ENV_VERSION}"
    local tg_current_name_cmd="etime-tg-${TARGET}-cmd-${current_color}-${ENV_VERSION}"

    local tg_next_name_skt="etime-tg-${TARGET}-skt-${next_color}-${ENV_VERSION}"
    local tg_next_name_normal="etime-tg-${TARGET}-${next_color}-${ENV_VERSION}"
    local tg_next_name_cmd="etime-tg-${TARGET}-cmd-${next_color}-${ENV_VERSION}"

    # Perform health checks with proper dependency logic
    log_info "Starting health checks for new instances"
    local health_check_start=$(date +%s)

    # CRITICAL: Socket must be healthy first, then normal can proceed
    # This matches the original script's dependency logic
    log_info "Waiting for Socket instances to become healthy (priority dependency)"
    if ! check_target_group_health "skt" "$tg_next_name_skt"; then
        log_error "Socket health check failed"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    log_info "Socket is healthy, now checking Normal instances"
    if ! check_target_group_health "normal" "$tg_next_name_normal"; then
        log_error "Normal health check failed"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    log_info "Both Socket and Normal instances are healthy - ready for traffic switch"
    local health_check_duration=$(( $(date +%s) - health_check_start ))
    log_info "Health checks completed successfully in ${health_check_duration}s"

    # Get target group ARNs with error handling
    log_info "Retrieving target group ARNs"
    local tg_current_arn_skt tg_current_arn_normal tg_current_arn_cmd
    local tg_next_arn_skt tg_next_arn_normal tg_next_arn_cmd

    # Get current target group ARNs
    if ! tg_current_arn_skt=$(get_tg_arn_by_name "$tg_current_name_skt"); then
        log_error "Failed to get current socket target group ARN"
        exit 1
    fi

    if ! tg_current_arn_normal=$(get_tg_arn_by_name "$tg_current_name_normal"); then
        log_error "Failed to get current normal target group ARN"
        exit 1
    fi

    if ! tg_current_arn_cmd=$(get_tg_arn_by_name "$tg_current_name_cmd"); then
        log_error "Failed to get current command target group ARN"
        exit 1
    fi

    # Get next target group ARNs
    if ! tg_next_arn_skt=$(get_tg_arn_by_name "$tg_next_name_skt"); then
        log_error "Failed to get next socket target group ARN"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    if ! tg_next_arn_normal=$(get_tg_arn_by_name "$tg_next_name_normal"); then
        log_error "Failed to get next normal target group ARN"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    if ! tg_next_arn_cmd=$(get_tg_arn_by_name "$tg_next_name_cmd"); then
        log_error "Failed to get next command target group ARN"
        cleanup_failed_deployment "$TARGET" "$next_color" "$ENV_VERSION"
        exit 1
    fi

    # Get listener ARNs with error handling
    log_info "Retrieving load balancer listener ARNs"
    local lb_listener_443_arn_skt lb_listener_443_arn_normal lb_listener_443_arn_cmd

    if ! lb_listener_443_arn_skt=$(get_lb_listener_443_arn "$TARGET" skt "$ENV_VERSION"); then
        log_error "Failed to get socket listener ARN"
        exit 1
    fi

    if ! lb_listener_443_arn_normal=$(get_lb_listener_443_arn "$TARGET" normal "$ENV_VERSION"); then
        log_error "Failed to get normal listener ARN"
        exit 1
    fi

    if ! lb_listener_443_arn_cmd=$(get_lb_listener_443_arn "$TARGET" cmd "$ENV_VERSION"); then
        log_error "Failed to get command listener ARN"
        exit 1
    fi

    # Switch traffic
    log_info "Switching traffic from $current_color to $next_color"

    if ! modify_listener_traffic "$lb_listener_443_arn_skt" "$tg_current_arn_skt" "$tg_next_arn_skt" "Socket"; then
        log_error "Failed to switch Socket traffic"
        exit 1
    fi

    if ! modify_listener_traffic "$lb_listener_443_arn_normal" "$tg_current_arn_normal" "$tg_next_arn_normal" "Normal"; then
        log_error "Failed to switch Normal traffic"
        exit 1
    fi

    if ! modify_listener_traffic "$lb_listener_443_arn_cmd" "$tg_current_arn_cmd" "$tg_next_arn_cmd" "Command"; then
        log_error "Failed to switch Command traffic"
        exit 1
    fi

    log_info "Traffic switching completed successfully"

    # Wait before scaling down old instances
    if [[ $SLEEP_BEFORE_SCALE_DOWN -gt 0 ]]; then
        log_info "Waiting ${SLEEP_BEFORE_SCALE_DOWN}s before scaling down old instances"
        sleep "$SLEEP_BEFORE_SCALE_DOWN"
    fi

    # Scale down old ASGs
    log_info "Scaling down old ASGs (color: $current_color)"
    local old_asgs=(
        "asg-etime-${TARGET}-skt-${current_color}-${ENV_VERSION}"
        "asg-etime-${TARGET}-${current_color}-${ENV_VERSION}"
        "asg-etime-${TARGET}-cmd-${current_color}-${ENV_VERSION}"
    )

    for asg in "${old_asgs[@]}"; do
        log_info "Scaling down ASG: $asg"
        if ! set_asg_count "$asg" 0 0 0; then
            log_error "Failed to scale down $asg"
        fi
    done

    local total_duration=$(( $(date +%s) - script_start_time ))
    log_info "Deployment completed successfully in ${total_duration}s"
    log_info "New active color: $next_color"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
