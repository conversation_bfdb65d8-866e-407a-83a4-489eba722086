<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\AuditTimeSheetsApprovalByPayRunReport;

use Element\ElementTime\Reports\ReportCategoryType;
use Element\ElementTime\Support\Reports\TenantReportConfig;

class AuditTimeSheetsApprovalByPayRunReportConfig extends TenantReportConfig
{
    const SLUG = 'audit-time-sheets-approval-by-payrun-report';
    const CATEGORY = ReportCategoryType::General;
    const NAME = 'Timesheets approval audit report';
    const DESCRIPTION = 'Choose a payrun to view all approved timesheets and details for the payrun period selected.';
    const ROUTE = 'audit-time-sheets-approval-by-payrun-report.{{type}}';
    const FILE_NAME_TEMPLATE = '{{tenant.slug}}__{{slug}}-{{payRun.id}}-{{payRun.status}}-{{department.id}}-{{genUser.id}}-{{genUser.doesIncludeIndirectReports}}.{{fileType}}';
    const DOWNLOAD_FILE_NAME_TEMPLATE = 'Timesheets approval audit report {{payRun.number}} ({{payRun.startDate}} - {{payRun.endDate}}) - {{department.name}} - {{tenant.name}}.{{fileType}}';

    const TYPES = [
        [
            'class' => AuditTimeSheetsApprovalByPayRunExcelReport::class,
            'options' => [
                'order' => 1,
            ],
        ],
    ];

    const FILTERS = [
        self::DEFAULT_FILTER_PAYRUNTYPE,
        self::DEFAULT_FILTER_PAYRUN,
        self::FILTER_DEPARTMENT_WITH_CHILDREN,
    ];

    const PARAMETERS = [
        'id' => 'PayRun.id',
        'idDepartment' => 'Department.id',
        'doesIncludeSubDepartments' => 'DoesIncludeSubDepartments',
    ];
}
