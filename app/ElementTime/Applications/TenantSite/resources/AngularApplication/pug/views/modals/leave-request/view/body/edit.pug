.row
  .col-xs-12
    .form-group(elt-help-text="modals.leave-request|fields.schedule")
      label.label-control Role / schedule
      .lead.m0.bb {{$ctrl.form.data.schedule.label}}
.row
  .col-xs-12
    .form-group(elt-help-text="modals.leave-request|fields.bankType")
      label.label-control Leave type
      .lead.m0.bb {{$ctrl.form.data.bankType.leaveName}}

div(ng-if='!!$ctrl.form.data.bankType && !!$ctrl.form.data.bankType.id' ng-include='app.baseLayoutPath + "/modals/leave-request/_common/form/default.html"')
