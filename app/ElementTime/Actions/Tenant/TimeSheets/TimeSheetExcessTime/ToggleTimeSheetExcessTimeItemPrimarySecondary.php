<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetExcessTime;

use Element\ElementTime\Actions\Tenant\TimeSheets\BaseTimeSheetsAction;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetDay\CalculateDailyExcessTime;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetDay\CalculateTimeSheetDayTotals;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTime;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetExcessTimeItem;
use Element\ElementTime\Support\Facades\CurrentUser;

class ToggleTimeSheetExcessTimeItemPrimarySecondary extends BaseTimeSheetsAction
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetExcessTimeItem $timeSheetExcessTimeItem,
    ): TimeSheetExcessTime
    {
        if (!$timeSheetExcessTimeItem->isOriginal) {
            $original = $timeSheetExcessTimeItem->originalItem;
            $original->repository->unReplace(CurrentUser::getUser());

            $timeSheetExcessTimeItem->forceDelete();

            return ApplyRelatedCalculationsPostExcessTimeChanges::make()->handle($original->timeSheetExcessTime);
        }

        $isPlanned = !$timeSheetExcessTimeItem->isPlanned;
        $excessTime = $timeSheetExcessTimeItem->timeSheetExcessTime;

        if ($excessTime->type != 'P') {
            CalculateDailyExcessTime\ApplyUnbufferedRules::make(
                $this,
                $excessTime->duration,
                $excessTime->blocks,
                new \stdClass,
                true,
                !$isPlanned,
            )();

            CalculateTimeSheetDayTotals::make()->handle($excessTime->timeSheetWork->timeSheetDay, excessTimeValues: true);
        } else {
            dd('a');
        }

        return $excessTime;
    }
}
