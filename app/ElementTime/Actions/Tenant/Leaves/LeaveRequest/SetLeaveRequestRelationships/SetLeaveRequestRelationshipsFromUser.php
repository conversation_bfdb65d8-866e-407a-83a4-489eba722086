<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Domains\Tenant\Users\Models\User;

class SetLeaveRequestRelationshipsFromUser extends BaseSetLeaveRequestRelationships
{
    /** @throws \Throwable */
    public function handle(User $user): void
    {
        $this->setRelationshipsFromUserOrUserRole($user);
    }

    /** @throws \Throwable */
    public function asJob(User $user): void
    {
        $this->handle($user);
    }
}
