<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Schedules\ScheduledPayRunPeriodDay;

use Element\Core\Types\TimeDuration;
use Element\ElementTime\Actions\Tenant\Schedules\BaseSchedulesAction;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequestDay;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDay;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetHoliday;
use Illuminate\Database\Eloquent\Collection;

class GetMissingDurationForScheduledDay extends BaseSchedulesAction
{
    /**
     * @param Collection<TimeSheetHoliday>|TimeSheetHoliday[] $holidays
     * @param Collection<LeaveRequestDay>|LeaveRequestDay[] $leaveRequestDays
     * @throws \Throwable
     */
    public function handle(ScheduledPayRunPeriodDay $day, Collection|array $holidays, Collection|array $leaveRequestDays): TimeDuration
    {
        $missingDuration = $day->getMinDuration();

        if ($missingDuration->lte0()) {
            return TimeDuration::zero();
        }

        foreach ($holidays as $holiday) {
            if (!$holiday->timeSheetDay->date->isSameDay($day->date)) {
                continue;
            }

            $missingDuration->deduct($holiday->duration);
        }

        if ($missingDuration->lte0()) {
            return TimeDuration::zero();
        }

        foreach ($leaveRequestDays as $leaveDay) {
            if (!$leaveDay->date->isSameDay($day->date)) {
                continue;
            }

            $missingDuration->deduct($leaveDay->duration);
        }

        if ($missingDuration->lte0()) {
            return TimeDuration::zero();
        }

        foreach ($day->timeSheetWorks as $work) {
            foreach ($work->items as $item) {
                $missingDuration->deduct($item->getOrdinaryDuration());

                if ($missingDuration->lte0()) {
                    return TimeDuration::zero();
                }
            }
        }

        return $missingDuration;
    }
}
