<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\_Tenants\Ngsc\KeypayBatchFile;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\Integrations\DataSources\PayRunDetails as DS;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\PayRuns\Repositories\PayRunRepository;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayRunType;
use Element\ElementTime\Domains\Tenant\Settings\Support\ExcessTimeRuleTypes as ExcessTimeRules;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ExcludedType;
use Element\ElementTime\Support\Facades\CurrentTenant;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantCsvReport;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class KeypayBatchFileCsvReport extends TenantCsvReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
    ];

    const TIME = '~1:30 min';
    const REPORT_SLUG = 'ngsc--keypay-batch-file';
    const TYPE_SLUG = 'ngsc--keypay-batch-file.csv';

    const LEAVE_HAS_ATTACHMENT_CODE = 'CP';
    const LEAVE_HAS_NO_ATTACHMENT_CODE = 'N';

    public $c = KeypayBatchFileReportConfig::class;

    protected PayRun|null $payRun;
    protected PayRunType|null $payRunType;
    protected float $fteHoursPerDay = 7.6;

    public function getReplacedString(string $string):string
    {
        $string = parent::getReplacedString($string);

        if (isset($this->payRun) && $this->payRun instanceof PayRun) {
            $string = Str::replace('{{payRun.processingDateTime}}', $this->payRun->processingDateTime->format('dmY_Hms'), $string);
        }

        return $string;
    }

    /** @throws InvalidArgumentException */
    protected function setData(array $info): void
    {
        if (!isset($info['id']) || !is_numeric($info['id'])) {
            throw new InvalidArgumentException('Pay run ID is missing');
        }

        $this->config->title = $this->c::NAME;
        $this->fteHoursPerDay = TenantSystemSettings::getValue('settings.fteHoursPerWeek') / 5;

        /** @var PayRun $payRun */
        $payRun = PayRunRepository::getOneOrFail([
            'constraints' => [
                ['PayRun.id', '=', $info['id']],
            ],
            'returnType' => 'model',
        ]);

        $this->payRun = $payRun;
        $this->payRunType = $this->payRun->payRunType;
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$doesCache || !$this->payRun->isFinished()) {
            $this->reportValidity = Carbon::now()->subSeconds(1);
        }

        return $this;
    }

    /** @return Collection|DS\BasePayRunDetailsRow[] */
    protected function getDataFromDataSource(string $order = 'userExternalId'): Collection|array
    {
        return DS\PayRunDetailsDataSource
            ::new($this->payRun)
            ->withAll()
            ->deductRdoFromHours()
            ->sortBy($order)
            ->get();
    }

    protected function dataRowShouldBeIncluded(DS\BasePayRunDetailsRow $sourceRow = null): bool
    {
        if (is_null($sourceRow)) {
            return false;
        }

        if ($sourceRow->timeSheetStatusId == ExcludedType::ID) {
            return false;
        }

        if (
            (
                $sourceRow instanceof DS\WorkedRow
                || $sourceRow instanceof DS\PublicHolidayRow
                || $sourceRow instanceof DS\LeaveRow
            )
            && $sourceRow->adjustedHours == 0
        ) {
            return false;
        }

        if ($sourceRow instanceof DS\PenaltyRow && $sourceRow->hours <= 0) {
            return false;
        }

        if ($sourceRow instanceof DS\ExcessTimeRow && in_array($sourceRow->excessTimeRuleType, [
            ExcessTimeRules\UnpaidHoursType::ID_SHORT,
            ExcessTimeRules\AccruedHoursType::ID_SHORT,
        ])) {
            return false;
        }

        return true;
    }

    /** @throws BindingResolutionException */
    protected function buildReportData(): void
    {
        $this->header = [
            'Employee External ID', // User.externalId
            'Date', // Last day of pay run - Format: YYYY-MM-DD 00:00:00
            'Units', // Adjusted hours OR allowance quantity
            'Location External ID', // Formatted code
            'Work Type External ID', // Hour code OR allowance code
            'Comments', // Blank
        ];

        $this->data = [];

        $sourceData = $this->getDataFromDataSource();

        foreach ($sourceData as $sourceRow) {
            if (!$this->dataRowShouldBeIncluded($sourceRow)) {
                continue;
            }

            $this->buildRowData($sourceRow);
        }

        $this->data = array_values($this->data);

        //region HARDCODE - code splits
        if (CurrentTenant::getTenant()->slug == 'ngsc') {
            $this->applyNGSCSplitsToData();
        }
        //endregion HARDCODE - code splits
    }

    protected function buildRowData(DS\BasePayRunDetailsRow $sourceRow): void
    {
        switch (get_class($sourceRow)) {
            case DS\WorkedRow::class:
                $this->buildWorkedRow($sourceRow);
                break;
            case DS\PublicHolidayRow::class:
                $this->buildPublicHolidayRow($sourceRow);
                break;
            case DS\LeaveRow::class:
                $this->buildLeaveRow($sourceRow);
                break;
            case DS\ExcessTimeRow::class:
                $this->buildExcessTimeRow($sourceRow);
                break;
            case DS\PenaltyRow::class:
                $this->buildPenaltyRow($sourceRow);
                break;
            case DS\AdhocAllowanceRow::class:
                $this->buildAdhocAllowanceRow($sourceRow);
                break;
            case DS\AutoAllowanceRow::class:
                $this->buildAutoAllowanceRow($sourceRow);
                break;
        }
    }

    protected function buildRow(
        ?string $userExternalId,
        ?Carbon $transactionDate,
        ?float $units,
        ?string $formattedCode,
        ?string $hourCode,
        ?string $comments = null,
    ): array
    {
        return [
            $userExternalId ?? 'TBA',
            !is_null($transactionDate) ? $transactionDate->format('d/m/Y') : null,
            $units ?? 0,
            $formattedCode ?? '',
            $hourCode ?? '',
            $comments ?? '',
        ];
    }

    protected function buildWorkedRow(DS\WorkedRow $sourceRow): void
    {
        if ($sourceRow->adjustedHoursMinusPenalties <= 0) {
            return;
        }

        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->adjustedHoursMinusPenalties,
            $sourceRow->formattedCode,
            $sourceRow->hourCode,
        );
    }

    protected function buildPublicHolidayRow(DS\PublicHolidayRow $sourceRow): void
    {
        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->adjustedHours,
            $sourceRow->formattedCode,
            $sourceRow->hourCode,
        );
    }

    protected function buildLeaveRow(DS\LeaveRow $sourceRow): void
    {
        if ($sourceRow->isFromCompleteMissingHours) {
            $rowId = 'l_cmh_' . $sourceRow->userExternalId . '_' . $sourceRow->formattedCode;

            if (!isset($this->data[$rowId])) {
                $this->data[$rowId] = $this->buildRow(
                    $sourceRow->userExternalId,
                    $sourceRow->payRunEndDate,
                    $sourceRow->adjustedHours,
                    $sourceRow->formattedCode,
                    $sourceRow->hourCode,
                );
            } else {
                $this->data[$rowId][2] += $sourceRow->adjustedHours;
            }

            return;
        }

        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->adjustedHours,
            $sourceRow->formattedCode,
            $sourceRow->hourCode,
        );
    }

    protected function buildExcessTimeRow(DS\ExcessTimeRow $sourceRow): void
    {
        if (
            $sourceRow->excessTimeRuleType == ExcessTimeRules\UnpaidHoursType::ID_SHORT
            || $sourceRow->excessTimeRuleType == ExcessTimeRules\AccruedHoursType::ID_SHORT
        ) {
            return;
        }

        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->hours,
            $sourceRow->formattedCode,
            $sourceRow->hourCode,
        );
    }

    protected function buildPenaltyRow(DS\PenaltyRow $sourceRow): void
    {
        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->hours,
            $sourceRow->formattedCode,
            $sourceRow->hourCode,
        );
    }

    protected function buildAdhocAllowanceRow(DS\AdhocAllowanceRow $sourceRow): void
    {
        switch ($sourceRow->rateType) {
            case 'F':
            case 'D':
                $quantity = $sourceRow->instances;
                break;
            case 'H':
                $quantity = $sourceRow->hours;
                break;
            case 'M':
                $quantity = $sourceRow->mileage;
                break;
            default:
                return;
        }

        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $quantity,
            $sourceRow->formattedCode,
            $sourceRow->externalId,
        );
    }

    protected function buildAutoAllowanceRow(DS\AutoAllowanceRow $sourceRow): void
    {
        $this->data[$sourceRow->_rowId] = $this->buildRow(
            $sourceRow->userExternalId,
            $sourceRow->payRunEndDate,
            $sourceRow->quantity,
            $sourceRow->formattedCode,
            $sourceRow->externalId,
        );
    }

    //region HARDCODE - split methods

    protected function applyNGSCSplitsToData(): void
    {
        $splits = [
            // Samantha Winter | Centre Coordinator
//            '85589_A000052.C000001' => [
//                [
//                    50,
//                    'A000052.C000001',
//                ],
//                [
//                    50,
//                    'A000053.C000056',
//                ],
//            ],

            // Penny Malpas | Acting Fitness Team Leader
//            '85492_A000052.C000001' => [
//                [
//                    50,
//                    'A000054.C000060',
//                ],
//                [
//                    50,
//                    'A000054.C000059',
//                ],
//            ],

            // Matthew Bourke | Sports and Aquatic Team Leader
//            '10165_A000052.C000001' => [
//                [
//                    30,
//                    'A000055.C000063',
//                ],
//                [
//                    40,
//                    'A000053.C000057',
//                ],
//                [
//                    10,
//                    'A000053.C000056',
//                ],
//                [
//                    10,
//                    'A000052.C000001',
//                ],
//                [
//                    10,
//                    'A000053.C000058',
//                ],
//            ],

            // Joanna Brown | Team Leader Fitness
//            '85717_A000054.C000059' => [
//                [
//                    33,
//                    'A000054.C000059',
//                ],
//                [
//                    33,
//                    'A000054.C000060',
//                ],
//                [
//                    34,
//                    'A000054.C000061',
//                ],
//            ],

            // Jenna Greene | Community Safety and Amenity Support Officer
            '85507_A000030.C000001' => [
                [
                    60,
                    'A000030.C000001',
                ],
                [
                    40,
                    'A000028.C000001',
                ],
            ],

            // Logan Sage | Community Safety and Amenity Support Officer
            '85753_A000030.C000001' => [
                [
                    60,
                    'A000030.C000001',
                ],
                [
                    40,
                    'A000028.C000001',
                ],
            ],

            // Vishnuprasad Vinodukumar | Asset and Project Engineer
            // '85685_CP000069.C000001' => [
            //     [
            //         60,
            //         'A000020.C000001',
            //     ],
            //     [
            //         40,
            //         'CP000069.C000001',
            //     ],
            // ],
        ];

        $this->applySplitsToData($splits);
    }

    protected function applySplitsToData(array $splits): void
    {
        /**
            'Employee External ID', // User.externalId
            'Date', // Last day of pay run - Format: YYYY-MM-DD 00:00:00
            'Units', // Adjusted hours OR allowance quantity
            'Location External ID', // Formatted code
            'Work Type External ID', // Hour code OR allowance code
            'Comments', // Blank
         */

        $i = 0;
        foreach ($this->data as $datum) {
            $id = $datum[0] . '_' . $datum['3'];

            if (isset($splits[$id])) {
                $newColumns = [];

                foreach ($splits[$id] as $split) {
                    $newColumn = $datum;
                    $newColumn[2] = $newColumn[2] * $split[0] / 100;
                    $newColumn[3] = $split[1];

                    $newColumns[] = $newColumn;
                }

                array_splice($this->data, $i, 1, $newColumns);

                $i += count($newColumns) - 1;
            }

            $i++;
        }
    }

    //endregion HARDCODE - split methods
}
