<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('Contribution', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // Contribution Details
            $table->boolean('isDefaultScheme')->nullable();
            $table->string('name')->nullable();

            // Rules
            $table->float('employeeContributionAmount')->nullable();
            $table->char('employeeRateType', 1)->nullable();
            $table->float('employerContributionAmount')->nullable();
            $table->char('employerRateType', 1)->nullable();
            $table->boolean('canChangeRateType')->nullable();
            $table->char('madeOn', 1)->nullable();
            $table->boolean('canChangeAmount')->nullable();
            $table->float('protectedThresholdAmount')->nullable();
            $table->boolean('additionalThroughSacrifice')->nullable();

            // Internal Fund Information
            $table->bigInteger('fundProject_id')->unsigned()->nullable();
            $table->bigInteger('fundProjectActivity_id')->unsigned()->nullable();
            $table->string('bankAccount')->nullable();

            // External Fund Information
            $table->bigInteger('fundOrganisation_id')->unsigned()->nullable();

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('fundProject_id', 'contribution_fundproject_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('fundProjectActivity_id', 'contribution_fundprojectactivity_fn')->references('id')->on('ProjectActivity')->onUpdate('cascade')->onDelete('set null');
            $table->foreign('fundOrganisation_id', 'contribution_fundrorgtanisation_fn')->references('id')->on('FundOrganisation')->onUpdate('cascade')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::drop('Contribution');
    }
};
