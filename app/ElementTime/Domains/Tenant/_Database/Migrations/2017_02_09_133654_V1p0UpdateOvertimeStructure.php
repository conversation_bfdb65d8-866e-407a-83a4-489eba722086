<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('OvertimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            $table->string('name');
            $table->bigInteger('incrementRate_id')->unsigned()->nullable();

            $table->char('calculationType', 1)->default('S'); // Options:
            // (S)tays the same
            // Changes depending on the (T)ime of day it is earnt
            // Changes based on how (M)uch Overtime is earnt over time
            $table->longText('formula')->nullable(); // Format:
            /*
                If calculationType is S
                    {
                        "ratioTo": 1 // float
                    }

                If calculationType is T
                    [
                        {
                            "startTime: "06:00", // time
                            "endTime": "18:00", // time
                            "ratioTo": 1, // float
                        },
                        {
                            "startTime: "18:00", // time
                            "endTime": "06:00", // time
                            "ratioTo": 1, // float
                        }
                    ]
                If calculationType is M
                    [
                        {
                            "hours": 4, // float
                            "ratioTo": 1, // float
                        },
                        {
                            "hours": 3, // time
                            "ratioTo": 1, // float
                        },
                        {
                            "hours": null, // last must be null
                            "ratioTo": 1, // float
                        }
                    ]
            */

            $table->boolean('appliesBufferBeforeAccrued')->default(false);
            $table->decimal('minutesAsBuffer', 16, 10)->nullable()->default(null);

            $table->boolean('roundsUp')->default(false);
            $table->boolean('roundsDown')->default(false);
            $table->integer('roundMinutes')->nullable(); // Options: 5, 10, 15, 30 and 60

            $table->string('accountCode')->nullable();
            $table->bigInteger('project_id')->unsigned()->nullable();

            $table->char('autoApplies', 1)->default('D'); // Options:
            // After worker exceeds (D)aily scheduled hours
            // After worker exceeds (W)eekly scheduled hours
            // After worker exceeds (P)ay run scheduled hours
            // After worker exceeds <hour> (H)ours per <period> period
            $table->decimal('autoApplyHours')->nullable();
            $table->char('autoApplyPeriod', 1)->nullable(); // Options:
            // (D)ay
            // (W)eek
            // (P)ay run

            $table->boolean('earntOnPublicHoliday')->default(true);
            $table->boolean('earntOnRegionalPublicHoliday')->default(true);
            $table->boolean('earntOnOrganisationPublicHoliday')->default(true);
            $table->boolean('earntOnSaturdays')->default(true);
            $table->boolean('earntOnSundays')->default(true);

            $table->char('status', 1);

            $table->decimal('limitBalance', 16, 10)->nullable();
            $table->char('ifLimitReached', 1)->default('M'); // Options:
            // Do (N)othing
            // Send Alert to (M)anager
            // Send Alert to (P)ayroll officer
            // Send (A)lert to Manager and Payroll Officer

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('project_id', 'overtimetype_project')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');
        });

        Schema::create('UserOvertimeType', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('overtimeType_id')->unsigned();
            $table->bigInteger('user_id')->unsigned();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();

            $table->text('comment')->nullable();
            $table->boolean('allowToCalculateButNotPay')->default(false);

            $table->char('status', 1)->default('A');
            // Options:
            //  A => Active
            //  I => Inactive

            $table->decimal('totalHoursAccrued', 16, 10)->nullable();
            $table->decimal('totalValueAccrued', 16, 10)->nullable();

            $table->foreign('overtimeType_id', 'uot_ot_fn')->references('id')->on('OvertimeType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('user_id', 'uot_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('Overtime', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->bigInteger('userOvertimeType_id')->unsigned()->nullable(); // If it was not earnt by transferring from Toil
            $table->bigInteger('userToilType_id')->unsigned()->nullable(); // If the balance was transferred from a Toil balance
            $table->bigInteger('timeSheet_id')->unsigned();
            $table->bigInteger('timeSheetDay_id')->unsigned()->nullable(); // If value of autoApplies field from OvertimeType related is D

            $table->longText('items');
            // Array/JSON:
            /*
             *  - If has UserOvertimeType and OvertimeType.autoApplies is D
             *  [
             *      {
             *          "date": "2016-01-01",
             *          "hours": 1.4,
             *          "value": 84.99
             *      },
             *      ...
             *  ]
             *
             *  - If has UserOvertimeType and OvertimeType.autoApplies is W or H
             *  [
             *      {
             *          "startDate": "2016-01-01",
             *          "endDate": "2016-01-08",
             *          "hours": 1.4,
             *          "value": 84.99
             *      }
             *  ]
             *
             *  - If has UserOvertimeType and OvertimeType.autoApplies is P or has UserToilType
             *  null or {} or []
             */
            $table->decimal('hours', 16, 10)->nullable();
            $table->decimal('value', 16, 10)->nullable();

            $table->dateTime('dateTimeAccrual')->nullable();

            $table->char('status', 1)->default('N'); // P => Paid | N => Not Paid

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userOvertimeType_id', 'overtime_uot_fn')->references('id')->on('UserOvertimeType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('userToilType_id', 'overtime_utt_fn')->references('id')->on('UserToilType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheet_id', 'overtime_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('timeSheetDay_id', 'overtime_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('UserToilTypeBalanceUse', function (Blueprint $table) {
            $table->bigInteger('overtime_id')->unsigned()->nullable();
            $table->foreign('overtime_id', 'uttbu_o_fn')->references('id')->on('Overtime')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserToilTypeBalanceUse', 'overtime_id')) {
            Schema::table('UserToilTypeBalanceUse', function (Blueprint $table) {
                $table->dropForeign('uttbu_o_fn');
                $table->dropColumn('overtime_id');
            });
        }

        Schema::dropIfExists('Overtime');

        Schema::dropIfExists('UserOvertimeType');

        Schema::dropIfExists('OvertimeType');
    }
};
