<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('AsyncProcess', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('rel_type')->nullable();
            $table->unsignedBigInteger('rel_id')->nullable();

            $table->text('jobClass')->nullable();
            $table->text('jobId')->nullable();
            $table->dateTime('queuedDateTime');
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('finishDateTime')->nullable();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('AsyncProcess');
    }
};
