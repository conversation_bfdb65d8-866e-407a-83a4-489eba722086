<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\Core\Structures;

use Element\Core\Support\Structures\BaseStruct;

/**
 * @property string $name
 * @property string $description
 */
class StructTestBasic extends BaseStruct
{
    protected array $properties = [
        'name' => null,
        'description' => null,
    ];

    protected array $casts = [
        'name' => 'string',
        'description' => 'string',
    ];
}
