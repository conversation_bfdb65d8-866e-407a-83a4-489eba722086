<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\IndicativeLeave\Traits;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Leaves\Models\UserLeaveBankType;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\LeaveRequestRepository;
use Element\ElementTime\Domains\Tenant\Leaves\Repositories\UserLeaveBankTypeRepository;
use Element\ElementTime\Domains\Tenant\Settings\Models\LeaveType;
use Element\ElementTime\Domains\Tenant\Settings\Models\RosteredTimeOffType;
use Element\ElementTime\Domains\Tenant\Settings\Repositories\LeaveTypeRepository;
use Element\ElementTime\Domains\Tenant\Settings\Repositories\RosteredTimeOffTypeRepository;
use Element\ElementTime\Domains\Tenant\Users\Models\User;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\PartiallyApprovedType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\SubmittedType;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantOrganisationSettings;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

trait IndicativeLeaveDataTrait
{
    //region --\\   Types   //--

    /**
     * @throws InvalidArgumentException
     */
    protected function getData(): Collection
    {
        $ret = collect([]);

        $ret->push($this->getAccruedHoursData());

        foreach ($this->getLeaveData() as $leaveDatum) {
            $ret->push($leaveDatum);
        }

        foreach ($this->getRosteredTimeOffData() as $rosteredTimeOffDatum) {
            $ret->push($rosteredTimeOffDatum);
        }

        return $ret;
    }

    /** @var UserLeaveBankType[]|Collection|null */
    protected Collection|array|null $bankTypes = null;

    /**
     * @return UserLeaveBankType[]|Collection
     * @throws InvalidArgumentException
     */
    protected function getAllBankTypes(): Collection|array
    {
        $constrains = [
            ['UserLeaveBankType.status', '=', ActiveStatus::ID],
            ['UserLeaveBankType.canTakeLeave', '=', true],
        ];

        $user = CurrentUser::getUser();
        if (!$user->access->isPayrollOfficer(true) && !$user->access->isBusinessIntelligence(true)) {
            $directReportUserIds = [];
            $reportUsersIds = $this->getDirectReportUserIds($user, $user->doesIncludeIndirectReportsOnReports);
            foreach ($reportUsersIds as $reportUserId) {
                $directReportUserIds[] = $reportUserId;
            }

            $constrains[] = ['IN', 'User.id', $directReportUserIds];
        }

        $constrains[] = [
            [
                ['IS NULL', 'User.endDate', 'OR'],
                ['User.endDate', '=', DB::raw('"0000-00-00"'), 'OR'],
            ],
        ];

        if (is_null($this->bankTypes)) {
            /** @var Collection|UserLeaveBankType[] $userLeaveBankTypes */
            $this->bankTypes = UserLeaveBankTypeRepository::getMany([
                'constraints' => $constrains,
                'relations' => [
                    'bank.user.userRoles.role',
                    'bank.user.userRoles.userRoleMasters',
                    'bank.user.userManagers.manager',
                    'bank.user.userRoles.userDepartments.department',
                    'bank.user.userRoles.userDepartments.userRole.user',
                ],
                'order' => 'CAST(User.externalId AS SIGNED)',
                'returnType' => 'model',
            ]);
        }

        return $this->bankTypes;
    }

    /**
     * @param LeaveType|RosteredTimeOffType|null $type
     * @return Collection|UserLeaveBankType[]
     * @throws InvalidArgumentException
     */
    protected function getBankTypesForType(RosteredTimeOffType|LeaveType|null $type = null): Collection|array
    {
        /** @var Collection|UserLeaveBankType[] $ret */
        $ret = collect([]);

        foreach ($this->getAllBankTypes() as $bankType) {
            if (
                (
                    is_null($type)
                    && is_null($bankType->type_type)
                )
                || (
                    $type instanceof LeaveType
                    && $bankType->type_type == LeaveType::class
                    && $bankType->type_id == $type->id
                )
                || (
                    $type instanceof RosteredTimeOffType
                    && $bankType->type_type == RosteredTimeOffType::class
                    && $bankType->type_id == $type->id
                )
            ) {
                $ret->push($bankType);
            }
        }

        return $ret;
    }

    /**
     * @throws InvalidArgumentException
     */
    protected function getAccruedHoursData(): \stdClass
    {
        $ret = new \stdClass();

        $ret->name = TenantSystemSettings::o()->customExcessTimeBalanceName;
        $ret->bankTypes = $this->getBankTypesForType(null);

        return $ret;

    }

    /**
     * @throws InvalidArgumentException
     */
    protected function getLeaveData(): Collection
    {
        $ret = collect([]);

        /** @var Collection|LeaveType[] $leaveTypes */
        $leaveTypes = LeaveTypeRepository::getMany([
            'constraints' => [
                ['LeaveType.status', '=', 'A'],
            ],
            'order' => 'LeaveType.name',
            'returnType' => 'model',
        ]);

        foreach ($leaveTypes as $leaveType) {
            $obj = new \stdClass();
            $obj->name = $leaveType->name;
            $obj->bankTypes = $this->getBankTypesForType($leaveType);

            $ret->push($obj);
        }

        return $ret;
    }

    /**
     * @throws InvalidArgumentException
     */
    protected function getRosteredTimeOffData(): Collection
    {
        $ret = collect([]);

        /** @var Collection|RosteredTimeOffType[] $rdoTypes */
        $rdoTypes = RosteredTimeOffTypeRepository::getMany([
            'constraints' => [
                ['RosteredTimeOffType.status', '=', 'A'],
            ],
            'order' => 'RosteredTimeOffType.name',
            'returnType' => 'model',
        ]);

        foreach ($rdoTypes as $rdoType) {
            $obj = new \stdClass();
            $obj->name = $rdoType->name;
            $obj->bankTypes = $this->getBankTypesForType($rdoType);

            $ret->push($obj);
        }

        return $ret;
    }

    //endregion --\\   Types   //--

    //region --\\   Leave requests   //--

    /** @var Collection|\stdClass[] */
    protected Collection|array|null $leaveRequests = null;

    /**
     * @return Collection|\stdClass[]
     */
    protected function getLeaveRequests(): array|Collection
    {
        if (is_null($this->leaveRequests)) {
            $this->leaveRequests = collect([]);

            /** @var LeaveRequest[] $leaveRequests */
            $leaveRequests = LeaveRequestRepository::getMany([
                'constraints' => [
                    ['IN', 'Workflow.status', [
                        NewType::ID,
                        SubmittedType::ID,
                        PartiallyApprovedType::ID,
                        ApprovedType::ID,
                    ]],
                    ['LeaveRequest.endDate', '>=', Carbon::parse(TenantOrganisationSettings::getValue('fiscalYearStart'))->toDateString()],
                ],
                'relations' => [
                    'userLeaveBankType.bank',
                ],
                'order' => 'CAST(User.externalId AS SIGNED), LeaveRequest.startDate',
                'returnType' => 'model',
            ]);

            foreach ($leaveRequests as $leaveRequest) {
                $obj = new \stdClass();
                $obj->user_id = $leaveRequest->userLeaveBankType->bank->user->id;
                $obj->bankType_id = $leaveRequest->userLeaveBankType_id;
                $obj->calculatedTotalHours = $leaveRequest->durationAdjusted->getTotalHours(2);
                $obj->startDateTime = $leaveRequest->startDate;
                $obj->endDateTime = $leaveRequest->endDate;
                $obj->period = $leaveRequest->periodLabel;

                $this->leaveRequests->push($obj);
            }
        }

        return $this->leaveRequests;
    }

    /** @var Collection[]|\stdClass[][] */
    protected array $leaveRequestsByUser = [];

    /**
     * @return Collection|\stdClass[]
     */
    protected function getLeaveRequestsFromUser(User $user): array|Collection
    {
        if (!isset($this->leaveRequestsByUser['u_' . $user->id])) {
            $this->leaveRequestsByUser['u_' . $user->id] = collect([]);

            foreach ($this->getLeaveRequests() as $leaveRequest) {
                if ($leaveRequest->{'user_id'} == $user->id) {
                    $this->leaveRequestsByUser['u_' . $user->id]->push($leaveRequest);
                }
            }
        }

        return $this->leaveRequestsByUser['u_' . $user->id];
    }

    /** @var Collection[]|\stdClass[][] */
    protected array $leaveRequestsByBankType = [];

    /**
     * @return Collection|\stdClass[]
     */
    protected function getLeaveRequestsFromBankType(UserLeaveBankType $bankType): array|Collection
    {
        if (!isset($this->leaveRequestsByBankType['t_' . $bankType->id])) {
            $this->leaveRequestsByBankType['t_' . $bankType->id] = collect([]);

            foreach ($this->getLeaveRequestsFromUser($bankType->bank->user) as $leaveRequest) {
                if ($leaveRequest->bankType_id == $bankType->id) {
                    $this->leaveRequestsByBankType['t_' . $bankType->id]->push($leaveRequest);
                }
            }
        }

        return $this->leaveRequestsByBankType['t_' . $bankType->id];
    }

    //endregion --\\   Leave requests   //--
}
