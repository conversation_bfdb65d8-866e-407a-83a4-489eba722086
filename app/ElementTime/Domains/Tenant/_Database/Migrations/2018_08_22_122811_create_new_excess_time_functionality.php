<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: Settings

        Schema::table('Settings', function (Blueprint $table) {
            // General
            $table->boolean('doesAllowOverridingExcessTime')->default(true);
            $table->boolean('doesRequireCommentToRecordExcessTime')->default(true);

            // Balance settings
            $table->char('excessTimeBalanceExpires', 1)->default('N'); // N (Never) | W (After number of weeks) | A (On employee anniversary) | D (On a fixed date) | Y (End of financial year)
            $table->integer('excessTimeBalanceExpiresWeeks')->default(12);
            $table->string('excessTimeBalanceExpiresDate')->default('01-01');
            $table->char('excessTimeBalanceExpiredAction', 1)->default('P'); // P (Paid) | L (Lost)
            $table->decimal('excessTimeBalanceRetainedAfterExpiry', 16, 10)->default(0);
            $table->decimal('maximumAllowedExcessTimeBalance', 16, 10)->default(80);
            $table->char('maximumAllowedExcessTimeBalanceAction', 1)->default('P'); // P (paid) | L (Lost)
            $table->decimal('alertExcessTimeBalanceReachesAmount', 16, 10)->default(60);
            $table->char('alertExcessTimeBalanceReachesAction', 1)->default('M'); // M (Send alert to manager) | P (Send alert to payroll officers) | B (Send alert to manager and payroll officers) | N (Do nothing)

            // Paying out balances
            $table->boolean('doesAllowExcessTimeBalanceToBePaidOnRequest')->default(true);
            $table->boolean('doesPayExcessTimeOnTermination')->default(true);
            $table->char('excessTimeIsPaidOutAtType', 1)->default('E'); // E (Hourly rate on the time earnt | C (Current rate) | A (Average rate)

            // Taking as leave
            $table->boolean('doesApplyFteCalculationOnTakingExcessTimeLeave')->default(true);
            $table->boolean('doesDeductBreaksOnTakingExcessTimeLeave')->default(true);
            $table->boolean('doesAccrueLeaveOnTakingExcessTimeLeave')->default(true);
            $table->decimal('maximumNegativeAllowedExcessTimeBalance', 16, 10)->default(8);
        });

        //endregion Table: Settings

        //region Table: ExcessTimeGroup

        Schema::create('ExcessTimeGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();
            $table->string('slug')->nullable();

            // General Information
            $table->string('name')->nullable();
            $table->text('description')->nullable();

            // Status
            $table->char('status', 1)->default('A');
            $table->boolean('hasIssues')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeGroup

        //region Table: ExcessTimeGroupSection

        Schema::create('ExcessTimeGroupSection', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->char('type', 1)->default('W'); // W (Weekday) | P (Period) | S (Saturday) | U (Sunday) | H (Public holiday)

            // Relations
            $table->unsignedBigInteger('excessTimeGroup_id');
            $table->foreign('excessTimeGroup_id', 'etgs_etg_fn')->references('id')->on('ExcessTimeGroup')->onUpdate('cascade')->onDelete('cascade');

            $table->unique(['type', 'excessTimeGroup_id'], 'etgs_t_etg_uq');

            // Rules
            $table->boolean('doesApplyBuffer')->default(false);
            $table->integer('bufferMinutes')->nullable();
            $table->char('calculationType', 1)->nullable(); // T (Scheduled hours plus excess time hours) | E (Only excess time hours)

            // Specific Rules
            $table->boolean('doesAccrueDayOffInLieu')->default(false); // For W (not applicable) | For P (not applicable) | For S (applicable) | For U (applicable) | For H (applicable)
            $table->decimal('accruesDayOffInLieuAfterHours', 16, 10)->nullable();
            $table->char('accrueDayOffInLieuType', 1)->nullable(); // F (FTE day) | S (Average scheduled day) | C (Custom hours)
            $table->decimal('accrueDayOffInLieuCustomHours', 16, 10)->nullable();
            $table->boolean('doesUseSameRulesAsOther')->default(false); // For W (not applicable) | For P (not applicable) | For S (same as W) | For U (Same as S) | For H (Same as U)

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeGroupSection

        //region Table: ExcessTimeGroupSectionLevel

        Schema::create('ExcessTimeGroupSectionLevel', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->integer('level')->default(1);
            $table->char('type', 1)->default('P'); // P (planned) | U (Unplanned)

            // Relations
            $table->unsignedBigInteger('excessTimeGroupSection_id');
            $table->foreign('excessTimeGroupSection_id', 'etgsl_etgs_fn')->references('id')->on('ExcessTimeGroupSection')->onUpdate('cascade')->onDelete('cascade');

            $table->unique(['level', 'type', 'excessTimeGroupSection_id'], 'etgsl_l_t_etgs_uq');

            // General Information
            $table->decimal('maximumHours', 16, 10)->nullable()->default(null);
            $table->boolean('doesApplyMinimumBalance')->default(false);
            $table->decimal('minimumBalance', 16, 10)->nullable();
            $table->decimal('requiresApprovalOverHours', 16, 10)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeGroupSectionLevel

        //region Table: ExcessTimeGroupSectionLevelRule

        Schema::create('ExcessTimeGroupSectionLevelRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();
            $table->string('slug')->nullable();

            // Relations
            $table->unsignedBigInteger('excessTimeGroupSectionLevel_id');
            $table->foreign('excessTimeGroupSectionLevel_id', 'etgslr_etgsl_fn')->references('id')->on('ExcessTimeGroupSectionLevel')->onUpdate('cascade')->onDelete('cascade');

            // General Information
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->string('projectAccountCode')->nullable();
            $table->unsignedBigInteger('project_id')->nullable();
            $table->foreign('project_id', 'etgslr_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('set null');

            // Rules
            $table->char('type', 1)->default('A'); // A (Accrued hours) | P (Paid hours)
            $table->decimal('value', 16, 10)->default(1);
            $table->boolean('doesAccrueLeave')->default(false);

            // Specific rules
            $table->boolean('isDefaultForDuration')->default(false); // Not applicable for P (period)
            $table->string('startTime')->nullable(); // Not applicable for P (period) sections but compulsory for the others
            $table->string('endTime')->nullable(); // Not applicable for P (period) sections but compulsory for the others

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeGroupSectionLevelRule

        //region Table: UserShiftExcessTimeGroup

        Schema::create('UserShiftExcessTimeGroup', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('userShift_id');
            $table->foreign('userShift_id', 'usetg_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('excessTimeGroup_id');
            $table->foreign('excessTimeGroup_id', 'usetg_etg_fn')->references('id')->on('ExcessTimeGroup')->onUpdate('cascade')->onDelete('restrict');

            $table->dateTime('startDate');
            $table->dateTime('endDate')->nullable();

            $table->timestamps();
        });

        //endregion Table: UserShiftExcessTimeGroup

        //region Table: UserExcessTimeBalance

        Schema::create('UserExcessTimeBalance', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'uetb_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unique(['user_id'], 'uetb_u_uq');

            // Balances
            $table->decimal('availableBalance', 16, 10)->default(0);
            $table->decimal('committedBalance', 16, 10)->default(0);
            $table->decimal('lostBalance', 16, 10)->default(0);
            $table->decimal('usedAsHoursBalance', 16, 10)->default(0);
            $table->decimal('usedAsPaidBalance', 16, 10)->default(0);

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: UserExcessTimeBalance

        //region Table: ExcessTimeLeave

        Schema::create('ExcessTimeLeave', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('userExcessTimeBalance_id');
            $table->foreign('userExcessTimeBalance_id', 'etl_uetb_fn')->references('id')->on('UserExcessTimeBalance')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('userShift_id')->nullable();
            $table->foreign('userShift_id', 'etl_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('originalExcessTimeLeave_id')->nullable();
            $table->foreign('originalExcessTimeLeave_id', 'etl_oetl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('restrict');

            // Request data
            $table->boolean('isFullDay')->default(false);
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();
            $table->string('glideDailyStartTime')->nullable();
            $table->string('glideDailyEndTime')->nullable();
            $table->decimal('glideDailyHours', 16, 10)->nullable();
            $table->string('reason')->nullable();

            // Calculated or imported values
            $table->string('name')->nullable();
            $table->longText('approvalWorkflowMap');
            $table->boolean('doesHaveFTECalculation')->default(false);
            $table->boolean('doesDeductBreaks')->default(false);
            $table->decimal('hoursBalance', 16, 10)->nullable();

            // Attachment settings
            $table->boolean('attachmentRequired')->default(false);
            $table->boolean('attachmentRequiredHasDuration')->default(false);
            $table->decimal('attachmentRequiredDurationValue', 16, 10)->nullable();
            $table->char('attachmentRequiredDurationType', 1)->nullable(); // H => Hours | D => Days

            // External calendar stuff
            $table->string('externalCalendarEmail')->nullable();
            $table->string('externalCalendarId')->nullable();

            // Status
            $table->char('status', 1)->default('W');
            // W => Waiting approval
            // B => Pending final approval
            // A => Approved
            // P => Past
            // D => Declined
            // C => Cancelled (by manager/payroll officer)
            // E => Cancelled - Historic (by manager/payroll officer)
            // N => Cancelled (by staff)
            // L => Locked (need to be related to another request)
            // R => Replaced (need to be related to another request)
            // H => Replaced - Historic (need to be related to another request)

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeLeave

        //region Table: ExcessTimeLeaveDay

        Schema::create('ExcessTimeLeaveDay', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('excessTimeLeave_id');
            $table->foreign('excessTimeLeave_id', 'etld_etl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'etld_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('userShift_id')->nullable();
            $table->foreign('userShift_id', 'etld_us_fn')->references('id')->on('UserShift')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('userShiftDay_id')->nullable();
            $table->foreign('userShiftDay_id', 'etld_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('restrict');
            $table->date('date');

            // General data
            $table->boolean('isFullDay')->default(false);
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();
            $table->text('breaks'); // Json

            // Calculated or imported values
            $table->boolean('doesHaveFTECalculation')->default(false);
            $table->boolean('doesDeductBreaks')->default(false);
            $table->decimal('hoursBalance', 16, 10)->nullable();
            $table->char('status', 1)->default('W'); // Same as ExcessTimeLeave (imported from there)

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeLeaveDay

        //region Table: ExcessTimeLeaveApproval

        Schema::create('ExcessTimeLeaveApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('excessTimeLeave_id');
            $table->foreign('excessTimeLeave_id', 'etla_etl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('cascade');

            // Relations
            $table->unsignedBigInteger('manager_id')->nullable();
            $table->foreign('manager_id', 'etla_manager_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            // General data
            $table->char('expectedUserSystemAccess', 1)->default('M'); // M => Manager | A => Admin | P => Payroll officer
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->default(1);
            $table->text('notes')->nullable();

            // Status data
            $table->char('status', 1)->default('W'); // W => Waiting to be approved | A => Approved | D => Declined

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeLeaveApproval

        //region Table: ExcessTimeLeaveStatusHistory

        Schema::create('ExcessTimeLeaveStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('excessTimeLeave_id');
            $table->foreign('excessTimeLeave_id', 'etlsh_etl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('otherExcessTimeLeave_id')->nullable();
            $table->foreign('otherExcessTimeLeave_id', 'etlsh_oetl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'etlsh_auser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->unsignedBigInteger('excessTimeLeaveApproval_id')->nullable();
            $table->foreign('excessTimeLeaveApproval_id', 'etlsh_etla_fn')->references('id')->on('ExcessTimeLeaveApproval')->onUpdate('cascade')->onDelete('cascade');

            // General data
            $table->dateTime('dateTime')->nullable();
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();

            // Status
            $table->char('status', 1)->default('W'); // Same as ExcessTimeLeave
            $table->char('oldStatus', 1)->nullable(); // Same as ExcessTimeLeave

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeLeaveStatusHistory

        //region Table: ExcessTimeLeaveAttachmentFile

        Schema::create('ExcessTimeLeaveAttachmentFile', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('excessTimeLeave_id');
            $table->foreign('excessTimeLeave_id', 'etlaf_etl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('mediaFile_id')->nullable();
            $table->foreign('mediaFile_id', 'etlaf_mf_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('cascade');

            // Timestamps
            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: ExcessTimeLeaveAttachmentFile

        //region Table: UserExcessTimeBalanceEntry

        Schema::create('UserExcessTimeBalanceEntry', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('userExcessTimeBalance_id');
            $table->foreign('userExcessTimeBalance_id', 'uetbe_uetb_fn')->references('id')->on('UserExcessTimeBalance')->onUpdate('cascade')->onDelete('cascade');

            // General information
            $table->char('entryType', 2)->default('MA');
            /*
            Possible values:
              - MA => Manually Add Balance
              - AA => Automatically Add Balance (relate to TimeSheet [field timeSheet_id] / TimeSheetDay [field timeSheetDay_id])
              - MR => Manually Deduct Balance
              - CM => Commit (Transfer from Accrued to Committed) (relate to waiting to approve/approved future Toil [field excessTimeLeave_id])   // TODO: Check field names when requests are done
              - UC => Uncommit (Transfer from Committed to Accrued) (relate to declined or cancelled Toil [field excessTimeLeave_id])   // TODO: Check field names when requests are done
              - US => Use as TOIL (relate to approved past Toil [fields excessTimeLeave_id] and usedEntry_id)   // TODO: Check field names when requests are done
              - RE => Returned (relate to cancelled past Toil [field excessTimeLeave_id] and usedEntry_id)   // TODO: Check field names when requests are done
              - OP => Paid as Overtime (relate to PayRunItem [field payRunItem_id and usedEntry_id])
              - MC => Manually Cancel (relate to cancelled [another] UserExcessTimeBalanceEntry field [cancelledEntry_id])
              - EX => Expired (relate to another UserExcessTimeBalanceEntry field [expiredEntry_id])
             */

            $table->dateTime('entryDateTime');
            $table->dateTime('currentDateTime')->nullable();
            $table->dateTime('validityDateTime')->nullable(); // ( MA | AA )
            $table->text('description')->nullable();
            $table->text('comments')->nullable();
            $table->boolean('isDeleted')->default(false);

            // Balances
            $table->decimal('entryBalance', 16, 10)->default(0); // The total balance for that entry (never changes)
            $table->decimal('remainingBalance', 16, 10)->default(0); // Balance still available for this entry - if used or deducted it will be deducted based on other entries ( MA | AA )
            $table->decimal('currentHourlyRate', 16, 10)->nullable(); // Used to calculate values when 'excessTimeIsPaidOutAtType' from 'Settings has value 'E'

            // Relations
            $table->unsignedBigInteger('timeSheet_id')->nullable(); // TimeSheet that TOIL was earnt ( MA | AA )
            $table->foreign('timeSheet_id', 'uetbe_ts_fn')->references('id')->on('TimeSheet')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('timeSheetDay_id')->nullable(); // TimeSheetDay that TOIL was earnt ( MA | AA )
            $table->foreign('timeSheetDay_id', 'uetbe_tsd_fn')->references('id')->on('TimeSheetDay')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('cancelledEntry_id')->nullable(); // UserExcessTimeBalanceEntry that was cancelled ( MC )
            $table->foreign('cancelledEntry_id', 'uetbe_ne_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('expiredEntry_id')->nullable(); // UserExcessTimeBalanceEntry that was expired ( EX )
            $table->foreign('expiredEntry_id', 'uetbe_ee_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('committedEntry_id')->nullable(); // UserExcessTimeBalanceEntry that was committed before ( UC )
            $table->foreign('committedEntry_id', 'uetbe_ce_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('usedEntry_id')->nullable(); // UserExcessTimeBalanceEntry committed that was used or paid ( US | OP )
            $table->foreign('usedEntry_id', 'uetbe_ue_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('returnedEntry_id')->nullable(); // UserExcessTimeBalanceEntry accrued that was returned ( RE )
            $table->foreign('returnedEntry_id', 'uetbe_re_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('excessTimeLeave_id')->nullable(); // Future request that committed this balance ( CM ) | Declined or cancelled request that uncommitted this balance ( UC ) | Past approved request that has used this balance ( US )
            $table->foreign('excessTimeLeave_id', 'uetbe_etl_fn')->references('id')->on('ExcessTimeLeave')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('payRunItem_id')->nullable(); // PayRunItem associated to balance paid as Overtime ( OP )
            $table->foreign('payRunItem_id', 'uetbe_pr_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('manager_id')->nullable(); // Manager that manually created this entry (MA|MR|MC)
            $table->foreign('manager_id', 'uetbe_m_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('attachmentFile_id')->nullable(); // Attached file
            $table->foreign('attachmentFile_id', 'uetbe_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: UserExcessTimeBalanceEntry

        //region Table: UserExcessTimeBalanceEntryCommitted

        Schema::create('UserExcessTimeBalanceEntryCommitted', function (Blueprint $table) {
            // Used when a entry types as committed (CM) is created, and balances for multiple accrued entries (MA | AA) need to be deducted
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('entryCommitted_id');
            $table->foreign('entryCommitted_id', 'uetbec_ec_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('entryAccrued_id');
            $table->foreign('entryAccrued_id', 'uetbec_ea_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            // Balances
            $table->decimal('balance', 16, 10);

            $table->timestamps();
        });

        //endregion Table: UserExcessTimeBalanceEntryCommitted

        //region Table: UserExcessTimeBalanceEntryDeducted

        Schema::create('UserExcessTimeBalanceEntryDeducted', function (Blueprint $table) {
            // Used when a entry types as manually deducted (MR) is created, and balances for multiple accrued entries (MA | AA) need to be deducted
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('entryDeducted_id');
            $table->foreign('entryDeducted_id', 'uetbed_ed_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('entryAccrued_id');
            $table->foreign('entryAccrued_id', 'uetbed_ea_fn')->references('id')->on('UserExcessTimeBalanceEntry')->onUpdate('cascade')->onDelete('cascade');

            // Balances
            $table->decimal('balance', 16, 10);

            $table->timestamps();
        });

        //endregion Table: UserExcessTimeBalanceEntryDeducted

        //region Table: TimeSheet

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->decimal('hoursScheduled', 16, 10)->default(0);
            $table->decimal('hoursRecorded', 16, 10)->default(0);
            $table->decimal('hoursRecordedCalculation', 16, 10)->default(0);
            $table->decimal('hoursWorked', 16, 10)->default(0);
            $table->decimal('hoursWorkedCalculation', 16, 10)->default(0);
            $table->decimal('hoursExcessTimePaid', 16, 10)->default(0);
            $table->decimal('hoursExcessTimePaidAdjusted', 16, 10)->default(0);
            $table->decimal('valueExcessTimePaid', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeAccrued', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeAccruedAdjusted', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeUsed', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeUsedCalculation', 16, 10)->default(0);
            $table->decimal('hoursLeaveEarnt', 16, 10)->default(0);
            $table->decimal('hoursLeaveUsed', 16, 10)->default(0);
            $table->decimal('hoursLeaveUsedCalculation', 16, 10)->default(0);
            $table->decimal('hoursRosteredTimeOffEarnt', 16, 10)->default(0);
            $table->decimal('hoursRosteredTimeOffUsed', 16, 10)->default(0);
            $table->decimal('hoursRosteredTimeOffUsedCalculation', 16, 10)->default(0);
            $table->decimal('hoursAllowance', 16, 10)->default(0);
            $table->decimal('hoursAllowanceCalculation', 16, 10)->default(0);
            $table->decimal('milesAllowance', 16, 10)->default(0);
            $table->decimal('valueAllowance', 16, 10)->default(0);
            $table->decimal('hoursMissing', 16, 10)->default(0);
        });

        //endregion Table: TimeSheet

        //region Table: TimeSheetDay

        Schema::table('TimeSheetDay', function (Blueprint $table) {
            $table->decimal('hoursExcessTimeAccrued', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeAccruedAdjusted', 16, 10)->default(0);
            $table->decimal('hoursExcessTimePaid', 16, 10)->default(0);
            $table->decimal('hoursExcessTimePaidAdjusted', 16, 10)->default(0);
            $table->decimal('valueExcessTimePaid', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeUsed', 16, 10)->default(0);
            $table->decimal('hoursExcessTimeUsedCalculation', 16, 10)->default(0);
            $table->decimal('glidePeriodHoursExcessTimeAccrued', 16, 10)->default(0);
            $table->decimal('glidePeriodHoursExcessTimeAccruedAdjusted', 16, 10)->default(0);
            $table->decimal('glidePeriodHoursExcessTimePaid', 16, 10)->default(0);
            $table->decimal('glidePeriodHoursExcessTimePaidAdjusted', 16, 10)->default(0);
            $table->decimal('glidePeriodValueExcessTimePaid', 16, 10)->default(0);
            $table->decimal('glidePeriodHoursExcessTimeUsed', 16, 10)->default(0);
        });

        //endregion Table: TimeSheetDay

        //region Table: TimeSheetItemDayTime

        Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
            $table->unsignedBigInteger('excessTimeLeaveDay_id')->nullable();
            $table->foreign('excessTimeLeaveDay_id', 'tsidt_etld_fn')->references('id')->on('ExcessTimeLeaveDay')->onUpdate('cascade')->onDelete('cascade');
        });

        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheetExcessTime

        Schema::create('TimeSheetExcessTime', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->char('type', 1)->default('W'); // W (Weekday) | P (Period) | S (Saturday) | U (Sunday) | H (Public holiday)
            $table->unsignedBigInteger('timeSheetItemDayTime_id')->nullable(); // Only if type is not P (Period)
            $table->foreign('timeSheetItemDayTime_id', 'tset_tsidt_fn')->references('id')->on('TimeSheetItemDayTime')->onUpdate('cascade')->onDelete('cascade');
            $table->unique(['timeSheetItemDayTime_id'], 'tset_tsidt_uq');

            // General information
            $table->date('date')->nullable(); // Only if type is not P (Period)
            $table->date('startDate')->nullable(); // Only for type = P (Period)
            $table->date('endDate')->nullable(); // Only for type = P (Period)
            $table->text('comments')->nullable();

            // Relations to time-sheet models
            $table->unsignedBigInteger('calendarDate_id')->nullable(); // Only if type is not P (Period)
            $table->foreign('calendarDate_id', 'tset_cd_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('startCalendarDate_id')->nullable(); // Only for type = P (Period)
            $table->foreign('startCalendarDate_id', 'tset_scd_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');
            $table->unsignedBigInteger('endCalendarDate_id')->nullable(); // Only for type = P (Period)
            $table->foreign('endCalendarDate_id', 'tset_ecd_fn')->references('id')->on('CalendarDate')->onUpdate('cascade')->onDelete('restrict');

            // Values
            $table->decimal('totalHours', 16, 10)->default(0); // calculated - from blocks
            $table->decimal('hourlyRate', 16, 10)->nullable(); // calculated - from relationship to UserPayType through TimeSheetItemDayTime->UserShiftDay

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTime

        //region Table: TimeSheetExcessTimeBlock

        Schema::create('TimeSheetExcessTimeBlock', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTime_id');
            $table->foreign('timeSheetExcessTime_id', 'tsetb_tset_fn')->references('id')->on('TimeSheetExcessTime')->onUpdate('cascade')->onDelete('cascade');

            // Values
            $table->decimal('hours', 16, 10)->nullable();
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeBlock

        //region Table: TimeSheetExcessTimeItem

        Schema::create('TimeSheetExcessTimeItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTime_id');
            $table->foreign('timeSheetExcessTime_id', 'tseti_tset_fn')->references('id')->on('TimeSheetExcessTime')->onUpdate('cascade')->onDelete('cascade');

            // Relationship
            $table->unsignedBigInteger('originalTimeSheetExcessTimeItem_id')->nullable();
            $table->foreign('originalTimeSheetExcessTimeItem_id', 'tseti_otseti_fn')->references('id')->on('TimeSheetExcessTimeItem')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'tsetr_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            // Values
            $table->decimal('totalHoursPaid', 16, 10)->default(0); // calculated - from rules
            $table->decimal('totalHoursAccrued', 16, 10)->default(0); // calculated - from rules
            $table->decimal('totalHoursPaidAdjusted', 16, 10)->default(0); // calculated - from rules
            $table->decimal('totalHoursAccruedAdjusted', 16, 10)->default(0); // calculated - from rules
            $table->boolean('isPlanned')->default(true);

            // Specific rules
            $table->boolean('doesHaveBonusTimeInLieu')->default(false); // Only for types S (Saturday), U (Sunday) or H (public holidays)
            $table->decimal('bonusTimeInLieuAmount', 16, 10)->default(0); // Only for types S (Saturday), U (Sunday) or H (public holidays) and if doesHaveBonusTimeInLieu is true

            // Status
            $table->char('status', 1)->default('W'); // W (Waiting for approval) | B (Pending final approval) | A (Approved) | D (Declined) | O (Overwritten)
            $table->boolean('isManual')->default(false);

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'tsetr_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeItem

        //region Table: TimeSheetExcessTimeItemRule

        Schema::create('TimeSheetExcessTimeItemRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTimeItem_id');
            $table->foreign('timeSheetExcessTimeItem_id', 'tsetir_tseti_fn')->references('id')->on('TimeSheetExcessTimeItem')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('excessTimeGroupSectionLevelRule_id')->nullable();
            $table->foreign('excessTimeGroupSectionLevelRule_id', 'tsetir_etgsr_fn')->references('id')->on('ExcessTimeGroupSectionLevelRule')->onUpdate('cascade')->onDelete('restrict');

            // General information
            $table->char('type', 1)->default('A'); // A (Accrued) | P (Paid) | U (Unpaid)
            $table->string('name')->nullable();
            $table->text('comments')->nullable();
            $table->boolean('doesAccrueLeave')->default(false);

            // Values
            $table->decimal('hours')->default(0);
            $table->decimal('ratio')->default(0);
            $table->decimal('hoursAdjusted')->default(0);

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeItemRule

        //region Table: TimeSheetExcessTimeItemApproval

        Schema::create('TimeSheetExcessTimeItemApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTimeItem_id');
            $table->foreign('timeSheetExcessTimeItem_id', 'tsetia_tseti_fn')->references('id')->on('TimeSheetExcessTimeItem')->onUpdate('cascade')->onDelete('cascade');

            // Relationship
            $table->unsignedBigInteger('manager_id')->nullable();
            $table->foreign('manager_id', 'tsetia_m_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            // Approval Data
            $table->char('expectedUserSystemAccess', 1)->default('M');
            $table->dateTime('answerDateTime')->nullable();
            $table->integer('level')->nullable();
            $table->text('notes')->nullable();

            // Status
            $table->char('status', 1)->default('W'); // W (Waiting for approval) | A (Approved) | D (Declined)

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeItemApproval

        //region Table: TimeSheetExcessTimeItemStatusHistory

        Schema::create('TimeSheetExcessTimeItemStatusHistory', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('timeSheetExcessTimeItem_id');
            $table->foreign('timeSheetExcessTimeItem_id', 'tsetish_tseti')->references('id')->on('TimeSheetExcessTimeItem')->onUpdate('cascade')->onDelete('cascade');

            // Relationships
            $table->unsignedBigInteger('otherTimeSheetExcessTimeItem_id')->nullable();
            $table->foreign('otherTimeSheetExcessTimeItem_id', 'tsetish_otseti')->references('id')->on('TimeSheetExcessTimeItem')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'tsetish_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->unsignedBigInteger('timeSheetExcessTimeItemApproval_id')->nullable();
            $table->foreign('timeSheetExcessTimeItemApproval_id', 'tsetish_tsetia_fn')->references('id')->on('TimeSheetExcessTimeItemApproval')->onUpdate('cascade')->onDelete('cascade');

            // History data
            $table->dateTime('dateTime')->nullable();
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();

            // Status
            $table->char('status', 1)->default('W'); // W (Waiting for approval) | B (Pending final approval) | A (Approved) | D (Declined) | O (Overwritten)
            $table->char('oldStatus', 1)->nullable(); // W (Waiting for approval) | B (Pending final approval) | A (Approved) | D (Declined) | O (Overwritten)

            $table->timestamps();
        });

        //endregion Table: TimeSheetExcessTimeItemStatusHistory
    }

    public function down(): void
    {
        //region TODO: Remove this before deploying

        if (Schema::hasColumn('TimeSheetItemDayTime', 'excessTimeLeave_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeave_id');
            });
        }

        if (Schema::hasColumn('TimeSheet', 'canChangeExcessTimeRule')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('canChangeExcessTimeRule');
            });
        }

        try {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropForeign('us_etg_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('UserShift', 'excessTimeGroup_id')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('excessTimeGroup_id');
            });
        }
        //endregion TODO: Remove this before deploying

        //region Table: TimeSheetExcessTimeItemStatusHistory

        Schema::dropIfExists('TimeSheetExcessTimeItemStatusHistory');

        //endregion Table: TimeSheetExcessTimeItemStatusHistory

        //region Table: TimeSheetExcessTimeItemApproval

        Schema::dropIfExists('TimeSheetExcessTimeItemApproval');

        //endregion Table: TimeSheetExcessTimeItemApproval

        //region Table: TimeSheetExcessTimeItemRule

        Schema::dropIfExists('TimeSheetExcessTimeItemRule');

        //endregion Table: TimeSheetExcessTimeItemRule

        //region Table: TimeSheetExcessTimeItem

        Schema::dropIfExists('TimeSheetExcessTimeItem');

        //endregion Table: TimeSheetExcessTimeItem

        //region Table: TimeSheetExcessTimeBlock

        Schema::dropIfExists('TimeSheetExcessTimeBlock');

        //endregion Table: TimeSheetExcessTimeBlock

        //region Table: TimeSheetExcessTime

        Schema::dropIfExists('TimeSheetExcessTime');

        //endregion Table: TimeSheetExcessTime

        //region Table: TimeSheetItemDayTime

        try {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropForeign('tsidt_etld_fn');
            });
        } catch (Throwable) {
        }
        if (Schema::hasColumn('TimeSheetItemDayTime', 'excessTimeLeaveDay_id')) {
            Schema::table('TimeSheetItemDayTime', function (Blueprint $table) {
                $table->dropColumn('excessTimeLeaveDay_id');
            });
        }

        //endregion Table: TimeSheetItemDayTime

        //region Table: TimeSheetDay

        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeAccrued')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeAccruedAdjusted')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccruedAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimePaidAdjusted')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaidAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'valueExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('valueExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeUsed')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsed');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'hoursExcessTimeUsedCalculation')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeAccrued')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeAccruedAdjusted')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeAccruedAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimePaidAdjusted')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimePaidAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodValueExcessTimePaid')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodValueExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheetDay', 'glidePeriodHoursExcessTimeUsed')) {
            Schema::table('TimeSheetDay', function (Blueprint $table) {
                $table->dropColumn('glidePeriodHoursExcessTimeUsed');
            });
        }

        //endregion Table: TimeSheetDay

        //region Table: TimeSheet

        if (Schema::hasColumn('TimeSheet', 'hoursScheduled')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursScheduled');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursRecorded')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRecorded');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursRecordedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRecordedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursWorked')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursWorked');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursWorkedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursWorkedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimePaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimePaidAdjusted')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimePaidAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueExcessTimePaid')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueExcessTimePaid');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeAccrued')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccrued');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeAccruedAdjusted')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeAccruedAdjusted');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsed');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursExcessTimeUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursExcessTimeUsedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursLeaveEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursLeaveUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveUsed');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursLeaveUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursLeaveUsedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursRosteredTimeOffEarnt')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffEarnt');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursRosteredTimeOffUsed')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffUsed');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursRosteredTimeOffUsedCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursRosteredTimeOffUsedCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursAllowanceCalculation')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursAllowanceCalculation');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'milesAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('milesAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'valueAllowance')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('valueAllowance');
            });
        }
        if (Schema::hasColumn('TimeSheet', 'hoursMissing')) {
            Schema::table('TimeSheet', function (Blueprint $table) {
                $table->dropColumn('hoursMissing');
            });
        }

        //endregion Table: TimeSheet

        //region Table: UserExcessTimeBalanceEntryDeducted

        Schema::dropIfExists('UserExcessTimeBalanceEntryDeducted');

        //endregion Table: UserExcessTimeBalanceEntryDeducted

        //region Table: UserExcessTimeBalanceEntryCommitted

        Schema::dropIfExists('UserExcessTimeBalanceEntryCommitted');

        //endregion Table: UserExcessTimeBalanceEntryCommitted

        //region Table: UserExcessTimeBalanceEntry

        Schema::dropIfExists('UserExcessTimeBalanceEntry');

        //endregion Table: UserExcessTimeBalanceEntry

        //region Table ExcessTimeLeaveAttachmentFile

        Schema::dropIfExists('ExcessTimeLeaveAttachmentFile');

        //endregion Table ExcessTimeLeaveAttachmentFile

        //region Table ExcessTimeLeaveStatusHistory

        Schema::dropIfExists('ExcessTimeLeaveStatusHistory');

        //endregion Table ExcessTimeLeaveStatusHistory

        //region Table ExcessTimeLeaveApproval

        Schema::dropIfExists('ExcessTimeLeaveApproval');

        //endregion Table ExcessTimeLeaveApproval

        //region Table ExcessTimeLeaveDay

        Schema::dropIfExists('ExcessTimeLeaveDay');

        //endregion Table ExcessTimeLeaveDay

        //region Table ExcessTimeLeave

        Schema::dropIfExists('ExcessTimeLeave');

        //endregion Table ExcessTimeLeave

        //region Table: UserExcessTimeBalance

        Schema::dropIfExists('UserExcessTimeBalance');

        //endregion Table: UserExcessTimeBalance

        //region Table: UserShiftExcessTimeGroup

        Schema::dropIfExists('UserShiftExcessTimeGroup');

        //endregion Table: UserShiftExcessTimeGroup

        //region Table: ExcessTimeGroupSectionLevelRule

        Schema::dropIfExists('ExcessTimeGroupSectionLevelRule');

        //endregion Table: ExcessTimeGroupSectionLevelRule

        //region Table: ExcessTimeGroupSectionLevel

        Schema::dropIfExists('ExcessTimeGroupSectionLevel');

        //endregion Table: ExcessTimeGroupSectionLevel

        //region Table: ExcessTimeGroupSection

        Schema::dropIfExists('ExcessTimeGroupSection');

        //endregion Table: ExcessTimeGroupSection

        //region Table: ExcessTimeGroup

        Schema::dropIfExists('ExcessTimeGroup');

        //endregion Table: ExcessTimeGroup

        //region Table: Settings

        // General
        if (Schema::hasColumn('Settings', 'doesAllowOverridingExcessTime')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAllowOverridingExcessTime');
            });
        }
        if (Schema::hasColumn('Settings', 'doesRequireCommentToRecordExcessTime')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesRequireCommentToRecordExcessTime');
            });
        }

        // Balance settings
        if (Schema::hasColumn('Settings', 'excessTimeBalanceExpires')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeBalanceExpires');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeBalanceExpiresWeeks')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeBalanceExpiresWeeks');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeBalanceExpiresDate')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeBalanceExpiresDate');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeBalanceExpiredAction')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeBalanceExpiredAction');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeBalanceRetainedAfterExpiry')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeBalanceRetainedAfterExpiry');
            });
        }
        if (Schema::hasColumn('Settings', 'maximumAllowedExcessTimeBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('maximumAllowedExcessTimeBalance');
            });
        }
        if (Schema::hasColumn('Settings', 'maximumAllowedExcessTimeBalanceAction')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('maximumAllowedExcessTimeBalanceAction');
            });
        }
        if (Schema::hasColumn('Settings', 'alertExcessTimeBalanceReachesAmount')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('alertExcessTimeBalanceReachesAmount');
            });
        }
        if (Schema::hasColumn('Settings', 'alertExcessTimeBalanceReachesAction')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('alertExcessTimeBalanceReachesAction');
            });
        }

        // Paying out balances
        if (Schema::hasColumn('Settings', 'doesAllowExcessTimeBalanceToBePaidOnRequest')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAllowExcessTimeBalanceToBePaidOnRequest');
            });
        }
        if (Schema::hasColumn('Settings', 'doesPayExcessTimeOnTermination')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesPayExcessTimeOnTermination');
            });
        }
        if (Schema::hasColumn('Settings', 'excessTimeIsPaidOutAtType')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('excessTimeIsPaidOutAtType');
            });
        }

        // Taking as leave
        if (Schema::hasColumn('Settings', 'doesApplyFteCalculationOnTakingExcessTimeLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesApplyFteCalculationOnTakingExcessTimeLeave');
            });
        }
        if (Schema::hasColumn('Settings', 'doesDeductBreaksOnTakingExcessTimeLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesDeductBreaksOnTakingExcessTimeLeave');
            });
        }
        if (Schema::hasColumn('Settings', 'doesAccrueLeaveOnTakingExcessTimeLeave')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAccrueLeaveOnTakingExcessTimeLeave');
            });
        }
        if (Schema::hasColumn('Settings', 'maximumNegativeAllowedExcessTimeBalance')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('maximumNegativeAllowedExcessTimeBalance');
            });
        }

        //endregion Table: Settings
    }
};
