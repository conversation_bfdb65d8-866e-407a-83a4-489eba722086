form.form-validate(role='form' method='POST' action='javascript:' ng-submit='$f.submit(false)' name='$f.form')
  .panel.panel-default
    .panel-heading
      h4.h4.m0 Edit leave request
    .panel-body
      .row.form-horizontal
        .col-xs-6
          elt-toggle(ng-model='$f.data.isFullDay' elt-name='isFullDay' elt-label='Apply for full day/s leave' elt-help-text='modals.leave-request|fields.isFullDay' elt-line='false' elt-change-tt='' ng-change='$f.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!$f.data.isFullDay && $f.selectedShift.type.id != "G"')
          elt-toggle(ng-model='$f.data.isApplyingByDuration' elt-name='isApplyingByDuration' elt-label='Apply by duration' elt-help-text='modals.leave-request|fields.isApplyingByDuration' elt-line='false' elt-change-tt='' ng-change='$f.changeRequestData()')

      .row
        .col-xs-12.col-md-6
          .form-group
            label.label-control(for='LeaveEdit_startDate') Start date
            p.input-group.mb-sm
              input.form-control(id="LeaveEdit_startDate" name='inputStartDate' type='text' date-picker ng-model='$f.data.startDate' uib-datepicker-popup='{{settings.system.settings.dateFormat}}' is-open='date_pickers["LeaveEdit_openStartDate"].opened' close-text='Close' ng-change='$f.changeStartDate()' datepicker-options="{minDate: $f.minDate, maxDate: $f.maxDate}" ng-required='true')
              span.input-group-btn
                button.btn.btn-default(type='button' ng-click='openDatePicker($event, "LeaveEdit_openStartDate")')
                  em.fa.fa-calendar
        .col-xs-12.col-md-6(ng-if='$f.selectedShift.type.id != "G" && !$f.data.isFullDay')
          .form-group
            label.label-control(for='LeaveEdit_startTime') Start time
            div.input-group(id='LeaveEdit_startTime' timepicker-popup ng-model="$f.data.startTime" is-meridian='settings.system.settings.meridian' is-date="true" ng-required='true' ng-change='$f.changeRequestData()')

        .clearfix(ng-if='$f.selectedShift.type.id != "G" && !$f.data.isFullDay')

        .col-xs-12.col-md-6(ng-if='!!$f.data.isFullDay || !$f.data.isApplyingByDuration')
          .form-group
            label.label-control(for='LeaveEdit_endDate') End date
            p.input-group.mb-sm
              input.form-control(id="LeaveEdit_endDate" name='inputEndDate' type='text' date-picker ng-model='$f.data.endDate' uib-datepicker-popup='{{settings.system.settings.dateFormat}}' is-open='date_pickers["LeaveEdit_openEndDate"].opened' close-text='Close' datepicker-options="{minDate: !!$f.data.startDate ? $f.data.startDate : $f.minDate, maxDate: $f.maxDate}" ng-required='true' ng-change='$f.changeRequestData()')
              span.input-group-btn
                button.btn.btn-default(type='button' ng-click='openDatePicker($event, "LeaveEdit_openEndDate")')
                  em.fa.fa-calendar
        .col-xs-12.col-md-6(ng-if='$f.selectedShift.type.id != "G" && !$f.data.isFullDay && !$f.data.isApplyingByDuration')
          .form-group
            label.label-control(for='LeaveEdit_endTime') End time
            div.input-group(id='LeaveEdit_endTime' timepicker-popup ng-model="$f.data.endTime" is-meridian='settings.system.settings.meridian' is-date="true" ng-required='true' ng-change='$f.changeRequestData()')

        .col-xs-12.col-md-6(ng-if='$f.selectedShift.type.id != "G" && !$f.data.isFullDay && !!$f.data.isApplyingByDuration')
          .form-group(elt-help-text='modals.leave-request|fields.duration')
            div
              label.label-md(for='LeaveEdit_duration') Duration (hours)
              .pull-right
                label.switch
                  small.mr-sm.text-normal Show in hour format
                  input(id='toggleHourFormat' type='checkbox' ng-model="$ctrl.toggleHourFormat")
                  span
            div(ng-if='!$ctrl.toggleHourFormat')
              input.form-control(id='LeaveEdit_duration' type='text' name='hours' ng-model='$f.data.duration' input-mask input-options='{"name": "number", "allowMinus": "false"}' ng-required='true' ng-change='$ctrl.updateFormattedHours($f)')
            div(ng-if='!!$ctrl.toggleHourFormat')
              div.input-group(id='LeaveEdit_durationFormatted' timepicker-popup ng-model='$f.data.durationFormatted' is-meridian='false' is-date="false" date="{{$f.data.startDate}}" ng-change='$ctrl.updateUnFormattedHours($f)')

      .row(ng-if='$f.selectedShift.type.id === "G" && !$f.data.isFullDay')
        .col-xs-12.col-md-6(ng-if='!!$f.selectedShift.glideRequireTimeBlock')
          .form-group
            label.label-control(for='LeaveEdit_glideDailyStartTime') Daily start time
            div.input-group(id='LeaveEdit_glideDailyStartTime' timepicker-popup ng-model="$f.data.glideDailyStartTime" is-meridian='settings.system.settings.meridian' ng-required='true' ng-change='$f.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!!$f.selectedShift.glideRequireTimeBlock == true')
          .form-group
            label.label-control(for='PersonalLeaveEdit_glideDailyEndTime') Daily end time
            div.input-group(id='PersonalLeaveEdit_glideDailyEndTime' timepicker-popup ng-model="$f.data.glideDailyEndTime" is-meridian='settings.system.settings.meridian' ng-required='true' ng-change='$f.changeRequestData()')
        .col-xs-12.col-md-6(ng-if='!$f.selectedShift.glideRequireTimeBlock')
          .form-group
            label.label-control(for="PersonalLeaveEdit_glideDailyHours") Enter daily hours
            input.form-control(id="PersonalLeaveEdit_glideDailyHours" type='text' name='glideDailyHours' ng-model='$f.data.glideDailyHours' input-mask input-options='{"name": "number"}' ng-required='true' ng-change='$f.changeRequestData()')

      div(ng-if='!$f.calculated')
        .row.mb-lg
          .col-xs-12.text-center
            button.btn.btn-info(type='button' ng-click='$f.calculateRequest()' ng-disabled='$f.isCalculating || !$f.canCalculate()')
              i.fa(ng-class='{"fa-circle-o-notch fa-spin": $f.isCalculating}')
              | {{$f.isCalculating ? '&nbsp; Calculating' : 'Check / Calculate' }}

      div(ng-if='!!$f.calculatedError')
        .row
          .col-xs-12.col-md-6.col-md-offset-3.mt.mb
            .p.rounded.bg-danger
              .row
                .col-xs-12.text-center
                  h4.h4 {{$f.calculatedError}}
              div(ng-if='!!$f.calculatedClashes')
                .row
                  .col-xs-12
                    hr.hr.mt.mb
                .row
                  .col-xs-12.text-center
                    a.btn.btn-default.btn-xs.m0.pt0.pb0.pl.pr.text-nowrap(type='button' href='javascript:' ng-click='$ctrl.showModalTimeFrame($f)') See more

      div(ng-if='!!$f.calculated')
        div(ng-include="app.baseLayoutPath + '/modals/leave-request-old/_common/form/availability-box.html'" ng-init='$availabilityBoxForm = $f;')

        .row
          .col-xs-12
            .form-group
              label.label-control(for='LeaveEdit_inputReason') Reason / comments{{!!$f.calculated.isCommentRequired ? ' (Required)' : ''}}
              textarea.form-control(id='LeaveEdit_inputReason' name='inputReason' ng-model='$f.data.reason' ng-required='!$f.calculated.isCommentRequired')

        .row
          .col-xs-12.col-md-6
            elt-attachment-file(id='LeaveEdit_attachmentFile' elt-label='{{"Attachment file" + (!!$f.calculated.isAttachmentRequired ? " (Required)" : "")}}' name='attachmentFile' ng-model='$f.data.attachmentFile' elt-file-options='{{settings.form.LeaveRequest.attachmentFileOptions}}' ng-required='$f.calculated.isAttachmentRequired')

      .row
        .col-xs-12
          hr.mt0.mb-lg
      .row.visible-sm.visible-md.visible-lg
        .col-xs-12.text-right
          button.btn.btn-default.ml(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Cancel editing
          button.btn.btn-success.ml(ng-if='!!$f.canSubmit()' type='submit' ng-disabled='!!$f.isSubmitting')
            i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$f.isSubmitting}')
            | {{!!$f.isSubmitting ? '&nbsp; Wait' : (!!$ctrl.fromPayrollOfficer ? 'Submit for approval' : 'Submit new request') }}
          button.btn.btn-success.ml(ng-if="!!$f.canApprove()" type="button" ng-click="$f.submit(true)" ng-disabled='!!$f.isSubmitting')
            i.fa(ng-class='{"fa-circle-o-notch fa-spin": !!$f.isSubmitting}')
            | {{!!$f.isSubmitting ? '&nbsp; Wait' : 'Approve' }}
      .row.visible-xs
        .col-xs-6
          button.btn.btn-default.btn-block(type='button' ng-click='$f.close()' ng-disabled='!!$f.isSubmitting')
            | Cancel editing
        .col-xs-6
          button.btn.btn-success.btn-block(ng-if='$f.canSubmit()' type='submit' ng-disabled='$f.isSubmitting')
            i.fa(ng-class='{"fa-circle-o-notch fa-spin": $f.isSubmitting}')
            | {{$f.isSubmitting ? '&nbsp; Wait' : 'Submit new request' }}
