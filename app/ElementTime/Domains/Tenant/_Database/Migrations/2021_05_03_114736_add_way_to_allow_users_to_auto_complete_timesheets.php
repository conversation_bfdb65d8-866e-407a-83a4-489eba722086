<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Settings', function (Blueprint $table) {
            $table->boolean('doesAllowUsersToAutoCompleteTimeSheets')->default(false)->after('doesRequireConfirmationOnApprovingTimeSheets');
        });

        Schema::table('User', function (Blueprint $table) {
            $table->boolean('doesAllowToAutoCompleteTimeSheets')->default(false)->after('optOutTimer');
        });

        Schema::table('LeaveRequest', function (Blueprint $table) {
            $table->boolean('isFromAutoComplete')->default(false)->after('originalSplit_id');
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('LeaveRequest', 'isFromAutoComplete')) {
            Schema::table('LeaveRequest', function (Blueprint $table) {
                $table->dropColumn('isFromAutoComplete');
            });
        }

        if (Schema::hasColumn('User', 'doesAllowToAutoCompleteTimeSheets')) {
            Schema::table('User', function (Blueprint $table) {
                $table->dropColumn('doesAllowToAutoCompleteTimeSheets');
            });
        }

        if (Schema::hasColumn('Settings', 'doesAllowUsersToAutoCompleteTimeSheets')) {
            Schema::table('Settings', function (Blueprint $table) {
                $table->dropColumn('doesAllowUsersToAutoCompleteTimeSheets');
            });
        }
    }
};
