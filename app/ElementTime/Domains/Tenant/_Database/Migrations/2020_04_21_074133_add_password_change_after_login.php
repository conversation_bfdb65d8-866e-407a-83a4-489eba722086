<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('UserPasswordChangeRequest', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id', 'upcr_u_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'upcr_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');
            $table->string('actorName')->nullable();

            $table->dateTime('requestedDateTime')->nullable();
            $table->dateTime('changedDateTime')->nullable();

            $table->timestamps();
        });

        Schema::table('UserLogin', function (Blueprint $table) {
            $table->string('loginMethod')->nullable();
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('UserLogin', 'loginMethod')) {
            Schema::table('UserLogin', function (Blueprint $table) {
                $table->dropColumn('loginMethod');
            });
        }

        Schema::dropIfExists('UserPasswordChangeRequest');
    }
};
