<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Domains\Tenant\_System\Models;

use Element\ElementTime\Domains\Tenant\_System\Config\KioskDeviceRequestConfig;
use Element\ElementTime\Domains\Tenant\_System\Enums\KioskDeviceRequestStatus\KioskDeviceRequestStatus;
use Element\ElementTime\Domains\Tenant\_System\Repositories\KioskDeviceRequestRepository;
use Element\ElementTime\Support\Domains\Models\TenantBaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 *
 * @property string $deviceId
 * @property string $ipAddress
 * @property string $name
 * @property KioskDeviceRequestStatus $status
 *
 * @property int $kioskDevice_id
 * @property-read KioskDevice|null $kioskDevice
 */
class KioskDeviceRequest extends TenantBaseModel
{
    /** @var KioskDeviceRequestConfig */
    public static $config = KioskDeviceRequestConfig::class;

    /** @var KioskDeviceRequestRepository */
    protected static $repositoryClass = KioskDeviceRequestRepository::class;

    /** @var KioskDeviceRequestRepository */
    public $repository;

    protected $primaryKey = 'id';
    public $incrementing = true;
    public $timestamps = true;
    protected $table = 'KioskDeviceRequest';

    public function kioskDevice(): BelongsTo
    {
        return $this->belongsTo(KioskDevice::class, 'kioskDevice_id');
    }
}
