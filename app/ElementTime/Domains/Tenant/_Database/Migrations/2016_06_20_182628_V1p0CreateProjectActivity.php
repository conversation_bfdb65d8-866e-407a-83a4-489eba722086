<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ProjectActivity', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');
            $table->bigInteger('project_id')->unsigned();

            $table->string('externalId')->nullable();
            $table->string('name')->nullable();
            $table->string('activityCode')->nullable();

            $table->float('budgetAmount')->nullable();
            $table->boolean('hasBudgetAlert')->nullable();
            $table->float('budgetAlertPercentage')->nullable();

            $table->char('rateType', 1)->nullable();
            $table->float('rateCost')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('project_id', 'projectactivity_project_fn')->references('id')->on('Project')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::drop('ProjectActivity');
    }
};
