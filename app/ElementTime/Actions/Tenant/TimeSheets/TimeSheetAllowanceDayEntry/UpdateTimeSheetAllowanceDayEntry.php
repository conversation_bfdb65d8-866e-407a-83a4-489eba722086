<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetAllowanceDayEntry;

use Carbon\CarbonInterface;
use Element\Core\Exceptions\ElementException;
use Element\Core\Types\TimeDuration;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetAllowanceDayEntry;

class UpdateTimeSheetAllowanceDayEntry extends BaseTimeSheetAllowanceDayEntry
{
    /** @throws \Throwable */
    public function handle(
        TimeSheetAllowanceDayEntry $entry,
        float|TimeDuration|string|null $duration = null,
        float|null $miles = null,
        float|null $quantity = null,
        CarbonInterface|string|null $startDateTime = null,
        CarbonInterface|string|null $endDateTime = null,
        string|null $notes = null,

        bool $doesSaveParent = true,
        bool $doesPreventJobDispatching = false,
    ): TimeSheetAllowanceDayEntry
    {
        $allowanceType = $entry->timeSheetAllowanceDay->timeSheetAllowance->allowanceType;
        $timeSheetAllowanceDay = $entry->timeSheetAllowanceDay;
        $timeSheetWorkItem = $entry->timeSheetWorkItem;
        $timeSheet = $timeSheetAllowanceDay->timeSheetAllowance->timeSheet;

        if (!$timeSheet->canBeUpdated($this->loggedUser)) {
            throw new ElementException('Timesheet is closed for editing');
        }

        if ($allowanceType->rateType === 'H') {
            $entry->duration = $this->getValidatedDuration($duration, $timeSheetWorkItem);
        } elseif ($allowanceType->rateType === 'M') {
            $entry->miles = $this->getValidatedMiles($miles);
        } elseif ($allowanceType->rateType === 'F') {
            $entry->quantity = $this->getValidatedQuantity($quantity);
        } elseif ($allowanceType->rateType == 'D') {
            $this->validateDaily($timeSheetAllowanceDay);
        } else {
            throw new ElementException('Allowance type is not valid. Contact support');
        }

        if ($allowanceType->requiresTime) {
            $period = $this->getValidatedPeriod($startDateTime, $endDateTime, $timeSheetWorkItem);

            $entry->startDateTime = $period->start;
            $entry->endDateTime = $period->end;
        }

        $entry->notes = $notes ?? '';

        CalculateRateForTimeSheetAllowanceDayEntry::make()->handle(
            $entry,
            allowanceType: $allowanceType,
            doesSave: false,
        );

        /// TODO: Change to normal save after cleaning model
        $entry->clearSaveOrFail();

        if ($doesSaveParent) {
            $timeSheetAllowanceDay->saveOrFail([
                'saveParent' => true,
                'preventJobsDispatching' => $doesPreventJobDispatching,
            ]);
        }

        return $entry;
    }
}
