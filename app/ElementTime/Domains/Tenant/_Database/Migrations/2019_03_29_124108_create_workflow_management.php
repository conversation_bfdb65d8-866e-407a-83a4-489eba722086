<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\NewType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        //region Table: WorkflowType

        Schema::create('WorkflowType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->string('externalId')->nullable();

            // General Information
            $table->string('name')->nullable();
            $table->string('type'); // ID from type class
            $table->longText('description')->nullable();
            $table->char('status', 1)->default('A');

            // Settings for default rule
            $table->boolean('isDefault')->default(false);
            $table->boolean('allowsManualReassignment')->default(false);
            $table->boolean('allowsCreatingDifferentRules')->default(true);

            $table->timestamps();
            $table->softDeletes();
        });

        //endregion Table: WorkflowType

        //region Table: WorkflowTypeTree

        Schema::create('WorkflowTypeTree', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflowType_id');
            $table->foreign('workflowType_id', 'wtt_wt_fn')->references('id')->on('WorkflowType')->onUpdate('cascade')->onDelete('cascade');

            $table->string('name')->nullable();
            $table->integer('order')->default(1);
            $table->integer('isDefault')->default(false);

            $table->timestamps();
        });

        //endregion Table: WorkflowTypeTree

        //region Table: WorkflowTypeTreeRule

        Schema::create('WorkflowTypeTreeRule', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflowTypeTree_id');
            $table->foreign('workflowTypeTree_id', 'wttr_wtt_fn')->references('id')->on('WorkflowTypeTree')->onUpdate('cascade')->onDelete('cascade');

            // Flow information
            $table->unsignedBigInteger('parentWorkflowTypeTreeRule_id')->nullable(); // ID of parent (AND) rule
            $table->foreign('parentWorkflowTypeTreeRule_id', 'wttr_pwttr_fn')->references('id')->on('WorkflowTypeTreeRule')->onUpdate('cascade')->onDelete('cascade');
            $table->boolean('isNot')->default(false); // Is a NOT rule

            // Conditions
            $table->string('ruleType'); // ID from type class
            $table->longText('details')->nullable(); // JSON with rule details

            $table->integer('order')->default(1); // Order on the OR list

            $table->unique(['workflowTypeTree_id', 'parentWorkflowTypeTreeRule_id', 'order'], 'wfttr_hor_rule');
            $table->timestamps();
        });

        //endregion Table: WorkflowTypeTreeRule

        //region Table: WorkflowTypeTreeApprovalStep

        Schema::create('WorkflowTypeTreeApprovalStep', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflowTypeTree_id');
            $table->foreign('workflowTypeTree_id', 'wttas_wtt_fn')->references('id')->on('WorkflowTypeTree')->onUpdate('cascade')->onDelete('cascade');

            $table->string('name')->nullable();
            $table->integer('number')->default(1);

            $table->unique(['workflowTypeTree_id', 'number']);
            $table->timestamps();
        });

        //endregion Table: WorkflowTypeTreeApprovalStep

        //region Table: WorkflowTypeTreeApprovalStepItem

        Schema::create('WorkflowTypeTreeApprovalStepItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflowTypeTreeApprovalStep_id');
            $table->foreign('workflowTypeTreeApprovalStep_id', 'wttasi_wttas_fn')->references('id')->on('WorkflowTypeTreeApprovalStep')->onUpdate('cascade')->onDelete('cascade');

            // Flow information
            $table->unsignedBigInteger('parentWorkflowTypeTreeApprovalStepItem_id')->nullable(); // ID of parent (AND) item
            $table->foreign('parentWorkflowTypeTreeApprovalStepItem_id', 'wttasi_pwttasi_fn')->references('id')->on('WorkflowTypeTreeApprovalStepItem')->onUpdate('cascade')->onDelete('cascade');

            $table->string('type')->nullable(); // ID from type class
            $table->longText('details')->nullable(); // JSON with rule details

            $table->integer('order')->default(1); // Order on the OR list

            $table->unique(['workflowTypeTreeApprovalStep_id', 'parentWorkflowTypeTreeApprovalStepItem_id', 'order'], 'wfttast_hor_item');
            $table->timestamps();
        });

        //endregion Table: WorkflowTypeTreeApprovalStepItem

        //region Table: UserWorkflowType

        Schema::create('UserWorkflowType', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id', 'uwt_user_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');
            $table->unsignedBigInteger('workflowType_id');
            $table->foreign('workflowType_id', 'uwt_wt_fn')->references('id')->on('WorkflowType')->onUpdate('cascade')->onDelete('restrict');

            $table->date('startDate');
            $table->date('endDate')->nullable();
            $table->text('comment')->nullable();

            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'uwt_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->char('status', 1)->default('A');

            $table->timestamps();
        });

        //endregion Table: UserWorkflowType

        //region Table: Workflow

        Schema::create('Workflow', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->string('rel_type');
            $table->unsignedBigInteger('rel_id');

            $table->string('otherRel_type')->nullable();
            $table->unsignedBigInteger('otherRel_id')->nullable();

            $table->unsignedBigInteger('assigningUser_id')->nullable();
            $table->foreign('assigningUser_id', 'wflow_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('ownerUser_id')->nullable();
            $table->foreign('ownerUser_id', 'wflow_ou_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('cascade');

            /** @see \Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowTypes\BaseType::CHILDREN */
            $table->string('type'); // ID from type class

            /** @see \Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\BaseType::CHILDREN */
            $table->string('status')->default(NewType::ID);

            $table->timestamps();
        });

        //endregion Table: Workflow

        //region Table: WorkflowStep

        Schema::create('WorkflowStep', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflow_id');
            $table->foreign('workflow_id', 'wfs_wflow_fn')->references('id')->on('Workflow')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('assigningUser_id')->nullable();
            $table->foreign('assigningUser_id', 'wfs_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            $table->string('name')->nullable();
            $table->integer('number')->default(1);

            $table->string('status')->default('not-started');
            // not-started => Not started
            // active => Active
            // finished => Finished

            $table->timestamps();
        });

        //endregion Table: WorkflowStep

        //region Table: WorkflowStepItem

        Schema::create('WorkflowStepItem', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');
            $table->unsignedBigInteger('workflowStep_id');
            $table->foreign('workflowStep_id', 'wfsi_wflows_fn')->references('id')->on('WorkflowStep')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('assigningUser_id')->nullable();
            $table->foreign('assigningUser_id', 'wfsi_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            // Flow information
            $table->unsignedBigInteger('parentWorkflowStepItem_id')->nullable(); // ID of parent (AND) item
            $table->foreign('parentWorkflowStepItem_id', 'wfsi_pwfsi_fn')->references('id')->on('WorkflowStepItem')->onUpdate('cascade')->onDelete('cascade');

            $table->string('type')->nullable(); // ID from type class
            $table->longText('details')->nullable(); // JSON with rule details

            $table->integer('order')->default(1); // Order on the OR list

            $table->unique(['workflowStep_id', 'parentWorkflowStepItem_id', 'order'], 'wfsi_hor_item');

            $table->boolean('isDone')->default(false);

            $table->timestamps();
        });

        //endregion Table: WorkflowStepItem

        //region Table: WorkflowApproval

        Schema::create('WorkflowApproval', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('workflow_id');
            $table->foreign('workflow_id', 'wfa_wf_fn')->references('id')->on('Workflow')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('workflowStepItem_id')->nullable();
            $table->foreign('workflowStepItem_id', 'wfa_wfsi_fn')->references('id')->on('WorkflowStepItem')->onUpdate('cascade')->onDelete('set null');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'wfa_au_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            $table->char('type', 1)->default('A');
            // A => Approval
            // D => Declining

            // Approval data
            $table->dateTime('dateTime')->nullable();
            $table->text('notes')->nullable();

            $table->unsignedBigInteger('attachmentFile_id')->nullable();
            $table->foreign('attachmentFile_id', 'wfa_af_fn')->references('id')->on('MediaFile')->onUpdate('cascade')->onDelete('set null');

            $table->timestamps();
        });

        //endregion Table: WorkflowApproval

        //region Table: WorkflowTimeline

        Schema::create('WorkflowTimeline', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->unsignedBigInteger('workflow_id');
            $table->foreign('workflow_id', 'wftl_wflow_fn')->references('id')->on('Workflow')->onUpdate('cascade')->onDelete('cascade');

            $table->unsignedBigInteger('actorUser_id')->nullable();
            $table->foreign('actorUser_id', 'wftl_auser_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('approval_id')->nullable();
            $table->foreign('approval_id', 'wftl_app_fn')->references('id')->on('WorkflowApproval')->onUpdate('cascade')->onDelete('set null');

            // General data
            $table->dateTime('dateTime')->nullable();
            $table->text('reason')->nullable();
            $table->text('comment')->nullable();

            $table->string('type'); // Type ID (class ID)

            // Status
            /** @see \Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\BaseType::CHILDREN */
            $table->string('status')->default('new');
            $table->string('oldStatus')->nullable();

            $table->timestamps();
        });

        //endregion Table: WorkflowTimeline
    }

    public function down(): void
    {
        //region Table: WorkflowTimeline

        Schema::dropIfExists('WorkflowTimeline');

        //endregion Table: WorkflowTimeline

        //region Table: WorkflowApproval

        Schema::dropIfExists('WorkflowApproval');

        //endregion Table: WorkflowApproval

        //region Table: WorkflowStepItem

        Schema::dropIfExists('WorkflowStepItem');

        //endregion Table: WorkflowStepItem

        //region Table: WorkflowStep

        Schema::dropIfExists('WorkflowStep');

        //endregion Table: WorkflowStep

        //region Table: Workflow

        Schema::dropIfExists('Workflow');

        //endregion Table: Workflow

        //region Table: UserWorkflowType

        Schema::dropIfExists('UserWorkflowType');

        //endregion Table: UserWorkflowType

        //region Table: WorkflowTypeTreeApprovalStepItem

        Schema::dropIfExists('WorkflowTypeTreeApprovalStepItem');

        //endregion Table: WorkflowTypeTreeApprovalStepItem

        //region Table: WorkflowTypeTreeApprovalStep

        Schema::dropIfExists('WorkflowTypeTreeApprovalStep');

        //endregion Table: WorkflowTypeTreeApprovalStep

        //region Table: WorkflowTypeTreeRule

        Schema::dropIfExists('WorkflowTypeTreeRule');

        //endregion Table: WorkflowTypeTreeRule

        //region Table: WorkflowTypeTree

        Schema::dropIfExists('WorkflowTypeTree');

        //endregion Table: WorkflowTypeTree

        //region Table: WorkflowType

        Schema::dropIfExists('WorkflowType');

        //endregion Table: WorkflowType
    }
};
