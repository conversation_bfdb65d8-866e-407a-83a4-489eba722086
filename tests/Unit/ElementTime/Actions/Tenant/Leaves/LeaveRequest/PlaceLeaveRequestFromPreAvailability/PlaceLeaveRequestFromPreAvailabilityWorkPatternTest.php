<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Tests\Unit\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability;

use Carbon\CarbonInterface;
use Element\Core\Exceptions\AttachmentRequiredException;
use Element\Core\Exceptions\CommentsRequiredException;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Types\TimeDuration;
use Element\Core\Types\TimeSpan;
use Element\Core\Types\TimeSpanList;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\CalculateLeavePreAvailabilityFromSchedule\CalculateLeavePreAvailabilityFromScheduleWorkPattern;
use Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\PlaceLeaveRequestFromPreAvailability\PlaceLeaveRequestFromPreAvailabilityWorkPattern;
use Element\ElementTime\Actions\Tenant\TimeSheets\TimeSheetWork\CreateTimeSheetWork;
use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantClass;
use Element\ElementTime\Domains\Tenant\Plant\Models\PlantItem;
use Element\ElementTime\Domains\Tenant\Plant\Models\UserRolePlantItem;
use Element\ElementTime\Domains\Tenant\Plant\Support\PlantItemHoursRecordingTypes\HourMeterType;
use Element\ElementTime\Domains\Tenant\Plant\Support\PlantItemRelationshipTypes\IndependentRelationshipType;
use Element\ElementTime\Domains\Tenant\Schedules\Enums\UserRoleScheduleType\UserRoleScheduleType;
use Element\ElementTime\Domains\Tenant\Schedules\Models\Holiday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\PublicHoliday;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriod\ScheduledPayRunPeriodWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\ScheduledPayRunPeriodDay\ScheduledPayRunPeriodDayWorkPattern;
use Element\ElementTime\Domains\Tenant\Schedules\Models\UserRoleSchedulePatternPeriodShiftDay;
use Element\ElementTime\Domains\Tenant\Schedules\Structures\StructScheduledPayRunPeriodDayDefaultData;
use Element\ElementTime\Domains\Tenant\Settings\Enums\AllowanceTypeCostingTypeType\AllowanceTypeCostingType;
use Element\ElementTime\Domains\Tenant\Settings\Models\AllowanceType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkBreak;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleAllowanceType;
use Element\ElementTime\Domains\Tenant\Users\Models\UserRoleProject;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags\SuperUserFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Enums\ModelTaskType\ModelTaskType;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes as StatusTypes;
use Element\ElementTime\Support\Domains\Type\StatusTypes\ActiveStatus;
use Element\ElementTime\Support\Enums\RecordedVia;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Illuminate\Database\Eloquent\Collection;
use Tests\Traits\Functionalities\Tenant\Leaves\LeaveGeneralFunctionality;
use Tests\Traits\Functionalities\Tenant\MediaFileFunctionality;
use Tests\Traits\Functionalities\Tenant\TimeSheets\TimeSheetWorkFunctionality;
use Tests\Traits\Functionalities\Tenant\Workflows\ModelActionGeneralFunctionality;

uses(LeaveGeneralFunctionality::class, ModelActionGeneralFunctionality::class, MediaFileFunctionality::class, TimeSheetWorkFunctionality::class);

describe('Work pattern - Place leave request', function () {
    beforeEach(function () {
        LeaveRequest::query()->whereNotNull('LeaveRequest.userRoleSchedule_id')->delete();
    });

    it('can place part day leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('09:00:00', '16:00:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(6);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;

        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledPayRunPeriodDayWorkPattern->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;

        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodWorkPattern->patternPeriodShift->scheduleShift->clearSave();

        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($leaveTimeSpan->getStart())
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($leaveTimeSpan->getEnd())
        ;
    });

    it('can place part day leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '17:00:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:00:00', '13:00:00', $scheduledDayData->date);
        $leaveTimeSpan = TimeSpan::make('09:00:00', '16:00:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(6);

        /** @var UserRoleSchedulePatternPeriodShiftDay $userRoleSchedulePatternPeriodShiftDay */
        $userRoleSchedulePatternPeriodShiftDay = $scheduledDayData->type;
        $scheduleShift = $userRoleSchedulePatternPeriodShiftDay->scheduleShiftDay->scheduleShift;
        $scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduleShift->doesHaveExpectedTimeSpan = true;
        $scheduleShift->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduleShift->expectedSpan = $expectedTimeSpan->copy();
        $scheduleShift->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduleShift->clearSave();

        $userRoleSchedulePatternPeriodShiftDay->breaks = TimeSpanList::make([$breakTimeSpan]);
        $userRoleSchedulePatternPeriodShiftDay->doesHaveExpectedTimeSpan = true;
        $userRoleSchedulePatternPeriodShiftDay->ordinarySpan = TimeSpan::make('00:00', '22:00');
        $userRoleSchedulePatternPeriodShiftDay->minDuration = TimeDuration::zero();
        $userRoleSchedulePatternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $userRoleSchedulePatternPeriodShiftDay->expectedDuration = $scheduleShift->expectedDuration->copy();
        $userRoleSchedulePatternPeriodShiftDay->expectedSpan = $scheduleShift->expectedSpan->copy();
        $userRoleSchedulePatternPeriodShiftDay->clearSave();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $leaveTimeSpan->getStart(),
            dailyEndTime: $leaveTimeSpan->getEnd(),
            actorUser: SuperUserFlag::getOne(),
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($leaveTimeSpan->getStart())
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($leaveTimeSpan->getEnd())
        ;
    });

    it('can place single full day leave on day without expected time span on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinarySpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36', 'time');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = false;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinarySpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $ordinarySpan->copy();
        $scheduledPayRunPeriodDayWorkPattern->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $ordinarySpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $ordinarySpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        $expectedStart = $ordinarySpan->getStart();
        $expectedEnd = $ordinarySpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
        ;
    });

    it('can place single full day leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        $expectedStart = $expectedTimeSpan->getStart();
        $expectedEnd = $expectedTimeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
        ;
    });

    it('can place single full day leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36');

        /** @var UserRoleSchedulePatternPeriodShiftDay $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = TimeSpan::make('00:00', '22:00');
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        $expectedStart = $expectedTimeSpan->getStart();
        $expectedEnd = $expectedTimeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
        ;
    });

    it('can place multiple full day leave on open payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $holidayDates = Holiday::q()->many()->concat(PublicHoliday::q()->many())->pluck('date');
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $daysCount = 3;
        /** @var StructScheduledPayRunPeriodDayDefaultData[] $scheduledDays */
        $scheduledDays = [];

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $expectedLeaveDuration = TimeDuration::zero();
        $doesWorkOnHolidays = $userRoleSchedule->holidayCalculations->doesWorkOnHolidays || $userLeaveBankType->doesIgnorePublicHolidays;

        foreach ($days as $day) {
            if (!$doesWorkOnHolidays && $holidayDates->contains($day->date)) {
                continue;
            }

            if ($day->type instanceof ScheduledPayRunPeriodDayWorkPattern && $day->type->isScheduled) {
                $day->type->doesHaveExpectedTimeSpan = true;
                $day->type->ordinarySpan = TimeSpan::make('08:00', '20:00')->setDate($day->date);
                $day->type->expectedSpan = $expectedTimeSpan->copy()->setDate($day->date);
                $day->type->breaks = TimeSpanList::make([$breakTimeSpan->copy()])->setDate($day->date);
                $day->type->duration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                $day->type->expectedDuration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());

                $day->type->clearSave();

                $expectedLeaveDuration->add($day->type->duration);

                $scheduledDays[] = $day;
            }

            if (count($scheduledDays) >= $daysCount) {
                break;
            }
        }

        $laveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: reset($scheduledDays)->date->copy(),
            endDate: last($scheduledDays)->date->copy(),
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $laveAvailability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($laveAvailability->startDate)
            ->and($leaveRequest->endDate)->toEqual($laveAvailability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount($daysCount)
        ;
    });

    it('can place multiple full day leave on future payrun', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $holidayDates = Holiday::q()->many()->concat(PublicHoliday::q()->many())->pluck('date');

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $daysCount = 5;
        /** @var StructScheduledPayRunPeriodDayDefaultData[] $scheduledDays */
        $scheduledDays = [];

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $expectedLeaveDuration = TimeDuration::zero();
        $doesWorkOnHolidays = $userRoleSchedule->holidayCalculations->doesWorkOnHolidays || $userLeaveBankType->doesIgnorePublicHolidays;

        foreach ($days as $day) {
            if (!$doesWorkOnHolidays && $holidayDates->contains($day->date)) {
                continue;
            }

            if ($day->type instanceof UserRoleSchedulePatternPeriodShiftDay && $day->type->isScheduled) {
                $day->type->doesHaveExpectedTimeSpan = true;
                $day->type->ordinarySpan = TimeSpan::make('00:00', '22:00')->setDate($day->date);
                $day->type->expectedSpan = $expectedTimeSpan->copy()->setDate($day->date);
                $day->type->minDuration = TimeDuration::zero();
                $day->type->maxDuration = TimeDuration::parseFromHours(24);
                $day->type->duration = $day->type->expectedSpan->getDuration()->deduct($breakTimeSpan->getDuration());
                $day->type->expectedDuration = $day->type->expectedSpan->getDuration()->deduct($breakTimeSpan->getDuration());
                $day->type->breaks = TimeSpanList::make([$breakTimeSpan->copy()->setDate($day->date)]);
                $day->type->clearSave();

                $expectedLeaveDuration->add($day->type->expectedDuration);

                $scheduledDays[] = $day;
            }

            if (count($scheduledDays) >= $daysCount) {
                break;
            }
        }

        $leaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: reset($scheduledDays)->date->copy(),
            endDate: last($scheduledDays)->date->copy(),
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $leaveAvailability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($leaveAvailability->startDate)
            ->and($leaveRequest->endDate)->toEqual($leaveAvailability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount($daysCount)
        ;
    });

    it('can place leave across multiple payruns', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $holidayDates = Holiday::q()->many()->concat(PublicHoliday::q()->many())->pluck('date');

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        $actorUser = SuperUserFlag::getOne();
        $remainingAttempt = 10;
        $payruns = [];
        $scheduledDays = [];
        $payRunsCount = 2;
        $userLeaveBankType = null;
        $userRoleSchedule = null;

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $expectedLeaveDuration = TimeDuration::zero();

        while ($remainingAttempt > 0 && count($payruns) < $payRunsCount) {
            $remainingAttempt--;

            $dataFromPayRun = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
            [$user, $userRoleSchedule, $userLeaveBankType, $days] = $dataFromPayRun;

            if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
                continue;
            }

            $doesWorkOnHolidays = $userRoleSchedule->holidayCalculations->doesWorkOnHolidays || $userLeaveBankType->doesIgnorePublicHolidays;
            foreach ($days as $day) {
                if (!$doesWorkOnHolidays && $holidayDates->firstWhere(fn (CarbonInterface $holidayDate) => $holidayDate->isSameDay($day->date))) {
                    continue;
                }

                if ($day->type instanceof UserRoleSchedulePatternPeriodShiftDay && $day->type->isScheduled) {
                    $day->type->doesHaveExpectedTimeSpan = true;
                    $day->type->ordinarySpan = TimeSpan::make('00:00', '22:00')->setDate($day->date);
                    $day->type->expectedSpan = $expectedTimeSpan->setDate($day->date);
                    $day->type->breaks = TimeSpanList::make([$breakTimeSpan->copy()->setDate($day->date)]);
                    $day->type->minDuration = TimeDuration::zero();
                    $day->type->maxDuration = TimeDuration::parseFromHours(22);
                    $day->type->duration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                    $day->type->expectedDuration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                    $day->type->clearSave();

                    $expectedLeaveDuration->add($day->type->expectedDuration);

                    $scheduledDays[] = $day;
                }
            }
            $payruns[] = $payRun;
            $payRun = $payRun->next;
        }

        if (count($scheduledDays) < 1) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule on ' . $payRun->startDate->toDateString() . ' - ' . $payRun->endDate->toDateString() . ' period');
        }

        $startDate = reset($scheduledDays)->date->copy();
        $payRunAtStart = TenantSystemSettings::getPayRunByDate($startDate);
        $endDate = last($scheduledDays)->date->copy();
        $payRunAtEnd = TenantSystemSettings::getPayRunByDate($endDate);
        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->days)->toHaveCount(count($scheduledDays))
            ->and($leaveRequest->duration->getTotalHours())->toEqual($expectedLeaveDuration->getTotalHours())
            ->and($payRunAtStart->id)->not()->toEqual($payRunAtEnd)
        ;
    });

    it('can be submitted with attachment', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();
        $attachmentFiles = $this->g_mf_createRandomMediaFiles($actorUser);
        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            attachments: $attachmentFiles,
            actorUser: $actorUser,
        );

        $expectedStart = $expectedTimeSpan->getStart();
        $expectedEnd = $expectedTimeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
            ->and($leaveRequest->attachmentFiles)->toHaveCount($attachmentFiles->count())
        ;
    });

    it('can be submitted with reason', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'awesome reason for work pattern';

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            reason: $reason,
            actorUser: $actorUser,
        );

        $expectedStart = $expectedTimeSpan->getStart();
        $expectedEnd = $expectedTimeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->duration)->toEqual($availability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
            ->and($leaveRequest->reason)->toEqual($reason)
        ;
    });

    it('can be submitted and approved', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $beforeLeaveCommitedBalance = $userLeaveBankType->bank->refresh()->totalCommittedBalance;

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00', $scheduledDayData->date);
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00', $scheduledDayData->date);
        $expectedLeaveDuration = TimeDuration::make('07:36');

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->duration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $expectedTimeSpan->getDuration()->deduct($breakTimeSpan->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            actorUser: $actorUser,
            doesApprove: true
        );

        $expectedStart = $expectedTimeSpan->getStart();
        $expectedEnd = $expectedTimeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($fullDayLeaveAvailability->startDate)
            ->and($leaveRequest->endDate)->toEqual($fullDayLeaveAvailability->endDate)
            ->and($leaveRequest->duration)->toEqual($fullDayLeaveAvailability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\ApprovedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
            ->and($userLeaveBankType->bank->refresh()->totalCommittedBalance)->toEqual($beforeLeaveCommitedBalance + $leaveRequest->duration->getTotalHours())
        ;
    });

    it('splits leave request when placing and approving multiple payruns leave', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        $holidayDates = Holiday::q()->many()->concat(PublicHoliday::q()->many())->pluck('date');

        while (!is_null($payRun->id)) {
            $payRun = $payRun->next;
        }

        $actorUser = SuperUserFlag::getOne();
        $remainingAttempt = 10;
        $payruns = [];
        $scheduledDays = [];
        $payRunsCount = 2;
        $userLeaveBankType = null;
        $userRoleSchedule = null;

        $expectedTimeSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $expectedLeaveDuration = TimeDuration::zero();

        while (count($payruns) < $payRunsCount && $remainingAttempt > 0) {
            $remainingAttempt--;

            $dataFromPayRun = $this->l_lg_getUserWithDefaultScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
            [$user, $userRoleSchedule, $userLeaveBankType, $days] = $dataFromPayRun;

            if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
                continue;
            }

            $payruns[] = $dataFromPayRun;
            $doesWorkOnHolidays = $userRoleSchedule->holidayCalculations->doesWorkOnHolidays || $userLeaveBankType->doesIgnorePublicHolidays;

            foreach ($days as $day) {
                if (!$doesWorkOnHolidays && $holidayDates->contains($day->date)) {
                    continue;
                }

                if ($day->type instanceof UserRoleSchedulePatternPeriodShiftDay && $day->type->isScheduled) {
                    $day->type->doesHaveExpectedTimeSpan = true;
                    $day->type->ordinarySpan = TimeSpan::make('08:00', '20:00')->setDate($day->date);
                    $day->type->expectedSpan = $expectedTimeSpan->copy()->setDate($day->date);
                    $day->type->minDuration = TimeDuration::zero();
                    $day->type->maxDuration = TimeDuration::parseFromHours(24);
                    $day->type->breaks = TimeSpanList::make([$breakTimeSpan->copy()])->setDate($day->date);
                    $day->type->duration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                    $day->type->expectedDuration = $day->type->expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                    $day->type->clearSave();

                    $expectedLeaveDuration->add($day->type->expectedDuration);

                    $scheduledDays[] = $day;
                }
            }

            $payRun = $payRun->next;
        }

        if (count($scheduledDays) < 1) {
            throw new \Exception("Not found scheduled day on " . UserRoleScheduleType::WorkPattern->title() . ' schedule');
        }

        $startDate = reset($scheduledDays)->date->copy();
        $payRunAtStart = TenantSystemSettings::getPayRunByDate($startDate);
        $endDate = last($scheduledDays)->date->copy();
        $payRunAtEnd = TenantSystemSettings::getPayRunByDate($endDate);
        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $startDate,
            endDate: $endDate,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
            doesApprove: true,
        );

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($availability->startDate)
            ->and($leaveRequest->endDate)->toEqual($availability->endDate)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SplitType::ID)
            ->and($leaveRequest->splitParts)->toHaveCount($payRunsCount)
            ->and($leaveRequest->duration->getTotalHours())->toEqual($expectedLeaveDuration->getTotalHours())
            ->and($leaveRequest->days)->toHaveCount(count($scheduledDays))
            ->and($payRunAtStart->id)->not()->toEqual($payRunAtEnd)
        ;
    });

    it('replaces part of worked hours and keep break record if break does not clashes with placed leave', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);
        $partDayLeaveTimeSpan = TimeSpan::make('10:00', '11:00')->setDate($date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(1);

        $workBlockFirstItem = TimeSpan::make('09:00','12:00', $date);
        $workBlockSecondItem = TimeSpan::make('13:00', '17:00', $date);
        $expectedWorkBlockAfterSplit1 = TimeSpan::make('09:00', '10:00', $date);
        $expectedWorkBlockAfterSplit2 = TimeSpan::make('11:00', '17:00', $date);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $workBlockFirstItem->getStartAsCarbon(),
            end: $workBlockSecondItem->getEndAsCarbon(),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockFirstItem->getStart(),
                    'end' => $workBlockFirstItem->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'break',
                    'isScheduled' => false,
                    'start' => $breakTimeSpan->getStartAsCarbon()->toDateTimeString(),
                    'end' => $breakTimeSpan->getEndAsCarbon()->toDateTimeString(),
                    'notes' => 'break time',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockSecondItem->getStart(),
                    'end' => $workBlockSecondItem->getEnd(),
                    'notes' => 'Second work item block right after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        /** @var TimeSheetWork[] $works */
        $works = $scheduledPayRunPeriodDay->timeSheetDay->works()->get();

        expect($leaveRequest->id)
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($works)->toHaveCount(2)
            ->and($works[0]->itemsWithBreaks)->toHaveCount(1)
            ->and($works[0]->itemsWithBreaks[0]->start)->toEqual($expectedWorkBlockAfterSplit1->getStartAsCarbon())
            ->and($works[0]->itemsWithBreaks[0]->end)->toEqual($expectedWorkBlockAfterSplit1->getEndAsCarbon())
            ->and($works[0]->itemsWithBreaks[0])->toBeInstanceOf(TimeSheetWorkItem::class)

            ->and($works[1]->itemsWithBreaks)->toHaveCount(3)
            ->and($works[1]->itemsWithBreaks[0]->start)->toEqual($expectedWorkBlockAfterSplit2->getStartAsCarbon())
            ->and($works[1]->itemsWithBreaks[0]->end)->toEqual($breakTimeSpan->getStartAsCarbon())
            ->and($works[1]->itemsWithBreaks[0])->toBeInstanceOf(TimeSheetWorkItem::class)

            ->and($works[1]->itemsWithBreaks[1]->start)->toEqual($breakTimeSpan->getStartAsCarbon())
            ->and($works[1]->itemsWithBreaks[1]->end)->toEqual($breakTimeSpan->getEndAsCarbon())
            ->and($works[1]->itemsWithBreaks[1])->toBeInstanceOf(TimeSheetWorkBreak::class)

            ->and($works[1]->itemsWithBreaks[2]->start)->toEqual($breakTimeSpan->getEndAsCarbon())
            ->and($works[1]->itemsWithBreaks[2]->end)->toEqual($expectedWorkBlockAfterSplit2->getEndAsCarbon())
            ->and($works[1]->itemsWithBreaks[2])->toBeInstanceOf(TimeSheetWorkItem::class)
        ;
    });

    it('replaces completely all recorded work hours if clashes on full day leave and user has doesFullDayLeaveReplacesAllRecordedHours flag as true', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $userLeaveBankType->bank->user->settings->doesFullDayLeaveReplacesAllRecordedHours = true;
        $userLeaveBankType->bank->user->saveOrFail();

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $workBlockFirstItem = TimeSpan::make('09:00','12:00', $date);
        $workBlockSecondItem = TimeSpan::make('12:00', '17:00', $date);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());

        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $workBlockFirstItem->getStartAsCarbon(),
            end: $workBlockSecondItem->getEndAsCarbon(),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockFirstItem->getStart(),
                    'end' => $workBlockFirstItem->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockSecondItem->getStart(),
                    'end' => $workBlockSecondItem->getEnd(),
                    'notes' => 'Second work item block right after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        /** @var TimeSheetWork[] $works */
        $works = $scheduledPayRunPeriodDay->timeSheetDay->works()->get();

        expect($leaveRequest->id)->toBeInt()
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($works)->toHaveCount(0);
    });

    it('replaces time sheet work item record from beginning of time sheet work item if clashes on part day leave covering time sheet work item', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $timeSheetWorkBlock1 = TimeSpan::make('08:00','12:00', $date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);
        $timeSheetWorkBlock2 = TimeSpan::make('13:00','17:00', $date);

        $partDayLeaveTimeSpan = TimeSpan::make('08:00', '12:00')->setDate($date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(4);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());

        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock1->getStartAsCarbon(),
            end: $timeSheetWorkBlock2->getEndAsCarbon(),
            notes: 'It will be replaced/deleted by leave request',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock1->getStart(),
                    'end' => $timeSheetWorkBlock1->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'break',
                    'isScheduled' => true,
                    'start' => $breakTimeSpan->getStart(),
                    'end' => $breakTimeSpan->getEnd(),
                    'notes' => 'break timespan note',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock2->getStart(),
                    'end' => $timeSheetWorkBlock2->getEnd(),
                    'notes' => 'Second work block item after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        /** @var TimeSheetWork[]|Collection $works */
        $works = $timeSheetDay->works()->get();

        expect($leaveRequest->id)
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($partDayLeaveTimeSpan->getStart())
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($partDayLeaveTimeSpan->getEnd())
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($works)->toHaveCount(1)
            ->and($works[0]->breaks)->toHaveCount(0)
            ->and($works[0]->itemsWithBreaks)->toHaveCount(1)
            ->and($works[0]->itemsWithBreaks[0])->toBeInstanceOf(TimeSheetWorkItem::class)
            ->and($works[0]->itemsWithBreaks[0]->start)->toEqual($timeSheetWorkBlock2->getStartAsCarbon())
            ->and($works[0]->itemsWithBreaks[0]->end)->toEqual($timeSheetWorkBlock2->getEndAsCarbon())
        ;
    });

    it('replaces time sheet work item record from end of time sheet work item if clashes on part day leave covering time sheet work item', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $timeSheetWorkBlock1 = TimeSpan::make('08:00','12:00', $date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);
        $timeSheetWorkBlock2 = TimeSpan::make('13:00','17:00', $date);

        $partDayLeaveTimeSpan = TimeSpan::make('12:30', '17:00')->setDate($date);
        $expectedLeaveTimeSpan = TimeSpan::make('13:00', '17:00')->setDate($date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(4);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());

        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock1->getStartAsCarbon(),
            end: $timeSheetWorkBlock2->getEndAsCarbon(),
            notes: 'It will be replaced/deleted by leave request',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock1->getStart(),
                    'end' => $timeSheetWorkBlock1->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'break',
                    'isScheduled' => true,
                    'start' => $breakTimeSpan->getStart(),
                    'end' => $breakTimeSpan->getEnd(),
                    'notes' => 'break timespan note',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock2->getStart(),
                    'end' => $timeSheetWorkBlock2->getEnd(),
                    'notes' => 'Second work block item after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        /** @var TimeSheetWork[]|Collection $works */
        $works = $timeSheetDay->works()->get();

        expect($leaveRequest->id)
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedLeaveTimeSpan->getStart())
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedLeaveTimeSpan->getEnd())
            ->and($leaveRequest->days[0]->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($works)->toHaveCount(1)
            ->and($works[0]->breaks)->toHaveCount(0)
            ->and($works[0]->itemsWithBreaks)->toHaveCount(1)
            ->and($works[0]->itemsWithBreaks[0])->toBeInstanceOf(TimeSheetWorkItem::class)
            ->and($works[0]->itemsWithBreaks[0]->start)->toEqual($timeSheetWorkBlock1->getStartAsCarbon())
            ->and($works[0]->itemsWithBreaks[0]->end)->toEqual($timeSheetWorkBlock1->getEndAsCarbon())
        ;
    });

    it('replaces part of worked hours along with plant sheet day time and allowance entry record if clashes with placed leave', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $allowanceType = new AllowanceType;
        $allowanceType->name = 'Worked hours and master test';
        $allowanceType->costingType = AllowanceTypeCostingType::WorkedHoursAndMaster->id();
        $allowanceType->rateType = 'H';
        $allowanceType->requiresTime = true;
        $allowanceType->status = ActiveStatus::ID;
        $allowanceType->clearSave();

        $userRoleAllowanceType = new UserRoleAllowanceType;
        $userRoleAllowanceType->userRole_id = $userRole->id;
        $userRoleAllowanceType->allowanceType_id = $allowanceType->id;
        $userRoleAllowanceType->startDate = $userRoleSchedule->startDate->copy();
        $userRoleAllowanceType->saveOrFail();

        $plantClass = new PlantClass();
        $plantClass->name = 'Test plant class';
        $plantClass->description = 'Created just for test purpose';
        $plantClass->doesAllowCostingItemsStandalone = false;
        $plantClass->clearSave();

        $plantItem = new PlantItem;
        $plantItem->plantClass_id = $plantClass->id;
        $plantItem->name = 'Plant item with duration/hours fields';
        $plantItem->externalId = 'TEST';
        $plantItem->relationshipType = IndependentRelationshipType::ID;
        $plantItem->ownerUser_id = $user->id;
        $plantItem->description = '...';
        $plantItem->doesHaveHours = true;
        $plantItem->hoursRecordingType = HourMeterType::ID;
        $plantItem->doesAllowCostingStandalone = false;
        $plantItem->clearSave();

        $userRolePlantItem = new UserRolePlantItem;
        $userRolePlantItem->userRole_id = $userRole->id;
        $userRolePlantItem->plantItem_id = $plantItem->id;
        $userRolePlantItem->startDate = $userRoleSchedule->startDate->copy();
        $userRolePlantItem->clearSave();

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);
        $partDayLeaveStart = '09:00';
        $partDayLeaveDuration = TimeDuration::parseFromHours(6);

        $timeSheetWorkBlock1 = TimeSpan::make('08:00','10:00', $date);
        $timeSheetWorkBlock2 = TimeSpan::make('15:00','17:00', $date);

        $expectedTimeSheetWorkBlock1 = TimeSpan::make('08:00', '09:00', $date);
        $expectedTimeSheetWorkBlock1Duration = TimeDuration::parseFromHours(1);
        $expectedTimeSheetWorkBlock2 = TimeSpan::make('16:00', '17:00', $date);
        $expectedTimeSheetWorkBlock2Duration = TimeDuration::parseFromHours(1);
        $plantDurationStart = TimeDuration::parseFromHours(1);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());

        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        $timeSheetWork1 = CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock1->getStartAsCarbon(),
            end: $timeSheetWorkBlock1->getEndAsCarbon(),
            notes: 'It need to shrunk with allowance',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock1->getStart(),
                    'end' => $timeSheetWorkBlock1->getEnd(),
                    'notes' => 'First work item block',
                    'allowances' => [
                        [
                            'allowanceType_id' => $allowanceType->id,
                            'startDateTime' => $timeSheetWorkBlock1->getStartAsCarbon(),
                            'endDateTime' => $timeSheetWorkBlock1->getEndAsCarbon(),
                            'duration' => $timeSheetWorkBlock1->getDuration()->getTotalHours(),
                        ]
                    ],
                ],
            ],
            doesPreventBroadcasting: true,
        );
        $timeSheetWork2 = CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock2->getStartAsCarbon(),
            end: $timeSheetWorkBlock2->getEndAsCarbon(),
            notes: 'It need to shrunk with plant',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $timeSheetWorkBlock2->getStart(),
                    'end' => $timeSheetWorkBlock2->getEnd(),
                    'notes' => 'Second work item block',
                    'plant_items' => [
                        [
                            'plantItem_id' => $plantItem->id,
                            'rel_id' => $userRolePlantItem->id,
                            'rel_type' => get_class($userRolePlantItem),
                            'durationStart' => $plantDurationStart->copy(),
                            'durationEnd' => $plantDurationStart->copy()->add($timeSheetWorkBlock2->getDuration()),
                            'duration' => $timeSheetWorkBlock2->getDuration()->getTotalHours(),
                        ]
                    ],
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveStart,
            dailyDuration: $partDayLeaveDuration->copy(),
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        $timeSheetWork1->refresh();
        $timeSheetWork2->refresh();

        expect($leaveRequest->id)
            ->and($availability->clashedWorks)->toHaveCount(2)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($timeSheetWork1->start)->toEqual($expectedTimeSheetWorkBlock1->getStartAsCarbon())
            ->and($timeSheetWork1->end)->toEqual($expectedTimeSheetWorkBlock1->getEndAsCarbon())
            ->and($timeSheetWork1->breaks)->toHaveCount(0)
            ->and($timeSheetWork1->itemsWithBreaks)->toHaveCount(1)
            ->and($timeSheetWork1->itemsWithBreaks[0]->allowanceEntries)->toHaveCount(1)
            ->and($timeSheetWork1->itemsWithBreaks[0]->allowanceEntries[0]->duration)->toEqual($expectedTimeSheetWorkBlock1Duration)
            ->and($timeSheetWork1->itemsWithBreaks[0]->allowanceEntries[0]->startDateTime)->toEqual($timeSheetWork1->start)
            ->and($timeSheetWork1->itemsWithBreaks[0]->allowanceEntries[0]->endDateTime)->toEqual($timeSheetWork1->end)
            ->and($timeSheetWork2->breaks)->toHaveCount(0)
            ->and($timeSheetWork2->start)->toEqual($expectedTimeSheetWorkBlock2->getStartAsCarbon())
            ->and($timeSheetWork2->end)->toEqual($expectedTimeSheetWorkBlock2->getEndAsCarbon())
            ->and($timeSheetWork2->sumDuration)->toEqual($expectedTimeSheetWorkBlock2Duration)
            ->and($timeSheetWork2->itemsWithBreaks[0]->plantSheetDayTimes)->toHaveCount(1)
            ->and($timeSheetWork2->itemsWithBreaks[0]->plantSheetDayTimes[0]->duration)->toEqual($expectedTimeSheetWorkBlock1Duration)
            ->and($timeSheetWork2->itemsWithBreaks[0]->plantSheetDayTimes[0]->durationStart)->toEqual($plantDurationStart)
            ->and($timeSheetWork2->itemsWithBreaks[0]->plantSheetDayTimes[0]->durationEnd)->toEqual($plantDurationStart->copy()->add($expectedTimeSheetWorkBlock1Duration->copy()))
        ;
    });

    it('splits recorded work time span by replacing part of work block if leave is placed in the middle of work time block', function () {
        [$userRole, $userRoleTimeType, $scheduledPayRun, $scheduledPayRunPeriodDay, $timeSheetDay] = $this->ts_tsw_getOneClearScheduledPayRun(UserRoleScheduleType::WorkPattern);
        $actorUser = SuperUserFlag::getOne();
        $date = $timeSheetDay->date;

        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, [
            ['UserRoleSchedule.id', '=', $scheduledPayRun->userRoleSchedule_id],
        ], date: $date);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active work-pattern schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $ordinaryTimeSpan = TimeSpan::make('08:00', '18:00')->setDate($date);
        $expectedTimeSpan = TimeSpan::make('09:00', '17:00')->setDate($date);
        $breakTimeSpan = TimeSpan::make('12:00', '13:00')->setDate($date);
        $partDayLeaveTimeSpan = TimeSpan::make('10:00', '16:00')->setDate($date);
        $expectedLeaveDuration = TimeDuration::parseFromHours(5);

        $timeSheetWorkBlock = $expectedTimeSpan->copy();
        $workBlockFirstItem = TimeSpan::make('09:00','12:00', $date);
        $workBlockSecondItem = TimeSpan::make('13:00', '17:00', $date);

        $expectedFirstWorkBlockAfterSplit = TimeSpan::make('09:00', '10:00', $date);
        $expectedSecondWorkBlockAfterSplit = TimeSpan::make('16:00', '17:00', $date);

        /** @var ScheduledPayRunPeriodDayWorkPattern $scheduledPayRunPeriodDayWorkPattern */
        $scheduledPayRunPeriodDayWorkPattern = $scheduledDayData->type;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->maxDuration = TimeDuration::parseFromHours(24);
        $scheduledPayRunPeriodDayWorkPattern->patternPeriodShiftDay->minDuration = TimeDuration::zero();
        $scheduledPayRunPeriodDayWorkPattern->doesHaveExpectedTimeSpan = true;
        $scheduledPayRunPeriodDayWorkPattern->ordinarySpan = $ordinaryTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->expectedSpan = $expectedTimeSpan;
        $scheduledPayRunPeriodDayWorkPattern->breaks = TimeSpanList::make([$breakTimeSpan]);
        $scheduledPayRunPeriodDayWorkPattern->expectedDuration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());
        $scheduledPayRunPeriodDayWorkPattern->duration = $scheduledPayRunPeriodDayWorkPattern->expectedSpan->getDuration()->deduct($scheduledPayRunPeriodDayWorkPattern->breaks->getDuration());

        $scheduledPayRunPeriodDayWorkPattern->clearSave();

        $masterUserRole = UserRoleProject::getMasterForRoleOnDate($scheduledPayRun->userRoleSchedule->userRole, $date);
        CreateTimeSheetWork::make()->handle(
            timeSheetDay: $timeSheetDay,
            scheduledPayRunPeriodDay: $scheduledPayRunPeriodDay,
            userRoleTimeType: $userRoleTimeType,
            recordedVia: RecordedVia::Web,
            start: $timeSheetWorkBlock->getStartAsCarbon(),
            end: $timeSheetWorkBlock->getEndAsCarbon(),
            notes: 'Testing work record',
            itemsAndBreaksInfo: [
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockFirstItem->getStart(),
                    'end' => $workBlockFirstItem->getEnd(),
                    'notes' => 'First work item block',
                ],
                [
                    '_' => 'break',
                    'isScheduled' => false,
                    'start' => $breakTimeSpan->getStartAsCarbon()->toDateTimeString(),
                    'end' => $breakTimeSpan->getEndAsCarbon()->toDateTimeString(),
                    'notes' => 'break time',
                ],
                [
                    '_' => 'item',
                    'type' => 'project',
                    'model_id' => $masterUserRole->id,
                    'start' => $workBlockSecondItem->getStart(),
                    'end' => $workBlockSecondItem->getEnd(),
                    'notes' => 'Second work item block right after break',
                ],
            ],
            doesPreventBroadcasting: true,
        );

        $availability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: false,
            startDate: $scheduledDayData->date,
            dailyStartTime: $partDayLeaveTimeSpan->getStart(),
            dailyEndTime: $partDayLeaveTimeSpan->getEnd(),
            actorUser: $actorUser,
        );

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $availability,
            actorUser: $actorUser,
        );

        /** @var TimeSheetWork[] $works */
        $works = $scheduledPayRunPeriodDay->timeSheetDay->works()->get();

        expect($leaveRequest->id)
            ->and($availability->clashedWorks)->toHaveCount(1)
            ->and($leaveRequest->startDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->endDate)->toEqual($scheduledDayData->date)
            ->and($leaveRequest->duration)->toEqual($expectedLeaveDuration)
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($works[0]->start)->toEqual($expectedFirstWorkBlockAfterSplit->getStartAsCarbon())
            ->and($works[0]->end)->toEqual($expectedFirstWorkBlockAfterSplit->getEndAsCarbon())
            ->and($works[0]->breaks)->toHaveCount(0)
            ->and($works[1]->start)->toEqual($expectedSecondWorkBlockAfterSplit->getStartAsCarbon())
            ->and($works[1]->end)->toEqual($expectedSecondWorkBlockAfterSplit->getEndAsCarbon())
            ->and($works[1]->breaks)->toHaveCount(0)
        ;
    });

    it('can replace leave', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $holidayDates = Holiday::q()->many()->concat(PublicHoliday::q()->many())->pluck('date');
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $expectedSpan = TimeSpan::make('08:00:00', '16:36:00');
        $breakTimeSpan = TimeSpan::make('12:30:00', '13:30:00');
        $leaveExpectedDuration = TimeDuration::zero();
        $scheduledDays = [];

        foreach ($days as $day) {
            if ($day->type instanceof ScheduledPayRunPeriodDayWorkPattern && $day->type->isScheduled) {
                $scheduledDays[] = $day;

                $day->type->patternPeriodShiftDay->scheduleShiftDay->scheduleShift->dailyDoesHaveFixedHours = false;
                $day->type->doesHaveExpectedTimeSpan = true;
                $day->type->expectedSpan = $expectedSpan->copy()->setDate($day->date);
                $day->type->breaks = TimeSpanList::make([$breakTimeSpan->copy()->setDate($day->date)]);
                $day->type->duration = $expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                $day->type->expectedDuration = $expectedSpan->getDuration()->deduct($day->type->breaks->getDuration());
                $day->type->clearSave();

                $leaveExpectedDuration->add($day->type->expectedDuration->copy());
            }
        }

        $oldLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: reset($scheduledDays)->date,
            endDate: reset($scheduledDays)->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->twice();

        $oldLeaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $oldLeaveAvailability,
            actorUser: $actorUser,
        );

        $newLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: reset($scheduledDays)->date,
            endDate: last($scheduledDays)->date,
            actorUser: SuperUserFlag::getOne(),
        );

        $newLeaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $newLeaveAvailability,
            replaced: $oldLeaveRequest,
            actorUser: $actorUser,
        );

        expect($oldLeaveRequest->id)->toBeInt()
            ->and($newLeaveRequest->id)->toBeInt()
            ->and($oldLeaveRequest->refresh()->replacedBy->id)->toEqual($newLeaveRequest->id)
            ->and($oldLeaveRequest->workflow->status)->toEqual(StatusTypes\ReplacedType::ID)
            ->and($newLeaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($newLeaveRequest->originalReplaced->id)->toEqual($oldLeaveRequest->id)
            ->and($newLeaveRequest->duration)->toEqual($leaveExpectedDuration)
            ->and($oldLeaveRequest->days)->toHaveCount(1)
            ->and($newLeaveRequest->days)->toHaveSameSize($scheduledDays)
        ;
    });

    it('can be submitted and applies FTE calculation', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);
        /** @var ScheduledPayRunPeriodWorkPattern $scheduledPayRunPeriodWorkPattern */
        $scheduledPayRunPeriodWorkPattern = $scheduledDayData->type->scheduledPayRunPeriodDay->scheduledPayRunPeriod->specific;
        $scheduledPayRunPeriodWorkPattern->duration->divide(5);
        $scheduledPayRunPeriodWorkPattern->clearSave();

        $userLeaveBankType->bank->user->employeeType->statusType = 'F';
        $userLeaveBankType->bank->user->employeeType->clearSave();

        $userLeaveBankType->type->doesApplyFteAdjustmentsOnFullDayLeave = true;
        $userLeaveBankType->type->clearSave();

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->once();

        $leaveRequest = PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            actorUser: $actorUser,
        );

        /** @var UserRoleSchedulePatternPeriodShiftDay $type */
        $type = $scheduledDayData->type;

        if ($type->doesHaveExpectedTimeSpan || !is_null($type->expectedDuration)) {
            $timeSpan = $type->expectedSpan;
            $expectedDuration = $type->expectedDuration;
        } else {
            $expectedDuration = $type->duration;
            $timeSpan = $type->ordinarySpan;
        }

        $expectedStart = $timeSpan->getStart();
        $expectedEnd = $timeSpan->getEnd();

        expect($leaveRequest->id)->toBeInt()
            ->and($leaveRequest->startDate)->toEqual($fullDayLeaveAvailability->startDate)
            ->and($leaveRequest->endDate)->toEqual($fullDayLeaveAvailability->endDate)
            ->and($leaveRequest->duration)->toEqual($fullDayLeaveAvailability->duration)
            ->and($leaveRequest->workflow->status)->toEqual(StatusTypes\SubmittedType::ID)
            ->and($leaveRequest->duration)->toEqual($expectedDuration)
            ->and($leaveRequest->durationAdjusted)->toEqual($expectedDuration->copy()->multiply($scheduledPayRunPeriodWorkPattern->getFteRatio()))
            ->and($leaveRequest->doesHaveFTECalculation)->toBeTrue()
            ->and($fullDayLeaveAvailability->doesHaveFteAdjustments)->toBeTrue()
            ->and($leaveRequest->days)->toHaveCount(1)
            ->and($leaveRequest->days[0]->timeSpan->getStart())->toEqual($expectedStart)
            ->and($leaveRequest->days[0]->timeSpan->getEnd())->toEqual($expectedEnd)
        ;
    });

    it('cannot place request without attachment if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should throw exception';
        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            reason: $reason,
            actorUser: $actorUser,
        );
    })->throws(AttachmentRequiredException::class);

    it('cannot place request without reason if required', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $this->w_mag_createLeaveModelActionWithSubmitRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
        );
    })->throws(CommentsRequiredException::class);

    it('cannot approve request if attachment is required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $reason = 'it should throw exception';
        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

       $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            reason: $reason,
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireAttachmentPriorToApprove->title());

    it('cannot approve request if reason is required for approval', function () {
        $payRun = TenantSystemSettings::getEarliestOpenPayRun();
        [$user, $userRoleSchedule, $userLeaveBankType, $days] = $this->l_lg_getUserWithActualScheduleAndLeaveAssignment(UserRoleScheduleType::WorkPattern, date: $payRun->startDate);
        $actorUser = SuperUserFlag::getOne();
        $this->w_mag_createLeaveModelActionWithApproveRequirements($userLeaveBankType->type);

        if (is_null($user) || is_null($userRoleSchedule) || is_null($userLeaveBankType)) {
            $this->markTestSkipped('User picked does not have an active duration only schedule with leave types assigned - ' . $user->fullName . ' - start date: ' . $days[0]->date->toDateString());
        }

        $scheduledDayData = $this->l_lg_getFirstScheduledDayFromScheduledPayRunPeriodDaysDefaultData($days);

        if (is_null($scheduledDayData)) {
            expect(true)->not()->toBeTrue('No schedule - ' . $user->externalId . ' - ' . $days[0]->date->toDateString());
        }

        $fullDayLeaveAvailability = CalculateLeavePreAvailabilityFromScheduleWorkPattern::make()->handle(
            userLeaveBankType: $userLeaveBankType,
            userRoleSchedule: $userRoleSchedule,
            isFullDay: true,
            startDate: $scheduledDayData->date,
            endDate: $scheduledDayData->date,
            actorUser: SuperUserFlag::getOne(),
        );

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::partialMock()->expects('sendBroadcastEvents')->never();

        PlaceLeaveRequestFromPreAvailabilityWorkPattern::make()->handle(
            $userRoleSchedule,
            $userLeaveBankType,
            $fullDayLeaveAvailability,
            attachments: $this->g_mf_createRandomMediaFiles($actorUser),
            actorUser: $actorUser,
            doesApprove: true,
        );
    })->throws(InvalidArgumentException::class, 'Pending outstanding action: ' . ModelTaskType::LeaveRequireCommentPriorToApprove->title());
});
