<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Reports\AuditTimeSheetsApprovalByPayRunReport;

use Carbon\Carbon;
use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Support\Helpers\Str;
use Element\ElementTime\Domains\Tenant\PayRuns\Models\PayRun;
use Element\ElementTime\Domains\Tenant\Settings\Models\Department;
use Element\ElementTime\Domains\Tenant\Settings\Models\PayRunType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Users\Support\UserSystemAccessFlags as AccessFlag;
use Element\ElementTime\Domains\Tenant\Workflows\Models\WorkflowTimeline;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ExcludedType;
use Element\ElementTime\Support\Enums\ExcessTimeHourType;
use Element\ElementTime\Support\Facades\CurrentUser;
use Element\ElementTime\Support\Facades\TenantSystemSettings;
use Element\ElementTime\Support\Reports\TenantExcelReport;
use Illuminate\Database\Eloquent\Collection;

class AuditTimeSheetsApprovalByPayRunExcelReport extends TenantExcelReport
{
    const ALLOWED_SYSTEM_ACCESSES = [
        AccessFlag\PayrollOfficerFlag::class,
        AccessFlag\BusinessIntelligenceFlag::class,
        AccessFlag\StaffManagerFlag::class,
        AccessFlag\RoleManagerFlag::class,
    ];

    const TIME = '~30 sec';
    const REPORT_SLUG = 'audit-time-sheets-approval-by-payrun-report';
    const TYPE_SLUG = 'audit-time-sheets-approval-by-payrun-report.xls';

    public $c = AuditTimeSheetsApprovalByPayRunReportConfig::class;

    protected PayRun $payRun;

    protected PayRunType $payRunType;

    protected Department|null $department = null;

    protected bool $doesIncludeSubDepartments = false;

    protected float $fteHoursPerDay = 7.6;

    /** @throws InvalidArgumentException */
    protected function setData(array $info): void
    {
        if (!isset($info['id']) || !is_numeric($info['id'])) {
            throw new InvalidArgumentException('Pay run ID is missing');
        }
        $this->dateFormat = TenantSystemSettings::getValue('settings.dateFormat');
        $this->config->title = $this->c::NAME;
        $this->fteHoursPerDay = TenantSystemSettings::getValue('settings.fteHoursPerWeek') / 5;
        $this->payRun = PayRun::q()->findOrFail($info['id']);
        $this->payRunType = $this->payRun->payRunType;

        if (isset($info['idDepartment']) && strlen($info['idDepartment']) > 0) {
            $this->department = Department::q()->findOrFail($info['idDepartment']);

            if (isset($info['doesIncludeSubDepartments']) && strlen($info['doesIncludeSubDepartments']) > 0) {
                $this->doesIncludeSubDepartments = !!$info['doesIncludeSubDepartments'];
            }
        }
    }

    protected function setValidityDate(bool $doesCache = true): static
    {
        if (!$doesCache || !$this->payRun->isFinished()) {
            $this->reportValidity = Carbon::now()->subSeconds(1);
        }

        return $this;
    }

    public function getReplacedString(string $string): string
    {
        $string = parent::getReplacedString($string);

        if (!is_null($this->department)) {
            $string = Str::replace('{{department.id}}', $this->department->id . ($this->doesIncludeSubDepartments ? '-s' : ''), $string);
            $string = Str::replace('{{department.name}}', $this->department->name . ($this->doesIncludeSubDepartments ? ' and sub departments' : ''), $string);
        } else {
            $string = Str::replace('{{department.id}}', '0', $string);
            $string = Str::replace('{{department.name}}', 'All departments', $string);
        }

        return $string;
    }

    /** @throws \Throwable */
    protected function collectData(): Collection|array
    {
        $constraints = [
            ['PayRun.id', '=', $this->payRun->id],
            [
                [
                    ['IS NULL', 'User.endDate', 'OR'],
                    ['User.endDate', '=', '0000-00-00', 'OR'],
                    ['User.endDate', '>=', $this->payRun->endDate->toDateString(), 'OR'],
                ],
            ],
            ['Workflow.status', '<>', ExcludedType::ID],
        ];

        if (CurrentUser::isLogged()) {
            $user = CurrentUser::getUser();

            if (!$this->doesUserHaveFullAccess($user)) {
                $reports = $this->getReportsUser($user, true, $user->doesIncludeIndirectReportsOnReports);
                $reportIds = [];

                foreach ($reports as $report) {
                    $reportIds[] = $report->id;
                }

                $constraints[] = ['IN', 'Workflow.ownerUser_id', $reportIds];
            }
        }

        if (!is_null($this->department)) {
            $reports = $this->department->repository->getAllStaff($this->doesIncludeSubDepartments, false, $this->payRun->startDate, $this->payRun->endDate);
            $reportIds = [];

            foreach ($reports as $report) {
                $reportIds[] = $report->id;
            }

            $constraints[] = ['IN', 'Workflow.ownerUser_id', $reportIds];
        }

        return TimeSheet::q()
            ->constraints($constraints)
            ->relations([
                'payRunItem.user.userRoles.userRoleMasters',
                'payRunItem.user.userRoles.role',
                'payRunItem.user.userRoles.userDepartments.userRole.user',
                'payRunItem.user.userRoles.userDepartments.department',
                'payRunItem.user.employeeType',
                'payRunItem.user.userManagers.manager',

                'timeSheetDays.excessTime.items',
                'timeSheetDayTimes.excessTimes.items',

                'workflow.timelineEntries.actorUser.userRoles.userRoleMasters',
                'workflow.timelineEntries.actorUser.userRoles.role',
            ])
            ->order('CAST(User.externalId AS SIGNED)')
            ->many();
    }

    /** @throws \Throwable */
    protected function buildReportData(): void
    {
        $timeSheets = $this->collectData();

        $this->fetchSheetSettings($sheet);
        $this->fetchSheetTitle($sheet);
        $this->fetchSheetTotals($sheet, $timeSheets->count());
        $this->fetchSheetData($sheet, $timeSheets);

        $this->sheets = [
            $sheet,
        ];
    }

    protected function fetchSheetSettings(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $sheet->label = $this->payRun->payRunType->name . ' #' . $this->payRun->number;
        $sheet->autoSize = true;
        $sheet->data = [];
    }

    protected function fetchSheetTitle(\stdClass &$sheet = null): void
    {
        $this->validateSheet($sheet);

        $title = new \stdClass();
        $title->type = 'sheet-title';
        $title->title = 'Timesheets approval audit report - ' . $this->payRunType->name . ' #' . $this->payRun->number . ' (' . $this->payRun->startDate->format($this->dateFormat) . ' - ' . $this->payRun->endDate->format($this->dateFormat) . ')';
        $sheet->data[] = $title;
    }

    protected function fetchSheetTotals(\stdClass &$sheet = null, int $numTimeSheets = 0): void
    {
        $this->validateSheet($sheet);

        $data = new \stdClass();
        $data->type = 'data-table';
        $data->columns = [
            $this->createColumn('Pay-run number', 'text', 'center', 2),
            $this->createColumn('Pay-run period', 'text', 'center', 4),
            $this->createColumn('Time-sheets on this pay-run', 'integer', 'center', 5),
            $this->createColumn('Department', 'text', 'center', 7),
        ];
        $data->data = [[
            'config' => [],
            'data' => [
                $this->payRun->number, // Pay-run number
                $this->payRun->startDate->format($this->dateFormat) . ' - ' . $this->payRun->endDate->format($this->dateFormat), // Pay-run period
                $numTimeSheets, // Time-sheets on this pay-run
                !is_null($this->department) ? $this->department->name . ($this->doesIncludeSubDepartments ? ' (and sub-departments)' : '') : 'All departments',
            ],
        ]];
        $sheet->data[] = $data;
    }

    /**
     * @param Collection|TimeSheet[] $timeSheets
     */
    protected function fetchSheetData(\stdClass &$sheet = null, Collection|array $timeSheets = []): void
    {
        $this->validateSheet($sheet);

        $table = new \stdClass();
        $table->type = 'data-table';
        $table->parentColumns = $this->getSheetDataParentHeader();
        $table->columns = $this->getSheetDataHeader();
        $table->columnTitleHeight = 46;
        $table->data = $this->getSheetDataData($timeSheets);
        $sheet->data[] = $table;
    }

    protected function getSheetDataParentHeader(): array
    {
        return [
            $this->createColumn('Staff', 'text', 'left', 6),
            $this->createColumn('Timesheet hours', 'text', 'left', 4),
            $this->createColumn('Excess-time recorded', 'text', 'left', 3),
            $this->createColumn('Excess-time adjusted', 'text', 'left', 3),
            $this->createColumn('Leave', 'text', 'left', 4),
            $this->createColumn('Other', 'text', 'left', 2),
            $this->createColumn('Workflow', 'text', 'left', 2),
        ];
    }

    protected function getSheetDataHeader(): array
    {
        $columns = [
            // Staff
            $this->createColumn('Ext ref ID'),
            $this->createColumn('Name'),
            $this->createColumn('Role'),
            $this->createColumn('Department'),
            $this->createColumn('Employee type'),
            $this->createColumn('Direct manager'),

            // Timesheet hours
            $this->createColumn('Scheduled hours', 'float', 'right'),
            $this->createColumn('Total hours' . "\n" . 'prior to' . "\n" . 'adjustment', 'float', 'right'),
            $this->createColumn('Total hours' . "\n" . 'after' . "\n" . 'adjustment', 'float', 'right'),
            $this->createColumn('Ordinary hours', 'float', 'right'),

            // Excess-time recorded
            $this->createColumn('Accrued', 'float', 'right'),
            $this->createColumn('Paid', 'float', 'right'),
            $this->createColumn('Unpaid', 'float', 'right'),

            // Excess-time adjusted
            $this->createColumn('Accrued', 'float', 'right'),
            $this->createColumn('Paid', 'float', 'right'),
            $this->createColumn('Unpaid', 'float', 'right'),

            // Leave
            $this->createColumn('Total leave', 'float', 'right'),
            $this->createColumn('Total' . "\n" . 'accrued' . "\n" . 'hours', 'float', 'right'),
            $this->createColumn('Total RDO', 'float', 'right'),
            $this->createColumn('Total' . "\n" . 'public' . "\n" . 'holidays', 'float', 'right'),

            // Other
            $this->createColumn('Total' . "\n" . 'penalties', 'money', 'right'),
            $this->createColumn('Total' . "\n" . 'allowances', 'money', 'right'),
        ];

        $columns[] = $this->createColumn('Current status');
        $columns[] = $this->createColumn('Comments');

        return $columns;
    }

    /**
     * @param Collection|TimeSheet[] $timeSheets
     */
    protected function getSheetDataData(Collection|array $timeSheets = []): array
    {
        $tableData = [];

        $alternate = 0;
        foreach ($timeSheets as $timeSheet) {
            $data = $this->getItemData($timeSheet, $lineCount);
            $rowConfig = [
                'color' => $alternate ? 'FFFFFF' : 'DFDFF3',
                'height' => ($lineCount * 14) + 6,
            ];

            $tableData[] = [
                'config' => $rowConfig,
                'data' => $data,
            ];

            $alternate = !$alternate;
        }

        return $tableData;
    }

    protected function getItemData(TimeSheet $timeSheet, &$lineCount): array
    {
        $date = $this->payRun->startDate->copy();

        $user = $timeSheet->payRunItem->user;

        $userRole = $user->repository->getMasterUserRole($date, false);

        $department = '';
        if (!is_null($userRole)) {
            foreach ($userRole->userDepartments as $userDepartment) {
                if ($userDepartment->isActiveOnTime($date)) {
                    if (strlen($department) > 0) {
                        $department .= ', ';
                    }
                    $department .= $userDepartment->department->name;
                    break;
                }
            }
        }
        if (strlen($department) == 0) {
            $department = '-';
        }

        try {
            $directManager = $user->repository->getManager($date);
        } catch (InvalidArgumentException) {
            $directManager = null;
        }

        $data = [
            // Staff
            $user->externalId, // Ext ref ID
            $user->fullName, // Name
            !is_null($userRole) ? $userRole->role->name : '-', // Role
            $department, // Department
            $user->employeeType->name, // Employee type
            !is_null($directManager) ? $directManager->fullName : '-', // Direct manager

            // Timesheet hours
            $this->parseFloat($timeSheet->hoursScheduled), // Scheduled hours
            $this->parseFloat($timeSheet->hoursRecordedCalculation), // Total hours prior to adjustments
            $this->parseFloat($timeSheet->hoursTotalAdjusted), // Total hours after adjustments
            $this->parseFloat($timeSheet->hoursWorkedCalculation), // Ordinary hours

            // Excess-time recorded
            $this->parseFloat($timeSheet->repository->getTotalExcessTimeAccruedHours(ExcessTimeHourType::ActualOriginal)), // Accrued
            $this->parseFloat($timeSheet->repository->getTotalExcessTimePaidHours(ExcessTimeHourType::ActualOriginal) + $timeSheet->repository->getTotalExcessTimeAdditionalHours(ExcessTimeHourType::ActualOriginal)), // Paid
            $this->parseFloat($timeSheet->repository->getTotalExcessTimeUnpaidHours(ExcessTimeHourType::ActualOriginal)), // Unpaid

            // Excess-time adjusted
            $this->parseFloat($timeSheet->repository->getTotalExcessTimeAccruedHoursAdjusted()), // Accrued
            $this->parseFloat($timeSheet->repository->getTotalExcessTimePaidHoursAdjusted() + $timeSheet->repository->getTotalExcessTimeAdditionalHoursAdjusted()), // Paid
            $this->parseFloat($timeSheet->repository->getTotalExcessTimeUnpaidHoursAdjusted()), // Unpaid

            // Leave
            $this->parseFloat($timeSheet->hoursLeaveUsed), // Total leave
            $this->parseFloat($timeSheet->hoursExcessTimeUsed), // Total accrued hours
            $this->parseFloat($timeSheet->hoursRosteredTimeOffUsed), // Total RDO
            $this->parseFloat($timeSheet->hoursPublicHoliday), // Total public holidays

            // Other
            $this->parseFloat($timeSheet->getTotalPenaltyAmount()), // Total penalties
            $this->parseFloat($timeSheet->getTotalAllowanceAmount()), // Total allowances
        ];

        // Workflow
        $currentStatus = '';
        $entries = '';
        foreach ($timeSheet->workflow->timelineEntries as $entry) {
            if (strlen($entries) > 0) {
                $entries .= "\n";
            }

            $entries .= $this->getEntries($entry);
            $currentStatus = $this->getCurrentStatus($entry);
        }

        if ($currentStatus) {
            $data[] = $currentStatus;
        } else {
            $data[] = $timeSheet->workflow->statusClass::NAME;
        }

        $data[] = $entries;

        $lineCount = max([
            substr_count($currentStatus, "\n"),
            substr_count($entries, "\n"),
        ]) + 1;

        return $data;
    }

    protected function getCurrentStatus(WorkflowTimeline $entry): string
    {
        $date = $this->payRun->startDate->copy();
        $label = ucfirst($entry->typeClass::ACTION);

        if (!is_null($entry->actorUser)) {
            $label .= ' by ' . $entry->actorUser->fullName;

            $userRole = $entry->actorUser->repository->getMasterUserRole($date, false);
            if (!is_null($userRole)) {
                $label .= "\n";
                $label .= $userRole->role->name;
            }
        } else {
            $label .= ' automatically';
        }

        $label .= "\n";
        $label .= $entry->dateTime->format($this->dateTimeFormat);

        if (!is_null($entry->reason) && strlen($entry->reason) > 0) {
            $label .= "\n";
            $label .= $entry->reason;
        }

        return $label;
    }

    protected function getEntries(WorkflowTimeline $entry): string
    {
        $timeFormat = TenantSystemSettings::getValue('settings.timeFormat');
        $dateTimeFormat = $this->dateFormat . ' ' . $timeFormat;
        $timeStamp = "[" . $entry->dateTime->format($dateTimeFormat) . "]";
        $personName = !is_null($entry->actorUser) ? $entry->actorUser->fullName : '';
        $description = $entry->typeClass::getDescription($personName, 'timesheet', $entry->reason);

        return $timeStamp . " " . $description;
    }
}
