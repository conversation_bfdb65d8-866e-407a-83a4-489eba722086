<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
            $table->unsignedBigInteger('originalRun_id')->nullable();
            $table->foreign('originalRun_id', 'eimr_or_fn')
                ->references('id')
                ->on('ExternalIntegrationModuleRun')
                ->onUpdate('cascade')
                ->onDelete('set null');
            $table->string('rerunType')->nullable();
        });
    }

    public function down(): void
    {
        try {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropForeign('eimr_or_fn');
            });
        } catch (Throwable) {
        }

        if (Schema::hasColumn('ExternalIntegrationModuleRun', 'originalRun_id')) {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropColumn('originalRun_id');
            });
        }

        if (Schema::hasColumn('ExternalIntegrationModuleRun', 'rerunType')) {
            Schema::table('ExternalIntegrationModuleRun', function (Blueprint $table) {
                $table->dropColumn('rerunType');
            });
        }
    }
};
