<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('PayRun', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            // Identification
            $table->bigIncrements('id');

            $table->bigInteger('payRunType_id')->unsigned();
            $table->bigInteger('payrollOfficer_id')->unsigned()->nullable();
            $table->bigInteger('auditor_id')->unsigned()->nullable();

            $table->date('startDate')->nullable();
            $table->date('endDate')->nullable();
            $table->date('dueDate')->nullable();
            $table->date('processingDate')->nullable();
            $table->dateTime('finishedDateTime')->nullable();

            $table->string('name')->nullable();
            $table->string('description')->nullable();
            $table->string('notes')->nullable();

            $table->longText('approvalWorkflowMap')->nullable(); // From PayRunType (duplicated to keep the current map)

            $table->char('status', 1)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('payRunType_id', 'payrun_payruntype_fn')->references('id')->on('PayRunType')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('payrollOfficer_id', 'payrun_userPRO_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
            $table->foreign('auditor_id', 'payrun_userAUD_fn')->references('id')->on('User')->onUpdate('cascade')->onDelete('restrict');
        });

        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->bigInteger('payRun_id')->unsigned();
            $table->foreign('payRun_id', 'timesheet_payrun_fn')->references('id')->on('PayRun')->onUpdate('cascade')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::drop('PayRun');
        Schema::table('TimeSheet', function (Blueprint $table) {
            $table->dropForeign([
                'timesheet_payrun_fn',
            ]);
            $table->dropColumn([
                'payRun_id',
            ]);
        });
    }
};
