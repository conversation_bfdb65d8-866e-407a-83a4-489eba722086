<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\WorkRecord;

use Element\ElementTime\Applications\TenantApi\Http\V4\Resources\TimeCard\TimeCardBaseResource;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetRecordType\TimeSheetRecordType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Enums\TimeSheetWorkType\TimeSheetWorkType;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWork;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkBreak;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheetWorkItem;
use Element\ElementTime\Support\Enums\RecordedVia;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'WorkRecordBasicResource',
    title: 'Time-card - recorded item',
    required: ['id', 'roleName', 'timeTypeName', 'start', 'end', 'workDuration', 'breakDuration', 'sumDuration', 'notes', 'recordedVia', 'itemsWithBreaks'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 42),
        new OA\Property(property: 'roleName', type: 'string', example: "The jedi"),
        new OA\Property(property: 'timeTypeName', type: 'string', example: "Ordinary"),
        new OA\Property(property: 'start', type: 'string', example: '03:30:00', nullable: true),
        new OA\Property(property: 'end', type: 'string', example: '04:30:00', nullable: true),
        new OA\Property(property: 'workDuration', type: 'float', example: 1.0),
        new OA\Property(property: 'breakDuration', type: 'float', example: 1.56777),
        new OA\Property(property: 'sumDuration', type: 'float', example: 2.56777),
        new OA\Property(property: 'notes', type: 'string', example: 'this is a note', nullable: true),
        new OA\Property(property: 'recordedVia', type: 'string', enum: RecordedVia::class, example: RecordedVia::KioskTimer),
        new OA\Property(property: 'items_and_breaks', type: 'array', items: new OA\Items(required: ['_', 'id', 'type', 'fullCostCentreLabel', 'isDurationOnly', 'start', 'end', 'duration', 'notes', 'plantTimesCount', 'allowanceItemsCount'], properties: [
            new OA\Property(property: '_', type: 'string', enum: TimeSheetRecordType::class, example: TimeSheetRecordType::Item),
            new OA\Property(property: 'id', type: 'integer', example: 41),
            new OA\Property(property: 'type', description: 'this only returns if "_" = item', type: 'string', enum: TimeSheetWorkType::class, example: TimeSheetWorkType::Project),
            new OA\Property(property: 'fullCostCentreLabel', description: 'this only returns if "_" = item', type: 'string', example: 'Capital Mains - Trunk (Town to Town) Jugiong - Valve Installation/Replacement - W3253 - 914'),
            new OA\Property(property: 'isDurationOnly', description: 'this only returns if "_" = item', type: 'boolean', example: false),
            new OA\Property(property: 'start', type: 'string', example: '03:30:00'),
            new OA\Property(property: 'end', type: 'string', example: '04:30:00'),
            new OA\Property(property: 'duration', type: 'float', example: 1.0),
            new OA\Property(property: 'notes', type: 'string', example: 'this is a note', nullable: true),
            new OA\Property(property: 'plantTimesCount', description: 'this only returns if "_" = item', type: 'integer', example: 2),
            new OA\Property(property: 'allowanceItemsCount', description: 'this only returns if "_" = item', type: 'integer', example: 1),
        ])),
    ],
)]
class WorkRecordBasicResource extends TimeCardBaseResource
{
    public static $relations = [
        'userRoleTimeType.userRole.role',
        'userRoleTimeType.timeType',
        'breaks',
        'items.plantSheetDayTimes',
        'items.allowanceEntries',
    ];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var TimeSheetWork $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'roleName' => $r->userRoleTimeType->userRole->role->name,
            'timeTypeName' => $r->userRoleTimeType->timeType->name,
            'start' => $this->fetchDateTime($r->start),
            'end' => $this->fetchDateTime($r->end),
            'workDuration' => $r->workDuration->getTotalHours(),
            'breakDuration' => $r->breakDuration->getTotalHours(),
            'sumDuration' => $r->sumDuration->getTotalHours(),
            'notes' => $r->notes,
            'recordedVia' => $r->recordedVia->id(),
            'items_and_breaks' => $this->fetchItemsWithBreaks($r),
        ];
    }

    /** @throws \Throwable */
    protected function fetchItemsWithBreaks(TimeSheetWork $r): array
    {
        $ret = [];

        foreach ($r->itemsWithBreaks as $item) {
            $v = [];

            if ($item instanceof TimeSheetWorkItem) {
                $v['_'] = TimeSheetRecordType::Item->id();
                $v['id'] = $item->id;
                $v['type'] = $item->type->id();
                $v['fullCostCentreLabel'] = $item->fullCostCentreLabel;
                $v['isDurationOnly'] = $item->isDurationOnly;
                $v['start'] = $this->fetchDateTime($item->start);
                $v['end'] = $this->fetchDateTime($item->end);
                $v['duration'] = $item->duration->getTotalHours();
                $v['notes'] = $item->notes;
                $v['plantTimesCount'] = $item->plantSheetDayTimes->count();
                $v['allowanceItemsCount'] = $item->allowanceEntries->count();
            } else if ($item instanceof TimeSheetWorkBreak) {
                $v['_'] = TimeSheetRecordType::Break->id();
                $v['id'] = $item->id;
                $v['start'] = $this->fetchDateTime($item->start);
                $v['end'] = $this->fetchDateTime($item->end);
                $v['duration'] = $item->duration->getTotalHours();
                $v['notes'] = $item->notes;
            }

            $ret[] = $v;
        }

        return $ret;
    }
}
