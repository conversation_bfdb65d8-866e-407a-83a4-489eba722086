<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('UserRoleManager', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->id();
            $table->unsignedBigInteger('userRole_id');
            $table->foreign('userRole_id', 'urman_ur_fn')
                ->references('id')->on('UserRole')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->unsignedBigInteger('manager_id');
            $table->foreign('manager_id', 'urman_u_fn')
                ->references('id')->on('User')
                ->onUpdate('cascade')->onDelete('restrict');

            $table->date('startDate');
            $table->date('endDate')->nullable()->default(null);
            $table->text('comments')->nullable()->default(null);

            $table->timestamps();
        });

        Schema::table('UserRole', function (Blueprint $table) {
            $table->string('externalId')->nullable()->after('id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('UserRoleManager');

        if (Schema::hasColumn('UserRole', 'externalId')) {
            Schema::table('UserRole', function (Blueprint $table) {
                $table->dropColumn('externalId');
            });
        }
    }
};
