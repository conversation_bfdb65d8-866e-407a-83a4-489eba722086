<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::create('___I_CivicaAuthorityLeaveTypeMap', function (Blueprint $table) {
            $table->engine = 'InnoDB';
            $table->bigIncrements('id');

            $table->unsignedBigInteger('externalIntegrationModule_id');
            $table->foreign('externalIntegrationModule_id', '_i_caltm_eim_fn')
                ->references('id')
                ->on('ExternalIntegrationModule')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // Type identification (can be a LeaveType, RosteredTimeOffType, or null if it's Accrued hours)
            $table->string('type_type')->nullable();
            $table->unsignedBigInteger('type_id')->nullable();

            $table->string('hourTypeCode')->nullable();
            $table->boolean('isActive')->default(false);

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('___I_CivicaAuthorityLeaveTypeMap');
    }
};
