<?php

namespace Element\ElementTime\Applications\TenantApi\Http\V4\Resources\Kiosk;

use Element\ElementTime\Domains\Tenant\_System\Models\KioskDevice;
use Element\ElementTime\Support\Domains\Resource;
use Illuminate\Http\Request;
use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'KioskDeviceBasicWithKeyPairResource',
    title: 'Kiosk Device data with key pair',
    required: ['id', 'name', 'localization', 'appId', 'secret'],
    properties: [
        new OA\Property(property: 'id', type: 'integer', example: 1),
        new OA\Property(property: 'name', type: 'string', example: 'kiosk tablet front door'),
        new OA\Property(property: 'localization', type: 'string', example: 'front door'),
        new OA\Property(property: 'appId', type: 'string', example: 'uuid-for-tablet'),
        new OA\Property(property: 'secret', type: 'string', example: 'secret'),
    ],
)]
class KioskDeviceBasicWithKeyPairResource extends Resource
{
    public static $relations = [];

    /** @throws \Throwable */
    public function toArray(Request $request): array
    {
        /** @var KioskDevice $r */
        $r = $this->getModel();

        return [
            'id' => $r->id,
            'name' => $r->name,
            'localization' => $r->localization,

            'appId' => $r->appId,
            'secret' => $r->secret,
        ];
    }
}
