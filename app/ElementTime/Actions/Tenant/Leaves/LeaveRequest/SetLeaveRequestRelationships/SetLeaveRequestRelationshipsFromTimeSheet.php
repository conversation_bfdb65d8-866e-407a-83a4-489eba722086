<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\SetLeaveRequestRelationships;

use Element\ElementTime\Domains\Tenant\Leaves\Models\LeaveRequest;
use Element\ElementTime\Domains\Tenant\TimeSheets\Models\TimeSheet;
use Element\ElementTime\Domains\Tenant\Workflows\Support\WorkflowStatusTypes\ApprovedType;
use Illuminate\Database\Eloquent\Collection;

class SetLeaveRequestRelationshipsFromTimeSheet extends BaseSetLeaveRequestRelationships
{
    /** @throws \Throwable */
    public function handle(TimeSheet $timeSheet): void
    {
        if ($timeSheet->payRunItem->payRun->isFinished()) {
            return;
        }

        if ($timeSheet->repository->isApproved()) {
            return;
        }

        $timeSheet->loadMissing([
            'payRunItem.payRun',
            'timeSheetDays',
            ...static::getScheduledPayRunRelationships('scheduledPayRuns'),
        ]);

        $allLeaveRequests = LeaveRequest::q()
            ->constraints([
                ['User.id', '=', $timeSheet->payRunItem->user_id],
                ['Workflow.status', '=', ApprovedType::ID],
                ['LeaveRequest.startDate', '>=', $timeSheet->startDate->toDateString()],
                ['LeaveRequest.endDate', '<=', $timeSheet->endDate->toDateString()],
            ])
            ->relations(static::getLeaveRequestRelationships())
            ->many();

        foreach ($timeSheet->scheduledPayRuns as $scheduledPayRun) {
            /** @var LeaveRequest[]|Collection $leaveRequests */
            $leaveRequests = $allLeaveRequests->where('userRoleSchedule_id', '=', $scheduledPayRun->userRoleSchedule_id);

            foreach ($leaveRequests as $leaveRequest) {
                $this->setLeaveRequestRelationships($leaveRequest, $scheduledPayRun, $timeSheet);
            }
        }

        $timeSheet->load(['leaveRequests']);
    }

    /** @throws \Throwable */
    public function asJob(TimeSheet $timeSheet): void
    {
        $this->handle($timeSheet);
    }
}
