<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

namespace Element\ElementTime\Actions\Tenant\Leaves\LeaveRequest\GetOptionsForLeaveRequest;

use Element\Core\Exceptions\InvalidArgumentException;
use Element\Core\Exceptions\NotAssignedException;
use Element\Core\Exceptions\UnauthorizedActionException;
use Element\ElementTime\Actions\Tenant\Leaves\BaseLeavesAction;
use Element\ElementTime\Domains\Tenant\Users\Models\User;

abstract class BaseGetOptionsForLeaveRequest extends BaseLeavesAction
{
    /** @throws \Throwable */
    protected function validateAccess(User $user, User $actorUser): void
    {
        if (!$user->hasLeaveOptions) {
            throw new NotAssignedException([
                'model1' => 'Staff',
                'model2' => 'have leave',
            ]);
        }

        if (!$this->canActorApplyForLeave($user, $actorUser)) {
            throw new UnauthorizedActionException([
                'action' => 'place',
                'model' => 'leave',
                'reason' => '',
            ]);
        }
    }

    /** \@throws InvalidArgumentException */
    protected function canActorApplyForLeave(User $user, User $actor): bool
    {
        if ($user->id == $actor->id) {
            return true;
        }

        if ($actor->isSuperUser()) {
            return true;
        }

        if ($actor->access->isPayrollOfficer(true)) {
            return true;
        }

        return $user->repository->doesUserHaveManagerRights($actor, true, true);
    }
}
