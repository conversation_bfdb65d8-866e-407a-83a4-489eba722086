<?php
/*
 *       _                           _  _____ ___ __  __ _____
 *   ___| | ___ _ __ ___   ___ _ __ | ||_   _|_ _|  \/  | ____|
 *  / _ \ |/ _ \ '_ ` _ \ / _ \ '_ \| __|| |  | || |\/| |  _|
 * |  __/ |  __/ | | | | |  __/ | | | |_ | |  | || |  | | |___
 *  \___|_|\___|_| |_| |_|\___|_| |_|\__||_| |___|_|  |_|_____|
 *
 * @link https://www.elementtime.com
 * @copyright 2025 Adroit Creations
 */

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up(): void
    {
        $this->down();

        Schema::table('Shift', function (Blueprint $table) {
            $table->boolean('doesDeductBreak')->default(false);
            $table->decimal('minimumHoursToDeductBreak', 16, 10)->nullable();
        });

        Schema::table('UserShift', function (Blueprint $table) {
            $table->boolean('doesDeductBreak')->default(false);
            $table->decimal('minimumHoursToDeductBreak', 16, 10)->nullable();
        });

        Schema::table('UserShiftDay', function (Blueprint $table) {
            $table->dateTime('startDateTime')->nullable();
            $table->dateTime('endDateTime')->nullable();
            $table->decimal('hours', 16, 10)->nullable();
        });

        Schema::create('UserShiftDayBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('userShiftDay_id')->unsigned();

            $table->dateTime('startDateTime');
            $table->dateTime('endDateTime');
            $table->decimal('hours', 16, 10);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('userShiftDay_id', 'usdb_usd_fn')->references('id')->on('UserShiftDay')->onUpdate('cascade')->onDelete('cascade');
        });

        Schema::create('TimeSheetItemDayTimeBreak', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->bigInteger('timeSheetItemDayTime_id')->unsigned();
            $table->bigInteger('userShiftDayBreak_id')->unsigned();

            $table->dateTime('startDateTime');
            $table->dateTime('endDateTime');
            $table->decimal('hours', 16, 10);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('timeSheetItemDayTime_id', 'tsidtb_tsidt_fn')->references('id')->on('TimeSheetItemDayTime')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign('userShiftDayBreak_id', 'tsidtb_usdb_fn')->references('id')->on('UserShiftDayBreak')->onUpdate('cascade')->onDelete('cascade');
        });

        // Migrate data from old UserShiftDay structure
        if (Schema::hasColumn('UserShiftDay', 'value')) {
            DB
                ::table('UserShiftDayBreak')
                ->delete();

            $userShiftDays = DB
                ::table('UserShiftDay')
                ->whereNotNull('value')
                ->whereNull('startDateTime')
                ->whereNull('endDateTime')
                ->whereNull('hours')
                ->get();
            foreach ($userShiftDays as $userShiftDay) {
                $value = json_decode($userShiftDay->value);

                if ($userShiftDay->type == 'O'
                    && isset($value->time)
                    && !is_null($value->time)
                ) {
                    $value->time = floatval($value->time);
                    if ($value->time < 0) {
                        $value->time = 0;
                    }
                    DB
                        ::table('UserShiftDay')
                        ->where('id', '=', $userShiftDay->id)
                        ->update([
                            'hours' => $value->time,
                        ]);
                } elseif ($userShiftDay->type != 'O'
                    && isset($value->startTime)
                    && !is_null($value->startTime)
                    && isset($value->endTime)
                    && !is_null($value->endTime)
                ) {
                    // try {
                    $startDateTime = Carbon::parse($userShiftDay->date . ' ' . $value->startTime);
                    $endDateTime = Carbon::parse($userShiftDay->date . ' ' . $value->endTime);
                    if ($startDateTime->gt($endDateTime)) {
                        $endDateTime->addDays(1);
                    }

                    $breaks = [];
                    if (isset($value->breaks)
                        && is_array($value->breaks)
                        && count($value->breaks) > 0
                    ) {
                        foreach ($value->breaks as $break) {
                            $breakStartDateTime = Carbon::parse($userShiftDay->date . ' ' . $break->startTime);
                            $breakEndDateTime = Carbon::parse($userShiftDay->date . ' ' . $break->endTime);
                            if ($breakStartDateTime->gt($breakEndDateTime)) {
                                $breakEndDateTime->addDays(1);
                            }

                            $newBreak = new stdClass();
                            $newBreak->startDateTime = $breakStartDateTime;
                            $newBreak->endDateTime = $breakEndDateTime;
                            $newBreak->hours = $breakStartDateTime->diffInSeconds($breakEndDateTime) / 60 / 60;

                            DB
                                    ::table('UserShiftDayBreak')
                                    ->insert([
                                        'userShiftDay_id' => $userShiftDay->id,
                                        'startDateTime' => $newBreak->startDateTime->toDateTimeString(),
                                        'endDateTime' => $newBreak->endDateTime->toDateTimeString(),
                                        'hours' => $newBreak->hours,
                                    ]);

                            $breaks[] = $newBreak;
                        }
                    }

                    $hours = $startDateTime->diffInSeconds($endDateTime) / 60 / 60;

                    foreach ($breaks as $break) {
                        $hours = $hours - $break->hours;
                    }

                    DB
                            ::table('UserShiftDay')
                            ->where('id', '=', $userShiftDay->id)
                            ->update([
                                'startDateTime' => $startDateTime->toDateTimeString(),
                                'endDateTime' => $endDateTime->toDateTimeString(),
                                'hours' => $hours,
                            ]);
                    // }
                    // catch(\Throwable $e) {
                    //     continue;
                    // }
                }
            }
        }
    }

    public function down(): void
    {
        //region Table TimeSheetItemDayTimeBreak
        Schema::dropIfExists('TimeSheetItemDayTimeBreak');
        //endregion

        //region Table UserShiftDayBreak
        Schema::dropIfExists('UserShiftDayBreak');
        //endregion

        //region Table UserShiftDay
        if (Schema::hasColumn('UserShiftDay', 'hours')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('hours');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'endDateTime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('endDateTime');
            });
        }
        if (Schema::hasColumn('UserShiftDay', 'startDateTime')) {
            Schema::table('UserShiftDay', function (Blueprint $table) {
                $table->dropColumn('startDateTime');
            });
        }
        //endregion

        //region Table UserShift
        if (Schema::hasColumn('UserShift', 'minimumHoursToDeductBreak')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('minimumHoursToDeductBreak');
            });
        }

        if (Schema::hasColumn('UserShift', 'doesDeductBreak')) {
            Schema::table('UserShift', function (Blueprint $table) {
                $table->dropColumn('doesDeductBreak');
            });
        }
        //endregion

        //region Table Shift
        if (Schema::hasColumn('Shift', 'minimumHoursToDeductBreak')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('minimumHoursToDeductBreak');
            });
        }

        if (Schema::hasColumn('Shift', 'doesDeductBreak')) {
            Schema::table('Shift', function (Blueprint $table) {
                $table->dropColumn('doesDeductBreak');
            });
        }
        //endregion
    }
};
